# 代码文档 - Makefile

## 文件作用
项目构建和部署的自动化脚本，定义了完整的CI/CD流程。

## 关键变量
- **V**: 版本号，默认 2.0.1
- **CHANNEL**: 渠道标识，默认 privatization

## 主要构建目标

### 核心流程
- **all**: 执行完整打包流程
- **package**: 构建 → 配置 → Docker镜像 → 基础镜像打包
- **build**: 构建所有服务组件

### 服务构建
- **build-know**: 构建知识服务 (DataChat-API)
  - 镜像: `know-server:$(V)-$(CHANNEL)`
  - 推送到: `d.ai2e.cn:5000`

- **build-manager**: 构建管理服务 (manager-server)
  - 镜像: `connectai-manager:$(V)-$(CHANNEL)`
  - 推送到: `d.ai2e.cn:5000`

- **build-admin-panel**: 构建管理面板前端
  - 使用 pnpm 构建 Vue.js 应用

- **build-messenger**: 构建消息Web端
  - 使用 pnpm 构建前端应用

- **build-helper**: 构建浏览器扩展
  - 生成 Chrome 扩展 (.crx 文件)

### 部署配置
- **build-config**: 生成部署配置
  - 复制 deploy 目录到 build
  - 替换 docker-compose.yml 中的镜像版本
  - 合并前端构建产物
  - 生成 `deploy-$(V)-$(CHANNEL).tar.gz`

### 镜像管理
- **images-base.tar.gz**: 打包基础依赖镜像
- **docker-images**: 打包项目镜像

## 技术特点
- 支持多渠道版本构建
- 自动化Docker镜像构建和推送
- 统一的前端构建流程
- 完整的部署包生成
