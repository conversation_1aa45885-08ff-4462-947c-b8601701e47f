from core.base_handler import (
    arguments,
    authenticated
)

from core.utils import ObjIDStr
from modules.account.model import NewAdminModel, ProductModel

from .base_handler import BaseHandler

# /admin/company
# TODO: 注意，在写好管理后台用户注册登陆后要换成新的基类，不使用 BaseHandler
class CompanyHandler(BaseHandler):

    benifits = False

    def initialize(self, benifits=False):
        self.benifits = benifits

    # GET /admin/company
    # GET /admin/company/benifits
    @authenticated
    @arguments
    async def get(
        self,
        tenant_id: ObjIDStr = "", # 占位符，不会被使用 
        page: int = 1,
        page_size: int = 10,
        query: str = "",
        version: str = "",        # 此处注意，主要设置一个非空的默认值
        model: NewAdminModel = None
    ):
        if self.benifits:
            with ProductModel() as product_model:
                product_list = await product_model.get_product_list_simple()

                self.finish({
                    "code": 0,
                    "msg": "success",
                    "data": product_list
                })
                return

        if not page:
            page = 1
        if not page_size:
            page_size = 10

        tenant_list, total = await model.get_tenant_list(query, query, version, page, page_size)

        # 当提供的page和page_size不合法时，返回空列表（get_tenant_list处理）
        self.finish({
            "code": 0,
            "msg": "success",
            "page": page,
            "page_size": page_size,
            "total": total,
            "data": [
                {
                    "id": tenant["id"],
                    "name": tenant["name"],
                    "display_name": tenant["display_name"],
                    "contact": tenant["email"],
                    "version": tenant["version"],
                    "bot_count": tenant["bot_count"],
                    # "proxy_count": 0, # TODO: 这里的 proxy 需要加上（远程查询？考虑效率问题）
                    "seat_count": tenant["seat_count"],
                    "expire_date": (tenant["product_expired"]).split(" ")[0] if tenant["product_expired"] else "",
                } for tenant in tenant_list
            ]
        })

    # POST /admin/company
    # POST /admin/company/{company_id}/benifits
    @authenticated
    @arguments
    async def post(
        self, 
        tenant_id: ObjIDStr = "", action: str = "", # 占位符，不会被使用
        display_name: str = "",
        email: str = "",
        telephone: str = "",
        password: str = "",
        version: str = "",
        seat_count: int = 0,
        expire_time: int = 0,
        model: NewAdminModel = None
    ):
        if self.benifits:
            await model.add_benifits(tenant_id, version, seat_count, expire_time)
            self.finish({
                "code": 0,
                "msg": "success",
            })
            return

        no_email = email == "" or email is None
        no_telephone = telephone == "" or telephone is None
        if no_email and no_telephone:
            self.finish({
                "code": 1,
                "msg": "邮箱和手机号不能同时为空",
            })
            return

        if display_name == "" or display_name is None:
            self.finish({
                "code": 1,
                "msg": "公司展示名称不能为空",
            })
            return

        if password == "" or password is None:
            self.finish({
                "code": 1,
                "msg": "密码不能为空",
            })
            return

        tenant_id, user_id, admin_id = await model.register(email, password, display_name, telephone)

        self.finish({
            "code": 0,
            "msg": "success",
            "data": {
                "id": tenant_id,
                "display_name": display_name,
            }
        })

    # PUT /admin/company/{company_id}
    @authenticated
    @arguments
    async def put(
        self, 
        tenant_id: ObjIDStr = "", 
        display_name: str = "",
        account: str = "",
        password: str = "",
        seat_count: int = 0,
        # expire_date: str = "",
        model: NewAdminModel = None
    ):
        # for arg in [display_name, account, password, seat_count]:
        #     if arg == "" or arg is None:
        #         self.finish({
        #             "code": 1,
        #             "msg": "参数不能为空",
        #         })
        #         return

        await model.update_tenant(tenant_id, display_name, account, password, seat_count)

        self.finish({
            "code": 0,
            "msg": "success",
        })