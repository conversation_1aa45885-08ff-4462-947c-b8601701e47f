export const createConfig = (type: 'feishu' | 'dingding' | 'wework' | 'wxwork') => {
  const confMap = {
    feishu: {
      name: '',
      app_id: '',
      app_secret: '',
      encript_key: '',
      validation_token: ''
    },
    dingding: {
      name: '',
      agent_id: '',
      app_id: '', // 这里还是使用app_id字段，只是在钉钉配置的时候label展示处理成app_key
      app_secret: ''
    },
    wework: {
      name: '',
      crop_id: '',
      agent_id: '',
      app_secret: '',
      validation_token: '',
      encript_key: '',
    }
  };

  return confMap[type];
};
