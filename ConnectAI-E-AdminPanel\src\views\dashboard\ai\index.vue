<template>
  <div>
    <n-card
      class="h-full shadow-sm rounded-16px pt-2"
      content-style="overflow:hidden"
      header-style="padding:20px 20px 10px 40px"
    >
      <template #header>
        <div class="w-full flex justify-between items-center">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
              Search
            </label>
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="t('message.ai.srzy')"
              />
              <button
                class="text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ t('message.ai.ss') }}
              </button>
            </div>
          </form>
          <a href="https://connect-ai.feishu.cn/docx/CoBydipXSoQoQ7xmTBVcsZECnTf" target="_blank">
            <button
              type="button"
              class="text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16" class="mr-2"><path fill="currentColor" fill-rule="evenodd" d="M14.5 2H9l-.35.15l-.65.64l-.65-.64L7 2H1.5l-.5.5v10l.5.5h5.29l.86.85h.7l.86-.85h5.29l.5-.5v-10l-.5-.5zm-7 10.32l-.18-.17L7 12H2V3h4.79l.74.74l-.03 8.58zM14 12H9l-.35.15l-.14.13V3.7l.7-.7H14v9zM6 5H3v1h3V5zm0 4H3v1h3V9zM3 7h3v1H3V7zm10-2h-3v1h3V5zm-3 2h3v1h-3V7zm0 2h3v1h-3V9z" clip-rule="evenodd"/></svg>
              {{ t('message.dashboard.pzsc') }}
            </button>
          </a>
        </div>
        <n-divider class="p0 !mb-2" />
      </template>
      <div class="flex content-start justify-center flex-wrap mx-auto w-[100%] gap-[38px] h-full overflow-auto">
        <div
          v-for="item in data"
          :key="item.id"
          class="flex flex-wrap flex-col justify-between w-[470px] h-[210px] p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"
        >
          <div class="flex mb-2 items-center justify-between">
            <h5 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
              {{ item.name }}
            </h5>
            <span
              v-if="deny(`resource.edit.${item.id}`)"
              class="bg-yellow-100 text-yellow-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300 ml-2"
              >{{ UPGRADE_LABEL }}</span
            >
            <span class="flex-1" />
            <n-tag v-if="!item.api_key" round :bordered="false">
              {{ t('message.ai.dpz') }}
              <template #icon>
                <n-icon :component="StopCircleSharp" />
              </template>
            </n-tag>
            <n-tag v-else round :bordered="false" type="success" class="cursor-pointer">
              {{ t('message.ai.ypz') }}
              <template #icon>
                <n-icon :component="CheckmarkCircle" />
              </template>
            </n-tag>
          </div>
          <p class="h-16 mb-2 font-normal text-gray-700 dark:text-gray-400 line-clamp-2" :title="item.description">
            {{ item.description }}
          </p>
          <div class="flex justify-between items-center">
            <div class="flex-center text-gray-400">
              <div>{{ t('message.ai.glyy') + ' ' + item.bot_instance_count }}</div>
              <n-divider v-if="item.amount !== null" vertical />
              <div v-if="item.amount !== null">{{ t('message.ai.ye') }}: {{ item.amount }}</div>
              <n-divider v-if="item.recharge_link !== null" vertical />
              <a
                v-if="item.recharge_link !== null"
                href="#"
                class="inline-flex items-center text-blue-600 hover:underline"
                @click="handleBuy(item.recharge_link)"
              >
                {{ t('message.ai.cz') }}
                <svg
                  class="w-3 h-3 ml-2.5"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 18 18"
                >
                  <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11v4.833A1.166 1.166 0 0 1 13.833 17H2.167A1.167 1.167 0 0 1 1 15.833V4.167A1.166 1.166 0 0 1 2.167 3h4.618m4.447-2H17v5.768M9.111 8.889l7.778-7.778"
                  />
                </svg>
              </a>
            </div>
            <a
              href="#"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="(e) => handleConfig(e, item)"
            >
              {{ t('message.ai.pz') }}
              <svg
                class="w-3.5 h-3.5 ml-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 10"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M1 5h12m0 0L9 1m4 4L9 9"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </n-card>
    <n-modal v-model:show="showModal">
      <n-card style="width: 600px" :title="title" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <n-alert type="info" class="mb-4">
          <template #header>
            <div class="flex justify-between text-sm">
              <div>{{ top.label }}</div>
              <a :href="top.url" target="_blank" class="text-blue-700 text-center">{{ $t('message.market.ckxq') }}</a>
            </div>
          </template>
        </n-alert>
        <n-form ref="formRef" :label-width="80" :model="formValue">
          <div class="flex flex-wrap gap-2">
            <div v-for="(item, i) in form" :key="i" :class="item.size === 'full' ? 'w-full' : 'w-[49%]'">
              <model-check-list
                v-if="item.options"
                :config="item"
                :data="formValue[item.name]"
                @change="(data) => handleModelChange(data, item.name)"
              />
              <div v-else-if="item.name === 'tip'" class="text-gray-500 mb-2">
                {{ item.label }}
              </div>
              <n-form-item
                v-else
                :label="item.label"
                :path="item.name"
                :rule="{
                  required: item.required,
                  trigger: ['input', 'blur'],
                  message: `${t('message.ai.qsr') + item.label}`
                }"
              >
                <n-input v-model:value="formValue[item.name]" :placeholder="item.placeholder" />
              </n-form-item>
            </div>
          </div>
        </n-form>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.ai.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              {{ t('message.ai.qd') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- 弹框 -->
    <purchase-tip v-model:value="showTip" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst } from 'naive-ui';
import { StopCircleSharp, CheckmarkCircle } from '@vicons/ionicons5';
import { isEmpty } from 'lodash-es';
import { UPGRADE_LABEL } from '@/constants';
import { useTenantPrivilege } from '@/hooks';
import { getAiResourceList, updateAiResource } from '@/service/api/ai-resource';
import ModelCheckList from './components/modelCheckList.vue';
import { t } from '@/locales';

const { allow, deny } = useTenantPrivilege();
const showModal = ref(false);
const showTip = ref(false);

const keyword = ref('');

const message = useMessage();

const data = ref<ApiAIResource.AIResource[]>([]);
const formRef = ref<FormInst | null>(null);

const title = ref('');
const top = ref({
  label: '',
  url: ''
});

const form = ref();

const formValue = ref<any>({});

function handleClose() {
  showModal.value = false;
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (formValue.value.id) {
        const { id, ...data } = formValue.value;
        await updateAiResource({ id, data });
      }
      message.success(t('message.msg.bccg'));
      getList();
      handleClose();
    }
  });
}

function handleConfig(e: Event, item: ApiAIResource.AIResource) {
  e.preventDefault();

  if (allow(`resource.edit.${item.id}`)) {
    title.value = item.config.title;
    top.value = item.config.top;
    item.config.form.forEach((item: any) => {
      if (item.options) {
        formValue.value[item.name] = isEmpty(item.value) ? [] : item.value;
      } else {
        formValue.value[item.name] = item.value;
      }
    });
    formValue.value.id = item.id;
    form.value = { ...item.config.form };
    showModal.value = true;
  } else {
    showTip.value = true;
  }
}

function handleBuy(url: string) {
  open(url);
}

async function getList(keyword?: string) {
  try {
    const res = await getAiResourceList({ page: 1, size: 99999, keyword });
    data.value = res.data?.data || [];
  } catch (err) {}
}

function handleSearch(e: Event) {
  e.preventDefault();
  getList(keyword.value);
}

function handleModelChange(data: any, name: string) {
  formValue.value[name] = data;
}

onMounted(() => {
  getList();
});
</script>
<style scoped lang="scss">
.n-alert:deep(.n-alert-body) {
  padding-top: 8px;
  padding-bottom: 8px;
}

.n-alert:deep(.n-alert__icon) {
  margin-top: 6px;
  margin-left: 10px;
}

.cardShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}
</style>
