'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M12 8V2H6a2 2 0 0 0-2 2v10.035a3.5 3.5 0 0 1 2.377 1.471A2.95 2.95 0 0 1 8.95 14h.1c1.02 0 1.92.518 2.45 1.306A2.947 2.947 0 0 1 13.95 14h.1A2.95 2.95 0 0 1 17 16.95V17c0 .45-.17.86-.45 1.17c.288.439.45.96.45 1.507v.373c0 .747-.278 1.43-.736 1.95H18a2 2 0 0 0 2-2V10h-6a2 2 0 0 1-2-2zm2.05 14A1.95 1.95 0 0 0 16 20.05v-.373a1.748 1.748 0 0 0-1.1-1.625l-.566-.226l-.713-.428a.25.25 0 0 1-.121-.214v-.234a.45.45 0 0 1 .45-.45h.1a.45.45 0 0 1 .45.45V17a.75.75 0 0 0 1.5 0v-.05A1.95 1.95 0 0 0 14.05 15h-.1A1.95 1.95 0 0 0 12 16.95v.373a1.748 1.748 0 0 0 1.1 1.625l.566.226l.713.428a.25.25 0 0 1 .121.214v.234a.45.45 0 0 1-.45.45h-.1a.45.45 0 0 1-.45-.45V20a.75.75 0 0 0-1.5 0v.05A1.95 1.95 0 0 0 13.95 22h.1zM11 20.05A1.95 1.95 0 0 1 9.05 22h-.1A1.95 1.95 0 0 1 7 20.05V20a.747.747 0 0 1 .75-.75a.751.751 0 0 1 .75.75v.05a.45.45 0 0 0 .45.45h.1a.45.45 0 0 0 .45-.45v-.234a.25.25 0 0 0-.121-.214l-.713-.428l-.566-.226A1.75 1.75 0 0 1 7 17.323v-.373A1.95 1.95 0 0 1 8.95 15h.1A1.95 1.95 0 0 1 11 16.95V17a.747.747 0 0 1-.75.75a.75.75 0 0 1-.75-.75v-.05a.45.45 0 0 0-.45-.45h-.1a.45.45 0 0 0-.45.45v.234c0 .088.046.169.121.214l.713.428l.566.226a1.75 1.75 0 0 1 1.1 1.625v.373zM5.95 22l.05-.05V22h-.05zm-1.124-.38A2.498 2.498 0 0 0 6 19.5a.75.75 0 1 0-1.5 0a1 1 0 1 1-2 0v-2a1 1 0 1 1 2 0a.75.75 0 1 0 1.5 0a2.5 2.5 0 0 0-5 0v2a2.5 2.5 0 0 0 3.826 2.12zM13.5 8V2.5l6 6H14a.5.5 0 0 1-.5-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DocumentCss24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
