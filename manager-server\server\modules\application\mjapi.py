class MJApiTool(CTool):
    name: str = 'mjapi'
    # https://connect-ai.feishu.cn/wiki/Ufv1wjXZoiFWBPkD7ZYcuQ7dnug
    description: str = 'midjourney api'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs):
                if model.streaming and platform == 'feishu':
                    if token and token != '100%':
                        contents = []
                        if kwargs.get('imageUrl', ''):
                            try:
                                img_bytes = syncify(download_bytes)(kwargs['imageUrl'], 2)
                                img_bytes = compress_image(img_bytes, quality=80, max_size=1024)
                                img_key = syncify(client.upload_image_binary)(img_bytes)
                                contents.append(FeishuMessageImage(img_key=img_key, alt='图片'))
                            except Exception as e:
                                # 中间图上传失败不影响后续
                                logging.error(e)
                        contents.append(
                            # FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(
                                FeishuMessagePlainText(
                                    '{}({})'.format(kwargs.get('action', _('正在生成')).capitalize(), token if token != '100%' else _('完成'))
                                )
                            )
                        )
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                *contents
                            )
                        )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info('logging on_llm_end: {}'.format(response))
                img_url = response.generations[0][0].text
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                if not additional_kwargs:
                    send_message(AppResult.ReplyText, 'created:error')
                    return None
                if additional_kwargs.get('action', '').lower() == 'describe':
                    if platform == 'feishu':
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                FeishuMessageDiv(additional_kwargs.get('prompt')),
                                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                                header=FeishuMessageCardHeader(content='Midjourney Bot {}🎉'.format(
                                    additional_kwargs.get('action', '').capitalize()), template='blue')
                            )
                        )
                    elif platform == 'dingding':
                        send_message(
                            AppResult.ReplyActionCard,
                            DingDingActionCardMessage(
                                title='Midjourney Bot {}🎉'.format(additional_kwargs.get('action', '').capitalize()),
                                text=additional_kwargs.get('prompt')
                            )
                        )
                    return
                taskId = additional_kwargs.get('id', '')
                action_options = {
                    'U1': ['U1', {'action': 'upscale', 'index': 1, 'level': '2x', 'taskId': taskId}],
                    'U2': ['U2', {'action': 'upscale', 'index': 2, 'level': '2x', 'taskId': taskId}],
                    'U3': ['U3', {'action': 'upscale', 'index': 3, 'level': '2x', 'taskId': taskId}],
                    'U4': ['U4', {'action': 'upscale', 'index': 4, 'level': '2x', 'taskId': taskId}],
                    'V1': ['V1', {'action': 'variation', 'index': 1, 'level': 'high', 'taskId': taskId}],
                    'V2': ['V2', {'action': 'variation', 'index': 2, 'level': 'high', 'taskId': taskId}],
                    'V3': ['V3', {'action': 'variation', 'index': 3, 'level': 'high', 'taskId': taskId}],
                    'V4': ['V4', {'action': 'variation', 'index': 4, 'level': 'high', 'taskId': taskId}],
                    'Z1': ['Zoom Out 1.5x', {'action': 'zoomout', 'index': 0, 'level': '1.5x', 'taskId': taskId}],
                    'Z2': ['Zoom Out 2x', {'action': 'zoomout', 'index': 0, 'level': '2x', 'taskId': taskId}],
                    'U': ['Upscale', {'action': 'upscale', 'index': 0, 'level': '2x', 'taskId': taskId}],
                    'VH': ['Vary High', {'action': 'variation', 'index': 0, 'level': 'high', 'taskId': taskId}],
                    'VL': ['Vary Low', {'action': 'variation', 'index': 0, 'level': 'low', 'taskId': taskId}],
                    'R': ['Reroll', {'action': 'reroll', 'index': 0, 'level': '', 'taskId': taskId}],
                }
                if platform == 'feishu':
                    img_bytes = syncify(download_bytes)(img_url, 2)
                    img_bytes = compress_image(img_bytes, quality=80, max_size=1024)
                    img_key = syncify(client.upload_image_binary)(img_bytes)
                    contents = [FeishuMessageImage(img_key=img_key, alt='图片')]
                    col_keys = [
                        ['U1', 'U2', 'U3', 'U4'],
                        ['V1', 'V2', 'V3', 'V4', 'R'],
                    ]
                    action_data = dict(data.extra.extra.get('action', {}))
                    if action_data and 'value' in action_data:
                        action_value = dict(action_data.get('value', {}))
                        if action_value.get('action', '') == 'upscale':
                            if action_value.get('index'):
                                col_keys = [['VH', 'VL'], ['Z1', 'Z2']]
                        elif action_value.get('action', '') == 'reroll':
                            col_keys = action_value.get('extra', {}).get('col_keys') or [['R']]
                        elif action_value.get('action', '') in ['variation', 'zoomout']:
                            # 保持col_keys不变
                            pass
                        else:
                            col_keys = []
                    if not img_url:
                        col_keys = []
                    for col_key in col_keys:
                        btns = []
                        for key in col_key:
                            if key not in action_options:
                                continue
                            title, value = action_options[key]
                            if key == 'R':
                                value['extra'] = {'col_keys': col_keys}
                            btns.append(FeishuMessageButton(title, value=value))
                        contents.append(FeishuMessageAction(*btns))
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            FeishuMessageDiv('[{}]({})'.format(_('原图'), img_url), tag='lark_md', quote=False),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功') + '\n' + _(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader(content='Midjourney Bot {}🎉'.format(additional_kwargs.get('action', '').capitalize()), template='blue')
                        )
                    )
                elif platform == 'dingding':
                    # logging.info('logging dingding end: {} {}'.format(img_url, additional_kwargs))
                    action_data = dict(data.extra.extra.get('action', {}))
                    options = ['U1', 'U2', 'U3', 'U4', 'V1', 'V2', 'V3', 'V4', 'R']
                    if action_data and 'value' in action_data:
                        action_value = dict(action_data.get('value', {}))
                        if action_value.get('action', '') == 'upscale':
                            if action_value.get('index'):
                                options = ['VH', 'VL', 'Z1', 'Z2']
                        elif action_value.get('action', '') == 'reroll':
                            # options = ['R']
                            pass
                        elif action_value.get('action', '') in ['variation', 'zoomout']:
                            pass
                        else:
                            options = []
                    # 特殊情况
                    if not img_url:
                        options = []
                    btns = []
                    for key in options:
                        if key not in action_options:
                            continue
                        title, value = action_options[key]
                        btn_quote = ':'.join(list(map(str, dict(value).values())))
                        btns.append(DingDingActionCardButton(
                            'dtmd://dingtalkclient/sendMessage?content={}'.format(quote(btn_quote)), title
                        ))
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            *btns,
                            text=_('![图片]({})  \n🤖️：生成成功').format(img_url),
                            title='Midjourney Bot {}🎉'.format(additional_kwargs.get('action', '').capitalize())
                        )
                    )

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any):
                logging.error('mj llm error: %r', error)
                if platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md')
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [OpenAICallbackHandler()]
        chat = MJApiChat(**model)
        if 'action' in data.extra.extra:
            action_value = dict(data.extra.extra.get('action', {}).get('value', {}))
            if 'extra' in action_value:
                del action_value['extra']
            messages = [HumanMessage(content='', additional_kwargs=dict(action_value))]
            return chat(messages)
        else:
            # logging.info(f'logging message data: {data}')
            data_extra = dict(data.get('extra', {}))
            message_type = data_extra.get('message_type', '')
            message_id = data_extra.get('message_id')
            if platform == 'feishu':
                if message_type == 'text':
                    messages = [HumanMessage(content=input, additional_kwargs=dict(action='imagine', prompt=input))]
                    return chat(messages)
                elif message_type == 'post':
                    input_kwargs = data_extra.get('extra', {}).get('input_kwargs', {})
                    with FeishuModel() as fmodel:
                        fmodel.init_by_bot_id(data.extra.bot_instance_id)
                        try:
                            loop = asyncio.get_event_loop()
                        except Exception as e:
                            logging.error(e)
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                        if input_kwargs.get('action', '') == 'blend':
                            img_raws = loop.run_until_complete(asyncio.gather(*[functools.partial(fmodel.client.get_message_resource, message_id, img_key)() for img_key in input_kwargs.get('image_raws')]))
                            input_kwargs.update({'image_raws': img_raws})
                        else:
                            img_raw = loop.run_until_complete(functools.partial(fmodel.client.get_message_resource, message_id, input_kwargs.get('image_raw'))())
                            input_kwargs.update({'image_raw': img_raw})
                    messages = [HumanMessage(content=input, additional_kwargs=dict(input_kwargs))]
                    del data_extra.get('extra', {})['input_kwargs']
                    return chat(messages)
            elif platform == 'dingding':
                if message_type == 'text':
                    input_kwargs = data_extra.get('extra', {}).get('input_kwargs', {})
                    messages = [HumanMessage(content="", additional_kwargs=dict(input_kwargs))]
                    del data_extra.get('extra', {})['input_kwargs']
                    return chat(messages)
                elif message_type == 'richText':
                    input_kwargs = data_extra.get('extra', {}).get('input_kwargs', {})
                    with DingDingModel() as ddmodel:
                        ddmodel.init_by_bot_id(data_extra.get('bot_instance_id'))
                        try:
                            loop = asyncio.get_event_loop()
                        except Exception as e:
                            logging.error(e)
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                        robot_code = ddmodel.bot.app_id
                        if input_kwargs.get('action', '') == 'blend':
                            img_urls = loop.run_until_complete(asyncio.gather(*[functools.partial(ddmodel.client.get_message_resource, robot_code, img_key)() for img_key in input_kwargs.get('image_raws')]))
                            input_kwargs.update({'image_raws': [httpx.get(img_url).content for img_url in img_urls]})
                        else:
                            img_url = loop.run_until_complete(functools.partial(ddmodel.client.get_message_resource, robot_code, input_kwargs.get('image_raw'))())
                            logging.info('logging dd img: %r', img_url)
                            input_kwargs.update({'image_raw': httpx.get(img_url).content})
                        messages = [HumanMessage(content=input, additional_kwargs=dict(input_kwargs))]
                        del data_extra.get('extra', {})['input_kwargs']
                        return chat(messages)


# NOT available
class MJApiDescribeTool(CTool):
    name: str = 'mjapi_describe'
    description: str = 'Midjourney api decribe action'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                pass

            def on_llm_end(self, response, *args, **kwargs):
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                # 再判断一下
                if additional_kwargs.get('action', '').lower() != 'describe':
                    return
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageNote(FeishuMessagePlainText(additional_kwargs.get('prompt'))),
                            header=FeishuMessageCardHeader(content='Midjourney Bot {}🎉'.format(additional_kwargs.get('action', '').capitalize()), template='blue')
                        )
                    )
                elif platform == 'dingding':
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            text=additional_kwargs.get('prompt'),
                            title='Midjourney Bot {}🎉'.format(additional_kwargs.get('action', '').capitalize())
                        )
                    )

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [OpenAICallbackHandler()]
        chat = MJChat(**model)
        messages = [HumanMessage(content=input, additional_kwargs=dict(
            action='imagine',
            prompt=input,
        ))]
        return chat(messages)

class DingdingCommand(CommandTool):
    next_tool_name: str = 'mjapi'
    name: str = 'mjapi_dingding_command'
    description: str = 'midjourney api dingding command'

    def send_usage(self):
        return send_message(
            AppResult.ReplyActionCard,
            DingDingMarkdownMessage(
                text=_('💥 **我是Midjourney AI绘图小助手**\n\n---\n🌠 **Imagine**\n\n通过提示词进行绘图，您可以直接在输入框输入提示词进行文生图，或者展开输入框后同时输入图片及文字进行图生图，比如: 图片+换行+提示词\n\n---\n📝 **Describe**\n\n通过图片反推提示词，您可以在输入框的展开按钮中只上传1张图片，并输入文字 /describe\n\n---\n🧪 **Blend**\n\n将多张图片进行混合，您可以在输入框的展开按钮中上传2-5张图片\n\n---\n🔍 **Upscale**\n\n对图片进行放大，您可以在图片生成后，点击图片下方的 U1、U2 等按钮进行操作\n\n---\n🎨 **Variation**\n\n支持对图片进行变体，可点击图片下方的 V1、V2 等按钮进行操作。\n支持高变体及低变体，可在 Upscale 操作后，点击Vary high 或 Vary low 进行操作\n\n---\n🔮 **Zoom Out**\n\n对图片按比例进行扩充，需要先进行 Upscale 放大图片，支持1.5x 和 2x\n\n---\n🎲 **Reroll**\n\n按照当前提示词，重新生成图片'),
                title=_('🎒 需要帮助吗？')
            )
        )

    def parse_command(self, input, action):
        # if '/describe' in input:
        #     self.next_tool_name = 'mjapi_describe'
        # else:
        #     self.next_tool_name = 'mjapi'
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',

        input_split = input.split(':')
        if len(input_split) == 4:
            action, index, level, taskId = input.split(':')
            # 钉钉，只能回复字符串，如果符合格式，就模拟飞书点击按钮返回事件内容
            data_extra = data.get('extra', {})
            action_value = {'value': dict(
                action=action.lower(),
                index=int(index) if index.isdigit() else 0,
                level=level,
                taskId=taskId,
            )}
            data_extra['extra']['action'] = action_value
            # logging.info('logging dd cmd: %r %r', input, data_extra)
            if action == 'upscale' and app_model.duplicate(input):
                return 'duplicate',
        message_type = data.get('extra', {}).get('message_type', '')
        if message_type == 'text':
            return 'text', input.strip()
        if message_type == 'richText':
            return 'post',
        return None,

    def on_duplicate(self):
        send_message(AppResult.ReplyText, _('🤖️：已放大图片，请勿重复操作...'))
        return 'duplicate upscale'

    def on_text(self, input):
        # text消息
        data_extra = data.get('extra', {})
        input_split = input.split(':')
        input_kwargs = {}
        if len(input_split) == 1:
            input_kwargs = {
                'action': 'imagine',
                'prompt': input
            }
        elif len(input_split) == 4:
            action, index, level, taskId = input_split
            if not index.isdigit():
                send_message(AppResult.ReplyText, _('🤖️：输入不合法！'))
                return None
            input_kwargs = {
                'action': action.lower(),
                'index': int(index) if index.isdigit() else 0,
                'level': level or '',
                'taskId': taskId
            }
        data_extra['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_post(self):
        data_extra = data.get('extra', {})
        rich_text = data_extra.get('extra', {}).get('platform_content', {}).get('richText', [])
        # logging.info('logging dd richtext: %r', rich_text)
        input_text, input_img_keys = '', []
        for msg in rich_text:
            if 'text' in msg and '@' not in msg:
                input_text = msg['text']
            elif msg.get('type', '') == 'picture':
                input_img_keys.append(msg['downloadCode'])
        input_text = input_text.strip()
        input_kwargs = {}
        if not input_img_keys:
            send_message(AppResult.ReplyText, _('🤖️：请上传图片！'))
            return None
        elif len(input_img_keys) == 1:
            if not input_text or input_text == '/describe':
                input_kwargs = {'action': 'describe', 'image_raw': input_img_keys[0]}
            elif input_text[0] != '/':
                input_kwargs = {'action': 'imagine', 'prompt': input_text, 'image_raw': input_img_keys[0]}
        else:
            if not 2 <= len(input_img_keys) <= 5:
                send_message(AppResult.ReplyText, _('🤖️：Blend操作上传的图片数量应在2-5之间！'))
                return None
            input_kwargs = {'action': 'blend', 'image_raws': input_img_keys}
        if not input_kwargs:
            send_message(AppResult.ReplyText, _('🤖️：输入格式不合法，请参考帮助！'))
            return None
        data_extra['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name


class FeishuCommand(CommandTool):
    next_tool_name: str = 'mjapi'
    name: str = 'mjapi_feishu_command'
    description: str = 'midjourney api feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '您可以直接在输入框输入提示词进行文生图，无需添加/imagine前缀',
        '您可以直接在输入框输入提示词+图片进行图生图',
        '您可以直接在输入框输入图片 + /describe 进行图生文',
        '您可以直接在输入框输入多张图片进行混合生图的操作',
        '生成图片后，您可以点击图片下方的U1、U2等按钮放大图片',
        '生成图片后，您可以点击图片下方的V1、V2等按钮生成变体图片',
        '放大图片后，您可以点击图片下方的Zoom out按钮扩展图片',
        '若您对当前生成的图片不满意，可进行Reroll操作重新生成',
        '您可以在后台创建AI绘图的提示词模板，并修改该提示词进行生图'
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def template_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in prompts],
            placeholder=_('选择内置提示词模板'),
            value={'command': 'template'}
        ) if len(prompts) > 0 else None

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是Midjourney AI绘图小助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🌠 **Imagine**\n通过提示词进行绘图，您可以直接在输入框输入提示词进行文生图，或者展开输入框后同时输入图片及文字进行图生图，比如: 图片+换行+提示词'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('📝 **Describe**\n通过图片反推提示词，您可以在输入框的展开按钮中只上传1张图片，并输入文字 /describe'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🧪 **Blend**\n将多张图片进行混合，您可以在输入框的展开按钮中上传2-5张图片'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🔍 **Upscale**\n对图片进行放大，您可以在图片生成后，点击图片下方的 U1、U2 等按钮进行操作'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎨 **Variation**\n支持对图片进行变体，可点击图片下方的 V1、V2 等按钮进行操作。\n支持高变体及低变体，可在 Upscale 操作后，点击Vary high 或 Vary low 进行操作'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🔮 **Zoom Out**\n对图片按比例进行扩充，需要先进行 Upscale 放大图片，支持1.5x 和 2x'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎲 **Reroll**\n按照当前提示词，重新生成图片'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🏠 **内置提示词模板**\n文本回复 *模板* 或 */template*'),
                    tag='lark_md',
                    extra=self.template_select
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue')
            )
        )

    def parse_command(self, input, action):
        # if '/describe' in input:
        #     self.next_tool_name = 'mjapi_describe'
        # else:
        #     self.next_tool_name = 'mjapi'
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:9] == '/template' or input[:2] == '模板':
            return 'template',
        if data.get('extra', {}).get('message_type', '') == 'post':
            return 'post',
        elif not input and action:
            try:
                content = ':'.join([action.value.action, str(action.value.index), action.value.level, action.value.taskId])
                app_model.update_content(content)
                if action.value.action == 'upscale' and app_model.duplicate(content):
                    return 'duplicate',
            except Exception as e:
                logging.error(e)
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', True
            elif action['tag'] == 'select_static':
                return action['value']['command'], action['option']
            elif action['tag'] == 'input':
                return action['name'], action
        return None,

    def on_duplicate(self):
        send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：已放大图片，请勿重复操作...'))),
                header=FeishuMessageCardHeader('Midjourney Bot🎉', template='blue')
            )
        )
        # 这个字符串会保存到chat_log里面
        return 'duplicate upscale'

    def on_post(self):
        # 富文本消息
        data_extra = data.get('extra', {})
        platform_content = data_extra['extra'].get('platform_content', {})
        input_text, input_img_keys = '', []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg.get('tag', '') == 'text':
                    input_text = msg['text']
                elif msg.get('tag', '') == 'img':
                    input_img_keys.append(msg['image_key'])
        input_text = input_text.strip()
        input_kwargs = {}
        if not input_img_keys:
            send_message(AppResult.ReplyText, _('🤖️：请上传图片！'))
            return None
        elif len(input_img_keys) == 1:
            if input_text and input_text == '/describe':
                input_kwargs = {'action': 'describe', 'image_raw': input_img_keys[0]}
            elif input_text and input_text[0] != '/':
                input_kwargs = {'action': 'imagine', 'prompt': input_text, 'image_raw': input_img_keys[0]}
        else:
            if not 2 <= len(input_img_keys) <= 5:
                send_message(AppResult.ReplyText, _('🤖️：Blend操作上传的图片数量应在2-5之间！'))
                return None
            input_kwargs = {'action': 'blend', 'image_raws': input_img_keys}
        if not input_kwargs:
            send_message(AppResult.ReplyText, _('🤖️：输入格式不合法，请参考帮助！'))
            return None
        data_extra['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_template(self, template_name=None):
        if not template_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.template_select) if len(prompts) else FeishuMessageDiv(_("无可用提示词模板选择")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('选择提示词模板，获取更加精准的绘图提示词。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖️️：机器人提醒'), template='blue'),
                )
            )
        prompt = get_prompt_by_id(template_name)
        # 提取大括号中的提示词变量
        pattern = r'\{(.*?)\}'
        variables = re.findall(pattern, prompt.content)
        default_value = ' '.join([f'{{{v}}}' for v in variables])
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageAction(
                    FeishuMessageInput(placeholder='', default_value=default_value, label=prompt.content, value={'prompt': prompt.content, 'variables': variables}, name='prompt'),
                ),
                FeishuMessageNote(FeishuMessagePlainText(_('请替换提示词模板大括号中的变量，并保留大括号'))),
                header=FeishuMessageCardHeader(_('👺 修改提示词'), template='blue'),
            )
        )

    def on_prompt(self, action):
        if not action:
            return
        action_value = action.get('value', {})
        prompt = action_value['prompt']
        pattern = r'\{(.*?)\}'
        variables = re.findall(pattern, action['input_value'])
        for k, v in zip(action_value['variables'], variables):
            prompt = prompt.replace(f'{{{k}}}', v)
        prompt = prompt.replace('{', '').replace('}', '')
        action['value'] = {'action': 'imagine', 'prompt': prompt}
        logging.info('mj prompt input: %r', prompt)
        return self.next_tool_name


class MJApiAgent(CAgent):
    class AppConfig(BaseAppConfig):
        category: str = 'AI绘画'
        name: str = 'Midjourney'
        title: str = 'Midjourney绘图助手'
        title_en: str = 'Midjourney Bot'
        description: str = '🍎 追求极致创作体验的Midjourney-协同办公版'
        description_en: str = '🍎 Supercharged experience for midJourney on oa platform '
        problem: str = '可以在IM平台中使用Midjourney吗'
        problem_en: str = 'Can midjourney be used in IM platform'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/HWZ5dQPAUoOFAoxjd6dckjkonIc'
        manual_en: str = 'https://connect-ai.feishu.cn/docx/PeykdAlMlodXsGxNtZGcxRcGnLg'
        icon: str = 'https://pic1.forkway.cn/cdn/202308242003554.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/202308242003554.png?imageMogr2/thumbnail/720x'
        sorted: int = 3
        support_resource: List[object] = [dict(
            category=ModelCategory.Draw.value,
            scene=ModelCategory.Draw.value,
            title='AI绘画',
            tip='',
            required=True,
            resource=['Midjourney']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcn8oPlGmsCQlhTAAVul5Eg7o'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log='')
            return AgentFinish(
                {'output': last_result},
                'result for action {}: {}'.format(last_action.tool, str(last_result)))

        if platform == 'feishu':
            return AgentAction(tool='mjapi_feishu_command', tool_input=kwargs, log='')
        elif platform == 'dingding':
            return AgentAction(tool='mjapi_dingding_command', tool_input=kwargs, log='')
        return AgentAction(tool='mjapi', tool_input=kwargs, log='')
