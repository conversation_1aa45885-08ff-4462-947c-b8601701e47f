{"version": 3, "sources": ["../../.pnpm/vue@3.3.0/node_modules/vue/dist/vue.runtime.esm-bundler.js"], "sourcesContent": ["import { initCustomFormatter, warn } from '@vue/runtime-dom';\nexport * from '@vue/runtime-dom';\n\nfunction initDev() {\n  {\n    initCustomFormatter();\n  }\n}\n\nif (process.env.NODE_ENV !== \"production\") {\n  initDev();\n}\nconst compile = () => {\n  if (process.env.NODE_ENV !== \"production\") {\n    warn(\n      `Runtime compilation is not supported in this build of Vue.` + (` Configure your bundler to alias \"vue\" to \"vue/dist/vue.esm-bundler.js\".` )\n      /* should not happen */\n    );\n  }\n};\n\nexport { compile };\n"], "mappings": ";;;;;;;AAAA;AACA;AAEA,SAAS,UAAU;AACjB;AACE,wBAAoB;AAAA,EACtB;AACF;AAEA,IAAI,MAAuC;AACzC,UAAQ;AACV;AACA,IAAM,UAAU,MAAM;AACpB,MAAI,MAAuC;AACzC;AAAA,MACE;AAAA;AAAA,IAEF;AAAA,EACF;AACF;", "names": []}