class DouyinShopTool(CTool):
    name: str = 'douyin_shop'
    description: str = 'douyin_shop app'
    douyin_prompt: str = """Goal: 帮助用户生成抖音带货文案
Required variables:
接下来，您可以编写一些问题来进一步指导用户，但必须用中文。以下是一些示例问题：
* 这个产品有什么特点或优点吗？
* 你计划在文案中强调哪些方面的产品特性？
* 这个产品的价格是多少？
* 你希望在文案中提及哪些关于产品价格的信息？
* 你觉得这个产品最吸引人的地方是什么？
基于用户的回答，您可以进一步询问一些问题，以便生成更具针对性的文案。例如，如果用户告诉您他们想要销售一款口红，您可以问以下问题：
* 这个口红的颜色和质地怎么样？
* 你计划在文案中提及口红的滋润程度吗？
* 这个口红的价格如何？和其他口红相比有什么优势？
* 你希望在文案中突出的口红特点是什么？
基于用户提供的信息，抖音带货文案生成器可以为用户生成一个文案，并建议一些文案改进的建议。例如，生成的文案可能包括一个有吸引力的标题，如“尽显唇色迷人！这是你一定要试试的口红！”，然后列出产品的特点，优点和价格，并结合一些生动的形容词和图片，以吸引更多的购买者。
通过与用户多次对话，抖音带货文案生成器可以帮助用户生成更加具体和有针对性的文案，并提供一些优化的建议，从而帮助用户卖更多货物。
Topic: 抖音带货文案生成器
Prompt: “您好，欢迎使用抖音带货文案生成器。我可以帮助您撰写吸引人的抖音带货文案。请告诉我您想要销售的产品的名称是什么？”"""

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class DouyinShopCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                content = response.generations[0][0].text
                session.add_message({'role': 'human', 'content': input.strip()})
                session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    # TODO feishu更换markdown，不用上传图片
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [DouyinShopCallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])
        if model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        system_message = [SystemMessage(content=self.douyin_prompt)]
        messages = system_message + chat_history + [HumanMessage(content=input)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class DingdingCommand(CommandTool):

    next_tool_name: str = 'douyin_shop'
    name: str = 'douyin_shop_dingding_command'
    description: str = 'douyin_shop dingding command'

    def send_usage(self):

        # TODO 可使用ActionCardMessage替代，dtmdLink放到actionURL内即可
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/clear',
                    _('🆑 清除话题上下文')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/model',
                    _('🚀 AI模型切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("我是抖音带货文案生成器，可以帮您根据销售产品的信息为您生成一份极具吸引力的抖音带货文案")
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            # 如果"/model {model_name}"，就发送成功消息，否则发送选项
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_clear(self):
        session['chat_history'] = []  # 清除历史
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_("🆑 机器人提醒"),
                text=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧")
            )
        )

    def on_model(self, model_name=None):
        # 如果"/model {model_name}"，就发送成功消息，否则发送选项
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            action_value = m[model_name]
            session['model_id'] = action_value
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择模型：%(model)s", model=model_name),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🚀 机器人提醒'),
                        text=_('无可用模型'),
                    )
                )
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/model ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下模型：'),
                    title=_('🚀 AI模型切换'),
                ),
            )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class FeishuCommand(CommandTool):

    next_tool_name: str = 'douyin_shop'
    name: str = 'douyin_shop_feishu_command'
    description: str = 'douyin_shop feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        # '输入<发散模式> 或 /ai_mode 即可选择发散模式',
        '输入<清除> 或 /clear 即可清除上下文'
    ]

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是抖音带货文案生成器，可以帮您根据销售产品的信息为您生成一份极具吸引力的抖音带货文案**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_clear(self, flag=None, reply_log_id=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')),
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        session['chat_history'] = []  # 清除历史
        return send_message(
            AppResult.SendCard if not reply_log_id else AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            ),
            **({'reply_log_id': reply_log_id} if reply_log_id else {})
        )

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('已选择模型：**%(model)s**', model=m.get(model_name, model_name)), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class DouyinShopAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'douyin_shop'
        title: str = '抖音带货文案生成'
        title_en: str = 'TikTok Copywriter'
        description: str = '🔥 抖音带货文案写手，帮助你写出最有吸引力的文案'
        description_en: str = '🔥 TikTok product copywriter, helping you write the most attractive copy.'
        problem: str = '如何写出爆款带货文案？'
        problem_en: str = 'How to write an explosive copy with goods'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/MTJKd8vRVoP4rfx1kQocdMdgngb'
        manual_en: str = 'https://q5o2cctqdb7.sg.larksuite.com/docx/GN9KdksAyorpPgx4VtTlZX7cgrc'
        icon: str = 'https://pic1.forkway.cn/cdn/douyinPrompt.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/douyinPrompt.png?imageMogr2/thumbnail/720x'
        sorted: int = 101
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['OpenAI', 'Azure']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcny9n6Af3TRkSW88Mp1IP6yd'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/HdXmwPZCviflfVkUG5ucMfl9ncd'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='douyin_shop_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='douyin_shop_dingding_command', tool_input=kwargs, log="")
        # if platform == 'feishu' and self.parse_command(**kwargs):
        #     return AgentFinish({"output": "command"}, kwargs.get('input'))
        return AgentAction(tool='douyin_shop', tool_input=kwargs, log="")


