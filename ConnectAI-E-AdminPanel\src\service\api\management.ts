import { adapter } from '@/utils';
import { mockRequest } from '../request';
import { adapterOfFetchUserList } from './management.adapter';
import { request } from '~/src/service/request';

/** 获取用户列表 */
export const fetchUserList = async () => {
  const data = await mockRequest.post<ApiUserManagement.User[] | null>('/getAllUserList');
  return adapter(adapterOfFetchUserList, data);
};

/** 获取座席列表 */
export function fetchSeatList(params: ApiUserManagement.Req.SeatList) {
  return request.get<ApiUserManagement.Resp.SeatList>('/api/seats', { params });
}

/** 添加座席 */
export function AddSeat(params: ApiUserManagement.Req.SeatAdd) {
  return request.post('/api/seats', params);
}

/** 更新座席 */
export function UpdateSeat(params: ApiUserManagement.Req.SeatUpdate) {
  return request.put('/api/seats', params);
}

/** 移除座席 */
export function RemoveSeat(seat_id) {
  return request.delete(`/api/seats?seat_id=${seat_id}`);
}

/** 更新座席状态 */
export function SeatAction(params: ApiUserManagement.Req.SeatAction) {
  return request.put('/api/seats', params);
}

/** 导入座席 */
export function ImportSeats(seats: ApiUserManagement.Req.SeatAdd[]) {
  return request.post('/api/seats/import', { seats });
}

/** 座席部门列表 */
export function SeatDepartment() {
  return request.get('/api/seats/department');
}

/** 自动激活开关 */
export function SeatConfig() {
  return request.get('/api/seats/config');
}

/** 自动激活开关 */
export function UpdateSeatConfig(auto_add_seat ) {
  return request.post('/api/seats/config', { auto_add_seat });
}

