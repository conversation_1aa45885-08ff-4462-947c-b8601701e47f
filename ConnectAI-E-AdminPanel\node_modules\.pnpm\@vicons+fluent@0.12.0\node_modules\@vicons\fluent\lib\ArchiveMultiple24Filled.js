'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4.75 2A1.75 1.75 0 0 0 3 3.75v1.5C3 6.216 3.784 7 4.75 7h13.5A1.75 1.75 0 0 0 20 5.25v-1.5A1.75 1.75 0 0 0 18.25 2H4.75zM19 8H4v7.75a3.75 3.75 0 0 0 3.75 3.75h7.5A3.75 3.75 0 0 0 19 15.75V8zM9 11.25a.75.75 0 0 1 .75-.75h3.477a.75.75 0 0 1 0 1.5H9.75a.75.75 0 0 1-.75-.75zm12.5.5c0-1.227-.589-2.316-1.5-3v7.5a4.25 4.25 0 0 1-4.25 4.25h-8.5c.684.91 1.773 1.5 3 1.5h5.5a5.75 5.75 0 0 0 5.75-5.75v-4.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ArchiveMultiple24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
