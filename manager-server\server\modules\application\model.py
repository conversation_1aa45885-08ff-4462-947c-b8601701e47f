import re
import hashlib
import urllib
import urllib3
import random
import time
from datetime import datetime
import httpx
import requests
import asyncio
import logging
import json
import functools 
import textwrap
from uuid import uuid4
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Optional, List, Union
from tornado.options import options
from tornado.httpclient import AsyncHTTPClient, HTTPRequest, HTTPError
from sqlalchemy import alias, and_, or_, select, func, distinct, text
from core.base_model import MysqlModel
from core.exception import NotFound, PermissionDenied, InternalError, Duplicate
from core.utils import (
    row2dict, ObjectDict, _,
    get_event_loop, syncify,
    compress_image,
    parse_urls,
    upload_file,
    mp3_to_opus,
    get_image_info,
)
from core.duckduckgo import DDGS
from core.downloader import ChunkedDownloader, download_bytes
from core.api_base import ApiBase
from core.feishu import *
from core.dingding import *
from core.dingdingbot import *
from core.wework import *
from core.know import *
from core.gpts import *
from core.rabbitmq import MqSession
from core.redisdb import redis_cli, pickle, stalecache
from core.wenxin import Wen<PERSON>in<PERSON><PERSON>, WenXinClient
from core.baichuan import <PERSON>chuanChat, BaichuanClient
from core.rwkv import RWKVChat, RWKVClient
from core.minimax import MiniMaxChat, MiniMaxClient
from core.sensenova import SenseNovaClient, SenseNovaChat
from core.xinghuo import XingHuoChat, XingHuoClient
from core.chatglm import ChatGLMChat, ChatGLMClient
from core.removebg import RMClient,RMChat
from core.midjourney import MJChat, MJClient
from core.removevocal import RVClient,RVChat
from core.sdimage import SDImageChat
from core.mjapi import MJApiChat
from core.bibigpt import BibiGPTChat
from core.clipdrop import ClipdropChat
from core.elevenlabs import ElevenLabsChat
from core.moonshot import MoonshotChat
from core.tongyi import TongYiChat
from core.bittosvg import BSChat
from core.taichu import TaichuChat
from core.translate_document import AliyunChat
from core.hunyuan import HunyuanChat
from core.gemini import GeminiChat
from core.schema import (
    ObjID,
    Application,
    ApplicationImplementation,
    Bot,
    BotInstance,
    AppInstance,
    UserAccessAppInstance, Policy,
    Resource, Model, TenantWithKey, TenantResource,
    ChatLog,
    Prompt,
    AppInstancePrompt,
    ApplicationSupportResource,
)
from os.path import abspath, dirname
from settings.constant import ModelCategory, AppResult, CHAT_SESSION_CACHE_EXPIRE, KNOWLEDGE_DEFAULT_PROMPT

from langchain.text_splitter import CharacterTextSplitter, RecursiveCharacterTextSplitter
from langchain.agents.agent import Agent, AgentExecutor, AgentOutputParser, BaseSingleActionAgent
from langchain.schema import AgentAction, AgentFinish, OutputParserException, Document
from langchain.callbacks.base import BaseCallbackHandler
from langchain.callbacks.manager import CallbackManager
from langchain.chains.base import Chain
from langchain.tools.base import BaseTool
from langchain.base_language import BaseLanguageModel
from langchain_community.chat_models import ChatAnthropic
from langchain_openai import AzureChatOpenAI, ChatOpenAI
from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain_community.llms import AzureOpenAI, OpenAI
from langchain.chains.summarize import load_summarize_chain
from langchain.prompts import PromptTemplate
from langchain.schema import (
    get_buffer_string,
    HumanMessage,
    AIMessage,
    SystemMessage,
    ChatMessage,
    FunctionMessage
)
from modules.callback.model import *
from modules.messenger.model import *
import itertools


class BaseAppConfig(object):
    # 每一个CAgent内部增加AppConfig(BaseAppConfig)，方便自动注册应用
    # 对于openai.py单个脚本支持多个应用的情况，只需内部配置多个即可
    name: str = 'openai'
    title: str = 'OpenAI聊天机器人'
    description: str = ''
    icon: str = ''
    logo: str = ''
    sorted: int = 1
    problem: str = ''
    video: str = ''
    manual: str = ''
    title_en: str = ''
    description_en: str = ''
    problem_en: str = ''
    video_en: str = ''
    manual_en: str = ''
    feedback_url: str = ''
    wx_suite_id: str = ''
    show: int = 1
    tips: List[str] = []  # 玩法提示，暂不进入数据库


class CAgent(BaseSingleActionAgent):

    allowed_tools: Optional[List[str]] = None

    # 先继承一次
    @property
    def input_keys(self):
        return ["input"]

    def plan(self, intermediate_steps, **kwargs):
        if len(intermediate_steps) >= 1:
            action, result = intermediate_steps[-1]
        else:
            action, result = None, None
        # 这里将plan再抽象一次，变成一个next函数，主要是将最近一次action, result给抽取一下方便使用
        return self.next(action, result, intermediate_steps, **kwargs)

    async def aplan(self, intermediate_steps, **kwargs):
        return self.plan(intermediate_steps, **kwargs)


class CTool(BaseTool):

    # 先继承一下
    async def _arun(self, *args, **kwargs):
        return self._run(*args, **kwargs)


class CommandTool(CTool):

    next_tool_name: str = 'openai_chat'
    tips: List[str] = []

    @classmethod
    def choice_tip(cls):
        return '✨：' + random.choice(cls.__fields__['tips'].default)

    def on_help(self):
        # 如果匹配出来的命令是help，就执行send_usage
        return self.send_usage()

    def send_usage(self):
        raise NotImplementedError

    def parse_command(self, input, action):
        # 子类自己处理这里的逻辑
        return None,

    def _run(self, *args, run_manager=None, input='', **kwargs):
        action = kwargs['extra']['extra']['action'] if 'action' in kwargs['extra'].get('extra', {}) else None
        command, *arguments = self.parse_command(input.strip(), action)
        name = 'on_{}'.format(command)
        if command:
            if hasattr(self, name):
                return getattr(self, name)(*arguments)
            else:
                return self.send_usage()
        else:
            return self.next_tool_name


class ChatSession(MysqlModel):

    # <open_id, chat_id>: session
    def __init__(self, open_id, chat_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.open_id = open_id
        self.chat_id = chat_id
        self._session = self.get_user(open_id, chat_id)
        self.dirty = False

    def __setitem__(self, name, value):
        self.dirty = True
        self._session.__setitem__(name, value)

    def __delitem__(self, name):
        self.dirty = True
        self._session.__delitem__(name)

    def __getattr__(self, name):
        try:
            return self._session.get(name)
        except KeyError:
            raise AttributeError(name)

    def get_extra(self, name, default):
        return self._session.get('extra', {}).get(name, default)

    def set_extra(self, name, value):
        self._session.get('extra', {}).update({name: value})
        self.dirty = True

    def save(self):
        if not self.dirty:
            return
        session_key = "{}:{}".format(options.REDIS_NAMESPACE, self.session_key)
        redis_cli(False).pipeline().set(
            session_key, pickle.dumps(self._session)
        ).expire(
            session_key, CHAT_SESSION_CACHE_EXPIRE,
        ).execute()

    @property
    def session_key(self):
        return 'chat:{}:{}'.format(self.open_id, self.chat_id)

    @stalecache(stale=0, expire=CHAT_SESSION_CACHE_EXPIRE, attr_key='session_key')
    def get_user(self, open_id, chat_id):
        return dict(
            open_id=open_id,
            chat_id=chat_id,
            chat_history=[],
            temperature=0.7,
            model_id='',
            prompt_id='',
            system_role='',
            extra=dict(),
        )

    @property
    def chat_history(self):
        # 计算max_token数...返回history
        history = []
        for i in self._session['chat_history']:
            content = i.get('content')
            role = i.get('role', '')
            additional_kwargs = i.get('additional_kwargs', {})
            # 空消息不保存
            if not content:
                continue
            if isinstance(content, str) and not content.strip():
                continue
            if role == 'human':
                history.append(HumanMessage(content=content, additional_kwargs=additional_kwargs))
            elif role == 'ai' or role == 'assistant':
                history.append(AIMessage(content=content, additional_kwargs=additional_kwargs))
            elif role == 'system':
                history.append(SystemMessage(content=content, additional_kwargs=additional_kwargs))
            else:
                history.append(ChatMessage(content=content, role=role or 'human', additional_kwargs=additional_kwargs))
        return history

    def add_message(self, message):
        self.dirty = True
        self._session['chat_history'].append(message)
        # TODO 按消息数量限制暂定20
        self._session['chat_history'] = self._session['chat_history'][-8:]
        # TODO 按模型token数限制
        # if len(get_buffer_string(self.chat_history)) >= self.model_max_token:

    def update_message(self, index=-1, message=None, content='', additional_kwargs={}):
        if not (0 <= index < len(self._session['chat_history'])):
            return
        self.dirty = True
        history_message = self._session['chat_history'][index]
        if message:
            self._session['chat_history'][index] = message
        else:
            if content:
                history_message['content'] = content
            if additional_kwargs:
                history_message.get('additional_kwargs', {}).update(additional_kwargs)


class ForwardChat(SimpleChatModel):
    def _llm_type(self) -> str:
        return "forward_chat"

    def _call(self, messages, stop=None, run_manager=None, **kwargs) -> str:
        return messages[0].content if messages else ''


class ApplicationModel(MysqlModel):

    def get_instance_by_id(self, instance_id):
        instance = self.session.query(AppInstance).filter(AppInstance.id == instance_id).first()
        if not instance:
            raise NotFound('找不到应用')
        return instance

    def get_bot_instance_by_id(self, bot_instance_id):
        bot = self.session.query(BotInstance).filter(BotInstance.id == bot_instance_id).first()
        if not bot:
            raise NotFound('找不到机器人')
        return bot

    def get_application_by_id(self, application_id):
        app = self.session.query(Application).filter(Application.id == application_id).first()
        if not app:
            raise NotFound('找不到应用')
        return app

    def get_application_by_instance_id(self, instance_id):
        app = self.session.query(Application).join(
            AppInstance,
            AppInstance.application_id == Application.id,
        ).filter(
            AppInstance.id == instance_id
        ).first()
        if not app:
            raise NotFound('找不到应用')
        return app

    def get_application_impl_by_id(self, impl_id):
        impl = self.session.query(ApplicationImplementation).filter(
            ApplicationImplementation.id == impl_id,
        ).first()
        if not impl:
            raise NotFound('找不到应用版本')
        return impl

    def get_application_impl_by_application_id(self, application_id):
        impl = self.session.query(ApplicationImplementation).filter(
            ApplicationImplementation.application_id == application_id,
            ApplicationImplementation.status == 0,  # 当前生效的最高版本
        ).order_by(
            ApplicationImplementation.version.desc(),
        ).first()
        if not impl:
            raise NotFound('找不到应用版本')
        return impl

    def get_model_by_id(self, model_id):
        model = self.session.query(Model).filter(
            Model.id == model_id,
        ).first()
        if not model:
            raise NotFound('找不到资源版本')
        return model

    def get_resource_by_id(self, resource_id, tenant_id):
        resource = self.session.query(Resource).filter(
            Resource.id == resource_id,
        ).first()
        if not resource:
            raise NotFound('找不到资源')
        tresource = self.session.query(TenantResource).filter(
            TenantResource.resource_id == resource_id,
            TenantResource.tenant_id == tenant_id,
        ).first()
        return resource, tresource

    def get_resource_by_ids(self, resource_ids, tenant_id):
        if not resource_ids:
            raise NotFound('找不到资源')
        resources = self.session.query(Resource).filter(
            Resource.id.in_(resource_ids),
            ).all()
        if not resources:
            raise NotFound('找不到资源')
        tresources = self.session.query(TenantResource).filter(
            TenantResource.resource_id.in_(resource_ids),
            TenantResource.tenant_id == tenant_id,
            ).all()
        return resources, tresources

    def get_resource_category_by_resource_ids(self, resource_ids, application_id):
        resources = self.session.query(ApplicationSupportResource.scene, ApplicationSupportResource.category).filter(
            ApplicationSupportResource.application_id == application_id,
            ApplicationSupportResource.resource_id.in_(resource_ids.values()),
            ApplicationSupportResource.scene.in_(resource_ids.keys()),
            ApplicationSupportResource.status == 0,
        ).order_by(
            ApplicationSupportResource.sorted.asc(),
        ).all()
        scenes = {}
        for r in resources:
            # 保持原顺序
            if r.scene not in scenes:
                scenes[r.scene] = (resource_ids[r.scene], r.category)
        return scenes

    def get_vars(self, instance, bot, resources, tenant_resources, apikey, data, app, resource_ids):

        def send_message(message_type, content=dict(), **kwargs):
            queue_map = {
                'feishu': options.QUEUE_FEISHU,
                'dingding': options.QUEUE_DINGDING,
                'wework': options.QUEUE_WEWORK,
                'wxwork': options.QUEUE_WXWORK,
                'messenger': options.QUEUE_MESSENGER,
            }
            logging.info('send_message %r', (self.platform, message_type, content))
            if self.platform in queue_map:
                data.update(
                    result_type=message_type.value,
                    result_content=content,
                    additional_kwargs=kwargs
                )
                pikachu = MqSession()
                pikachu.put(queue_map[self.platform], json.dumps(data))
                pikachu.close()
            return True

        def send_note(text, title='', type=None, **kwargs):
            if not text: return
            if self.platform == 'feishu':
                return send_message(
                    type or AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(text)),
                        header=FeishuMessageCardHeader(title, template='blue') if title else None
                    ),
                    **kwargs
                )
            elif self.platform == 'dingding':
                return send_message(
                    type or AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=title,
                        text=text,
                    ),
                    **kwargs
                )
            else:
                return send_message(AppResult.ReplyText, text)

        def get_prompt_by_id(prompt_id):
            return self.session.query(Prompt).filter(Prompt.id == prompt_id).first()

        session = ChatSession(data.extra.sender_id, data.extra.chat_id)
        # 多个资源下对应的多个模型
        # 这里不考虑使用dict 应用通过models[i]拿更简单
        models = []
        ai_models = []
        ai_mode = self.get_ai_mode()

        def get_resource_by_id(resource_id):
            for x, y in zip(resources, tenant_resources):
                if resource_id == x.id:
                    return x, y
        if not resource_ids:
            resource_scenes = {'': (instance.resource_id, '')}
        else:
            resource_scenes = self.get_resource_category_by_resource_ids(resource_ids, app.id)
        for resource_id, model_category in resource_scenes.values():
            resource, tenant_resource = get_resource_by_id(resource_id)
            # 这里增加权限判断只返回当前用户能选的模型
            model_names = [i['value'] for i in tenant_resource.extra.get('model', [])]
            ai_model = self.get_model_by_instance_id(data.extra.instance_id, model_names, resource_id, model_category)
            ai_models.append(ai_model)
            m = {value: content for value, content in ai_model}
            model_name = m.get(session.model_id, ai_model[0][1])
            if resource.name == 'Azure':
                # 先使用proxy.forkway.cn这边默认的deployment_name
                deployment_name = 'gpt-3.5-turbo'
                if 'gpt-4-32k' in model_name:
                    deployment_name = 'gpt-4-32k'
                elif 'gpt-4-1106-preview' in model_name:
                    deployment_name = 'gpt-4-1106-preview'
                elif 'gpt-35-turbo-1106' in model_name:
                    deployment_name = 'gpt-35-turbo-1106'
                elif 'gpt-4' in model_name:
                    deployment_name = 'gpt-4'
                # 如果tenant_resource.extra支持自定义的字段，那就使用这边配置的deployment_name
                # 如果这边没有填，那还是默认的
                for i in tenant_resource.extra.get('model', []):
                    if model_name == i['value'] and i.get('deploy_name'):
                        deployment_name = i['deploy_name']
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="azure",
                    openai_api_key=tenant_resource.api_key or apikey,
                    openai_api_version=tenant_resource.extra.get('api_version', '2023-03-15-preview'),
                    azure_endpoint=tenant_resource.api_base or ApiBase(resource.name).url,
                    deployment_name=deployment_name,
                )
                if 'gpt' in model_name:
                    ai_mode = self.get_ai_mode(2)
                if not session.system_role:
                    session.system_role = "You are an AI assistant, a large language model trained by OpenAI. Answer in user's language as concisely as possible."
            elif resource.name == '文心一言':
                # 使用文心一言的官方的配置，需要使用统一的forward代理
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="文心一言",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://wenxin' != api_base[:14]:
                    model['app_id'] = tenant_resource.extra.get('app_id')
                    model['secret_key'] = tenant_resource.extra.get('secret_key')
            elif resource.name == '百川大模型':
                # 使用百川的官方的配置，需要使用统一的forward代理
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="百川大模型",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://baichuan' != api_base[:16]:
                    model['secret_key'] = tenant_resource.extra.get('secret_key')
            elif resource.name == 'ChatGLM':
                # 使用官方资源的时候，虽然都是只有api_key，但是需要使用jwt
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="ChatGLM",
                    api_key=tenant_resource.api_key or apikey,
                    # default="https://open.bigmodel.cn/api/paas/v3/model-api/chatglm_std/invoke",
                    api_base=tenant_resource.api_base or ApiBase(resource.name).url,
                )
            elif resource.name == '商汤日日新':
                # 需要使用jwt
                api_base=tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="商汤日日新",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://sensenova' != api_base[:17]:
                    model['secret_key'] = tenant_resource.extra.get('secret_key')
            elif resource.name == 'MiniMax':
                # default="https://api.minimax.chat/v1",
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="minimax",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://minimax' != api_base[:15]:
                    model['group_id'] = tenant_resource.extra.get('group_id')
            elif resource.name == '星火认知大模型':
                # default="ws(s)://spark-api.xf-yun.com/v1.1/chat",
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="xinghuo",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://xinghuo' != api_base[:15]:
                    model['app_id'] = tenant_resource.extra.get('app_id')
                    model['secret_key'] = tenant_resource.extra.get('secret_key')
                elif api_base.endswith('/v1.1'):
                    model['api_base'] = api_base[:-5]
            elif resource.name == 'RWKV':
                # TODO 这个暂时没有官方
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="rwkv",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=tenant_resource.api_base or ApiBase(resource.name).url
                )
            elif resource.name == 'Midjourney':
                # TODO 这个暂时没有官方
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                    # TODO 新的ChatClient需要传user_id
                    # 不管风变那边怎么处理负载均衡，至少确保相同用户前后的id是一样的
                    user_id=data.extra.user_id,
                )
                if 'https://mjf' in api_base and api_base.endswith('/mj'):
                    # TODO 旧版的mj填写的api_base带有mj后缀，需要移除
                    model['api_base'] = api_base[:-3]
            elif resource.name == 'Claude':
                # 代理和官方基本保持一致
                anthropic_api_url = tenant_resource.api_base or ApiBase(resource.name).url
                # 使用代理
                anthropic_api_url = anthropic_api_url.replace('https://api.anthropic.com', options.CLAUDE_PROXY)
                model = ObjectDict(
                    # 当前的 ChatAnthropic streaming模式有问题
                    # 我们封装的claudeapi没有传version，导致返回数据格式问题
                    streaming=self.platform == "feishu",
                    openai_api_type="claude",
                    anthropic_api_key=tenant_resource.api_key or apikey,
                    # default="https://api.anthropic.com",
                    anthropic_api_url=anthropic_api_url,
                )
            elif resource.name == 'Stable Diffusion':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == 'feishu',
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://sdimage' != api_base[:15]:
                    model['app_id'] = tenant_resource.extra.get('app_id')
                    model['secret_key'] = tenant_resource.extra.get('secret_key')
            elif resource.name == '360智脑':
                # 代理和openai保持一致
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="360智脑",
                    openai_api_key=tenant_resource.api_key or apikey,
                    openai_api_base=tenant_resource.api_base or ApiBase(resource.name).url
                )
            elif resource.name == 'BibiGPT':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == 'feishu',
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == 'Pixian.AI':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == 'feishu',
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == 'Moises.AI':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=False,
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == 'Clipdrop':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == 'feishu',
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == 'Moonshot':
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="Moonshot",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=tenant_resource.api_base or ApiBase(resource.name).url
                )
            elif resource.name == '通义千问':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="通义千问",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == '紫东太初':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="紫东太初",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == 'AliyunOpenAPI':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                    region_id="connectai",
                    access_key_secret="test",
                )
                if 'https://aliyun' != api_base[:14]:
                    model['region_id'] = tenant_resource.extra.get('region_id')
                    model['access_key_id'] = tenant_resource.extra.get('access_key_id')
                    model['access_key_secret'] = tenant_resource.extra.get('secret_key')
            elif resource.name == 'Vectorizer.AI':
                model = ObjectDict(
                    streaming=False,
                    api_key=tenant_resource.api_key or apikey,
                    api_base=tenant_resource.api_base or ApiBase(resource.name).url
                )
            elif resource.name == 'ElevenLabs':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == 'feishu',
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            elif resource.name == 'Hunyuan':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="Hunyuan",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
                if 'https://hunyuan' != api_base[:15]:
                    model['app_id'] = tenant_resource.extra.get('app_id')
                    model['secret_id'] = tenant_resource.extra.get('secret_id')
                    model['secret_key'] = tenant_resource.extra.get('secret_key')
                ai_mode = self.get_ai_mode(2)
            elif resource.name == 'Gemini':
                api_base = tenant_resource.api_base or ApiBase(resource.name).url
                model = ObjectDict(
                    streaming=self.platform == 'feishu',
                    openai_api_type="Gemini",
                    api_key=tenant_resource.api_key or apikey,
                    api_base=api_base,
                )
            else:  # 使用openai资源
                # 代理和官方基本保持一致
                openai_api_base = tenant_resource.api_base or ApiBase(resource.name).url
                # 使用代理
                openai_api_base = openai_api_base.replace('https://api.openai.com', options.OPENAI_PROXY)
                model = ObjectDict(
                    streaming=self.platform == "feishu",
                    openai_api_type="OpenAI",
                    openai_api_key=tenant_resource.api_key or apikey,
                    openai_api_base=openai_api_base,
                    max_retries=5,
                )
                if 'gpt' in model_name:
                    ai_mode = self.get_ai_mode(2)
                    # HOTFIX very high temperature
                    session.temperature = min(session.temperature, 1.2)
                elif session.temperature > 1:
                    session.temperature = 0.7
            models.append(model)
        g = ObjectDict(
            app_model=self,
            model=models[0],  # 兼容旧版，新版多资源使用列表
            models=models,
            resource=tenant_resources[0],
            resources=tenant_resources,
            platform=self.platform,
            send_message=send_message,
            get_prompt_by_id=get_prompt_by_id,
            session=session,
            # 支持上下文开关
            chat_history=[] if instance.extra.get('chat_history', '') != 'enable' else session.chat_history,
            ai_mode=ai_mode,
            app=app,
            instance=instance,
            bot=bot,
            client=Lark(bot.app_id, bot.app_secret) if self.platform == 'feishu' else DingDing(bot.agent_id, bot.app_id, bot.app_secret),
            ai_model=ai_models[0],
            ai_models=ai_models,
            prompts=self.get_prompt_by_instance_id(data.extra.instance_id),
            data=data,  # 这里把消息队列里面的东西也传进去
            # TODO 处于安全考虑，后面这里还是进来先少量，做加法
            send_note=send_note,
            web_search=self.web_search,
            **globals(),
        )

        # 处理翻译函数，
        def translate(x, **kwargs):
            return _(x, lang=data.lang or options.DEFAULT_LOCALE, **kwargs)
        g['_'] = translate
        loc = {}
        return g, loc

    @functools.lru_cache
    def gen_run_code(self, code):
        g, loc = {**globals()}, {}
        exec(code, g, loc)
        agent_name, tools, tool_names = None, [], []
        # 从locals拿到自定义app中定义的Agent以及Tool，并实例化
        for name in loc:
            value = loc[name]
            if issubclass(value, CAgent):
                agent_name = name
            elif issubclass(value, CTool):
                tool_names.append(value.__fields__['name'].default)
                tools.append(name)

        run_code = """
agent_executor = AgentExecutor.from_agent_and_tools(
    agent={agent_cls}(allowed_tools={tool_names}),
    tools={tools}, verbose=True,
)
result = agent_executor.invoke(input)
        """.format(
            agent_cls=agent_name,
            tool_names='["' + '", "'.join(tool_names) + '"]',
            tools='[' + ', '.join([t + '()' for t in tools]) + ']',
        )
        return run_code, loc

    def get_agent(self, data):
        app = self.get_application_by_instance_id(data.extra.instance_id)
        instance = self.get_instance_by_id(data.extra.instance_id)
        resource_ids = instance.extra.get('resource_ids', {})
        bot = self.get_bot_instance_by_id(data.extra.bot_instance_id)
        apikey = self.session.query(TenantWithKey.apikey).filter(
            TenantWithKey.id == instance.tenant_id
        ).limit(1).scalar()
        if not resource_ids:
            resource, tenant_resource = self.get_resource_by_id(instance.resource_id, instance.tenant_id)
            # 兼容旧版单资源
            resources, tenant_resources = [resource], [tenant_resource]
        else:
            # 多资源
            resources, tenant_resources = self.get_resource_by_ids(list(resource_ids.values()), instance.tenant_id)
        g, loc = self.get_vars(instance, bot, resources, tenant_resources, apikey, data, app, resource_ids)
        impl = self.get_application_impl_by_application_id(app.id)
        content = impl.code

        # 先运行一遍app的代码，然后生成一段代码放在后面还是每次使用exec运行
        run_code, local = self.gen_run_code(content)
        # logging.info("run_code %r %r", run_code)
        def run(input):
            loc['input'] = input
            exec(content + run_code, {**g, **local}, loc)
            g['session'].save()
            return loc['result']
        return ObjectDict(run=run)

    def get_ai_mode(self, limit=1):
        # https://platform.openai.com/docs/api-reference/chat/create
        # https://learn.microsoft.com/en-us/azure/ai-services/openai/reference
        if limit == 2:
            return [
                ('0.1', _('严谨')),
                ('0.7', _('简洁')),
                ('1.0', _('标准')),
                ('1.2', _('发散')),
            ]
        return [
            ('0.1', _('严谨')),
            ('0.4', _('简洁')),
            ('0.7', _('标准')),
            ('1.0', _('发散')),
        ]

    def web_search(self, keyword, **kwargs):
        search_results = []
        kwargs['region'] = kwargs.get('region', 'zh_CN').replace('_', '-')
        with DDGS() as ddgs:
            # {title, href, body}
            try:
                ddgs_gen = ddgs.text(keyword, **kwargs)
                for r in itertools.islice(ddgs_gen, 10):
                    search_results.append({'snippet': r.get('body'), 'title': r.get('title'), 'url': r.get('href', r.get('url'))})
            except Exception as e:
                logging.error(e)

        prompt_content = keyword if not search_results else textwrap.dedent(
            """
            Web search results:

            {search_results}
            
            Current date: {current_date}
            Query: {question}
            Reply in the language: {language}

            Instructions: Using the provided web search results, write a comprehensive reply to the given query. Make sure to cite results using [[title](url)] notation after the reference. If the provided search results refer to multiple subjects with the same name, write separate answers for each subject.  The title and url in notation is from web search results, and DONOT use number as notation.
            """
        ).strip().format(search_results=search_results, current_date=datetime.today().strftime("%Y-%m-%d"), question=keyword, language=kwargs['region'])
        return search_results, prompt_content

    def get_prompt_by_instance_id(self, instance_id):
        return [(i.id, i.title) for i in self.session.query(Prompt).join(
            AppInstancePrompt,
            AppInstancePrompt.prompt_id == Prompt.id,
        ).filter(
            AppInstancePrompt.instance_id == instance_id,
            AppInstancePrompt.status == 0,
            Prompt.status == 0,
        ).all()]

    def get_model_by_instance_id(self, instance_id, model_names=list(), resource_id='', model_category=''):
        # 新版里面resource.extra内部会选择能用的模型列表，那就过滤一下
        return [(i.id, _(i.name)) for i in self.session.query(Model).join(
            Resource,
            Resource.id == Model.resource_id,
        ).filter(
            Model.name.in_(model_names) if len(model_names) > 0 else True,
            Model.category == model_category if model_category not in ['', ModelCategory.All.value] else True,
            AppInstance.id == instance_id,
            Model.resource_id == resource_id,
            # AppInstance.status == 0,
            Resource.status == 0,
            Model.status == 0,
        ).order_by(
            Model.price.asc(),
        ).all()]

    def save_result(self, result, show=True):
        if not hasattr(self, 'log_id'):
            logging.info("skip save result")
            return
        self.session.begin_nested()
        self.session.query(ChatLog).filter(
            ChatLog.id == self.log_id,
        ).update(dict(
            result=result,
            status=1 if show else 0,  # 控制隐藏或者显示
        ), synchronize_session=False)
        self.session.commit()

    def update_content(self, content):
        self.session.begin_nested()
        self.session.query(ChatLog).filter(
            ChatLog.id == self.log_id,
        ).update(dict(
            content=content,
        ), synchronize_session=False)
        self.session.commit()

    def duplicate(self, content):
        return True if self.session.query(ChatLog.id).filter(
            ChatLog.id != self.log_id,
            ChatLog.content == content,
        ).limit(1).scalar() else False
