import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.749 7c1.358 0 1.952.899 2 2.326l.002.189V12l2.872.423a2.75 2.75 0 0 1 2.292 3.284l-.041.168l-1.227 4.443a1.75 1.75 0 0 1-1.293 1.24l-.145.026l-3.061.44a1.75 1.75 0 0 1-1.825-.973l-.06-.14l-.217-.571a4.13 4.13 0 0 0-1.176-1.675l-.203-.162l-1.597-1.202a1.749 1.749 0 0 0-.266-.165l-.143-.064l-2.195-.868a.75.75 0 0 1-.473-.66c-.036-.722.49-1.246 1.421-1.712c.72-.36 1.728-.33 3.067.044l.272.079v-4.45C9.753 7.969 10.33 7 11.749 7zm0-5a7.25 7.25 0 0 1 6.668 10.102a3.74 3.74 0 0 0-1.195-.572l-.174-.043a5.75 5.75 0 1 0-9.918 1.19c-.504.038-.892.126-1.163.262c-.12.06-.236.122-.348.185A7.25 7.25 0 0 1 11.75 2zm0 2.502a4.749 4.749 0 0 1 4.273 6.823l-1.27-.187v-.647a3.24 3.24 0 0 0 .245-1.24a3.24 3.24 0 0 0-1.077-2.417l-.146-.124c-.48-.45-1.147-.71-2.025-.71c-.888 0-1.56.264-2.04.721a3.244 3.244 0 0 0-.957 3.786v1.857a.3.3 0 0 1-.3.3A4.718 4.718 0 0 1 7 9.251a4.749 4.749 0 0 1 4.749-4.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapDouble24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
