import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.59 2.53a2.25 2.25 0 0 0-3.181 0L2.53 12.41a2.25 2.25 0 0 0 0 3.182l1.172 1.171c.51.511 1.227.42 1.66.162a1.25 1.25 0 0 1 1.713 1.713c-.257.433-.349 1.15.162 1.66L8.41 21.47a2.25 2.25 0 0 0 3.182 0l9.878-9.878a2.25 2.25 0 0 0 0-3.182l-1.171-1.172c-.51-.51-1.228-.42-1.661-.162a1.25 1.25 0 0 1-1.713-1.713c.258-.433.349-1.15-.162-1.66L15.591 2.53z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
