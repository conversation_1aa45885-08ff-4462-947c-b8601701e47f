import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.483 18.296c-.799.837-2.092.387-2.431-.59c-.28-.807-.644-1.772-.998-2.483c-1.06-2.126-1.678-3.336-3.384-4.85a2.84 2.84 0 0 0-.841-.49c-1.13-.446-2.19-1.615-1.913-3.004l.353-1.765a2.5 2.5 0 0 1 1.794-1.922l5.6-1.527a4.5 4.5 0 0 1 5.61 3.536l.685 3.762a3 3 0 0 1-2.952 3.537h-.883l.01.052c.08.408.176.97.24 1.583c.065.61.1 1.284.049 1.912c-.05.617-.184 1.25-.504 1.73c-.11.164-.272.348-.435.519z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ThumbDislike20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
