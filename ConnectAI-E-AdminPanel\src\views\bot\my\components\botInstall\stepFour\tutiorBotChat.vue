<template>
  <div>
    <div class="block p-8 text-white rounded-lg bg-blue-500 min-h-520px">
      <h2 class="mb-1 text-2xl font-semibold">{{ $t('message.my.jqrcjzn') }}</h2>
      <a href="https://work.weixin.qq.com/wework_admin/frame#apps" target="_blank">
        <p class="mb-4 hover:underline text-xs font-bold">{{ $t('message.my.wework_tutior_title') }}</p>
      </a>
      <!-- List -->
      <div class="h-2" />
      <ul role="list" class="space-y-4 text-left">
        <li v-for="(item, index) in steps" :key="index" class="flex items-center space-x-2">
          <!--          <icon-akar-icons-light-bulb v-if="item.step === step" class="text-yellow-300 text-xl" />-->
          <icon-akar-icons-check v-if="item.step <= step" class="text-green-300 text-xl" />
          <icon-akar-icons-more-horizontal v-if="item.step > step" class="text-red-300 text-xl" />
          <span class="flex-1">{{ $t(item.content) }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineProps<{ step: number }>();

interface IStep {
  content: string;
  number: number;
  step: number;
  active?: boolean;
}

const steps = computed<IStep[]>(() => [
  {
    content: 'message.my.wework1',
    number: 1,
    step: 1
  },
  {
    content: 'message.my.wework2',
    number: 2,
    step: 1
  },
  {
    content: 'message.my.wework3',
    number: 3,
    step: 2
  },
  {
    content: 'message.my.wework4',
    number: 4,
    step: 2
  },

  {
    content: 'message.my.wework5',
    number: 5,
    step: 3
  },
  {
    content: 'message.my.wework6',
    number: 6,
    step: 3
  },
  {
    content: 'message.my.wework7',
    number: 7,
    step: 3
  },
]);
</script>

<style scoped></style>
