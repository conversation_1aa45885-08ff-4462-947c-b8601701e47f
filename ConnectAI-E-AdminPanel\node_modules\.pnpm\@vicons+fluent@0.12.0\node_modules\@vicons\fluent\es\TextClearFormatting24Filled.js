import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.761 14.001a.841.841 0 0 1-.55-.18a.597.597 0 0 1-.211-.464c0-.11.031-.245.093-.408L6.02 2.71c.18-.473.513-.71.998-.71c.457 0 .775.234.956.702l3.934 10.246c.062.163.093.299.093.408c0 .184-.073.34-.22.465a.807.807 0 0 1-.541.179c-.361 0-.61-.188-.745-.563l-1.083-2.92h-4.83l-1.075 2.92c-.135.375-.384.563-.745.563zm2.26-4.731H8.98L7.025 3.902h-.05L5.02 9.27zm7.77 5.46l5.48 5.48l4.157-4.156a1.95 1.95 0 0 0 .002-2.758l-2.724-2.724a1.947 1.947 0 0 0-2.759.001l-4.156 4.157zm1.957-3.372L13 13.106V2.756a.77.77 0 0 1 .195-.544a.68.68 0 0 1 .51-.211c.206 0 .374.07.504.21c.135.142.203.323.203.545v4.073h.032c.27-.487.641-.869 1.112-1.144a3.13 3.13 0 0 1 1.598-.414c1.066 0 1.926.397 2.58 1.192c.655.796.982 1.853.982 3.173c0 .176-.006.347-.017.514l-.286-.286a2.935 2.935 0 0 0-1.161-.715c-.063-.732-.273-1.33-.63-1.793c-.432-.563-1.022-.844-1.768-.844c-.72 0-1.309.29-1.769.868c-.454.574-.681 1.326-.681 2.256c0 .666.115 1.24.345 1.722zm2.463 9.913l-5.481-5.48l-1.157 1.157a1.95 1.95 0 0 0-.002 2.759l2.724 2.723a1.94 1.94 0 0 0 1.208.564L14.5 23H20a.75.75 0 1 0 0-1.5h-3.02l.23-.23z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextClearFormatting24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
