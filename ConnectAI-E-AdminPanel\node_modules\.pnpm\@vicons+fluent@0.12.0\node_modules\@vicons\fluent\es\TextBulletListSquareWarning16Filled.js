import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.002 4.001a2 2 0 0 1 2-2H12a2 2 0 0 1 2 2v6.13l-1.27-2.154a1.977 1.977 0 0 0-.743-.722a2.032 2.032 0 0 0-2.323.245H7.004a.5.5 0 0 0 0 1h1.958l-1.18 2h-.778a.5.5 0 0 0 0 1h.189l-.925 1.568c-.167.283-.26.604-.267.932h-2a2 2 0 0 1-2-2V4zM4.75 5.75a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5zM5.5 8a.75.75 0 1 0-1.5 0a.75.75 0 0 0 1.5 0zM5.5 11A.75.75 0 1 0 4 11a.75.75 0 0 0 1.5 0zm1.004-6a.5.5 0 0 0 .5.5h4.474a.5.5 0 1 0 0-1H7.004a.5.5 0 0 0-.5.5zm4.231 3.034c.251-.066.526-.039.767.095c.154.086.28.21.367.356l3.002 5.09c.134.228.16.484.095.716a.956.956 0 0 1-.462.58c-.152.084-.325.129-.501.129H7.998c-.28 0-.53-.11-.71-.285a.93.93 0 0 1-.159-1.14l3.003-5.09a.988.988 0 0 1 .603-.45zm.766 1.468a.5.5 0 0 0-1 0v1.996a.5.5 0 1 0 1 0V9.502zM11 14.5a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListSquareWarning16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
