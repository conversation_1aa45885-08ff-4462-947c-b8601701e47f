import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.997 18.532a1 1 0 0 1 .993.883l.007.117v1.456a1 1 0 0 1-1.993.116l-.007-.116v-1.456a1 1 0 0 1 1-1zm6.036-1.932l1.03 1.03a1 1 0 0 1-1.415 1.413l-1.029-1.029a1 1 0 0 1 1.414-1.414zm-10.66 0a1 1 0 0 1 0 1.414l-1.028 1.03a1 1 0 0 1-1.415-1.415l1.03-1.03a1 1 0 0 1 1.414 0zM12.01 6.472a5.525 5.525 0 1 1 0 11.05a5.525 5.525 0 0 1 0-11.05zM11.25 9a.75.75 0 0 0-.743.648l-.007.102v3.004l.007.102a.75.75 0 0 0 .642.641l.101.007h2l.102-.007a.75.75 0 0 0 .641-.641l.007-.102l-.007-.102a.75.75 0 0 0-.641-.641l-.102-.007H12V9.75l-.006-.102A.75.75 0 0 0 11.25 9zm9.727 2.018a1 1 0 0 1 .117 1.993l-.117.007h-1.455a1 1 0 0 1-.117-1.993l.117-.007h1.456zM4.48 10.99a1 1 0 0 1 .117 1.993l-.117.007H3.023a1 1 0 0 1-.116-1.993l.116-.007H4.48zM6.25 4.874l.095.083l1.029 1.03a1 1 0 0 1-1.32 1.497L5.96 7.4L4.93 6.371a1 1 0 0 1 1.32-1.497zm12.813.083a1 1 0 0 1 .083 1.32l-.083.094l-1.03 1.03a1 1 0 0 1-1.497-1.32l.083-.095l1.03-1.03a1 1 0 0 1 1.414 0zM12 2.013a1 1 0 0 1 .993.883l.007.117v1.455a1 1 0 0 1-1.994.117l-.006-.117V3.013a1 1 0 0 1 1-1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TimeAndWeather24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
