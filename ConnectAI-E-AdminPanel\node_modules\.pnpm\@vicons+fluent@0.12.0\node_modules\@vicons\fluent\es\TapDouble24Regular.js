import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.75 7c1.348 0 2.179.97 2.245 2.329l.005.18v2.115l2.219.403a3.252 3.252 0 0 1 2.602 3.857l-.043.18l-1.048 3.923a2.25 2.25 0 0 1-1.689 1.616l-.165.03l-2.418.347a2.25 2.25 0 0 1-2.28-1.123l-.08-.155l-.03-.063a3.464 3.464 0 0 0-1.025-1.283l-.194-.138l-1.884-1.256l-.093-.059l-.098-.052l-2.363-1.175a.75.75 0 0 1-.416-.655c-.024-1.109.466-1.964 1.42-2.44c.701-.352 1.634-.332 2.826.016l.259.08V9.507C9.5 8.055 10.342 7 11.75 7zm0 1.5c-.46 0-.713.275-.746.866L11 9.508v5.245a.75.75 0 0 1-1.036.693c-1.46-.602-2.441-.743-2.879-.524c-.254.127-.42.29-.51.519l-.038.12l1.905.947l.181.097l.174.109l1.884 1.256a4.966 4.966 0 0 1 1.626 1.792l.122.245l.03.062a.75.75 0 0 0 .677.434l.109-.008l2.418-.347a.75.75 0 0 0 .581-.443l.037-.105l1.048-3.924a1.75 1.75 0 0 0-1.239-2.142l-.07-.017l-2.904-.529a.75.75 0 0 1-.609-.63l-.007-.108V9.508c0-.69-.255-1.008-.75-1.008zM11.749 2a7.25 7.25 0 0 1 6.697 10.034a4.204 4.204 0 0 0-1.305-.785a5.75 5.75 0 1 0-10.219 1.13a3.316 3.316 0 0 0-.955.307c-.16.08-.312.17-.455.266A7.25 7.25 0 0 1 11.748 2zm0 2.502A4.749 4.749 0 0 1 16.165 11L15 10.789V9.51l-.004-.195l.002-.063c0-.681-.21-1.313-.567-1.835l-.09-.132C13.783 6.496 12.891 6 11.75 6c-1.175 0-2.085.526-2.64 1.353a3.237 3.237 0 0 0-.61 1.898l.002.11l-.002.147v2.89l-.262-.036a1 1 0 0 1-.617-.32C7.207 11.583 7 10.653 7 9.25a4.749 4.749 0 0 1 4.749-4.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapDouble24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
