import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.435 1.942a.6.6 0 0 0-.83.018L1.99 8.576a.6.6 0 0 0 0 .849l.926.926c.003 0 .01.003.024.003c.03 0 .07-.011.108-.036a1.9 1.9 0 0 1 2.635 2.635a.205.205 0 0 0-.036.108c0 .013.002.02.003.023l.904.905a.6.6 0 0 0 .862-.013l6.599-7.012a.6.6 0 0 0-.032-.853l-.886-.813h-.003a.11.11 0 0 0-.043 0a.278.278 0 0 0-.147.083a1.9 1.9 0 1 1-2.8-2.568a.277.277 0 0 0 .069-.154a.11.11 0 0 0-.003-.042v-.003l-.734-.672zM8.04 1.394a1.4 1.4 0 0 1 1.936-.042l.733.673c.219.2.28.473.26.706c-.021.23-.122.455-.278.624a1.1 1.1 0 1 0 1.622 1.487c.155-.17.37-.29.597-.331a.842.842 0 0 1 .726.197l.887.813a1.4 1.4 0 0 1 .073 1.991l-6.599 7.012a1.4 1.4 0 0 1-2.01.03l-.904-.904c-.347-.347-.263-.847-.067-1.14a1.1 1.1 0 0 0-1.525-1.525c-.295.195-.794.279-1.141-.069l-.926-.926a1.4 1.4 0 0 1 0-1.98L8.04 1.395z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
