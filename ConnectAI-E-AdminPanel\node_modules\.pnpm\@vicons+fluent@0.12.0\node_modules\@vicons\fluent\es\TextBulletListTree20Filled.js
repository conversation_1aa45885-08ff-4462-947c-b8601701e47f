import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.25 7a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zM7 5.75A.75.75 0 0 1 7.75 5h9.5a.75.75 0 0 1 0 1.5h-9.5A.75.75 0 0 1 7 5.75zM7.75 10a.75.75 0 0 0 0 1.5h9.5a.75.75 0 0 0 0-1.5h-9.5zm4 5a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zM4.5 10.75a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0zM7.25 17a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListTree20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
