<template>
  <div
    v-if="loading"
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space vertical>
      <n-skeleton height="40px" width="33%" :sharp="false" />
      <n-skeleton height="60px" :sharp="false" />
      <n-skeleton height="60px" />
      <n-skeleton height="60px" />
      <n-skeleton height="40px" width="100px" :sharp="false" />
    </n-space>
  </div>
  <div
    v-else
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flow-root mb-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold dark:text-white">{{ $t('message.messenger.jdry') }}</h3>
        <button
          type="button"
          class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          :disabled="createLoading"
          @click="handleAddSeat"
        >
          <svg
            v-if="createLoading"
            aria-hidden="true"
            role="status"
            class="inline w-4 h-4 mr-3 text-white animate-spin"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="#E5E7EB"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentColor"
            />
          </svg>
          {{ t('message.messenger.xzcy') }}
        </button>
      </div>
      <div class="mb-4 font-normal text-gray-500 dark:text-gray-400">
        {{ $t('message.messenger.pzhinfo') }}
      </div>
      <div>
        <n-data-table scroll-x="1000" :columns="columns" :data="tableData" :bordered="false" />
        <div class="flex justify-end mt-4" v-if="itemCount > 10"><n-pagination v-bind="paginationOptions" /></div>
      </div>
    </div>
  </div>
  <n-modal v-model:show="showModal">
    <n-card style="width: 500px" :title="t('message.messenger.xzcy')" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-alert type="info" class="mb-4">
        <template #header>
          <div class="flex justify-between text-sm">
            <div>{{ t('message.messenger.tjkfinfo') }}</div>
          </div>
        </template>
      </n-alert>
      <n-form ref="formRef" :label-width="80" :model="formValue" :rules="rules">
        <n-form-item :label="t('message.messenger.xzcytj')" path="open_id">
          <n-select :options="chatMemberOptions" @update-value="handleSeatChange" v-model:value="formValue.open_id" />
        </n-form-item>
        <div class="flex items-center gap-4">
          <n-form-item class="flex-1" :label="t('message.messenger.kssj')" path="start_time">
            <n-time-picker v-model:formatted-value="formValue.start_time" class="w-full" value-format="HH:mm:ss" />
          </n-form-item>
          <n-form-item class="flex-1" :label="t('message.messenger.jssj')" path="end_time">
            <n-time-picker v-model:formatted-value="formValue.end_time" class="w-full" value-format="HH:mm:ss" />
          </n-form-item>
        </div>
      </n-form>
      <template #footer>
        <div class="flex justify-end">
          <button
            type="button"
            class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
            @click="handleClose"
          >
            {{ t('message.ai.qx') }}
          </button>
          <button
            type="button"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
            @click="handleConfirm"
          >
            {{ t('message.messenger.qd') }}
          </button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="tsx">
import { ref, onMounted, computed, watch, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import {
  getMessengerSeatList,
  getMessengerChatMemberList,
  createSeatMember,
  updateSeatMember,
  updateSeatMemberAction
} from '@/service/api/messenger';
import { t } from '@/locales';
import type { DataTableColumns } from 'naive-ui';
import { NButton, useDialog, NSwitch, useMessage } from 'naive-ui';
import { usePagination } from '@/hooks';
const props = defineProps<{
  messengerInfo: ApiMessenger.MessengerChatInfoDetails;
}>();

const { messengerInfo } = toRefs(props);

const message = useMessage();
const route = useRoute();
const id = route.query.id as string;
const showModal = ref(false);
const dialog = useDialog();

const itemCount = ref<number>(0);
const { pagination, paginationOptions } = usePagination({ itemCount });

const tableData = ref<ApiMessenger.MessengerSeat[]>([]);

const chatMember = ref<ApiMessenger.MessengerChatMember[]>([]);

const loading = ref(false);

const createLoading = ref(false);

const formRef = ref();

const formValue = ref<{
  start_time: string | null;
  end_time: string | null;
  open_id: string;
  name: string;
  id?: string;
}>({
  start_time: '00:00:00',
  end_time: '23:59:59',
  open_id: '',
  name: ''
});

const rules = {
  open_id: {
    required: true,
    message: t('message.messenger.qsrxm'),
    trigger: 'blur'
  },
  start_time: { type: 'string', required: true, message: t('message.messenger.qsrnl'), trigger: ['change', 'blur'] },
  end_time: { type: 'string', required: true, message: t('message.messenger.qsrnl'), trigger: ['change', 'blur'] }
};

const columns: DataTableColumns<any> = [
  {
    title: t('message.messenger.cyxm'),
    key: 'name',
    width: 100,
    align: 'center'
  },
  {
    title: t('message.messenger.zt'),
    key: 'status',
    width: 100,
    align: 'center',
    render({ status }) {
      return (
        <NSwitch value={Boolean(status)}>
          {{
            checked: () => t('message.messenger.on'),
            unchecked: () => t('message.messenger.off')
          }}
        </NSwitch>
      );
    }
  },
  {
    title: t('message.messenger.jdsjfw'),
    key: 'start_time',
    width: 100,
    align: 'center',
    render(record) {
      return (
        <div class="flex gap-2 justify-center">
          {record.start_time}-{record.end_time}
        </div>
      );
    }
  },
  {
    title: t('message.log.cz'),
    key: 'actions',
    width: 100,
    align: 'center',
    fixed: 'right',
    render(record) {
      return (
        <div class="flex gap-2 justify-center">
          <NButton tertiary size={'small'} onClick={() => handleEditSeat(record)}>
            {t('message.messenger.bj')}
          </NButton>
          <NButton tertiary size={'small'} onClick={() => handleEnableSeat(record)}>
            {t(`${record.status === 1 ? t('message.messenger.off') : t('message.messenger.on')}`)}
          </NButton>
          <NButton tertiary size={'small'} onClick={() => handleDeleteSeat(record)}>
            {t('message.messenger.sc')}
          </NButton>
        </div>
      );
    }
  }
];

const chatMemberOptions = computed(() => {
  return chatMember.value.map((item) => {
    return {
      label: item.name,
      value: item.member_id,
      disabled: Boolean(tableData.value.find((seat) => seat.open_id === item.member_id))
    };
  });
});

function handleSeatChange(open_id: string) {
  const member = chatMember.value.find((item) => item.member_id === open_id);
  if (member) {
    formValue.value.name = member.name;
  }
}

async function handleEditSeat(record: any) {
  const {
    data: { data }
  } = await getMessengerChatMemberList({ id });
  formValue.value = record;
  chatMember.value = data;
  showModal.value = true;
}

async function handleAddSeat() {
  if (!messengerInfo.value.chat_id) {
    message.warning(t('message.messenger.qxglfs'));
    return;
  }

  try {
    createLoading.value = true;
    const {
      data: { data }
    } = await getMessengerChatMemberList({ id });
    showModal.value = true;
    chatMember.value = data;
    createLoading.value = false;
  } catch (error) {
    createLoading.value = false;
  }
}

function handleClose() {
  showModal.value = false;
  formValue.value = {
    name: '',
    open_id: '',
    start_time: '00:00:00',
    end_time: '23:59:59'
  };
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (formValue.value.id) {
        await updateSeatMember({
          id,
          data: {
            ...formValue.value,
            seat_id: formValue.value.id
          }
        });
      } else {
        await createSeatMember({
          id,
          data: {
            ...formValue.value
          }
        });
      }
      message.success(t('message.msg.bccg'));
      getSeatList();
      handleClose();
    }
  });
}
async function getSeatList() {
  try {
    loading.value = true;
    const {
      data: { data, total }
    } = await getMessengerSeatList(id, { ...pagination });
    itemCount.value = total;
    tableData.value = data;
    loading.value = false;
  } catch (err) {
    loading.value = false;
  }
}
watch(pagination, () => {
  getSeatList();
});

async function handleEnableSeat(record: any) {
  await updateSeatMemberAction({
    id,
    data: {
      seat_id: record.id,
      action: record.status === 1 ? 'disable' : 'enable'
    }
  });
  getSeatList();
  message.success(t(`${record.status === 1 ? t('message.messenger.jycg') : t('message.messenger.qycg')}`));
}

async function handleDeleteSeat(record: any) {
  dialog.warning({
    title: t('message.messenger.warn'),
    content: t('message.messenger.qrsc'),
    positiveText: t('message.messenger.qd'),
    negativeText: t('message.messenger.qx'),
    onPositiveClick: async () => {
      await updateSeatMemberAction({
        id,
        data: {
          seat_id: record.id,
          action: 'delete'
        }
      });
      getSeatList();
      message.success(t('message.messenger.sccg'));
    }
  });
}

onMounted(() => {
  getSeatList();
});
</script>
