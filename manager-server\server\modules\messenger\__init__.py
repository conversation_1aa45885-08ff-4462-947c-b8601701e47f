
from .handler import (
    MessengerListHandler,
    MessengerInfoHandler,
    MessengerBotHandler,
    MessengerClientHandler,
    MessengerChatListHandler,
    MessengerChatMemberHandler,
    MessengerSeatHandler,
    MessengerInstanceHandler,
    MessengerCallbackHandler,
    MessengerAppHandler,
)

urls = [
    # websocket回调
    (r"/ws/(auth|sub|unsub|pub)/([0-9a-z]{24})", MessengerCallbackHandler),
    (r"/api/app/messenger", MessengerAppHandler),
    # 客服列表
    (r"/api/messenger", MessengerListHandler),
    (r"/api/messenger/([0-9a-z]{24})/info", MessengerInfoHandler),
    # 更新客服对应的bot信息
    (r"/api/messenger/([0-9a-z]{24})/bot", MessengerBotHandler),
    # 客服对应客户端配置（web）
    (r"/api/messenger/([0-9a-z]{24})/client", MessengerClientHandler),
    # 这个接口给chat box使用
    (r"/chat/([0-9a-z]{24})/client", MessengerClientHandler),
    # 客服选择群chat_id，以及群成员
    (r"/api/messenger/([0-9a-z]{24})/chat", MessengerChatListHandler),
    (r"/api/messenger/([0-9a-z]{24})/chat/member", MessengerChatMemberHandler),
    (r"/api/messenger/([0-9a-z]{24})/seat", MessengerSeatHandler),
    # 客服开启AI能力之后获取app
    (r"/api/messenger/([0-9a-z]{24})/app", MessengerInstanceHandler),
]
