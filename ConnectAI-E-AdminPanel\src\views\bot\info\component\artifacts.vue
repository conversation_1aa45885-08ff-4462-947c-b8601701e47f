<template>
  <div
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flex justify-between items-center">
      <h3 class="mb-4 text-xl font-semibold dark:text-white">{{ $t('message.bot.artifacts') }}</h3>
      <n-switch
        v-model:value="data.artifacts"
        checked-value="enable"
        @click="firstSwitch"
        unchecked-value="disable"
      ></n-switch>
    </div>

    <div class="mb-4 font-normal text-gray-500 dark:text-gray-400">
      {{ $t('message.bot.artifactsinfo') }}
    </div>

    <div class="flex items-center">
      <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
      <button
        v-else
        type="button"
        class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
        @click="handleSave"
      >
        <icon-akar-icons-save class="mr-2" />
        {{ $t('message.bot.save') }}
      </button>

      <button
        type="button"
        class="inline-flex items-center px-3 py-2 ml-4 text-sm font-medium text-blue-600 border border-blue-600 bg-transparent rounded-lg hover:bg-blue-50 focus:ring-4 focus:ring-blue-300"
        @click="showModal = true"
      >
        {{ $t('message.bot.ConfigTutorial') }}
      </button>
    </div>
  </div>
  <n-modal v-model:show="showModal">
    <n-card class="w-full mx-auto lg:w-5/11">
      <template #header>
        <h1 class="text-xl font-bold">{{ $t('message.bot.ConfigTutorial') }}</h1>
      </template>
      <n-alert class="mb-6" :title="t('message.bot.artifactsalert')" type="info"> </n-alert>
      <div class="mb-6">
        <p style="margin: 8px; font-weight: 900; font-size: 16px">{{ $t('message.bot.artifatsone') }}</p>
        <div class="relative">
          <input
            id="card"
            type="search"
            readonly
            :value="oauth"
            class="block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
          />
          <n-tooltip placement="bottom" trigger="click">
            <template #trigger>
              <button
                class="copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                data-clipboard-target="#card"
              >
                <icon-akar-icons-chat-add class="text-lg mr-2" />
                {{ t('message.global.fzlj') }}
              </button>
            </template>
            <span>{{ t('message.market.copy_success') }}</span>
          </n-tooltip>
        </div>
      </div>
      <div class="mb-6">
        <p style="margin: 8px; font-weight: 900; font-size: 16px">{{ $t('message.bot.artifatstwo') }}</p>
        <img :src="feishuduoweibiao" class="w-full" />
      </div>
      <template #footer>
        <div class="flex justify-end">
          <button
            type="button"
            class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            @click="showModal = false"
          >
            {{ $t('message.bot.ISee') }}
          </button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import feishuduoweibiao from '@/assets/images/feishuduoweibiao.jpg';
import { updateAppSetting } from '@/service/api/app';
import { getAppClient, getAppClientBot } from '@/service/api/app';
import { t } from '@/locales';

const message = useMessage();

const route = useRoute();

const emit = defineEmits(['change', 'update:data']);

const props = defineProps<{
  data: ApiApp.APPSetting;
  loading: boolean;
}>();

const showModal = ref<boolean>(false);

const firstSwitch = () => {
  if (props.data.artifacts === 'enable') {
    if (
      !localStorage.getItem('artifacts') &&
      localStorage.getItem('artifacts') !== `${route.query.id}ai-zhoulin.forkway`
    ) {
      localStorage.setItem('artifacts', `${route.query.id}ai-zhoulin.forkway` as string);
      showModal.value = true;
    }
  }
};

const oauth = ref<string>('');

async function fetchData() {
  const {
    data: { data: clientData }
  } = await getAppClient({ id: route.query.id as string });
  const id = clientData.filter((item) => item.platform === 'feishu')[0].id;
  const {
    data: {
      data: { callback_url }
    }
  } = await getAppClientBot({ id: route.query.id as string, botId: id });
  oauth.value = callback_url.card.replace(/card$/, 'oauth');
}

const data = useVModel(props, 'data', emit);

async function handleSave() {
  await updateAppSetting({ id: route.query.id as string, data: data.value });
  message.success(t('message.msg.bccg'));
}

onMounted(() => {
  fetchData();
});
</script>
