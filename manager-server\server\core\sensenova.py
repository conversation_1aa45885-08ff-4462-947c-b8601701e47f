import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)   

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.adapters.openai import convert_dict_to_message, convert_message_to_dict
from langchain.schema import ChatGeneration, ChatResult, BaseMessage

logger = logging.getLogger(__name__)


MODELS = {
    'nova-ptc-xl-v1': '',
    'nova-ptc-xs-v1':'',
}


class SenseNovaClient(object):

    def build_query(
        self,
        messages=list(),
        model='nova-ptc-xl-v1',
        stream=False,
        api_base='',
        api_key='',
        secret_key='',
        **kwargs
    ):
        body = json.dumps(dict(messages=messages,model=model,stream=stream, **kwargs))
        if model not in MODELS.keys():
            raise Exception('not supported model {}'.format(model))
        headers = {'api-key': api_key}

        if secret_key:
            headers = {
                'api-base': api_base,
                'api-key': api_key,
                'secret-key': secret_key,
            }

            from core.api_base import NewApiBase
            api_base = NewApiBase('商汤日日新').url 

        return '{}/llm/chat-completions'.format(api_base), body, headers
        #return 'https://api.sensenova.cn/v1/llm/chat-completions', body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            for line in r.iter_lines():
                if 'data:' == line[:5] and '[DONE]' not in line:
                    yield json.loads(line[5:])

    def create(self, messages=list(), model='nova-ptc-xl-v1', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages=messages, model=model, stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            return response.json()

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5] and '[DONE]' not in line:
                    yield json.loads(line[5:])

    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


async def acompletion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the async completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    async def _completion_with_retry(**kwargs: Any) -> Any:
        # Use OpenAI's async api https://github.com/openai/openai-python#async-api
        return await llm.client.acreate(**kwargs)

    return await _completion_with_retry(**kwargs)


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class SenseNovaChat(SimpleChatModel):
    """
    
    """
    client: Any  #: :meta private:
    # 当前支持2个模型
    model_name: str = "nova-ptc-xl-v1"  # ERNIE-Bot/BLOOMZ-7B
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    secret_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 0.8   # 温度采样参数，大于1的值倾向于生成更加多样的回复，小于1倾向于生成更加稳定的回复
    top_p: float = 0.7          # 核采样参数，解码生成token时，在概率和大于等于top_p的最小token集合中进行采样
    repetition_penalty: float = 1.05  # 重复惩罚系数，1代表不惩罚，大于1倾向于生成不重复token，小于1倾向于生成重复token，推荐使用范围为[1,1.2]
    user: str = ''           # 外部用户ID，应用开发者可将应用系统用户ID传入，方便追踪

    def _llm_type(self) -> str:
        return "sensenova_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'secret_key': self.secret_key,
            'api_base': self.api_base,
            'model': self.model_name,
        }
        params.update({
            'temperature': self.temperature,
            'top_p': self.top_p,
            'user': self.user,
            'repetition_penalty': self.repetition_penalty
        })

        message_dicts = [convert_message_to_dict(m) for m in messages]
        self.client = SenseNovaClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, messages=message_dicts, **params):

                choices = stream_resp.get("data", {}).get("choices", [])
                if len(choices) > 0:
                    token = choices[0]['delta']
                    if run_manager:
                        run_manager.on_llm_new_token(token)
                response += token
            return response
        else:
            full_response = completion_with_retry(self, messages=message_dicts, **params)
            choices = full_response.get("data", {}).get("choices", [])
            return choices[0]['message']


if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    from core.api_base import NewApiBase
    async def main():
        client = SenseNovaClient()
        messages = [{"role":"user","content":"帮我推荐一条旅游路线"}]
        api_key = ''
        api_secret=''
        api_base = NewApiBase('商汤日日新').url

        # response = client.create(messages, api_base=api_base, api_key=api_key, stream=True)
        # print(response)
        # print(list(response))

        # response = client.acreate(messages, api_base=api_base, api_key=api_key, stream=True)
        # print(response)
        # async for stream_resp in await response:
        #     print(stream_resp)

        from langchain.schema import HumanMessage
        chat = SenseNovaChat(
            api_base=api_base,
            api_key=api_key,
            api_secret=api_secret,
            streaming=True,
        )
        # messages = [HumanMessage(content='你是谁')]
        messages = [HumanMessage(content='帮我推荐一条西藏旅游线路')]
        result = chat(messages)
        print(result)

        asyncio.run(main())

#message_dicts
