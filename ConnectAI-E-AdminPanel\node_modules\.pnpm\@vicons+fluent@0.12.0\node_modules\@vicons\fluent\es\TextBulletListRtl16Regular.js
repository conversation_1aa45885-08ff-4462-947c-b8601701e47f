import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14 4.5a1 1 0 1 1 0-2a1 1 0 0 1 0 2zM14 9a1 1 0 1 1 0-2a1 1 0 0 1 0 2zm-1 3.5a1 1 0 1 0 2 0a1 1 0 0 0-2 0zM10.5 3a.5.5 0 0 1 0 1h-9a.5.5 0 0 1 0-1h9zm.5 5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0 0 1h9A.5.5 0 0 0 11 8zm-.5 4a.5.5 0 0 1 0 1h-9a.5.5 0 0 1 0-1h9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListRtl16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
