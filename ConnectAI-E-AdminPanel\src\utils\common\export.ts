export async function exportCSV(data: { backend: any; disp: string }) {
  try {
    const dl = `data:text/csv;charset=utf-8,\ufeff${encodeURIComponent(data.backend)}`;
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = dl;
    link.setAttribute('download', data.disp.split('filename=')[1].replace(/['"]/g, ''));
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(dl);
  } catch (err) {
    console.error(err);
  }
}
