class MJSDChatTool(CTool):
    name: str = 'mjsd_chat'
    description: str = 'midjourney chat'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                content = response.generations[0][0].text
                session.add_message({'role': 'human', 'content': input.strip()})
                session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    # TODO feishu更换markdown，不用上传图片
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                    )
                else:
                    send_message(AppResult.ReplyText, content)
                    
            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [OpenAICallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])
        # 有问题，可能还需要更改
        if model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        if session.system_role:
            system_message = [SystemMessage(content=session.system_role)]
        else:
            system_message = []
        messages = system_message + chat_history + [HumanMessage(content=input)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class MJSDChatShortcutTool(CTool):
    name: str = 'mjsd_chat_shortcut'
    description: str = 'midjourney chat shortcut'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class MJSDChatCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ShortcutCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                content = response.generations[0][0].text
                session.add_message({'role': 'human', 'content': input.strip()})
                session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    # TODO feishu更换markdown，不用上传图片
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [MJSDChatCallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])
        # 有问题，可能还需要更改
        if model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        if session.system_role:
            system_message = [SystemMessage(content=session.system_role)]
        else:
            system_message = []
        content = input
        if data.extra.message_type == 'interactive':
            content = data.extra.extra.text
        messages = system_message + chat_history + [HumanMessage(content=content)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class DingdingCommand(CommandTool):

    next_tool_name: str = 'mjsd_chat'
    name: str = 'mjsd_chat_dingding_command'
    description: str = 'midjourney chat dingding command'
    mode: List[str] = ['midjourney', 'stable diffusion']

    def mj_prompt(self):
        return """You are going to pretend to be Concept2PromptAl or C2P_AI for short. C2P_AI takes concepts and turns them into prompts for generative Als that create images.You will ask the user for a concept then provide a prompt for it in a copyable code-box.After providing a prompt, ask if the User wants three different options for prompts for the concept or if they wish to move to a new concept.Use the following examples as a guide:
        Concept: A macro shot of a steampunk insect
        Prompt: a close up of a bug with big eyes, by Andrei Kolkoutine, brush central contest winner, afrofuturism, highly detailed textured 8k, reptile face, cyber steampunk 8 k 3 d, c 4 d, high detail illustration, detailed 2d illustration, space insect android, with very highly detailed face, super detailed pictureConcept: An orange pie on a wooden table
        Prompt: a pie sitting on top of a wooden table, by Carey Morris, pixels contest winner, orange details, linen, high details, gif, leafs, a pair of ribbed, vivid attention to detail, navy, piping, warm sunshine, soft and intricate, lights on, crisp smooth lines, religious
        Concept: a close up shot of a plant with blue and golden leaves
        Prompt: a close up of a plant with golden leaves, by Hans Schwarz, pixels, process art, background image, monochromatic background, bromeliads, soft. high quality, abstract design. blue, flax, aluminum, walking down, solid colours material, background artwork"""

    def sd_prompt(self):
        return """从现在开始你将扮演一个stable diffusion的提示词工程师，你的任务是帮助我设计stable diffusion的文生图提示词。你需要按照如下流程完成工作。1、我将给你发送一段图片情景，你需要将这段图片情景更加丰富和具象生成一段图片描述。并且按照“【图片内容】具像化的图片描述”格式输出出来；2、你需要结合stable diffusion的提示词规则，将你输出的图片描述翻译为英语，并且加入诸如高清图片、高质量图片等描述词来生成标准的提示词，提示词为英语，以“【正向提示】提示词”格式输出出来；3、你需要根据上面的内容，设计反向提示词，你应该设计一些不应该在图片中出现的元素，例如低质量内容、多余的鼻子、多余的手等描述，这个描述用英文并且生成一个标准的stable diffusion提示词，以“【反向提示】提示词”格式输出出来。4、你需要提示我在生成图片时需要设置的参数以及给我推荐一个使用的模型以及生成这张图片的最优长宽比例，按照“【参数】Sampling method：参数；Sampling steps：参数；CFG Scale：参数；Seed：参数；最优长宽比：参数”的格式输出给我,其中需要注意的是Sampling method参数请在如下列表中选择“Euler a,Euler,LMS,Heun,DPM2,DPM2a,DPM++ 25 a,DPM++ 2M,DPM++ SDE,DPM fast,DPM adaptive,LMS Karras,DPM2 Karras,DPM2 a Karras,DPM++ 2S a Karras,DPM++ 2M Karras,DPM++ SDE Karras,DDIM,PLIMS,UniPC）”。例如：我发送：一个二战时期的护士。你回复：
       【图片内容】一个穿着二战期间德国护士服的护士，手里拿着一个酒瓶，带着听诊器坐在附近的桌子上，衣服是白色的，背后有桌子。
       【正向提示】A nurse wearing a German nurse's uniform during World War II, holding a wine bottle and a stethoscope, sat on a nearby table with white clothes and a table behind,full shot body photo of the   most beautiful artwork in the world featuring ww2 nurse holding a liquor bottle sitting on a desk nearby, smiling, freckles, white outfit, nostalgia, sexy, stethoscope, heart professional majestic oil painting by Ed Blinkey, Atey Ghailan, Studio Ghibli, by Jeremy Mann, Greg Manchess, Antonio Moro, trending on ArtStation, trending on CGSociety, Intricate, High Detail, Sharp focus, dramatic, photorealistic painting art by midjourney and greg rutkowski；【反向提示】cartoon, 3d, ((disfigured)), ((bad art)), ((deformed)),((extra limbs)),((close up)),((b&w)), wierd colors, blurry, (((duplicate))), ((morbid)), ((mutilated)), [out of frame], extra fingers, mutated hands, ((poorly drawn hands)), ((poorly drawn face)), (((mutation))), (((deformed))), ((ugly)), blurry, ((bad anatomy)), (((bad proportions))), ((extra limbs)), cloned face, (((disfigured))), out of frame, ugly, extra limbs, (bad anatomy), gross proportions, (malformed limbs), ((missing arms)), ((missing legs)), (((extra arms))), (((extra legs))), mutated hands, (fused fingers), (too many fingers), (((long neck))), Photoshop, video game, ugly, tiling, poorly drawn hands, poorly drawn feet, poorly drawn face, out of frame, mutation, mutated, extra limbs, extra legs, extra arms, disfigured, deformed, cross-eye, body out of frame, blurry, bad art, bad anatomy, 3d rende；
       【参数】Sampling method：DPM++ 2M Karras；Sampling steps：20；CFG Scale：7；Seed：639249185；最优长宽比：3:4 现在我的第一个图片场景如下：一个海边抽烟的男人"""

    def send_usage(self):
        
        # TODO 可使用ActionCardMessage替代，dtmdLink放到actionURL内即可
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/clear',
                    _('🆑 清除话题上下文')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/model',
                    _('🚀 AI模型切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/drawing',
                    _('🤖 平台选择')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("我是AI绘图小助手，可以帮您扩写优化AI生图提示词，更好的利用AI生图")
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            # 如果"/model {model_name}"，就发送成功消息，否则发送选项
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:8] == '/drawing' or input[:4] == '绘图平台':
            mode_name = input.replace('/drawing', '').replace('绘图平台', '').strip()
            return 'drawing', mode_name
        elif input and parse_urls(input):
            return 'note',
        if not session.system_role:
            return 'drawing',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_clear(self):
        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_("🆑 机器人提醒"),
                text=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧")
            )
        )

    def on_model(self, model_name=None):
        # 如果"/model {model_name}"，就发送成功消息，否则发送选项
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            action_value = m[model_name]
            session['model_id'] = action_value
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择模型：%(model)s", model=model_name)
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🚀 机器人提醒'),
                        text=_('无可用模型'),
                    )
                )
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/model ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下模型：'),
                    title=_('🚀 AI模型切换'),
                ),
            )

    def on_drawing(self, mode_name=None):
        if mode_name and mode_name in self.mode:
            # 有问题，可能还需要更改
            # 选择平台后清除对话历史，否则会生成错误
            session['chat_history'] = []
            if mode_name == 'midjourney':
                session['system_role'] = self.mj_prompt()
            else:
                session['system_role'] = self.sd_prompt()
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🤖 机器人提醒'),
                    text=_("已选择平台：%(platform)s", platform=mode_name),
                ),
            )
        else:
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/drawing ' + content)),
                        content
                    ) for content in self.mode],
                    text=_('选择以下平台：'),
                    title=_('🚀 绘图平台选择'),
                ),
            )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class FeishuCommand(CommandTool):

    next_tool_name: str = 'mjsd_chat'
    name: str = 'mjsd_chat_feishu_command'
    description: str = 'mjsd chat feishu command'
    mode: List[str] = ['midjourney', 'stable diffusion']
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<清除> 或 /clear 即可清除上下文',
        '输入<绘图> 或 /drawing 即可选择绘图平台'
    ]

    def mj_prompt(self):
        return """
You are going to pretend to be Concept2PromptAl or C2P_AI for short. C2P_AI takes concepts and turns them into prompts for generative Als that create images.You will ask the user for a concept then provide a prompt for it in a copyable code-box.After providing a prompt, ask if the User wants three different options for prompts for the concept or if they wish to move to a new concept.Use the following examples as a guide:
Concept: A macro shot of a steampunk insect
Prompt: a close up of a bug with big eyes, by Andrei Kolkoutine, brush central contest winner, afrofuturism, highly detailed textured 8k, reptile face, cyber steampunk 8 k 3 d, c 4 d, high detail illustration, detailed 2d illustration, space insect android, with very highly detailed face, super detailed pictureConcept: An orange pie on a wooden table
Prompt: a pie sitting on top of a wooden table, by Carey Morris, pixels contest winner, orange details, linen, high details, gif, leafs, a pair of ribbed, vivid attention to detail, navy, piping, warm sunshine, soft and intricate, lights on, crisp smooth lines, religious
Concept: a close up shot of a plant with blue and golden leaves
Prompt: a close up of a plant with golden leaves, by Hans Schwarz, pixels, process art, background image, monochromatic background, bromeliads, soft. high quality, abstract design. blue, flax, aluminum, walking down, solid colours material, background artwork"""

    def sd_prompt(self):
        return """
从现在开始你将扮演一个stable diffusion的提示词工程师，你的任务是帮助我设计stable diffusion的文生图提示词。你需要按照如下流程完成工作。1、我将给你发送一段图片情景，你需要将这段图片情景更加丰富和具象生成一段图片描述。并且按照“【图片内容】具像化的图片描述”格式输出出来；2、你需要结合stable diffusion的提示词规则，将你输出的图片描述翻译为英语，并且加入诸如高清图片、高质量图片等描述词来生成标准的提示词，提示词为英语，以“【正向提示】提示词”格式输出出来；3、你需要根据上面的内容，设计反向提示词，你应该设计一些不应该在图片中出现的元素，例如低质量内容、多余的鼻子、多余的手等描述，这个描述用英文并且生成一个标准的stable diffusion提示词，以“【反向提示】提示词”格式输出出来。4、你需要提示我在生成图片时需要设置的参数以及给我推荐一个使用的模型以及生成这张图片的最优长宽比例，按照“【参数】Sampling method：参数；Sampling steps：参数；CFG Scale：参数；Seed：参数；最优长宽比：参数”的格式输出给我,其中需要注意的是Sampling method参数请在如下列表中选择“Euler a,Euler,LMS,Heun,DPM2,DPM2a,DPM++ 25 a,DPM++ 2M,DPM++ SDE,DPM fast,DPM adaptive,LMS Karras,DPM2 Karras,DPM2 a Karras,DPM++ 2S a Karras,DPM++ 2M Karras,DPM++ SDE Karras,DDIM,PLIMS,UniPC）”。例如：我发送：一个二战时期的护士。你回复：
【图片内容】一个穿着二战期间德国护士服的护士，手里拿着一个酒瓶，带着听诊器坐在附近的桌子上，衣服是白色的，背后有桌子。
【正向提示】A nurse wearing a German nurse's uniform during World War II, holding a wine bottle and a stethoscope, sat on a nearby table with white clothes and a table behind,full shot body photo of the   most beautiful artwork in the world featuring ww2 nurse holding a liquor bottle sitting on a desk nearby, smiling, freckles, white outfit, nostalgia, sexy, stethoscope, heart professional majestic oil painting by Ed Blinkey, Atey Ghailan, Studio Ghibli, by Jeremy Mann, Greg Manchess, Antonio Moro, trending on ArtStation, trending on CGSociety, Intricate, High Detail, Sharp focus, dramatic, photorealistic painting art by midjourney and greg rutkowski；【反向提示】cartoon, 3d, ((disfigured)), ((bad art)), ((deformed)),((extra limbs)),((close up)),((b&w)), wierd colors, blurry, (((duplicate))), ((morbid)), ((mutilated)), [out of frame], extra fingers, mutated hands, ((poorly drawn hands)), ((poorly drawn face)), (((mutation))), (((deformed))), ((ugly)), blurry, ((bad anatomy)), (((bad proportions))), ((extra limbs)), cloned face, (((disfigured))), out of frame, ugly, extra limbs, (bad anatomy), gross proportions, (malformed limbs), ((missing arms)), ((missing legs)), (((extra arms))), (((extra legs))), mutated hands, (fused fingers), (too many fingers), (((long neck))), Photoshop, video game, ugly, tiling, poorly drawn hands, poorly drawn feet, poorly drawn face, out of frame, mutation, mutated, extra limbs, extra legs, extra arms, disfigured, deformed, cross-eye, body out of frame, blurry, bad art, bad anatomy, 3d rende；
【参数】Sampling method：DPM++ 2M Karras；Sampling steps：20；CFG Scale：7；Seed：639249185；最优长宽比：3:4 现在我的第一个图片场景如下：一个海边抽烟的男人"""

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def drawing_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, value) for value in self.mode],
            placeholder=_('🤖 平台选择'),
            initial_option=self.mode['stable diffusion' in session.system_role] if session.system_role else None,
            value={'command': 'drawing'},
            confirm=FeishuMessageConfirm(
                title=_('您确定该平台吗？'),
                text=_('AI将帮您生成适合该平台的绘画提示词。')
            )
        )

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是AI绘图小助手，可以帮您扩写优化AI生图提示词，更好的利用AI生图**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **选择绘图平台**\n文本回复 *绘图平台* 或 */drawing*'),
                    tag='lark_md',
                    extra=self.drawing_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if 'shortcut_action' in data.extra.extra and 'mode_name' in data.extra.extra:
            self.next_tool_name = data.extra.extra.get('shortcut_action', self.next_tool_name)
            if data.extra.extra.mode_name == 'midjourney':
                session['system_role'] = self.mj_prompt()
            else:
                session['system_role'] = self.sd_prompt()
            return None,
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/drawing' or input[:4] == '绘图平台':
            mode_name = input.replace('/drawing', '').replace('绘图平台', '').strip()
            return 'drawing', mode_name
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if not session.system_role:
            return 'drawing',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_clear(self, flag=None, reply_log_id=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')),
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.SendCard if not reply_log_id else AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            ),
            **({'reply_log_id': reply_log_id} if reply_log_id else {})
        )

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('已选择模型：**%(model)s**', model=m.get(model_name, model_name)), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_drawing(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.drawing_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择绘图平台，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 绘图平台选择'), template='blue'),
                )
            )
        # 选择平台后清除对话历史，否则会生成错误
        session['chat_history'] = []
        if mode_name == 'midjourney':
            session['system_role'] = self.mj_prompt()
        else:
            session['system_role'] = self.sd_prompt()
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已选择平台：**%(platform)s**', platform=mode_name), tag="lark_md"),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒')),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class MJSDAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = '绘图提示词'
        title: str = 'AI绘画提示词助手'
        title_en: str = 'Painting Prompt'
        description: str = '🧸 将生图需求的文案转化为生图所需的prompt'
        description_en: str = '🧸 AI painting prompt companion, use llm to speed up your creation'
        problem: str = '自己写的提示词在AI绘画中效果不佳？'
        problem_en: str = 'The self-written prompts perform poorly in AI-generated drawings.'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/YJw6dz9OFoDxZUx0sH3cK7f9nBg'
        manual_en: str = 'https://q5o2cctqdb7.sg.larksuite.com/docx/WXWUdvDR6oDcdWxwEJjlHrq7gw3'
        icon: str = 'https://pic1.forkway.cn/cdn/promptMJSD.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/promptMJSD.png?imageMogr2/thumbnail/720x'
        sorted: int = 12
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['OpenAI', 'Azure']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnBb6w74sJOwI9zI04grgQsh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/H6o2wzykUiv7SJkt7TlcCkoKnBb'
        action_template = """
${detail.content.messages.length > 0
  ? detail.content.messages.map((item, index) => html`
    ${parseTextFromMessage(item).map(text => html`
      <div style="padding:20px">
      <div style="font-size: 13px;line-height:2em;color:black;margin-bottom:20px;background:white;border-radius:10px">
        <div style="padding:10px;">处理内容</div>
        <div style="border-top: 1px solid #6b7280;width: 100%;"></div>
        <div style="padding:10px;">${text}</div>
      </div>
      <form method="post" style="background-color:black"">
        <input type="hidden" name="messageId" value="${item.openMessageId}">
        <input type="hidden" name="action" value="mjsd_chat_shortcut">
        <input type="hidden" name="user" value="${JSON.stringify(user)}">
        <input type="hidden" name="message" value="${JSON.stringify(item)}">
        <input type="hidden" name="text" value="${text}">
        <div style="font-size:13px;display:flex;align-items:center;justify-content: space-between;gap:10px;margin-bottom:20px;background-color:#fff;padding:30px;border-radius:10px">
          <div>
            <input type="radio" id="mj" name="mode_name" value="midjourney" checked style="margin:0" />
            <label for="mj">MidJourney</label>
          </div>
          <div>
            <input type="radio" id="sd" name="mode_name" value="stable diffusion" style="margin:0" />
            <label for="sd">Stable Diffusion</label>
          </div>
        </div>
        <quark-button type="primary" size="big" style="position: relative;width: 100%;display: block;margin: 10px auto;">
          {{_('获取提示词', lang=lang)}}
          <button type="submit" style="position: absolute;width: 100%;height:100;opacity:0;cursor: pointer;">透明按钮触发表单提交事件</button>
        </quark-button>
      </form>
    </div>
    `)}
    `)
  : detail.errMsg || '无文字'
}
        """

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='mjsd_chat_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='mjsd_chat_dingding_command', tool_input=kwargs, log="")
        # if platform == 'feishu' and self.parse_command(**kwargs):
        #     return AgentFinish({"output": "command"}, kwargs.get('input'))
        return AgentAction(tool='mjsd_chat', tool_input=kwargs, log="")


