<template>
  <div>
    <ol
      class="flex px-2 text-3xl mb-40px items-center w-full font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base"
    >
      <li
        class="text-blue-600 dark:text-blue-500 flex md:w-full text-xl w-auto items-center sm:after:content-[''] after:w-full after:h-1px after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700"
      >
        <span
          class="flex min-w-fit items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500"
          :class="ifActiveOne ? 'text-blue-600 dark:text-blue-500' : ''"
        >
          <icon-akar-icons-circle-check-fill v-show="ifActiveOne" class="mr-1" />
          {{ $t('message.my.cjjqr') }}
        </span>
      </li>
      <li
        class="flex md:w-full items-center after:content-[''] after:w-full after:h-1px after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700"
      >
        <span
          :class="ifActiveTwo ? 'text-blue-600 dark:text-blue-500' : ''"
          class="min-w-fit flex text-xl items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500"
        >
          <span v-show="!ifActiveTwo" class="mr-2">2</span>
          <icon-akar-icons-circle-check-fill v-show="ifActiveTwo" class="mr-1" />
          {{ $t('message.my.txpzxx') }}
        </span>
      </li>
      <li class="flex items-center text-xl min-w-max" :class="ifActiveThree ? 'text-blue-600 dark:text-blue-500' : ''">
        <span v-show="!ifActiveThree" class="mr-2 text-xl">3</span>
        <icon-akar-icons-circle-check-fill v-show="ifActiveThree" class="mr-1" />
        {{ $t('message.my.hqhddz') }}
      </li>
    </ol>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{ step: number }>();

const ifActiveOne = computed(() => props.step >= 1);
const ifActiveTwo = computed(() => props.step >= 2);
const ifActiveThree = computed(() => props.step > 2);
</script>

<style scoped></style>
