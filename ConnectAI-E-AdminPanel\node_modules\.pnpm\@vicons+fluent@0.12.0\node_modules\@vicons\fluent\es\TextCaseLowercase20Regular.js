import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.5 3.5a.5.5 0 0 1 .5.5v4.891C12.53 8.337 13.232 8 14 8c1.657 0 3 1.567 3 3.5S15.657 15 14 15c-.768 0-1.47-.337-2-.891v.391a.5.5 0 0 1-1 0V4a.5.5 0 0 1 .5-.5zM14 14c.966 0 2-.97 2-2.5S14.966 9 14 9s-2 .97-2 2.5s1.034 2.5 2 2.5zM5.894 8.687c-.486.019-.913.132-1.17.26a.5.5 0 1 1-.447-.894c.41-.205.982-.342 1.579-.365c.6-.023 1.272.067 1.868.365C9.004 8.693 9 9.961 9 10.463V14.5a.5.5 0 0 1-1 0v-.412c-.913.666-2.01 1.094-3.129.796c-1.884-.504-2.534-3.043-.649-4.3c.715-.476 1.584-.608 2.375-.568c.49.024.971.116 1.4.25c-.015-.48-.112-1.014-.72-1.319c-.405-.202-.9-.278-1.383-.26zM8 11.325a4.719 4.719 0 0 0-1.454-.31c-.66-.034-1.292.084-1.77.401c-1.115.744-.763 2.204.353 2.502c.902.24 1.938-.248 2.871-1.13v-1.463z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseLowercase20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
