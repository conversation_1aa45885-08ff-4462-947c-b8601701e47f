import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.76 2.453A.5.5 0 0 1 5.26 2H15.5a.5.5 0 0 1 .498.547l-.759 8a.5.5 0 0 1-.498.453h-.665l.919 6.43a.5.5 0 0 1-.99.14L13.638 15h-2.276l-.367 2.57a.5.5 0 0 1-.99-.14l.347-2.43h-2.99l-.367 2.57a.5.5 0 0 1-.99-.14L6.923 11H4.5a.5.5 0 0 1-.498-.547l.759-8zM7.506 14h2.99l.429-3h-2.99l-.429 3zm4 0h1.99l-.429-3h-1.132l-.429 3zm2.782-4l.663-7H5.713l-.663 7h9.237z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'SurfaceHub20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
