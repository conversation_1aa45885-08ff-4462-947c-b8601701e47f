#!/usr/bin/env python
# coding=utf-8
import asyncio
import json
import base64
import qrcode
import logging
from time import time
from io import BytesIO
from core.base_handler import (
    BaseHandler,
    ExportHandler,
    arguments,
    authenticated,
    WebSession,
)
from core.utils import ObjIDStr, DateStr
from core.redisdb import delete
from core.base_model import RedisModel
from .model import (
    AccountModel,
    PromptModel,
    SensitiveModel,
    ChatLogModel,
    TenantPrivilegeModel,
    ProductModel,
    TenantSeatModel,
)


# 1. 手机号/邮箱+密码-->登录        默认
# 2. 手机号+验证码（注册或者登录）  快捷登录
# 3. 邮箱+验证码+密码-->注册        邮箱注册
class AccountHandler(BaseHandler):


    @arguments
    async def post(
        self,
        email: str = '',
        passwd: str = '',
        phone: str = '',
        code: str = '',
        model: AccountModel = None,
    ):
        is_new = False
        if email and code and passwd:
            user_id, is_new = await model.login_by_code(email, '', code, passwd=passwd, register=True)
        elif (email or phone) and passwd:
            user_id = await model.login(email, phone, passwd)
        elif phone and code:
            user_id, is_new = await model.login_by_code('', phone, code, passwd='', register=True)
        else:
            raise PermissionDenied('请输入正确的帐号')
        self.current_user = user_id
        self.gen_session_id(user_id=user_id)

        if is_new:
            self.finish({
                'code': 0,
                'msg': '注册成功',
                'type': 'register',
            })
        else:
            self.finish({
                'code': 0,
                'msg': '登录成功',
                'type': 'login',
            })

    @authenticated
    @arguments
    async def delete(self, model: AccountModel = None):
        # 直接清除cookie即可,没有cookie拿不到session,session自己过期
        self.clear_all_cookies(path='/')
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    # 修改租户信息，当前只支持修改展示名
    @authenticated
    @arguments
    async def put(self, display_name: str = '', model: AccountModel = None):

        # 检查 display_name 的长度
        has_cn = False
        for _char in display_name:
            if '\u4e00' <= _char <= '\u9fa5':
                has_cn = True
                break

        if has_cn and len(display_name) < 2:
            raise ParametersError('展示名不能小于2个字')
        elif not has_cn and len(display_name) < 1:
            raise ParametersError('Display name cannot be blank')

        await model.update(tenant_id=self.session.tenant_id, display_name=display_name)
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def get(self, model: AccountModel = None):
        tenant_info = await model.get_tenant_info(self.session.tenant_id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': {
                "display_name": tenant_info.get('display_name', ''),
                "email": tenant_info.get('email', ''),
            },
        })

class AccountCodeHandler(BaseHandler):
    @arguments
    async def post(self, email: str = '', phone: str = '', model: AccountModel = None):
        await model.send_code(email, phone, key='send_login_code:{}:{}'.format(email, phone))
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class PromptCategoryHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, page: int = 1, size: int = 10, model: PromptModel = None):
        logging.info("debu session %r", self.session)
        categories, total = await model.get_prompt_category(
            self.session.tenant_id, self.session.id,
            page, size,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': categories,
            'total': total,
        })


class PromptHandler(ExportHandler):

    @authenticated
    @arguments
    async def get(
        self,
        prompt_id: ObjIDStr = '',  # 这两个是占位的参数，因为使用了两个不一样的url
        category_id: ObjIDStr = '', keyword: str = '',
        page: int = 1, size: int = 20,
        model: PromptModel = None,
    ):
        prompts, total = await model.get_prompt_list(
            self.session.tenant_id, self.session.id,
            category_id, keyword,
            # 导出的时候，直接忽略size
            1 if self.export else page,
            99999 if self.export else size,
            lang=self._get_locale(),
        )
        if self.export:
            columns = [
                ('title', '提示词标题'),
                ('description', '提示词描述'),
                ('content', '提示词内容'),
                ('example', '提示词示例'),
            ]
            return self.export_excel(prompts, columns)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': prompts,
            'total': total,
        })

    @authenticated
    @arguments
    async def post(
        self,
        prompt_id: ObjIDStr = '',  # 占位
        category_id: ObjIDStr = '',
        title: str = '',
        description: str = '',
        content: str = '',
        example: str = '',
        model: PromptModel = None,
    ):
        await model.create(
            self.session.tenant_id, self.session.id,
            category_id,
            title, description, content, example,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def put(
        self, prompt_id: ObjIDStr = '',
        category_id: ObjIDStr = '',
        title: str = '',
        description: str = '',
        content: str = '',
        example: str = '',
        model: PromptModel = None,
    ):
        await model.edit(
            self.session.tenant_id, self.session.id, prompt_id,
            category_id, title, description, content, example,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def delete(self, prompt_id: ObjIDStr = '', model: PromptModel = None):
        await model.remove(self.session.tenant_id, prompt_id)
        self.finish({
            'code': 0,
            'msg': 'success',
        })

class PromptImportHandler(BaseHandler):
    @authenticated
    @arguments
    async def post(self, model: PromptModel = None):
        file_obj = self.request.files['file'][0]
        await model.upload(self.session.tenant_id, self.session.id, file_obj)
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class SensitiveHandler(ExportHandler):

    @authenticated
    @arguments
    async def get(
        self,
        sensitive_id: ObjIDStr = '', action: str = '',  # 这两个是占位的参数，因为使用了两个不一样的url
        keyword: str = '',
        page: int = 1, size: int = 20,
        model: SensitiveModel = None,
    ):
        sensitive, total = await model.get_sensitive_list(
            self.session.tenant_id, self.session.id,
            keyword,
            # 导出的时候，直接忽略size
            1 if self.export else page,
            99999 if self.export else size,
        )
        if self.export:
            columns = [
                ('category', '敏感词分类'),
                ('name', '敏感词名称'),
            ]
            return self.export_excel(sensitive, columns)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': sensitive,
            'total': total,
        })

    @authenticated
    @arguments
    async def post(
        self,
        sensitive_id: ObjIDStr = '',  # 占位
        category: str = '', name: str = list(),
        model: SensitiveModel = None,
    ):
        await model.create(self.session.tenant_id, self.session.id, category, name)
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def put(
        self, sensitive_id: ObjIDStr = '', action: str = '',
        category: str = '', name: str = list(),
        model: SensitiveModel = None,
    ):
        if not action:
            await model.edit(self.session.tenant_id, self.session.id, sensitive_id, category, name)
        else:
            if action not in ['start', 'stop']:
                raise ParametersError()
            await model.action(self.session.tenant_id, self.session.id, sensitive_id, action)
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def delete(self, sensitive_id: ObjIDStr = '', action: str = '', model: SensitiveModel = None):
        await model.action(self.session.tenant_id, self.session.id, sensitive_id, action='remove')
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ChatLogHandler(ExportHandler):

    """
    1. 内容过滤
    2. 应用名称过滤
    3. 时间
    """
    @authenticated
    @arguments
    async def get(
        self,
        keyword: str = '',  # 内容过滤
        app: str = '',  # 应用名称过滤
        user: str = '',  # 姓名过滤
        start_date: DateStr = '',
        end_date: DateStr = '',
        page: int = 1, size: int = 20,
        model: ChatLogModel = None,
    ):
        logs, total = await model.get_chat_log_list(
            self.session.tenant_id, self.session.id,
            keyword, app, user,
            start_date, end_date,
            # 导出的时候，直接忽略size
            1 if self.export else page,
            99999 if self.export else size,
            lang=self._get_locale(),
        )
        if self.export:
            # TODO
            for index, item in enumerate(logs):
                result = item.get('result', {}) or {}
                if result == True:
                    # 触发敏感词的时候，这个字段是True
                    result = {}
                image = result.get('additional_kwargs', {}).get('imageURL')
                # 兼容sdimage imageurl
                if not image:
                    resp = result.get('additional_kwargs', {}).get('differentialImageRspList')
                    if resp:
                        image = resp[0].get('image_url')
                if image:
                    # 替换图片url 作兼容
                    # 这里直接使用原始的图算了...
                    logs[index]['result_str'] = image
                else:
                    result_content = result.get('content', '')
                    logs[index]['result_str'] = result_content
                logs[index]['is_sensitive'] = '是' if item['sensitive'] else '否'
                logs[index]['sensitive_name'] = item['sensitive']['name'] if item['sensitive'] else ''
            columns = [
                ('app_name', '应用名称'),
                ('username', '用户名'),
                ('content', '问题'),
                ('result_str', '回答'),  # 这个要单独处理
                ('is_sensitive', '风险提问'),  # 这个要单独处理
                ('sensitive_name', '风险词'),  # 这个要单独处理
                ('created', '创建时间'),
            ]

            return self.export_excel(logs, columns)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': logs,
            'total': total,
        })


class TenantHandlerHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, model: TenantPrivilegeModel = None):
        handler, product, expired = await model.get_handler(self.session.tenant_id, self.session.id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': handler,
            'product': product,
            'expired': expired,
        })


class ProductHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, model: ProductModel = None):
        product = await model.get_product_list(self.session.tenant_id, self.session.id, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': product,
        })


class OrderHandler(BaseHandler):

    @authenticated
    @arguments
    async def post(
        self,
        product_id: ObjIDStr = '',  # 购买套餐ID
        model: ProductModel = None,
    ):
        tenant_product_id, amount, name = await model.order(self.session.tenant_id, self.session.id, product_id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': {
                'amount': amount / 100,
                'tenant_product_id': tenant_product_id
            }
        })


class WXPayHandler(BaseHandler):

    def call_parent(self, data):
        callback = self.get_argument('callback', 'callback')
        self.write("""<script>parent.{}({});</script>\n\n""".format(callback, json.dumps(data)))
        self.flush()

    @arguments
    async def get(
        self,
        tenant_product_id: ObjIDStr = '',  # 下单后的id
        model: ProductModel = None,
    ):
        # 下单与支付直接放到一个请求里面
        # 支付使用chunked格式数据，返回二维码给前端
        # 扫码后接收结果通知前端
        self.set_header('Content-Type', 'text/html')
        try:
            pay_url = await model.get_pay_url(tenant_product_id)
            q = qrcode.make(pay_url, border=0)
            o = BytesIO()
            q.save(o)
            # TODO 下单，返回二维码
            self.write('''
<style>*{padding: 0;margin:0}img{width: 100%;height:100%;}iframe{display: none;}</style>
<img src="data:image/png; base64,''' + base64.b64encode(o.getvalue()).decode() + '"/>\n\n')
            self.flush()
            timeout = 50
            channel = 'wx:pay:{}'.format(tenant_product_id)
            with RedisModel() as r:
                res = await r.alisten(channel, timeout)
                logging.info("waiting info %r", res)
            if res:
                self.call_parent({ 'result': res })
            else:
                self.call_parent({ 'error': 'timeout' })
        except Exception as e:
            logging.exception(e)
            self.call_parent({ 'error': str(e) })


class WXPayCallbackHandler(BaseHandler):

    @arguments
    async def post(self, model: ProductModel = None):
        logging.info("wxpay callback request body %r", self.request.body)
        result = model.wxpay.callback(headers=self.request.headers, body=self.request.body)
        if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
            resource = result.get('resource', {})
            tenant_product_id = resource.get('out_trade_no')
            amount = resource.get('amount').get('total')
            channel = 'wx:pay:{}'.format(tenant_product_id)
            await model.pay_success(tenant_product_id)
            with RedisModel() as client:
                r = client.send(channel, tenant_product_id, amount)
            logging.info("wxpay callback %r", result)
            return self.finish({'code': 'SUCCESS', 'message': '成功'})
        return self.finish({'code': 'FAILED', 'message': '失败'})


class TenantSeatHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        keyword: str = '',  # 按名字搜索
        page: int = 1, size: int = 20,
        model: TenantSeatModel = None,
    ):
        seats, total = await model.get_seats_list(self.session.tenant_id, keyword, page, size)
        max_seats, current_seats = await model.get_current_seats(self.session.tenant_id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': seats,
            'total': total,
            'max_seats': max_seats,
            'current_seats': current_seats,
        })

    @authenticated
    @arguments
    async def post(
        self,
        name: str = '',
        telephone: str = '',
        department: str = '',
        model: TenantSeatModel = None,
    ):
        seat = await model.add_seat(
            self.session.tenant_id, self.session.id,
            name, telephone, department,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': seat,
        })

    @authenticated
    @arguments
    async def put(
        self,
        id: ObjIDStr = '',
        name: str = '',
        telephone: str = '',
        department: str = '',
        action: str = '',
        model: TenantSeatModel = None,
    ):
        if action and action in ['start', 'stop']:
            await model.seat_action(
                self.session.tenant_id, self.session.id,
                id,
                action,
            )
        else:
            await model.update_seat(
                self.session.tenant_id, self.session.id,
                id,
                name, telephone, department,
            )
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def delete(
        self,
        seat_id: ObjIDStr = '',
        model: TenantSeatModel = None,
    ):
        await model.remove_seat(
            self.session.tenant_id, self.session.id,
            seat_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class TenantSeatImportHandler(BaseHandler):

    @authenticated
    @arguments
    async def post(
        self,
        seats: dict = list(),  # 这里是列表，每一项都包含<name, telephone, department>
        model: TenantSeatModel = None,
    ):
        await model.import_seats(
            self.session.tenant_id, self.session.id,
            seats,
        )

        self.finish({
            'code': 0,
            'msg': 'success',
        })


class TenantSeatParseHandler(BaseHandler):

    @authenticated
    @arguments
    async def post(
        self,
        seats: dict = list(),  # 这里是列表，每一项都包含<name, telephone, department>
        model: TenantSeatModel = None,
    ):
        file_obj = self.request.files['file'][0]
        seats, total = await model.parse(self.session.tenant_id, self.session.id, file_obj)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': seats,
            'total': total,
        })


class TenantSeatDepartmentHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        model: TenantSeatModel = None,
    ):
        department = await model.get_department(self.session.tenant_id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': department,
        })


class TenantSeatConfigHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, model: TenantSeatModel = None):
        auto_add_seat = await model.get_config(self.session.tenant_id, 'auto_add_seat')
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': auto_add_seat,
        })

    @authenticated
    @arguments
    async def post(
        self,
        auto_add_seat: int = 1,
        model: TenantSeatModel = None,
    ):
        await model.change_config(self.session.tenant_id, 'auto_add_seat', auto_add_seat)
        self.finish({
            'code': 0,
            'msg': 'success',
        })


