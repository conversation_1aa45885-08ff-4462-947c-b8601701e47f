<template>
  <div
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold dark:text-white">{{ $t('message.messenger.helpdoc') }}</h3>
    </div>

    <div class="mb-4 font-normal text-gray-500 dark:text-gray-400">
      {{ $t('message.messenger.helpinfo') }}
    </div>

    <n-form :model="model" ref="formRef">
      <n-dynamic-input
        v-model:value="model.links"
        item-style="margin-bottom: 0;"
        :on-create="onCreate"
        :max="5"
        #="{ index, value }"
      >
        <div class="flex w-full">
          <!--
          通常，path 的变化会导致 form-item 验证内容或规则的改变，所以 naive-ui 会清理掉
          表项已有的验证信息。但是这个例子是个特殊情况，我们明确的知道，path 的改变不会导致
          form-item 验证内容和规则的变化，所以就 ignore-path-change
        -->
          <n-form-item
            class="flex-1"
            ignore-path-change
            :show-label="false"
            :path="`links[${index}].title`"
            :rule="{
              trigger: 'input',
              required: true,
              message: t('message.messenger.qsrbt')
            }"
          >
            <n-input v-model:value="model.links[index].title" :placeholder="t('message.messenger.qsrbt')" @keydown.enter.prevent />
            <!--
            由于在 input 元素里按回车会导致 form 里面的 button 被点击，所以阻止了默认行为
          -->
          </n-form-item>
          <div style="height: 34px; line-height: 34px; margin: 0 8px"></div>
          <n-form-item
            class="flex-1"
            ignore-path-change
            :show-label="false"
            :path="`links[${index}].href`"
            :rule="{
              trigger: 'input',
              required: true,
              message: t('message.messenger.qsrlj')
            }"
          >
            <n-input v-model:value="model.links[index].href" :placeholder="t('message.messenger.qsrlj')" @keydown.enter.prevent />
          </n-form-item>
        </div>
      </n-dynamic-input>
    </n-form>
    <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
    <button
      v-else
      type="button"
      class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      :class="{ 'mt-6': model?.links?.length === 0 }"
      @click="handleSave"
    >
      <icon-akar-icons-save class="mr-2" />
      {{ $t('message.bot.save') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { updateMessengerWebConfig } from '@/service/api/messenger';
import { t } from '@/locales';
import { ref } from 'vue';

const message = useMessage();

const route = useRoute();

const emit = defineEmits(['change', 'update:data']);

const formRef = ref();

const props = defineProps<{
  data: ApiMessenger.Resp.MessengerWebConfig;
  loading: boolean;
}>();

const data = useVModel(props, 'data', emit);
const model = ref(data);

const onCreate = () => {
  return {
    title: '',
    href: ''
  };
};

async function handleSave() {
  data.value = model.value;
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      await updateMessengerWebConfig({ id: route.query.id as string, data: data.value });
      message.success(t('message.msg.bccg'));
    }
  });
}
</script>
