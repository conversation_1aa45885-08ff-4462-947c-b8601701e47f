<template>
  <div
    class="relative h-full transition-width duration-300 ease-in-out"
    :style="{ width: app.mixSiderFixed ? theme.sider.mixChildMenuWidth + 'px' : '0px' }"
  >
    <dark-mode-container
      class="drawer-shadow absolute-lt flex-col-stretch h-full nowrap-hidden"
      :inverted="theme.sider.inverted"
      :style="{ width: showDrawer ? theme.sider.mixChildMenuWidth + 'px' : '0px' }"
    >
      <header class="header-height flex-y-center justify-between" :style="{ height: theme.header.height + 'px' }">
        <h2 class="text-gray-800 dark:text-white pl-22px text-16px font-400">{{ $t('message.system.htgl') }}</h2>
        <div class="px-8px text-16px text-gray-600 cursor-pointer" @click="app.toggleMixSiderFixed">
          <icon-mdi-pin-off v-if="app.mixSiderFixed" />
          <icon-mdi-pin v-else />
        </div>
      </header>
      <n-scrollbar class="flex-1-hidden">
        <n-menu
          :value="activeKey"
          :options="menus"
          :expanded-keys="expandedKeys"
          :indent="18"
          :inverted="!theme.darkMode && theme.sider.inverted"
          :render-extra="renderExtra"
          @update:value="handleUpdateMenu"
          @update:expanded-keys="handleUpdateExpandedKeys"
        />
      </n-scrollbar>
    </dark-mode-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, h } from 'vue';
import { useRoute } from 'vue-router';
import type { MenuOption } from 'naive-ui';
import { UPGRADE_LABEL } from '@/constants';
import { useAppStore, useThemeStore } from '@/store';
import { useAppInfo, useRouterPush } from '@/composables';
import { useTenantPrivilege } from '@/hooks';
import { getActiveKeyPathsOfMenus } from '@/utils';

const { deny } = useTenantPrivilege();
defineOptions({ name: 'MixMenuDrawer' });

interface Props {
  /** 菜单抽屉可见性 */
  visible: boolean;
  /** 子菜单数据 */
  menus: App.GlobalMenuOption[];
}

const props = defineProps<Props>();

// 当前需要控制的总的菜单数量，实际有没有权限还是看后端返回的数据
const handlers = new Set([
  'page.log.chat',
  'page.log.word',
  'page.knowledge.import',
  'page.knowledge.my',
  'page.knowledge.market'
]);

const renderExtra = (option: any) => {
  const pname = `page${option.routePath.replaceAll('/', '.')}`;
  if (handlers.has(pname) && deny(pname)) {
    return h(
      'span',
      { class: 'bg-yellow-100 text-yellow-800 text-xs font-semibold rounded dark:bg-yellow-900 dark:text-yellow-300' },
      UPGRADE_LABEL
    );
  }
  return null;
};

const route = useRoute();
const app = useAppStore();
const theme = useThemeStore();
const { routerPush } = useRouterPush();
const { title } = useAppInfo();

const showDrawer = computed(() => (props.visible && props.menus.length) || app.mixSiderFixed);

const activeKey = computed(() => (route.meta?.activeMenu ? route.meta.activeMenu : route.name) as string);
const expandedKeys = ref<string[]>([]);

function handleUpdateMenu(_key: string, item: MenuOption) {
  const menuItem = item as App.GlobalMenuOption;
  routerPush(menuItem.routePath);
}

function handleUpdateExpandedKeys(keys: string[]) {
  expandedKeys.value = keys;
}

watch(
  () => route.name,
  () => {
    expandedKeys.value = getActiveKeyPathsOfMenus(activeKey.value, props.menus);
  },
  { immediate: true }
);
</script>

<style scoped>
.drawer-shadow {
  box-shadow: 2px 0 8px 0 rgb(29 35 41 / 5%);
}
</style>
