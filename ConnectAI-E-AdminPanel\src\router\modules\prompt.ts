const prompt: AuthRoute.Route = {
  name: 'prompt',
  path: '/prompt',
  component: 'basic',
  children: [
    // {
    //   name: 'prompt_market',
    //   path: '/prompt/market',
    //   component: 'self',
    //   meta: {
    //     title: '提示词市场',
    //     requiresAuth: true,
    //     icon: 'akar-icons:glasses', i18nTitle: 'message.routes.prompt.tscsc'
    //   }
    // },
    {
      name: 'prompt_importexport',
      path: '/prompt/importexport',
      component: 'self',
      meta: {
        title: '导入提示词',
        requiresAuth: true,
        icon: 'akar-icons:door', i18nTitle: 'message.routes.prompt.pldr'
      }
    },
    {
      name: 'prompt_my',
      path: '/prompt/my',
      component: 'self',
      meta: {
        title: '我的提示词',
        requiresAuth: true,
        icon: 'akar-icons:data', i18nTitle: 'message.routes.prompt.wdtsc'
      }
    }
  ],
  meta: {
    title: '提示词管理',
    icon: 'akar-icons:rock-on',
    order: 3, i18nTitle: 'message.routes.prompt._value'
  }
};

export default prompt;
