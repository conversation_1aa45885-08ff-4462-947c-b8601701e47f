<template>
  <div class="w-full">
    <NoPermission v-if="deny('page.knowledge.my')" />
    <n-card v-else class="h-full shadow-sm rounded-16px pt-2" content-style="overflow:hidden">
      <template #header>
        <div class="w-full flex justify-between items-center">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
              >Search</label
            >
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="t('请输入知识库应用名称')"
              />
              <button
                type="submit"
                class="text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ t('message.knowledge.search') }}
              </button>
            </div>
          </form>
          <div class="flex justify-end gap-4 items-center min-w-280px">
            <a :href="docUrl" target="_blank">
            <button
              type="button"
              class="text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
            >
              <icon-akar-icons-chat-dots class="mr-2" />
              {{ $t('message.bot.ckpz') }}
            </button>
            </a>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAddPrompt"
            >
              <icon-akar-icons-circle-plus class="mr-2" />
              {{ t('新增知识库应用') }}
            </button>
          </div>
        </div>
      </template>
      <div
        class="flex content-start flex-grow-0 flex-wrap justify-start items-start flex-row mx-auto w-full h-full overflow-auto"
      >
        <div v-for="(item, index) in data" :key="index" class="ml-[30px] mt-[30px]">
          <DatasetAppCard :data="item" @handle-edit="handleEdit" @handle-delete="handleDelete"></DatasetAppCard>
        </div>
      </div>
    </n-card>
    <n-modal v-model:show="showModal" @after-leave="handleAfterLeave">
      <n-card
        style="width: 600px"
        :title="formValue.id ? t('修改知识库应用') : t('新增知识库应用')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div v-if="step === 0">
          <n-alert type="info" class="mb-4">
            <template #header>
              <div class="flex justify-between text-sm">
                <div>请注意填写知识库应用的名称，方便后续区分</div>
              </div>
            </template>
          </n-alert>
          <n-form ref="formRef" :label-width="80" :model="formValue" :rules="rules">
            <n-form-item :label="t('message.knowledge.zskmc')" path="name">
              <n-input v-model:value="formValue.name" :placeholder="t('message.knowledge.qsrzskmc')" />
            </n-form-item>
            <n-form-item :label="t('message.knowledge.zskinfo')" path="description">
              <n-input v-model:value="formValue.description" :placeholder="t('message.knowledge.qsrzskinfo')" />
            </n-form-item>
          </n-form>
        </div>
        <div v-else-if="step === 1">
          <ResourcesForm ref="resourcesformRef" v-model:data="selectedResources" :resources="resources" />
        </div>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.knowledge.qx') }}
            </button>
            <button
              v-if="step === 0"
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleNxet"
            >
              {{ t('下一步') }}
            </button>
            <button
              v-else
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              {{ t('message.knowledge.qd') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { isEmpty } from 'lodash-es';
import { onMounted, ref, provide } from 'vue';
import { useIconRender, useRouterPush } from '@/composables';
import { useMessage, useDialog } from 'naive-ui';
import type { FormInst, FormRules } from 'naive-ui';
import { useTenantPrivilege } from '@/hooks';
import { fetchDatasetAppList, createDatasetApp, fetchDatasetAppInfo } from '@/service/api/knowledge';
import { getAppResources } from '@/service/api/app';
import { deleteApp, updateAppSetting } from '@/service/api/messenger';

import { t } from '@/locales';
import ResourcesForm from '@/views/bot/info/component/resourcesForm.vue';
import DatasetAppCard from './components/datasetAppCard.vue';
import { routeName } from '@/router';

const { iconRender } = useIconRender();
const { routerPush } = useRouterPush();

const { deny } = useTenantPrivilege();
const data = ref<ApiDataset.DatasetApp[]>([]);

const keyword = ref('');

const message = useMessage();
const dialog = useDialog();
const formValue = ref<{
  name: string;
  description: string;
  id?: string;
}>({
  name: '',
  description: ''
});
const rules: FormRules = {
  name: {
    required: true,
    message: t('message.knowledge.qsrzskmc'),
    trigger: ['input']
  }
};
const formRef = ref<FormInst | null>(null);
const resourcesformRef = ref<FormInst | null>(null);

const showModal = ref(false);

const showTipsModal = ref(false);

provide('close', () => (showTipsModal.value = false));

const step = ref(0);

const resources = ref<ApiApp.AppResources[]>([]);

const selectedResources = ref({});

const application_id = ref();

const docUrl = ref('');

async function getDatasetAppList(keyword?: string) {
  try {
    const res = await fetchDatasetAppList({
      page: 1,
      size: 99999,
      keyword
    });
    data.value = res.data?.data || [];
  } catch (err) {
    console.error(err);
  }
}

function handleSearch(e: Event) {
  e.preventDefault();

  getDatasetAppList(keyword.value);
}

function handleAddPrompt() {
  showModal.value = true;
}

function handleClose() {
  showModal.value = false;
  formValue.value = {
    id: '',
    name: '',
    description: ''
  };
}

async function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  await resourcesformRef.value?.validate();
  if (formValue.value.id) {
    const { id, ...data } = formValue.value;
  } else {
    const res = await createDatasetApp({
      ...formValue.value,
      resource_ids: selectedResources.value,
      application_id: application_id.value
    });
    const instance_id = res?.data.data;
    await updateAppSetting({
      id: instance_id,
      data: {
        resource_ids: selectedResources.value
      }
    });
    routerPush({ name: routeName('knowledge_appInfo'), query: { id: instance_id } });
  }
  message.success(t('message.msg.bccg'));
  handleClose();
  getDatasetAppList();
}

function handleEdit({ id, name, description }: ApiDataset.DatasetApp) {
  formValue.value = {
    id,
    name,
    description
  };
  showModal.value = true;
}

function handleDelete({ id }: ApiDataset.DatasetApp) {
  dialog.warning({
    title: t('message.knowledge.warn'),
    content: t('message.knowledge.qdsc'),
    positiveText: t('message.knowledge.qd'),
    negativeText: t('message.knowledge.qx'),
    positiveButtonProps: {
      class: 'bg-[var(--n-color)]' // 解决button默认样式被flowbite覆盖
    },
    onPositiveClick: async () => {
      await deleteApp({ id });
      getDatasetAppList();
      message.success(t('message.msg.sccg'));
    }
  });
}

async function getDatasetAppInfo() {
  try {
    const res = await fetchDatasetAppInfo();
    docUrl.value = res?.data?.data?.deploy;
    application_id.value = res?.data?.data.id;
    const {
      data: { data: resourcesData }
    } = await getAppResources({ id: res?.data?.data.id });
    resources.value = resourcesData;
  } catch (error) {
    console.error(error);
  }
}

async function handleNxet() {
  await formRef.value?.validate();
  step.value += 1;
}

function handleAfterLeave() {
  step.value = 0;
  formValue.value = {
    id: '',
    name: '',
    description: ''
  };
  selectedResources.value = {};
}

onMounted(() => {
  getDatasetAppInfo();
  getDatasetAppList();
});
</script>
