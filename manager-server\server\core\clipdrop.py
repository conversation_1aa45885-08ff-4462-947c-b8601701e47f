import json
import logging
from typing import Any, List, Optional

import httpx
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import AIMessage, BaseMessage, ChatGeneration, ChatResult, HumanMessage

logger = logging.getLogger(__name__)


class ClipdropClient(object):
    def __init__(self, api_base, api_key=''):
        self.api_key = api_key
        self.api_base = api_base

    def upscale(self, content, timeout, rate=2):
        # https://clipdrop-api.co/image-upscaling/v1/upscale
        url = f"{self.api_base}/image-upscaling/v1/upscale"
        from PIL import Image
        from io import BytesIO
        with Image.open(BytesIO(content)) as img:
            # 获取宽高
            width, height = img.size
        width = min(width * rate, 4096)
        height = min(height * rate, 4096)
        params = {'target_width': width, 'target_height': height}
        r = httpx.post(
            url,
            files={'image_file': ('test.jpg', content, 'image/jpeg'), },
            headers={'x-api-key': self.api_key, 'api-key': self.api_key},
            data=params,
            timeout=timeout
        )

        if r.status_code == httpx.codes.OK:
            return {'image_bin': r.content, **params}
        else:
            r.raise_for_status()

    def removetext(self, content, timeout, rate=2):
        # https://clipdrop-api.co/remove-text/v1
        url = f"{self.api_base}/remove-text/v1"

        r = httpx.post(
            url,
            files={'image_file': ('test.jpg', content, 'image/jpeg'), },
            headers={'x-api-key': self.api_key, 'api-key': self.api_key},
            timeout=timeout
        )

        if r.status_code == httpx.codes.OK:
            return {'image_bin': r.content}
        else:
            r.raise_for_status()

    def create(self, content, timeout=600, **kwargs) -> Any:
        model_name = kwargs.get('model_name')
        if model_name.startswith("image-upscaling"):
            res = self.upscale(content=content, timeout=timeout)
        else:
            res = self.removetext(content=content, timeout=timeout)
        return res


class ClipdropChat(SimpleChatModel):
    """
    UpScaler
    """

    client: Any
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    streaming: bool = False
    model_name: str = "image-upscaling-v1"

    def _llm_type(self) -> str:
        return "upscaler_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:

        params = {
            'api_key': self.api_key,
            'api_base': self.api_base,
            'model_name': self.model_name,
        }

        # 只获取一条 message
        message = messages.pop()
        # 这意味着图片的二进制数据要通过 message 传输
        params.update(**message.additional_kwargs)

        self.client = ClipdropClient(
            api_base=self.api_base,
            api_key=self.api_key
        )
        response = self.client.create(**params)

        if not response.get('image_bin'):
            raise Exception("Error: {}".format(response['result']))

        message = AIMessage(content="", additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])



if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    async def main():
        from langchain.schema import HumanMessage

        api_key = ''
        api_base = 'https://clipdrop-api.co'

        chat = ClipdropChat(
            api_base=api_base,
            api_key=api_key,
            model_name="remove-text-v1",
        )

        params = {
            'content': open("C:/Users/<USER>/Desktop/test.jpg", 'rb'),
        }

        messages = [HumanMessage(content="", additional_kwargs=params)]

        result = chat(messages)

        # print(result)
        # print(result.additional_kwargs)

        from PIL import Image
        import io
        result_image_data = result.additional_kwargs['image_bin']
        image = Image.open(io.BytesIO(result_image_data))

        image.show('processed_image.jpg')

    asyncio.run(main())
