import base64
import logging
import random
from time import time, sleep
from typing import List, Optional, Any

import httpx
import json

from langchain.callbacks.manager import CallbackManagerFor<PERSON>MRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import BaseMessage, ChatResult, AIMessage, ChatGeneration
from tornado.options import options


def imageraw2base64(image_raw):
    base64_img = base64.b64encode(image_raw).decode()
    return base64_img

def imageurls2base64(image_urls):
    res = []
    for image_url in image_urls:
        img_raw = httpx.get(image_url).content
        base64_img = imageraw2base64(img_raw)
        res.append(base64_img)
    return res


class MJApiClient(object):
    def __init__(self, api_base, api_key='', api_secret='', api_token='', user_id=''):
        self.api_base = api_base
        self.api_key = api_key
        self.api_secret = api_secret
        self.api_token = api_token
        self.user_id = user_id

    @property
    def mj_headers(self):
        return {
            'api-key': self.api_key,
            'user-id': self.user_id,
            'MJ-API-TOKEN': self.api_token or self.api_key,
            'Content-Type': 'application/json'
        }

    @property
    def headers(self):
        return {
            'api-key': self.api_key,
            'OPEN-API-SECRET': self.api_secret,
            'Content-Type': 'application/json'
        }

    def build_query(self, action='imagine', **kwargs):
        uri, params = '', {}
        action = action.lower()
        if action == 'imagine':
            # prompt image_raw image_urls
            # {'code': 0, 'description': '提交成功', 'result': '1691691161867128832', 'properties': {}}
            if 'prompt' not in kwargs:
                return None
            uri = '/user/submit/imagine'
            params = {
                'prompt': kwargs['prompt'],
                'base64': '',
                'baseUrls': [],
            }
            if 'image_raw' in kwargs:
                base64 = 'data:image/png;base64,{}'.format(imageraw2base64(kwargs['image_raw']))
                params.update({'base64': base64})
            if 'image_urls' in kwargs:
                params.update({'base64Urls': imageurls2base64(kwargs['image_urls'])})
            logging.info('build query: %r %r', self.api_base + uri, kwargs['prompt'])
        elif action == 'blend':
            # image_raws dimensions
            # {'code': 0, 'description': '成功', 'result': {'action': 'BLEND', 'id': '1691797100519673856', 'prompt': None, 'promptEn': None, 'description': '/blend 1691797100519673856 2', 'state': None, 'submitTime': 1692190835491, 'startTime': 1692190835500, 'finishTime': 1692190863750, 'imageUrl': 'https://mj-cdn.7zz.co/attachments/1126783722767798324/1141355852041822298/Saithinis_None_4dc5c5f3-72f0-4e4c-b2c5-7f251ce1d2b3.png', 'status': 'SUCCESS', 'progress': '100%', 'failReason': None, 'properties': {'flags': 0, 'messageId': '1141355853388202005', 'messageHash': '4dc5c5f3-72f0-4e4c-b2c5-7f251ce1d2b3', 'nonce': '6583769150017971961', 'finalPrompt': '<https://s.mj.run/Wv-QxWV6kgU> <https://s.mj.run/9ZFyaymfMug> --ar 1:1', 'progressMessageId': '1141355776326254754'}}}
            if 'image_raws' not in kwargs:
                return None
            uri = '/user/submit/blend'
            params = {
                'dimensions': 'SQUARE',
            }
            params.update({
                'base64Array': ['data:image/png;base64,{}'.format(imageraw2base64(raw)) for raw in kwargs['image_raws']],
            })
            del kwargs['image_raws']
            if kwargs.get('dimensions', '').upper() in ['PORTRAIT', 'SQUARE', 'LANDSCAPE']:
                params.update({'dimensions': kwargs['dimensions']})
            logging.info('build query: %r %r', uri, params.keys())
        elif action == 'describe':
            # image_raw
            # {'code': 0, 'description': '成功', 'result': {'action': 'DESCRIBE', 'id': '1694165937844097024', 'prompt': '1️⃣ four young female anime characters in different portraits, in the style of whimsical cats, dark black and orange, realistic oil portraits, piles/stacks, contrasting light and dark tones, wimmelbilder, poodlepunk \n\n2️⃣ four anime characters with cats and kittens, in the style of [reylia slaby](https://goo.gl/search?artist%20reylia%20slaby), warm tones, self-portraits, dark palette, poodlepunk, amber, portraits with soft lighting \n\n3️⃣ 3 kittens with anime girl, in the style of detailed atmospheric portraits, black and amber, colorful portraits, portraits, self-portraits, playful and colorful depictions, realistic depictions, light orange and dark crimson \n\n4️⃣ an anime series featuring cats and girls, in the style of digital painting and drawing, warm color palettes, black paintings, applecore, realistic impression, dark bronze and orange, poodlepunk', 'promptEn': '1️⃣ four young female anime characters in different portraits, in the style of whimsical cats, dark black and orange, realistic oil portraits, piles/stacks, contrasting light and dark tones, wimmelbilder, poodlepunk \n\n2️⃣ four anime characters with cats and kittens, in the style of [reylia slaby](https://goo.gl/search?artist%20reylia%20slaby), warm tones, self-portraits, dark palette, poodlepunk, amber, portraits with soft lighting \n\n3️⃣ 3 kittens with anime girl, in the style of detailed atmospheric portraits, black and amber, colorful portraits, portraits, self-portraits, playful and colorful depictions, realistic depictions, light orange and dark crimson \n\n4️⃣ an anime series featuring cats and girls, in the style of digital painting and drawing, warm color palettes, black paintings, applecore, realistic impression, dark bronze and orange, poodlepunk', 'description': '/describe 1694165937844097024.png', 'state': None, 'submitTime': 1692755610320, 'startTime': 1692755610326, 'finishTime': 1692755620304, 'imageUrl': 'https://cdn.discordapp.com/ephemeral-attachments/1092492867185950852/1143724593140469851/1694165937844097024.png', 'cdnImageUrl': None, 'status': 'SUCCESS', 'progress': '100%', 'failReason': None, 'properties': {'flags': 0, 'messageId': '1143724598005870633', 'messageHash': 'https://cdn.discordapp.com/ephemeral-attachments/1092492867185950852/1143724593140469851/1694165937844097024', 'nonce': '4207904487395614961', 'finalPrompt': '1️⃣ four young female anime characters in different portraits, in the style of whimsical cats, dark black and orange, realistic oil portraits, piles/stacks, contrasting light and dark tones, wimmelbilder, poodlepunk \n\n2️⃣ four anime characters with cats and kittens, in the style of [reylia slaby](https://goo.gl/search?artist%20reylia%20slaby), warm tones, self-portraits, dark palette, poodlepunk, amber, portraits with soft lighting \n\n3️⃣ 3 kittens with anime girl, in the style of detailed atmospheric portraits, black and amber, colorful portraits, portraits, self-portraits, playful and colorful depictions, realistic depictions, light orange and dark crimson \n\n4️⃣ an anime series featuring cats and girls, in the style of digital painting and drawing, warm color palettes, black paintings, applecore, realistic impression, dark bronze and orange, poodlepunk', 'progressMessageId': '1143724598005870633'}}}
            if 'image_raw' not in kwargs:
                return None
            uri = '/user/submit/describe'
            base64 = 'data:image/png;base64,{}'.format(imageraw2base64(kwargs['image_raw']))
            params = {'base64': base64}
            logging.info('build query: %r %r', uri, params.keys())
        elif action in ['upscale', 'variation', 'reroll', 'zoomout']:
            # taskId index level
            if 'taskId' not in kwargs:
                return None
            uri = '/user/submit/change'
            params = {
                # UPSCALE(放大); VARIATION(变换); REROLL(重新生成); ZOOMOUT(缩远),可用值:UPSCALE,VARIATION,REROLL,ZOOMOUT,示例值(UPSCALE)
                # {'code': 4, 'description': '不支持的操作: REROLL', 'result': None, 'properties': {}}
                'action': action.upper(),
                # 序号(1~4), action为UPSCALE,VARIATION(宫格)时必传,示例值(1)
                'index': 1,
                # 类型(2x,1.5x,high,low), action为ZOOMOUT,VARIATION(UPSCALE后)时必传,示例值(2x)
                'level': '2x',
                'taskId': kwargs['taskId']
            }
            params.update({**kwargs})
            # 去掉无效参数 避免invalid form body
            if 'index' in params and params['index'] == 0:
                del params['index']
            if 'level' in params and params['level'] == '':
                del params['level']
            logging.info('build query: %r %r', uri, params)
        url = f'{self.api_base}{uri}'
        return url, self.mj_headers, json.dumps(params)


    def query_task(self, taskid):
        # url = f'{self.api_base}/user/task/fetch/{taskid}'
        url = f'{self.api_base}/app/task/fetch/{taskid}'
        # {'code': 0, 'description': '成功', 'result': {'action': 'IMAGINE', 'id': '1691385135922024448', 'prompt': 'a cat cartoon girl', 'promptEn': 'a cat cartoon girl', 'description': '/imagine a cat cartoon girl', 'state': None, 'submitTime': 1692092615477, 'startTime': 1692092615481, 'finishTime': 1692092646283, 'imageUrl': 'https://mj-cdn.7zz.co/attachments/1126783722767798324/1140943904825430036/Sazilkree_seq1691385135922024448_ca30d59c-a68e-4aeb-b4ca-52ae35e44dd1.png', 'status': 'SUCCESS', 'progress': '100%', 'failReason': None, 'properties': {'flags': 0, 'messageId': '1140943905525870612', 'messageHash': 'ca30d59c-a68e-4aeb-b4ca-52ae35e44dd1', 'finalPrompt': 'seq:1691385135922024448::0, a cat cartoon girl', 'progressMessageId': '1140943789960204298'}}}
        # {'code': 0, 'description': '成功', 'result': {'action': 'IMAGINE', 'id': '1691646805927923712', 'prompt': 'a girl', 'promptEn': 'a girl', 'description': '/imagine a girl', 'state': None, 'submitTime': 1692155002468, 'startTime': 1692155002472, 'finishTime': 1692155037597, 'imageUrl': 'https://mj-cdn.7zz.co/attachments/1126783722767798324/1141205592489332786/Sazilkree_seq1691646805927923712_eb44cd65-70cf-4a7b-9ece-932dc62fd7da.png', 'status': 'SUCCESS', 'progress': '100%', 'failReason': None, 'properties': {'flags': 0, 'messageId': '1141205593173020782', 'messageHash': 'eb44cd65-70cf-4a7b-9ece-932dc62fd7da', 'finalPrompt': 'seq:1691646805927923712::0, a girl', 'progressMessageId': '1141205450243706911'}}}
        try:
            return httpx.get(url, headers=self.headers, timeout=60).json()
        except Exception as e:
            logging.error('query task error: %r', e)
            return httpx.get(url, headers=self.headers, timeout=60).json()

    def query_task_list(self):
        url = f'{self.api_base}/user/task/list'
        result = httpx.get(url, headers=self.mj_headers, timeout=100).json()
        return result

    def stream(self, action, timeout=600, **kwargs):
        started = time()
        ended = started + timeout
        error_result = {'result': {'status': 'ERROR', 'reason': ''}}
        try:
            query = self.build_query(action=action, **kwargs)
            if not query:
                logging.error("build query error: %r", {action: action, **kwargs})
                yield error_result
                return
            url, headers, params = query
            create_result = httpx.post(url, headers=headers, data=params, timeout=60)
            if create_result.status_code != 200:
                code_reasons = {401: 'unauthorized', 403: 'forbidden', 404: 'not found'}
                logging.error('create error: %r %r %r', create_result.url, create_result.status_code, create_result.content)
                error_result['result']['reason'] = code_reasons.get(create_result.status_code, '')
                yield error_result
                return
            create_result = create_result.json()
            # 状态码: 0(提交成功), 3(数据未找到), 9(系统异常), 21(已存在), 22(排队中), 23(队列已满), other(错误)
            if create_result.get('code', 0) != 0:
                code_reasons = {4: 'validation error', 21: 'task exists', 22: 'task in queue', 24: 'sensitive prompt'}
                error_result['result']['reason'] = code_reasons.get(create_result.get('code', 0), '')
                logging.error('create error: %r %r', create_result, error_result)
                yield error_result
                return
            if 'result' not in create_result:
                logging.error('create error: %r', create_result)
                yield error_result
                return
            taskid = create_result['result']
            # 原接口 {result: taskid ...}
            # 统一为 {result: {id: taskid ...} ...}
            create_result['result'] = {'id': str(taskid)}
            yield create_result
            while time() < ended:
                result = self.query_task(taskid=taskid)
                if result.get('code', 0) != 0 or 'result' not in result:
                    logging.error('create task error: %r', result)
                    yield error_result
                    return
                # replace cdn
                if options.DEFAULT_LOCALE == 'zh_CN':
                    if result['result'].get('imageUrl'):
                        result['result']['imageUrl'] = result['result']['imageUrl'].replace('cdn.discordapp.com', 'mpic.forkway.cn')
                # 任务状态,可用值:NOT_START,SUBMITTED,IN_PROGRESS,FAILURE,SUCCESS
                status = result['result'].get('status', '')
                if status == 'SUCCESS':
                    yield result
                    break
                elif status == 'FAILURE':
                    logging.error('query task fail: %r', result)
                    # 交给后面处理
                    yield result
                    break
                elif status in ['NOT_START', 'SUBMITTED', 'IN_PROGRESS']:
                    # 正常状态
                    yield result
                    sleep(5 if action == 'describe' else 3)
                else:
                    # 其他异常情况
                    logging.error('query task error: %r', result)
                    yield error_result
                if time() >= ended:
                    # yield create_result
                    logging.error('task timeout: %r', create_result)
                    error_result['result']['reason'] = 'timeout'
                    yield error_result
        except Exception as e:
            logging.error('stream error: %r', e)
            yield error_result

    def create(self, action="imagine", stream=False, timeout=600, **kwargs):
        result = self.stream(action=action, timeout=timeout, **kwargs)
        if stream:
            return result
        else:
            return list(result).pop()


class MJApiChat(SimpleChatModel):
    client: Any  #: :meta private:
    api_base: Optional[str] = ''
    api_key: Optional[str] = ''
    api_secret: Optional[str] = ''
    api_token: Optional[str] = ''
    user_id: Optional[str] = ''
    max_retries: int = 6
    streaming: bool = False

    def _llm_type(self) -> str:
        return 'mjapi'

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        params = {'stream': self.streaming, **kwargs}
        message = messages.pop()
        params.update({**message.additional_kwargs})
        self.client = MJApiClient(
            api_base=self.api_base,
            api_key=self.api_key,
            api_secret=self.api_secret,
            api_token=self.api_token
        )

        def get_token_from_resp(resp):
            token = ''
            result = resp.get('result', {})
            status = result.get('status', '')
            if not result or status == 'FAILURE':
                token = 'created:failure'
                raise Exception(token)
            elif status == 'ERROR':
                # 新增内部status: ERROR
                token = '{} {}'.format('created:error', result.get('reason'))
                raise Exception(token)
            elif status == 'NOT_START' or status == 'SUBMITTED':
                token = 'submit:success'
            elif status == 'SUCCESS':
                token = '100%'
            elif status == 'IN_PROGRESS':
                # 接口自带的progress可能为0
                res_progress = result.get('progress')
                # 实际可能不是一个进度
                if res_progress and res_progress[: -1].isdigit():
                    progress = int(res_progress[: -1])
                else:
                    # 拿不到进度就不更新
                    progress = -1
                if progress >= 99:
                    progress = 99
                token = f'{progress}%' if 0 <= progress <= 100 else None
            else:
                # 不做任何操作 因为状态为空可能是正常的
                # token = 'unknown error'
                pass
            return token

        if self.streaming:
            response = {}
            for resp in self.client.create(**params):
                result = resp.get('result', {})
                if run_manager:
                    token = get_token_from_resp(resp)
                    run_manager.on_llm_new_token(token, **{
                        'action': result.get('action', ''),
                        'imageUrl': result.get('imageUrl', '')
                    })
                if result.get('status', '') == 'SUCCESS':
                    # 成功时才赋值 否则出现错误
                    response = resp
                    break
        else:
            response = self.client.create(**params)
            get_token_from_resp(response)
        data = response.get('result', {})
        logging.info('chat res: %r', response)
        # TODO describe操作时应该存文本而不是图片
        result_content = data.get('imageUrl', '')
        message = AIMessage(content=result_content, additional_kwargs=data)
        return ChatResult(generations=[ChatGeneration(message=message)])


if __name__ == '__main__':
    import asyncio
    async def main():
        api_base = 'http://mj-api.7zz.co'
        # api_base = 'https://mjapi.forkway.cn/user'
        api_key = ''
        api_secret = ''
        api_token = ''
        prompt = 'a girl'
        images = [
            'https://mj-cdn.7zz.co/attachments/1126783722767798324/1141205592489332786/Sazilkree_seq1691646805927923712_eb44cd65-70cf-4a7b-9ece-932dc62fd7da.png',
            'https://mj-cdn.7zz.co/attachments/1126783722767798324/1140943904825430036/Sazilkree_seq1691385135922024448_ca30d59c-a68e-4aeb-b4ca-52ae35e44dd1.png'
        ]

        from langchain.schema import HumanMessage
        params_imagine = {
            'action': 'imagine',
            'prompt': prompt,
            # 'image_raw': httpx.get('https://mj-cdn.7zz.co/attachments/1126783722767798324/1141205592489332786/Sazilkree_seq1691646805927923712_eb44cd65-70cf-4a7b-9ece-932dc62fd7da.png').content
        }
        params_describe = {
            'action': 'describe',
            # 'image_url': images[0],
            'image_raw': httpx.get(
                'https://mj-cdn.7zz.co/attachments/1126783722767798324/1140943904825430036/Sazilkree_seq1691385135922024448_ca30d59c-a68e-4aeb-b4ca-52ae35e44dd1.png').content
        }
        params_blend = {
            'action': 'blend',
            'image_raws': [httpx.get(img).content for img in images]
        }
        params_change = {
            'action': 'zoomout',
            'index': 0,
            'level': '2x',
            'taskId': '1694974895794053120'
        }
        chat = MJApiChat(
            api_base=api_base,
            api_key=api_key,
            api_secret=api_secret,
            api_token=api_token,
            streaming=True
        )
        messages = [HumanMessage(content=prompt, additional_kwargs=params_change)]
        result = chat(messages)


    asyncio.run(main())