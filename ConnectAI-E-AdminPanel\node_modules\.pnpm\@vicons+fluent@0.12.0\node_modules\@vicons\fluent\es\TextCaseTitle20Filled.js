import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.5 3.25a.75.75 0 0 1 .75.75v4.657A2.71 2.71 0 0 1 15 8c1.657 0 3 1.567 3 3.5S16.657 15 15 15a2.71 2.71 0 0 1-1.75-.657v.157a.75.75 0 0 1-1.5 0V4a.75.75 0 0 1 .75-.75zM15 13.5c.62 0 1.5-.67 1.5-2s-.88-2-1.5-2s-1.5.67-1.5 2s.88 2 1.5 2zM6.495 3.25a.75.75 0 0 1 .698.504l3.652 10.5a.75.75 0 1 1-1.417.492L8.647 12.5H4.06l-.86 2.266a.75.75 0 0 1-1.402-.532l3.984-10.5a.75.75 0 0 1 .712-.484zM4.63 11h3.495L6.454 6.195L4.63 11z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseTitle20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
