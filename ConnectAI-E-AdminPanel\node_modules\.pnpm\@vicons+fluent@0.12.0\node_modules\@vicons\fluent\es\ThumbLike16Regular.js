import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8.035 1.923c.21-.52.795-1.08 1.546-.871c.59.164.973.52 1.193.996c.204.443.254.965.264 1.462c.01.535-.102 1.176-.233 1.738a14.53 14.53 0 0 1-.195.74h1.385a2 2 0 0 1 1.919 2.563l-1.364 4.646a2.5 2.5 0 0 1-3.148 1.681l-5.356-1.682a2 2 0 0 1-1.273-1.205l-.52-1.384a2 2 0 0 1 .856-2.426l1.872-1.104a4.063 4.063 0 0 0 .523-.495c.344-.389.817-1.036 1.292-2.045c.205-.436.377-.779.533-1.09c.253-.503.465-.924.706-1.524zM5.519 7.92a.517.517 0 0 1-.017.01L3.617 9.042a1 1 0 0 0-.428 1.213l.52 1.384a1 1 0 0 0 .637.603L9.7 13.924a1.5 1.5 0 0 0 1.89-1.008l1.363-4.646a1 1 0 0 0-.96-1.282H9.927a.5.5 0 0 1-.471-.667c.1-.282.252-.771.376-1.302c.126-.538.215-1.082.207-1.49c-.01-.468-.059-.816-.173-1.062c-.098-.215-.251-.368-.552-.452c-.043-.012-.094-.008-.16.035a.538.538 0 0 0-.19.246a20.426 20.426 0 0 1-.765 1.651c-.155.309-.318.634-.497 1.015c-.514 1.094-1.04 1.822-1.45 2.283c-.204.23-.378.393-.506.502a2.922 2.922 0 0 1-.202.157l-.016.01l-.005.004l-.003.002zm-.543-.84l-.001.001z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ThumbLike16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
