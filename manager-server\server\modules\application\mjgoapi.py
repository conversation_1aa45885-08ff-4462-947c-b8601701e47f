class MjGoApiTool(CTool):
    name: str = 'mjgoapi'
    description: str = 'midjourney api'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class MJCallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs):
                if model.streaming and platform == 'feishu':
                    if token and token != '100%':
                        contents = []
                        if kwargs.get('imageUrl', ''):
                            try:
                                syncify(client.upload_image)(kwargs['imageUrl'])
                                contents.append(FeishuMessageImage(img_key=img_key, alt='图片'))
                            except Exception as e:
                                # 中间图上传失败不影响后续
                                logging.error(e)
                        contents.append(
                            FeishuMessageNote(
                                FeishuMessagePlainText(
                                    '{}({})'.format(kwargs.get('action', _('正在生成')).capitalize(), token if token != '100%' else _('完成'))
                                )
                            )
                        )
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                *contents
                            )
                        )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info('logging on_llm_end: {}'.format(response))
                image_url = response.generations[0][0].text
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                if not additional_kwargs:
                    send_message(AppResult.ReplyText, 'created:error')
                    return None
                # 下载使用原始的链接，download_bytes会自己处理剩下的逻辑
                img_url = additional_kwargs.get('task_result', {}).get('discord_image_url', '')
                action_map = {'outpaint': 'zoomout'}
                resp_action = additional_kwargs.get('meta', {}).get('task_type', '')
                display_action = action_map.get(resp_action, resp_action).capitalize()
                task_id = additional_kwargs.get('task_id', '')
                # {key: [title, {action: '', index: '', aspect_ratio: '', task_id, '', **kwargs}]}
                # TODO 暂时去除 aspect_ratio 参数的1:1默认值
                action_options = {
                    'U1': ['U1', {'action': 'upscale', 'index': '1', 'aspect_ratio': '', 'task_id': task_id}],
                    'U2': ['U2', {'action': 'upscale', 'index': '2', 'aspect_ratio': '', 'task_id': task_id}],
                    'U3': ['U3', {'action': 'upscale', 'index': '3', 'aspect_ratio': '', 'task_id': task_id}],
                    'U4': ['U4', {'action': 'upscale', 'index': '4', 'aspect_ratio': '', 'task_id': task_id}],
                    'V1': ['V1', {'action': 'variation', 'index': '1', 'aspect_ratio': '', 'task_id': task_id}],
                    'V2': ['V2', {'action': 'variation', 'index': '2', 'aspect_ratio': '', 'task_id': task_id}],
                    'V3': ['V3', {'action': 'variation', 'index': '3', 'aspect_ratio': '', 'task_id': task_id}],
                    'V4': ['V4', {'action': 'variation', 'index': '4', 'aspect_ratio': '', 'task_id': task_id}],
                    'Z1': ['Zoom Out 1.5x', {'action': 'outpaint', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'zoom_ratio': '1.5'}],
                    'Z2': ['Zoom Out 2x', {'action': 'outpaint', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'zoom_ratio': '2'}],
                    'ZN': ['Custom Zoom', {'action': 'outpaint', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'zoom_ratio': ''}],
                    'VH': ['Vary High', {'action': 'variation', 'index': 'high_variation', 'aspect_ratio': '', 'task_id': task_id}],
                    'VL': ['Vary Low', {'action': 'variation', 'index': 'low_variation', 'aspect_ratio': '', 'task_id': task_id}],
                    'PL': ['⬅️', {'action': 'pan', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'direction': 'left'}],
                    'PR': ['➡️', {'action': 'pan', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'direction': 'right'}],
                    'PU': ['⬆️', {'action': 'pan', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'direction': 'up'}],
                    'PD': ['⬇️', {'action': 'pan', 'index': '', 'aspect_ratio': '', 'task_id': task_id, 'direction': 'down'}],
                    'R': ['Reroll', {'action': 'reroll', 'index': '', 'aspect_ratio': '', 'task_id': task_id}],
                }
                options_map = {
                    'upscale1': 'U1', 'upscale2': 'U2', 'upscale3': 'U3', 'upscale4': 'U4',
                    'variation1': 'V1', 'variation2': 'V2', 'variation3': 'V3', 'variation4': 'V4',
                    'high_variation': 'VH', 'low_variation': 'VL',
                    'outpaint_1.5x': 'Z1', 'outpaint_2x': 'Z2', 'outpaint_custom': 'ZN', 'outpaint_1x': '',
                    'pan_left': 'PL', 'pan_right': 'PR', 'pan_up': 'PU', 'pan_down': 'PD',
                    'reroll': 'R',
                }
                # 按行和显示顺序进行分组
                options_group = [
                    ['U1', 'U2', 'U3', 'U4'],
                    ['V1', 'V2', 'V3', 'V4'],
                    ['VH', 'VL'],
                    ['Z1', 'Z2', 'ZN'],
                    ['PL', 'PR', 'PU', 'PD']
                ]
                # 所有下一个动作
                next_actions = [options_map.get(x, '') for x in additional_kwargs.get('task_result', {}).get('actions', [])]
                option_keys = []
                for group in options_group:
                    option_key = [option for option in group if option in next_actions]
                    if option_key:
                        option_keys.append(option_key)
                # 把reroll动作放到最后
                if 'R' in next_actions:
                    option_keys[-1].append('R') if option_keys else option_keys.append(['R'])
                logging.info('actions: %r', (next_actions, option_keys))
                if not img_url:
                    option_keys = []
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在上传，请稍等...')))
                        )
                    )
                    start_time = time.time()
                    contents = []
                    # 下载图片，压缩图片，上传飞书
                    # 上传失败后不影响后续发送消息
                    try:
                        img_bytes = syncify(download_bytes)(img_url, True)
                        img_bytes = compress_image(img_bytes, quality=90, max_size=1024)
                        img_key = syncify(client.upload_image_binary)(img_bytes)
                    except Exception as e:
                        logging.error('mj upload error: %r', e)
                        try:
                            # 前面的img_url是原始的，这里尝试使用cdn的图片再试一次
                            img_key = syncify(client.upload_image)(image_url)
                        except Exception as e:
                            img_key = None
                            logging.error('mj upload error: %r', e)
                    if img_key:
                        logging.info('mj image uploaded: %r', (img_url, img_key, time.time() - start_time))
                        contents.append(FeishuMessageImage(img_key=img_key, alt='图片'))
                    for col_key in option_keys:
                        action_elements = []
                        for key in col_key:
                            if key not in action_options:
                                continue
                            title, value = action_options[key]
                            if key == 'R':
                                value['extra'] = {'option_keys': option_keys}
                            action_elements.append(FeishuMessageButton(title, value=value, type='primary'))
                        contents.append(FeishuMessageAction(*action_elements))
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            # FeishuMessageDiv('[{}]({})'.format(_('原图'), img_url), tag='lark_md', quote=False),
                            FeishuMessageAction(FeishuMessageButton(_('查看原图'), type='primary', url=image_url)),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功') + '\n' + _(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader(content='Midjourney Bot {}🎉'.format(display_action), template='blue')
                        )
                    )
                elif platform == 'dingding':
                    action_elements = []
                    for col_key in option_keys:
                        for key in col_key:
                            # dingding不展示ZN
                            if key not in action_options or key == 'ZN':
                                continue
                            title, value = action_options[key]
                            action_elements.append(DingDingActionCardButton(
                                'dtmd://dingtalkclient/sendMessage?content={}'.format(quote(':'.join(list(map(str, dict(value).values()))))), title
                            ))
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            *action_elements,
                            title='Midjourney Bot {}🎉'.format(display_action),
                            text=_('![图片]({})  \n{}').format(image_url, _('🤖️：生成成功')),
                        )
                    )

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any):
                logging.error('mj llm error: %r', error)
                if platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md')
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [MJCallbackHandler()]
        chat = MJChat(**model)
        if 'action' in data.extra.extra:
            # logging.info('mj action input: %r', data.extra.extra.action)
            action_value = dict(data.extra.extra.get('action', {}).get('value', {}))
            input_kwargs = dict(action_value)
            input_kwargs['enable_translate'] = True
            if 'extra' in input_kwargs:
                del input_kwargs['extra']
            messages = [HumanMessage(content='', additional_kwargs=dict(input_kwargs))]
            return chat.invoke(messages)
        # logging.info(f'logging message data: {data}')
        data_extra = dict(data.get('extra', {}))
        message_type = data_extra.get('message_type', '')
        input_kwargs = data_extra.get('extra', {}).get('input_kwargs', {})
        input_kwargs['enable_translate'] = True
        # 默认fast模式
        input_kwargs.update(process_mode=session.extra.get('process_mode', 'fast'))
        if platform == 'feishu':
            messages = [HumanMessage(content=input, additional_kwargs=dict(input_kwargs))]
            return chat.invoke(messages)
        elif platform == 'dingding':
            messages = [HumanMessage(content=input, additional_kwargs=dict(input_kwargs))]
            return chat.invoke(messages)


class MJGoApiDescribeTool(CTool):
    name: str = 'mjgoapi_describe'
    description: str = 'Midjourney api decribe action'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class MJCallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                pass

            def on_llm_end(self, response, *args, **kwargs):
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                resp_prompt = additional_kwargs.get('task_result', {}).get('message', '')
                resp_action = additional_kwargs.get('meta', {}).get('task_type', '')
                task_id = additional_kwargs.get('meta', {}).get('origin_task_id') or additional_kwargs.get('task_id', '')
                # action_options = [[title, {'action': 'imagine', 'index': '', 'aspect_ratio': '', 'task_id': '', 'prompt': resp_prompts[i]}] for i, title in enumerate(['1️⃣', '2️⃣', '3️⃣', '4️⃣'])]
                action_options = [[prompt.split()[0], {'action': 'imagine', 'index': '', 'aspect_ratio': '', 'task_id': '', 'prompt': prompt.replace(prompt.split()[0], '').strip()}] for prompt in resp_prompt.split('\n') if len(prompt) > 1]
                # describe reroll 动作不可用
                # action_options.append(['Reroll', {'action': 'reroll', 'index': '', 'aspect_ratio': '', 'task_id': task_id}])
                if platform == 'feishu':
                    action_elements = [FeishuMessageButton(title, value=value, type='primary') for title, value in action_options]
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(resp_prompt, tag='lark_md', quote=False),
                            FeishuMessageAction(*action_elements),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader(content='Midjourney Bot {}🎉'.format(resp_action.capitalize()), template='blue')
                        )
                    )
                elif platform == 'dingding':
                    # action_elements = [DingDingActionCardButton('dtmd://dingtalkclient/sendMessage?content={}'.format(quote(':'.join(list(map(str, dict(value).values()))))), title) for title, value in action_options[-1:]]
                    action_elements = []
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            *action_elements,
                            title='Midjourney Bot {}🎉'.format(resp_action.capitalize()),
                            text=resp_prompt,
                        )
                    )

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [MJCallbackHandler()]
        chat = MJChat(**model)
        if 'action' in data.extra.extra:
            logging.info('mj action input: %r', data.extra.extra.action)
            action_value = dict(data.extra.extra.get('action', {}).get('value', {}))
            messages = [HumanMessage(content='', additional_kwargs=dict(action_value))]
            return chat.invoke(messages)
        data_extra = dict(data.get('extra', {}))
        input_kwargs = data_extra.get('extra', {}).get('input_kwargs', {})
        # 默认fast模式
        process_mode = session.extra.get('process_mode', 'fast')
        # if process_mode == 'mixed':
        #     process_mode = 'fast'
        input_kwargs.update(process_mode=process_mode)
        if platform == 'feishu':
            messages = [HumanMessage(content=input, additional_kwargs=dict(input_kwargs))]
            return chat.invoke(messages)
        elif platform == 'dingding':
            messages = [HumanMessage(content=input, additional_kwargs=dict(input_kwargs))]
            return chat.invoke(messages)


class DingdingCommand(CommandTool):
    next_tool_name: str = 'mjgoapi'
    name: str = 'mjgoapi_dingding_command'
    description: str = 'midjourney api dingding command'
    modes = ['fast', 'relax']

    def send_usage(self):
        return send_message(
            AppResult.ReplyActionCard,
            DingDingMarkdownMessage(
                # 🤖 **图片生成模式选择**\n\n文本回复 *模式* 或 */mode*\n\n---\n
                text=_('💥 **我是Midjourney AI绘图小助手**\n\n---\n🌠 **Imagine**\n\n通过提示词进行绘图，您可以直接在输入框输入提示词进行文生图，或者展开输入框后同时输入图片及文字进行图生图，比如: 图片+换行+提示词\n\n---\n📝 **Describe**\n\n通过图片反推提示词，您可以在输入框的展开按钮中只上传1张图片，并输入文字 /describe\n\n---\n🧪 **Blend**\n\n将多张图片进行混合，您可以在输入框的展开按钮中上传2-5张图片\n\n---\n🔍 **Upscale**\n\n对图片进行放大，您可以在图片生成后，点击图片下方的 U1、U2 等按钮进行操作\n\n---\n🎨 **Variation**\n\n支持对图片进行变体，可点击图片下方的 V1、V2 等按钮进行操作。\n支持高变体及低变体，可在 Upscale 操作后，点击Vary high 或 Vary low 进行操作\n\n---\n🔮 **Zoom Out**\n\n对图片按比例进行扩充，需要先进行 Upscale 放大图片，支持1.5x 和 2x\n\n---\n🔃 **Pan**\n在特定方向上对图片进行扩展，需要先进行 Upscale 放大图片\n\n---\n🎲 **Reroll**\n\n按照当前提示词，重新生成图片'),
                title=_('🎒 需要帮助吗？')
            )
            # DingDingActionCardMessage(
            #     DingDingActionCardButton(
            #         'dtmd://dingtalkclient/sendMessage?content=/mode',
            #         _('🤖 **图片生成模式选择**\n\n文本回复 *模式* 或 */mode*')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🌠 **Imagine**\n\n通过提示词进行绘图，您可以直接在输入框输入提示词进行文生图，或者展开输入框后同时输入图片及文字进行图生图，比如: 图片+换行+提示词')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('📝 **Describe**\n\n通过图片反推提示词，您可以在输入框的展开按钮中只上传1张图片，并输入文字 /describe')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🧪 **Blend**\n\n将多张图片进行混合，您可以在输入框的展开按钮中上传2-5张图片')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🔍 **Upscale**\n\n对图片进行放大，您可以在图片生成后，点击图片下方的 U1、U2 等按钮进行操作')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🎨 **Variation**\n\n支持对图片进行变体，可点击图片下方的 V1、V2 等按钮进行操作。\n支持高变体及低变体，可在 Upscale 操作后，点击Vary high 或 Vary low 进行操作')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🔮 **Zoom Out**\n\n对图片按比例进行扩充，需要先进行 Upscale 放大图片，支持1.5x 和 2x')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🔃 **Pan**\n在特定方向上对图片进行扩展，需要先进行 Upscale 放大图片')
            #     ),
            #     DingDingActionCardButton(
            #         '',
            #         _('🎲 **Reroll**\n\n按照当前提示词，重新生成图片')
            #     ),
            #     DingDingActionCardButton(
            #         'dtmd://dingtalkclient/sendMessage?content=/help',
            #         _('🎒 需要更多帮助')
            #     ),
            #     title=_('🎒 需要帮助吗？'),
            #     text=_("💥 **我是Midjourney AI绘图小助手**")
            # )
        )

    def parse_command(self, input, action):
        # 不可通过prompt调整生成模式
        input = input.replace('--relax', '').replace('--fast', '').replace('--turbo', '')
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        # 暂时屏蔽mode
        # elif input[:5] == '/mode' or input[:2] == '模式':
        #     mode = input.replace('/mode', '').replace('模式', '').strip()
        #     return 'mode', mode
        input_split = list(map(str, input.split(':')))
        if len(input_split) >= 4:
            # 钉钉，只能回复字符串，如果符合格式，就模拟飞书点击按钮返回事件内容
            input_kwargs = dict(
                action=input_split[0].lower(),
                index=input_split[1],
                aspect_ratio=input_split[2],
                task_id=input_split[3],
            )
            if input_kwargs['action'] == 'outpaint' and len(input_split) > 4:
                input_kwargs['zoom_ratio'] = input_split[4]
            elif input_kwargs['action'] == 'pan' and len(input_split) > 4:
                input_kwargs['direction'] = input_split[4]
            action_value = {'value': input_kwargs}
            data['extra']['extra']['action'] = action_value
            # logging.info('logging dd cmd: %r %r', input, data_extra)
            if input_kwargs['action'] == 'upscale' and app_model.duplicate(input):
                return 'duplicate',
            return None,
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text', 'richText']:
            return data.extra.message_type, input.strip()
        return 'note',

    def on_duplicate(self):
        send_message(AppResult.ReplyText, _('🤖️：已放大图片，请勿重复操作...'))
        return 'duplicate upscale'

    def on_text(self, text):
        # text消息
        # 都是字符串类型
        input_split = list(map(str, text.split(':')))
        if len(input_split) < 4:
            input_kwargs = {
                'action': 'imagine',
                'prompt': text
            }
        else:
            input_kwargs = {
                'action': input_split[0].lower(),
                'index': input_split[1],
                'aspect_ratio': input_split[2],
                'task_id': input_split[3]
            }
            if input_kwargs['action'] == 'outpaint' and len(input_split) > 4:
                input_kwargs['zoom_ratio'] = input_split[4]
            elif input_kwargs['action'] == 'pan' and len(input_split) > 4:
                input_kwargs['direction'] = input_split[4]
        data['extra']['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_richText(self, text=''):
        platform_content = data.extra.extra.platform_content
        input_text, input_img_keys = '', []
        for msg in platform_content['richText']:
            if 'text' in msg and '@' not in msg:
                input_text = msg['text']
            elif msg.get('type', '') == 'picture':
                input_img_keys.append(msg['downloadCode'])
        input_text = input_text.strip()
        input_kwargs = {}
        if not input_img_keys:
            pass
        elif len(input_img_keys) == 1:
            if not input_text or input_text == '/describe':
                self.next_tool_name = 'mjgoapi_describe'
                image_url = syncify(client.get_message_resource)(bot.app_id, input_img_keys[0])
                input_kwargs = {'action': 'describe', 'image_url': image_url}
            elif input_text[0] != '/':
                input_kwargs = {'action': 'imagine', 'prompt': input_text, 'image_raw': input_img_keys[0]}
        elif 2 <= len(input_img_keys) <= 5:
            image_urls = get_event_loop().run_until_complete(asyncio.gather(*[functools.partial(client.get_message_resource, bot.app_id, img_key)() for img_key in input_img_keys]))
            input_kwargs = {'action': 'blend', 'image_urls': image_urls}
        if not input_kwargs:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        data['extra']['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_mode(self, mode=''):
        if not mode or mode.lower() not in self.modes:
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/mode ' + mode)),
                        mode
                    ) for mode in self.modes],
                    text=_('请选择图片生成模式。'),
                    title=_('🤖 机器人提醒'),
                )
            )
        session.set_extra('process_mode', mode.lower())
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                text=_('已选择图片生成模式：%(mode)s', mode=mode),
                title=_('🤖 机器人提醒'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或图文（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/图文！')
        return send_note(text, title)


class FeishuCommand(CommandTool):
    next_tool_name: str = 'mjgoapi'
    name: str = 'mjgoapi_feishu_command'
    description: str = 'midjourney api feishu command'
    # modes = ['mixed', 'fast', 'turbo']
    modes = ['fast', 'relax']
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '您可以直接在输入框输入提示词进行文生图，无需添加/imagine前缀',
        '您可以直接在输入框输入提示词+图片进行图生图',
        '您可以直接在输入框输入图片 + /describe 进行图生文',
        '您可以直接在输入框输入多张图片进行混合生图的操作',
        # '您可以直接在输入框输入 /describe + 图片 url 进行图生文',
        # '您可以直接在输入框输入 /blend + 多个图片 url 进行混合生图的操作',
        '生成图片后，您可以点击图片下方的U1、U2等按钮放大图片',
        '生成图片后，您可以点击图片下方的V1、V2等按钮生成变体图片',
        '放大图片后，您可以点击图片下方的Zoom out按钮扩展图片',
        '放大图片后，您可以点击图片下方的Pan按钮在特定方向上扩展图片',
        '若您对当前生成的图片不满意，可进行Reroll操作重新生成',
        '可以通过切换图片生成模式更快地生成图片，但消耗的token也更多'
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def template_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in prompts],
            placeholder=_('选择内置提示词模板'),
            initial_option=session.prompt_id or None,
            value={'command': 'template'}
        ) if len(prompts) > 0 else None

    @property
    def mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(mode, mode) for mode in self.modes],
            placeholder=_('选择图片生成模式'),
            value={'command': 'mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定选择该模式吗?'),
                text=_('AI将使用该模式生成图片')
            )
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是Midjourney AI绘图小助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra = self.manual_btn,
        ),
                # FeishuMessageHr(),
                # FeishuMessageDiv(
                #     _('🤖 **图片生成模式选择**\n文本回复 *模式* 或 */mode*'),
                #     tag='lark_md',
                #     extra=self.mode_select,
                # ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🏠 **内置提示词模板**\n文本回复 *模板* 或 */template*'),
                    tag='lark_md',
                    extra=self.template_select
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🌠 **Imagine**\n通过提示词进行绘图，您可以直接在输入框输入提示词进行文生图，或者展开输入框后同时输入图片及文字进行图生图，比如: 图片+换行+提示词'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('📝 **Describe**\n通过图片反推提示词，您可以在输入框的展开按钮中只上传1张图片，并输入文字 /describe'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🧪 **Blend**\n将多张图片进行混合，您可以在输入框的展开按钮中上传2-5张图片'),
                    tag='lark_md'),
                # FeishuMessageHr(),
                # FeishuMessageDiv(
                #     _('📝 **Describe**\n通过图片反推提示词，文本回复 /describe + 图片 url'),
                #     tag='lark_md'),
                # FeishuMessageHr(),
                # FeishuMessageDiv(
                #     _('🧪 **Blend**\n将多张图片进行混合，文本回复 /blend + 多个图片 url'),
                #     tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🔍 **Upscale**\n对图片进行放大，您可以在图片生成后，点击图片下方的 U1、U2 等按钮进行操作'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎨 **Variation**\n支持对图片进行变体，可点击图片下方的 V1、V2 等按钮进行操作。\n支持高变体及低变体，可在 Upscale 操作后，点击Vary high 或 Vary low 进行操作'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🔮 **Zoom Out**\n对图片按比例进行扩充，需要先进行 Upscale 放大图片，支持1.5x 和 2x'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🔃 **Pan**\n在特定方向上对图片进行扩展，需要先进行 Upscale 放大图片'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎲 **Reroll**\n按照当前提示词，重新生成图片'),
                    tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue')
            )
        )

    def parse_command(self, input, action):
        # 不可通过prompt调整生成模式
        input = input.replace('--relax', '').replace('--fast', '').replace('--turbo', '')
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:8] == '/imagine':
            text = input.replace('/imagine', '').strip()
            return 'text', text
        elif input[:9] == '/describe':
            text = input.replace('/describe', '').strip()
            return 'text', text
        elif input[:6] == '/blend':
            text = input.replace('/blend', '').strip()
            return 'text', text
        elif input[:9] == '/template' or input[:2] == '模板':
            return 'template',
        # elif input[:5] == '/mode' or input[:2] == '模式':
        #     mode = input.replace('/mode', '').replace('模式', '').strip()
        #     return 'mode', mode
        elif not input and action:
            if action['tag'] == 'button':
                a_value = action['value']
                content_value = dict(a_value)
                if 'extra' in content_value:
                    del content_value['extra']
                content = ':'.join(list(content_value.values()))
                app_model.update_content(content)
                a_v_action = a_value.get('action', '')
                if a_v_action == 'upscale' and app_model.duplicate(content):
                    return 'duplicate',
                elif a_v_action == 'outpaint' and not a_value['zoom_ratio']:
                    return 'zoomout_custom', a_value
                return None,
            elif action['tag'] == 'input':
                return action['name'], action
            elif action['tag'] == 'select_static':
                return action['value']['command'], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text', 'post']:
            return data.extra.message_type, input.strip()
        return 'note',

    def on_mode(self, mode=''):
        if not mode or mode.lower() not in self.modes:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请选择图片生成模式。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
                )
            )
        session.set_extra('process_mode', mode.lower())
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageNote(
                    FeishuMessagePlainText(_('已选择图片生成模式：%(mode)s', mode=mode))
                ),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_duplicate(self):
        send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：已放大图片，请勿重复操作...'))),
                header=FeishuMessageCardHeader('Midjourney Bot🎉', template='blue')
            )
        )
        # 这个字符串会保存到chat_log里面
        return 'duplicate upscale'

    def on_text(self, text):
        input_urls = parse_urls(text)
        if not input_urls:
            input_kwargs = {'action': 'imagine', 'prompt': text}
        elif len(input_urls) == 1:
            input_kwargs = {'action': 'describe', 'image_url': input_urls[0]}
            self.next_tool_name = 'mjgoapi_describe'
        elif 2 <= len(input_urls) <= 5:
            input_kwargs = {'action': 'blend', 'image_urls': input_urls}
        else:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        data['extra']['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_post(self, text=''):
        # 富文本消息
        message_id = data.extra.message_id
        platform_content = data.extra.extra.platform_content
        input_text, input_img_keys = '', []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg.get('tag', '') == 'text':
                    input_text = msg['text']
                elif msg.get('tag', '') == 'img':
                    input_img_keys.append(msg['image_key'])
        input_text = input_text.strip()
        input_kwargs = {}
        if not input_img_keys:
            pass
        elif len(input_img_keys) == 1:
            image_url = f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?image_key={input_img_keys[0]}&message_id={message_id}&compress=true&quality=90&max_size=1024&auto_download='
            if input_text and input_text == '/describe':
                self.next_tool_name = 'mjgoapi_describe'
                input_kwargs = {'action': 'describe', 'image_url': image_url}
            elif input_text and input_text[0] != '/':
                # image prompt: url + 空格 + prompt
                input_kwargs = {'action': 'imagine', 'prompt': image_url + ' ' + input_text}
        elif 2 <= len(input_img_keys) <= 5:
            image_urls = [f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?image_key={img_key}&message_id={message_id}&compress=true&quality=90&max_size=1024&auto_download=' for img_key in input_img_keys]
            input_kwargs = {'action': 'blend', 'image_urls': image_urls}
        if not input_kwargs:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        data['extra']['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_template(self, template_name=None):
        if not template_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.template_select) if len(prompts) else FeishuMessageDiv(_("无可用提示词模板选择")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('选择提示词模板，获取更加精准的绘图提示词。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖️️：机器人提醒'), template='blue'),
                )
            )
        prompt = get_prompt_by_id(template_name)
        # 提取大括号中的提示词变量
        pattern = r'\{(.*?)\}'
        variables = re.findall(pattern, prompt.content)
        default_value = ' '.join([f'{{{v}}}' for v in variables])
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageAction(
                    FeishuMessageInput(
                        placeholder='',
                        default_value=default_value,
                        label=prompt.content,
                        value={'prompt': prompt.content, 'variables': variables},
                        name='prompt_input'
                    ),
                ),
                FeishuMessageNote(FeishuMessagePlainText(_('请替换提示词模板大括号中的内容，并保留大括号'))),
                header=FeishuMessageCardHeader(prompt.title, template='blue'),
            )
        )

    def on_prompt_input(self, action):
        if not action:
            return
        action_value = action.get('value', {})
        prompt = action_value['prompt']
        pattern = r'\{(.*?)\}'
        variables = re.findall(pattern, action['input_value'])
        for k, v in zip(action_value['variables'], variables):
            prompt = prompt.replace(f'{{{k}}}', v)
        prompt = prompt.replace('{', '').replace('}', '')
        action['value'] = {'action': 'imagine', 'prompt': prompt}
        logging.info('mj prompt input: %r', prompt)
        return self.next_tool_name

    def on_zoomout_custom(self, action_value):
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageAction(
                    FeishuMessageInput(placeholder='[1, 2]', label='Custom Zoom Out', name='zoomout_input', value=dict(action_value)),
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader('Zoom Out', template='blue')
            )
        )

    def on_zoomout_input(self, action):
        input_value = action['input_value']
        try:
            if 1 <= float(input_value) <= 2:
                action['value']['zoom_ratio'] = input_value
                return self.next_tool_name
            else:
                return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        except Exception as e:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或图文（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/图文！')
        return send_note(text, title)


class MjGoApiAgent(CAgent):
    class AppConfig(BaseAppConfig):
        category: str = 'AI绘画'
        name: str = 'Midjourney'
        title: str = 'Midjourney绘图助手'
        title_en: str = 'Midjourney Bot'
        description: str = '🍎 追求极致创作体验的Midjourney-协同办公版'
        description_en: str = '🍎 Supercharged experience for midJourney on oa platform '
        problem: str = '可以在IM平台中使用Midjourney吗'
        problem_en: str = 'Can midjourney be used in IM platform'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/HWZ5dQPAUoOFAoxjd6dckjkonIc'
        manual_en: str = 'https://connect-ai.feishu.cn/docx/PeykdAlMlodXsGxNtZGcxRcGnLg'
        icon: str = 'https://pic1.forkway.cn/cdn/202308242003554.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/202308242003554.png?imageMogr2/thumbnail/720x'
        sorted: int = 2
        support_resource: List[object] = [dict(
            category=ModelCategory.Draw.value,
            scene=ModelCategory.Draw.value,
            title='AI绘画',
            tip='',
            required=True,
            resource=['Midjourney']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcn8oPlGmsCQlhTAAVul5Eg7o'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/HlZGwSgS9iYRxqkJFZJc0iLance'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log='')
            return AgentFinish(
                {'output': last_result},
                'result for action {}: {}'.format(last_action.tool, str(last_result)))

        if platform == 'feishu':
            return AgentAction(tool='mjgoapi_feishu_command', tool_input=kwargs, log='')
        elif platform == 'dingding':
            return AgentAction(tool='mjgoapi_dingding_command', tool_input=kwargs, log='')
        return AgentAction(tool='mjgoapi', tool_input=kwargs, log='')
