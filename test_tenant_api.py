#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试租户API
"""

import requests
import json

def test_tenant_handler():
    """测试租户权限API"""
    url = "http://localhost:3000/api/tenant/handler"
    
    print(f"测试租户权限API: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"请求失败: {e}")

def test_account_info():
    """测试账户信息API"""
    url = "http://localhost:3000/api/account/info"
    
    print(f"\n测试账户信息API: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"请求失败: {e}")

def test_login_flow():
    """测试完整登录流程"""
    print("\n=== 测试完整登录流程 ===")
    
    # 1. 登录
    login_url = "http://localhost:3000/api/account/login"
    login_data = {
        "email": "<EMAIL>",
        "passwd": "admin123"
    }
    
    print("1. 登录...")
    session = requests.Session()
    
    try:
        login_response = session.post(login_url, json=login_data, timeout=10)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print("✅ 登录成功")
            
            # 2. 获取租户权限
            print("\n2. 获取租户权限...")
            tenant_response = session.get("http://localhost:3000/api/tenant/handler", timeout=10)
            print(f"租户权限状态码: {tenant_response.status_code}")
            
            if tenant_response.status_code == 200:
                print("✅ 租户权限获取成功")
                result = tenant_response.json()
                print(f"权限数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print("❌ 租户权限获取失败")
                print(f"错误内容: {tenant_response.text}")
            
            # 3. 获取账户信息
            print("\n3. 获取账户信息...")
            info_response = session.get("http://localhost:3000/api/account/info", timeout=10)
            print(f"账户信息状态码: {info_response.status_code}")
            
            if info_response.status_code == 200:
                print("✅ 账户信息获取成功")
                result = info_response.json()
                print(f"账户数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print("❌ 账户信息获取失败")
                print(f"错误内容: {info_response.text}")
        else:
            print("❌ 登录失败")
            print(f"错误内容: {login_response.text}")
            
    except Exception as e:
        print(f"流程测试失败: {e}")

if __name__ == "__main__":
    print("🧪 ConnectAI 租户API测试")
    print("=" * 50)
    
    # 测试单个接口
    test_tenant_handler()
    test_account_info()
    
    # 测试完整流程
    test_login_flow()
