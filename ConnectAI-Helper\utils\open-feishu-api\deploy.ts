import { OpenApp } from "./app";

export type DeployConfig = {
  appId?: string;
  appSecret?: string;
  avatar?: string;
  name?: string;
  desc?: string;
  encryptKey?: string;
  verificationToken?: string;
  events?: string[];
  scopeIds?: string[];
  cardRequestUrl?: string;
  verificationUrl?: string;
}

export class DeployTool {
  rawConfig: DeployConfig;

  openApi: OpenApp;

  constructor(private config: DeployConfig) {
    this.rawConfig = config;
  }

  async loadOpenApi(openApi: OpenApp) {
    this.openApi = openApi;
    await this.openApi.init()
  }

  get configString() {
    return JSON.stringify(this.rawConfig);
  }

  ifFirstDeploy() {
    return this.rawConfig.appId === undefined;
  }

  async creatNewBot() {
    const result = await this.openApi.createAndQueryApp({
      name: this.rawConfig.name,
      desc: this.rawConfig.desc
    });
    this.rawConfig.appId = result.appID;
    this.rawConfig.appSecret = result.secret;
    console.log(result)
    return result;
  }

  async resetToken() {
    await this.openApi.eventManager.resetEventEncryptKey(this.rawConfig.appId, this.rawConfig.encryptKey);
    await this.openApi.eventManager.resetEventVerificationToken(this.rawConfig.appId, this.rawConfig.verificationToken);
  }


  async addBot() {
    await this.openApi.enableBot(this.rawConfig.appId);
    await this.openApi.addScope(this.rawConfig.appId, this.rawConfig.scopeIds);
    await this.openApi.botManager.addBotCallBack(this.rawConfig.appId, this.rawConfig.cardRequestUrl);
  }

  async addEvent() {
    await this.openApi.eventManager.addEvent(this.rawConfig.appId, this.rawConfig.events);
    await this.openApi.eventManager.addEventCallBackByUrl(this.rawConfig.appId, this.rawConfig.verificationUrl);
  }

  async publishBot() {
    await this.openApi.versionManager.createAndPublishNextVersion(this.rawConfig.appId);
  }
  async deployBot() {
    await this.resetToken();
    await this.addEvent();
    await this.addBot();
  }

  async createAndDeploy() {
    await this.creatNewBot();
    await this.deployBot();
    await this.publishBot();
  }

}
