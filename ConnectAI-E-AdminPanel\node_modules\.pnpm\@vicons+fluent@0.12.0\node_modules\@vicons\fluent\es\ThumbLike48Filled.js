import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M28.227 1.989c-1.648-.49-2.956.716-3.383 1.88c-.245.667-.468 1.285-.678 1.865c-.834 2.306-1.449 4.005-2.289 5.715c-2.65 5.399-6.033 9.624-12.357 12.526c-2.414 1.108-3.964 3.798-3.205 6.525l1.234 4.432a7.25 7.25 0 0 0 5.295 5.107l14.07 3.37a9.25 9.25 0 0 0 11.079-6.56l3.334-12.217c.911-3.34-1.603-6.632-5.065-6.632h-5.04c.714-2.44 1.262-5.43 1.24-8.15c-.014-1.689-.248-3.368-.871-4.764c-.639-1.43-1.713-2.608-3.364-3.097z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ThumbLike48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
