import { describe, expect, it } from "vitest";
import type { DeployConfig } from "./deploy";
import type { FeishuLoginCookies } from "./configuration";
import { Configuration } from "./configuration";
import { OpenApp } from "./app";
import { DeployTool } from "./deploy";

const testDeployConfig: DeployConfig = {
  "name": "飞书-OpenAI-2",
  "desc": "一个飞书OpenAI机器人",
  "avatar": "https://s1-imfile.feishucdn.com/static-resource/v1/v2_b126c0fd-8f9d-49b0-9653-8c16caf0e28g",
  "events": [
    "im.message.message_read_v1",
    "im.message.receive_v1"
  ],
  "encryptKey": "UwM6vYMYyqLbTmNatwNjcfNT2T53U02n",
  "verificationToken": "eidEZjig7dbaQsgc9gADcwtny1YayigK",
  "scopeIds": [
    "21001",
    "7",
    "21003",
    "21002",
    "20001",
    "20011",
    "3001",
    "20012",
    "6005",
    "20010",
    "3000",
    "20013",
    "20014",
    "20015",
    "20008",
    "1000",
    "1006",
    "1005",
    "20009",
    '41003', // 查看新版文档
    '26010', // 查看知识库
    '101221', // 获取客户端用户代理信息

  ],
  "cardRequestUrl": "https://ai-feishu.forkway.cn/api/callback/lark/65/card",
  "verificationUrl": "https://ai-feishu.forkway.cn/api/callback/lark/65/event"
};


describe("feishu deploy", async() => {
  const testConfig: FeishuLoginCookies = {
    lark_oapi_csrf_token: "evxkN5zgFJepbEJEeKRptbKjbM95yO57ImVksvSrD5s=",
    session: "XN0YXJ0-0aamd8f0-907c-4e81-b59c-6b407c3840bb-WVuZA"
  };

  const config = new Configuration(testConfig);
  const app = new OpenApp(config);


  it("create new app", async () => {
    const deploy = new DeployTool(testDeployConfig);
    await deploy.loadOpenApi(app);
    await deploy.createAndDeploy();
  });

  it("update app", async () => {
    const deploy = new DeployTool(testDeployConfig);
    await deploy.loadOpenApi(app);
    await deploy.deployBot();
  })




});

describe("lark deploy", async() => {


  const testConfig: FeishuLoginCookies = {
    lark_oapi_csrf_token: "JUQuwxLaNvWvKCQRr6VYl4ioez87XNyMVr7epcAr8tw=",
    session: "XN0YXJ0-7ceg07d1-ee7e-4907-a552-37e240qpn4fv-WVuZA",
    baseUrl: "https://open.larksuite.com"
  };

  const config = new Configuration(testConfig);
  const app = new OpenApp(config);


  it("create new app", async () => {
    const deploy = new DeployTool(testDeployConfig);
    await deploy.loadOpenApi(app);
    await deploy.createAndDeploy();
  });


}, 1000000);
