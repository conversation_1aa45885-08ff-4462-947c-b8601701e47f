#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务是否正常运行
"""

import socket
import json
import time
from pathlib import Path

def test_port(host, port, service_name):
    """测试端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ {service_name} 端口 {port} 正在监听")
            return True
        else:
            print(f"❌ {service_name} 端口 {port} 未开放")
            return False
    except Exception as e:
        print(f"❌ {service_name} 端口测试失败: {e}")
        return False

def load_admin_info():
    """加载管理员信息"""
    try:
        with open("./data/admin_info.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 无法加载管理员信息: {e}")
        return None

def test_simple_http_request(host, port, path="/"):
    """简单的HTTP请求测试"""
    try:
        import urllib.request
        import urllib.error
        
        url = f"http://{host}:{port}{path}"
        req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req, timeout=5) as response:
            if response.status == 200:
                return True, response.read().decode('utf-8')
            else:
                return False, f"HTTP {response.status}"
                
    except urllib.error.URLError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def main():
    """主函数"""
    print("🧪 ConnectAI 服务运行状态检查")
    print("=" * 50)
    
    # 检查端口状态
    print("🔍 检查服务端口状态...")
    manager_port_ok = test_port("localhost", 3000, "Manager Server")
    datachat_port_ok = test_port("localhost", 5000, "DataChat API")
    
    print()
    
    # 检查HTTP响应
    print("🔍 检查HTTP响应...")
    
    if datachat_port_ok:
        success, response = test_simple_http_request("localhost", 5000, "/")
        if success:
            print("✅ DataChat API HTTP响应正常")
            try:
                data = json.loads(response)
                print(f"   消息: {data.get('message', 'N/A')}")
            except:
                print(f"   响应: {response[:100]}...")
        else:
            print(f"❌ DataChat API HTTP响应失败: {response}")
    
    if manager_port_ok:
        success, response = test_simple_http_request("localhost", 3000, "/")
        if success:
            print("✅ Manager Server HTTP响应正常")
            try:
                data = json.loads(response)
                print(f"   消息: {data.get('message', 'N/A')}")
            except:
                print(f"   响应: {response[:100]}...")
        else:
            print(f"❌ Manager Server HTTP响应失败: {response}")
    
    print()
    
    # 显示管理员信息
    admin_info = load_admin_info()
    if admin_info:
        print("👤 预置管理员账号信息:")
        print(f"   邮箱: {admin_info['admin_email']}")
        print(f"   密码: {admin_info['admin_password']}")
        print(f"   租户: {admin_info['tenant_name']}")
        print(f"   API Key: {admin_info['tenant_apikey']}")
    
    print()
    
    # 总结
    print("📊 服务状态总结:")
    if manager_port_ok and datachat_port_ok:
        print("🎉 所有服务正常运行！")
        print()
        print("🔗 访问地址:")
        print("   Manager Server: http://localhost:3000")
        print("   DataChat API: http://localhost:5000")
        print("   健康检查: http://localhost:5000/health")
        print()
        print("🧪 测试建议:")
        print("   1. 在浏览器访问上述地址")
        print("   2. 使用管理员账号登录")
        print("   3. 测试API接口功能")
        
        if admin_info:
            print()
            print("🔑 登录测试:")
            print("   curl -X POST http://localhost:3000/api/login \\")
            print("     -H 'Content-Type: application/json' \\")
            print(f"     -d '{{\"email\": \"{admin_info['admin_email']}\", \"password\": \"{admin_info['admin_password']}\"}}'")
        
        return True
    else:
        print("❌ 部分服务未正常运行")
        print()
        print("🔧 故障排除建议:")
        if not manager_port_ok:
            print("   - 检查Manager Server启动日志")
            print("   - 确认端口3000未被占用")
        if not datachat_port_ok:
            print("   - 检查DataChat API启动日志")
            print("   - 确认端口5000未被占用")
        
        return False

if __name__ == "__main__":
    main()
