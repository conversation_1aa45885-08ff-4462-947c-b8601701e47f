import asyncio
import random
import functools
import logging
import pickle
from inspect import iscoroutinefunction

import redis
from binascii import crc32
from tornado.ioloop import <PERSON><PERSON><PERSON>
from tornado.options import options
from .exception import PermissionDenied


decode_redis_pool = redis.ConnectionPool(
    socket_connect_timeout=0.5,
    host=options.REDIS_HOST,
    port=options.REDIS_PORT,
    db=options.REDIS_DB,
    decode_responses=True,  # 自动解码
)

redis_pool = redis.ConnectionPool(
    socket_connect_timeout=0.5,
    host=options.REDIS_HOST,
    port=options.REDIS_PORT,
    db=options.REDIS_DB,
    decode_responses=False,  # 不自动解码
)


def redis_cli(decode_responses=True):
    return redis.StrictRedis(
        connection_pool=decode_redis_pool if decode_responses else redis_pool,
    )


def gen_prefix(obj, method):
    return '.'.join([obj.__module__, obj.__class__.__name__, method.__name__])


def get_name(self, method, args, kwargs, key=None, prefix=None, attr_key=None, attr_prefix=None, namespace=options.REDIS_NAMESPACE):
    name = key or kwargs.get('key', None) or (attr_key and getattr(self, attr_key))
    if not name:
        _prefix = prefix or (attr_prefix and getattr(self, attr_prefix)) or gen_prefix(self, method)
        name = "%s:%u" % (_prefix, crc32(pickle.dumps(args) + pickle.dumps(kwargs)))
    name = namespace and "{}:{}".format(namespace, name) or name
    return name


def lock(key=None, prefix=None, attr_key=None, attr_prefix=None, timeout=60, namespace=options.REDIS_NAMESPACE, retry=1, retry_delay=1e-1):
    def decorate(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            redis_lock = redis.lock.Lock(redis_cli(), name, timeout=timeout)
            if redis_lock.acquire(blocking=False):
                try:
                    res = method(self, *args, **kwargs)
                    return res
                finally:
                    try:
                        redis_lock.release()
                    except Exception as e:
                        logging.exception(e)
            else:
                logging.info("lock name: %r, (*%r, **%r)", name, args, kwargs)
                raise PermissionDenied('任务忙，请稍后再试')

        @functools.wraps(method)
        async def async_wrapper(self, *args, **kwargs):
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            retry_time = retry if retry > 0 else 1
            while retry_time > 0:  # 由于在tornado里面只有异步的重试才不会block CPU
                retry_time = retry_time - 1
                redis_lock = redis.lock.Lock(redis_cli(), name, timeout=timeout)
                if redis_lock.acquire(blocking=False):
                    try:
                        res = await method(self, *args, **kwargs)
                        return res
                    finally:
                        try:
                            redis_lock.release()
                        except Exception as e:
                            logging.exception(e)
                else:
                    # 如果超过重试次数或者设置的重试延迟时间不正确就直接跳出
                    if retry_time <= 0 or retry_delay <= 0:
                        logging.debug("lock name: %r, (*%r, **%r)", name, args, kwargs)
                        raise PermissionDenied('任务忙，请稍后再试')
                    # 在重试次数内，延时retry_delay再重试一次
                    logging.debug("retry: %r to get lock for: %r in %rs", retry, name, retry_delay)
                    await asyncio.sleep(retry_delay)

        return async_wrapper if iscoroutinefunction(method) else wrapper
    return decorate


def stalecache(key=None, prefix=None, attr_key=None, attr_prefix=None, namespace=options.REDIS_NAMESPACE,
               expire=600, stale=3600, time_lock=1, time_delay=1, max_time_delay=10):
    def decorate(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            res = redis_cli(False).pipeline().ttl(name).get(name).execute()
            v = pickle.loads(res[1]) if res[0] > 0 and res[1] else None
            if res[0] <= 0 or res[0] < stale:

                def func():
                    value = method(self, *args, **kwargs)
                    logging.debug("update cache: %s", name)
                    redis_cli(False).pipeline().set(
                        name, pickle.dumps(value)
                    ).expire(name, expire + stale).execute()
                    return value

                # create new cache in blocking modal, if cache not exists.
                if res[0] <= 0:
                    return func()

                # create new cache in non blocking modal, and return stale data.
                # set expire to get a "lock", and delay to run the task
                real_time_delay = random.randrange(time_delay, max_time_delay)
                redis_cli(False).expire(name, stale + real_time_delay + time_lock)
                IOLoop.current().add_timeout(IOLoop.current().time() + real_time_delay, func)

            return v

        @functools.wraps(method)
        async def async_wrapper(self, *args, **kwargs):
            if kwargs.get('skip_cache'):
                return await method(self, *args, **kwargs)

            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            res = redis_cli(False).pipeline().ttl(name).get(name).execute()
            v = pickle.loads(res[1]) if res[0] > 0 and res[1] else None
            if res[0] <= 0 or res[0] < stale:

                async def func():
                    value = await method(self, *args, **kwargs)
                    logging.debug("update cache: %s", name)
                    redis_cli(False).pipeline().set(
                        name, pickle.dumps(value)
                    ).expire(name, expire + stale).execute()
                    return value

                # create new cache in blocking modal, if cache not exists.
                if res[0] <= 0:
                    return await func()

                # create new cache in non blocking modal, and return stale data.
                # set expire to get a "lock", and delay to run the task
                real_time_delay = random.randrange(time_delay, max_time_delay)
                redis_cli(False).expire(name, stale + real_time_delay + time_lock)
                IOLoop.current().add_timeout(IOLoop.current().time() + real_time_delay, func)

            return v

        return async_wrapper if iscoroutinefunction(method) else wrapper
    return decorate


def delete(key=None, prefix=None, attr_key=None, attr_prefix=None, namespace=options.REDIS_NAMESPACE,
           target=None, stale=3600):
    def decorate(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            value = method(self, *args, **kwargs)
            c = redis_cli()

            # delete by key
            name = key or kwargs.get('key', None) or (attr_key and getattr(self, attr_key))
            if name:
                name = namespace and "{}:{}".format(namespace, name) or name
                c.expire(name, stale)

            # delete by prefix
            _prefix = prefix or (attr_prefix and getattr(self, attr_prefix))\
                or (target and hasattr(self, target) and gen_prefix(self, getattr(self, target)))
            if _prefix:
                _prefix = namespace and "{}:{}".format(namespace, _prefix) or _prefix
                for name in c.keys(pattern="{}*".format(_prefix)):
                    c.expire(name, stale)

            return value
        return wrapper
    return decorate


def rate_limit(
    rate=10,
    interval=1,
    key=None, prefix=None, attr_key=None, attr_prefix=None,
    namespace=options.REDIS_NAMESPACE,
):
    def decorate(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            res = redis_cli().pipeline().pttl(name).get(name).execute()
            current_permits = int(res[1]) if res[0] > 0 and res[1] else 0
            if res[0] > 0 and current_permits > rate:
                logging.warn("请求次数超过限制: %r / %r > %r / %r", rate, interval - res[0], rate, interval)
                raise PermissionDenied('请求次数超过限制')
            inc = redis_cli().incrby(name, 1)
            if inc == 1:
                redis_cli().pexpire(name, interval * 1000)
            value = method(self, *args, **kwargs)
            return value

        @functools.wraps(method)
        async def async_wrapper(self, *args, **kwargs):
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            res = redis_cli().pipeline().pttl(name).get(name).execute()
            current_permits = int(res[1]) if res[0] > 0 and res[1] else 0
            if res[0] > 0 and current_permits > rate:
                logging.warn("请求次数超过限制: %r / %r > %r / %r", rate, interval * 1000 - res[0], rate, interval)
                raise PermissionDenied('请求次数超过限制')
            inc = redis_cli().incrby(name, 1)
            if inc == 1:
                redis_cli().pexpire(name, interval * 1000)
            value = await method(self, *args, **kwargs)
            return value

        return async_wrapper if iscoroutinefunction(method) else wrapper
    return decorate

