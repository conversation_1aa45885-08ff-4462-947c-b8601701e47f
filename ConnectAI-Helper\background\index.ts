import "@plasmohq/messaging/background"
import { relayMessage } from "@plasmohq/messaging";
import { getCsrfToken } from '~utils/browser'


function autoInjectScript() {
  /**
   * 1. ~~使用chrome.runtime.id拿到manifest的url地址~~
   * 2. ~~使用fetch拿到manifest中的content_scripts列表~~
   * 1. 使用chrome.runtime.getManifest()拿到manifest内容
   * 3. 按列表中每一项的matches列表分别使用chrome.tabs.query获取对应的tab。
   * 4. 针对获取到的url，使用executeScript直接注入对应的js
   */
  chrome.runtime.getManifest().content_scripts.forEach(({ js, matches }) => {
    matches.forEach(match => {
      console.log('query tabs by match', match)
      chrome.tabs.query({ url: match }).then((tabs) => {
        console.log('query tabs by match', match, tabs)
        tabs.forEach(tab => {
          if (tab.id) {
            console.log('executeScript', js, tab.id, tab.title, tab.url)
            chrome.scripting.executeScript({
              files: js,
              target: { tabId: tab.id }
            }).catch(e => {
              //
            }).finally(() => {
              // 将注入的页面显示到前台
              // 对于大多数用户来说，只有一个页面，所以，多调几次update也没关系
              chrome.tabs.update(tab.id, {active: true})
            })
          }
        })
      })
    })
  })
}


chrome.runtime.onInstalled.addListener(async (detail) => {
  console.log('onInstalled', detail)
  if (detail.reason === "install") {
    //
  }
  // 现在安装和升级都执行一下
  autoInjectScript()
})

