#!/usr/bin/env python
# coding=utf-8
import json
import logging
from uuid import uuid4
from time import time
from tornado.options import options
from tornado.httpclient import AsyncHTTPClient, HTTPRequest, HTTPError
from core.base_handler import (
    BaseHandler,
    arguments,
    authenticated,
)
from core.exception import ParametersError
from core.utils import ObjIDStr
from core.redisdb import delete, redis_cli, stalecache
from .resource_model import (
    ResourceModel,
    AppModel,
    AppSettingModel,
    KnowledgeModel,
)


class ResourceCategoryHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, page: int = 1, size: int = 10, model: ResourceModel = None):
        categories, total = await model.get_resource_category(page, size, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': categories,
            'total': total,
        })


class ResourcePriceHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, resource_id: ObjIDStr = '', model: ResourceModel = None):
        models = await model.get_resource_price(resource_id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': models,
        })


class TenantResourceHandler(BaseHandler):

    @authenticated
    @arguments
    async def post(
        self,
        resource_id: ObjIDStr = '',
        api_key: str = '',
        api_base: str = '',
        extra: dict = dict(),
        model: ResourceModel = None,
    ):
        await model.update_tenant_resource(self.session.tenant_id, resource_id, api_key, api_base, extra)
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class TenantResourceV1Handler(BaseHandler):

    @authenticated
    @arguments
    async def post(
        self,
        resource_id: ObjIDStr = '',
        api_base: str = '',
        api_key: str = '',
        app_id: str = '',
        secret_key: str = '',
        group_id: str = '',
        model: dict = list(),
        rmodel: ResourceModel = None,
    ):
        await rmodel.update_tenant_resource(
            self.session.tenant_id, resource_id,
            api_key.strip(),
            api_base.strip().strip('/'),  # 避免用户最后填写了空格或者最后有多余的斜杠
            dict(
                group_id=group_id.strip(),
                app_id=app_id.strip(),
                secret_key=secret_key.strip(),
                model=model,
            )
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ResourceHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        resource_id: ObjIDStr = '', action: str = '',  # 这两个是占位的参数，因为使用了两个不一样的url
        category_id: ObjIDStr = '', keyword: str = '',
        page: int = 1, size: int = 20,
        model: ResourceModel = None,
    ):
        resources, total = await model.get_resource_list(self.session.tenant_id, category_id, keyword, page, size, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': resources,
            'total': total,
        })

    @authenticated
    @arguments
    async def post(self, resource_id: ObjIDStr = '', action: str = '', model: ResourceModel = None):
        if action not in ['active', 'start', 'stop']:
            raise ParametersError()
        await model.action(self.session.tenant_id, resource_id, action)
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ApplicationCategoryHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, page: int = 1, size: int = 10, model: AppModel = None):
        categories, total = await model.get_application_category(page, size, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': categories,
            'total': total,
        })


class ApplicationHander(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        application_id: ObjIDStr = '', action: str = '',  # 这两个是占位的参数，因为使用了两个不一样的url
        category_id: ObjIDStr = '', keyword: str = '',
        page: int = 1, size: int = 20,
        model: AppModel = None,
    ):
        if application_id:
            app = await model.get_application_detail(self.session.tenant_id,application_id, lang=self._get_locale())
            self.finish({
                'code': 0,
                'msg': 'success',
                'data': app,
            })
            return
        else:
            apps, total = await model.get_application_list(self.session.tenant_id, category_id, keyword, page, size, lang=self._get_locale())
            self.finish({
                'code': 0,
                'msg': 'success',
                'data': apps,
                'total': total,
            })

    @authenticated
    @arguments
    async def post(self, application_id: ObjIDStr = '', action: str = '', model: AppModel = None):
        if action not in ['deploy', 'buy']:
            raise ParametersError()
        await model.action(self.session.tenant_id, application_id, action, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ApplicationShareHandler(BaseHandler):

    @arguments
    async def get(
        self,
        application_id: ObjIDStr = '',
        category_id: ObjIDStr = '', keyword: str = '',
        page: int = 1, size: int = 20,
        model: AppModel = None,
    ):
        app = await model.get_application_detail(
            "",  # 这个接口不需要登录，所以tenant_id为空
            application_id,
            lang=self._get_locale()
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': app,
        })
        return


class ApplicationShopHandler(BaseHandler):

    @arguments
    async def get(
        self,
        model: AppModel = None,
    ):
        apps = await model.get_application_list_all(lang="zh_CN")

        self.finish({
            'code': 0,
            'msg': 'success',
            'data': [
                {
                    "id": app["id"],
                    "title": app["title"],
                    "title_en": app["title_en"],
                    "logo": app["logo"],
                    "description": app["description"],
                    "description_en": app["description_en"],
                    "support_bot": app["support_bot"],
                }
                for app in apps
            ]
        })


class TenantApplicationHander(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        category_id: ObjIDStr = '', keyword: str = '',
        page: int = 1, size: int = 20,
        model: AppModel = None,
    ):
        apps, total = await model.get_tenant_application_list(self.session.tenant_id, category_id, keyword, page, size, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': apps,
            'total': total,
        })


class ApplicationResourceHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, application_id: ObjIDStr = '', model: AppSettingModel = None):
        resources = await model.get_support_resource(self.session.tenant_id, self.session.id, application_id=application_id, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': resources,
        })


class ApplicationResourcesHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, application_id: ObjIDStr = '', model: AppSettingModel = None):
        resources = await model.get_support_resources(self.session.tenant_id, self.session.id, application_id=application_id, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': resources,
        })


class ApplicationClientHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        application_id: ObjIDStr = '',
        bot_id: ObjIDStr = '',  # 这里是占位的变量
        model: AppSettingModel = None,
    ):
        bots = await model.get_support_bots(self.session.tenant_id, self.session.id, application_id=application_id, lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': bots,
        })


class AppInstanceClientSettingHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        instance_id: ObjIDStr = '', bot_id: ObjIDStr = '',
        model: AppSettingModel = None,
    ):
        setting = await model.get_bot_setting_by_appid_and_bit_id(
            self.session.tenant_id, self.session.id,
            '', bot_id,
            lang=self._get_locale(),
            instance_id=instance_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': setting,
        })

    @authenticated
    @arguments
    async def post(
        self,
        instance_id: ObjIDStr = '', bot_id: ObjIDStr = '',
        name: str = '', description: str = '',
        app_id: str = '', app_secret: str = '', app_key: str = '',
        encript_key: str = '', validation_token: str = '',
        agent_id: str = '', crop_id: str = '',
        model: AppSettingModel = None,
    ):
        await model.save_bot_setting_by_appid_and_bit_id(
            self.session.tenant_id, self.session.id,
            '', bot_id,
            name, description,
            app_id or app_key, app_secret,
            encript_key, validation_token,
            agent_id, crop_id,
            instance_id=instance_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ApplicationClientSettingHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        application_id: ObjIDStr = '', bot_id: ObjIDStr = '',
        model: AppSettingModel = None,
    ):
        setting = await model.get_bot_setting_by_appid_and_bit_id(
            self.session.tenant_id, self.session.id,
            application_id, bot_id,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': setting,
        })

    @authenticated
    @arguments
    async def post(
        self,
        application_id: ObjIDStr = '', bot_id: ObjIDStr = '',
        name: str = '', description: str = '',
        app_id: str = '', app_secret: str = '', app_key: str = '',
        encript_key: str = '', validation_token: str = '',
        agent_id: str = '', crop_id: str = '',
        model: AppSettingModel = None,
    ):
        await model.save_bot_setting_by_appid_and_bit_id(
            self.session.tenant_id, self.session.id,
            application_id, bot_id,
            name, description,
            app_id or app_key, app_secret,
            encript_key, validation_token,
            agent_id, crop_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class AppInstanceSettingHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, instance_id: ObjIDStr = '', model: AppSettingModel = None):
        setting = await model.get_setting_by_application_id(
            self.session.tenant_id, self.session.id, instance_id=instance_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': setting,
        })

    @authenticated
    @arguments
    async def post(
        self,
        instance_id: ObjIDStr = '',
        resource_id: ObjIDStr = '',
        resource_ids: dict = dict(),
        prompt_id: ObjIDStr = list(),
        sensitive_id: ObjIDStr = list(),
        user_permission: dict = list(),
        group_permission: dict = list(),
        collection_id: ObjIDStr = '',
        chat_history: str = 'enable',
        prompt: str = '',
        model_id: ObjIDStr = '',
        model: AppSettingModel = None,
    ):
        await model.save_setting_by_application_id(
            self.session.tenant_id, self.session.id, '',
            resource_id,
            prompt_id, sensitive_id,
            user_permission, group_permission,
            collection_id, prompt, chat_history,
            'disable',  # web search
            instance_id=instance_id,  # 前面的application_id传空的，使用后面的instance_id
            model_id=model_id,
            resource_ids=resource_ids,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ApplicationSettingHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, application_id: ObjIDStr = '', model: AppSettingModel = None):
        setting = await model.get_setting_by_application_id(
            self.session.tenant_id, self.session.id, application_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': setting,
        })

    @authenticated
    @arguments
    async def post(
        self,
        application_id: ObjIDStr = '',
        resource_id: ObjIDStr = '',
        prompt_id: ObjIDStr = list(),
        sensitive_id: ObjIDStr = list(),
        user_permission: dict = list(),
        group_permission: dict = list(),
        collection_id: ObjIDStr = '',
        chat_history: str = 'enable',
        web_search: str = 'enable',
        prompt: str = '',
        model_id: ObjIDStr = '',
        resource_ids: dict = dict(),
        model: AppSettingModel = None,
    ):
        await model.save_setting_by_application_id(
            self.session.tenant_id, self.session.id, application_id,
            resource_id,
            prompt_id, sensitive_id,
            user_permission, group_permission,
            collection_id, prompt, chat_history, web_search,
            model_id=model_id,
            resource_ids=resource_ids,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class AppInstanceInfoHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, instance_id: ObjIDStr = '', model: AppSettingModel = None):
        info = await model.get_info_by_instance_id(
            self.session.tenant_id, self.session.id, instance_id,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': info,
        })


    @authenticated
    @arguments
    async def delete(self, instance_id: ObjIDStr = '', model: AppSettingModel = None):
        await model.remove_app_by_instance_id(
            self.session.tenant_id, self.session.id, instance_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class ApplicationInfoHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, application_id: ObjIDStr = '', model: AppSettingModel = None):
        info = await model.get_info_by_application_id(
            self.session.tenant_id, self.session.id, application_id,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': info,
        })


class KnowledgeCode2SessionHandler(BaseHandler):

    @arguments
    async def get(self, code: str = ''):
        key = 'code:{}'.format(code)
        info = json.loads(redis_cli().get(key))
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': info,
        })


class KnowledgeHandler(BaseHandler):

    @stalecache(stale=0, expire=60)
    async def access_token(self, tenant_id):
        code = str(uuid4())
        key = 'code:{}'.format(code)
        user = {
            'openid': tenant_id,
            'permission': {
                'has_privilege': True,
                'expires': time() + 86400,
            }
        }
        redis_cli().pipeline().set(key, json.dumps(user)).expire(key, 1000).execute()
        request = HTTPRequest(
            '{}/api/access_token?code={}'.format(options.KNOW_SERVER, code),
            headers={
                'X-System-Url': f'{options.SCHEMA}://{options.DOMAIN}/api/code2session',
            }
        )
        response = await AsyncHTTPClient().fetch(request)
        return json.loads(response.body.decode()).get('access_token')

    @authenticated
    async def proxy(self, *args, **kwargs):
        request = HTTPRequest(
            '{}{}'.format(options.KNOW_SERVER, self.request.uri),
            method=self.request.method,
            body=self.request.body if self.request.method in ['POST', 'PUT'] else None,
            headers={
                'Authorization': 'Bearer {}'.format(await self.access_token(self.session.tenant_id)),
                'Content-Type': self.request.headers.get('Content-Type', 'application/json').replace('text/plain', 'application/json'),
            },
            request_timeout=50,
        )
        response = await AsyncHTTPClient().fetch(request)
        self.finish(response.body)

    async def get(self, *args, **kwargs):
        await self.proxy(*args, **kwargs)

    async def post(self, *args, **kwargs):
        await self.proxy(*args, **kwargs)

    async def put(self, *args, **kwargs):
        await self.proxy(*args, **kwargs)

    async def delete(self, *args, **kwargs):
        await self.proxy(*args, **kwargs)


class KnowledgeAppHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, model: KnowledgeModel = None):
        know = await model.get_knowledge_app(lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': know,
        })


class KnowledgeListHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, keyword: str = '', page: int = 1, size: int = 10, model: KnowledgeModel = None):
        knows, total = await model.get_knowledge(
            self.session.tenant_id, self.session.id,
            keyword, page, size, lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': knows,
            'total': total,
        })

    @authenticated
    @arguments
    async def post(
        self,
        application_id: ObjIDStr = '',
        name: str = '',
        description: str = '',
        resource_ids: dict = dict(),
        model: AppModel = None,
        smodel: AppSettingModel = None,
    ):
        instance_id = await model.action(
            self.session.tenant_id, application_id, 'buy',
            lang=self._get_locale(), multi=True,
            name=name, description=description,
        )

        # 保存resource_ids配置信息
        await smodel.save_setting_by_application_id(
            self.session.tenant_id, self.session.id, '',  # application_id=''
            None,  # resource_id,
            [], [],  # prompt_id, sensitive_id,
            [], [],  # user_permission, group_permission,
            [], '', 'enable',  # collection_id, prompt, chat_history,
            'disable',  # web_search
            instance_id=instance_id,
            model_id='',  # model_id=model_id,
            resource_ids=resource_ids,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': instance_id,
        })




