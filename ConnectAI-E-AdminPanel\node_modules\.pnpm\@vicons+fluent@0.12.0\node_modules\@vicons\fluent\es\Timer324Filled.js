import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<g fill="none"><path d="M12 5a8.5 8.5 0 0 1 8.434 9.563c-.62-.366-1.375-.563-2.184-.563c-.999 0-1.77.306-2.315.669a3.546 3.546 0 0 0-.787.718a2.37 2.37 0 0 0-.085.114l-.01.014l-.005.008l-.002.003l-.001.002s.143-.215-.001.001a1.75 1.75 0 0 0 1.788 2.69a1.75 1.75 0 0 0 0 1.063a1.75 1.75 0 0 0-2.004 2.236A8.5 8.5 0 1 1 12 5zm0 3a.75.75 0 0 0-.743.648l-.007.102v4.5l.007.102a.75.75 0 0 0 1.486 0l.007-.102v-4.5l-.007-.102A.75.75 0 0 0 12 8z" fill="currentColor"></path><path d="M19.17 5.123l.082.061l1.149 1a.75.75 0 0 1-.904 1.193l-.081-.061l-1.149-1a.75.75 0 0 1 .903-1.193z" fill="currentColor"></path><path d="M14.25 2.5a.75.75 0 0 1 .102 1.493L14.25 4h-4.5a.75.75 0 0 1-.102-1.493L9.75 2.5h4.5z" fill="currentColor"></path><path d="M19.375 17.734c-.135.126-.4.266-.875.266a.75.75 0 0 0 0 1.5c.474 0 .74.14.875.266s.19.283.18.44c-.015.263-.282.794-1.305.794c-.465 0-.76-.137-.928-.249a1.046 1.046 0 0 1-.208-.182a.75.75 0 0 0-1.03-.193c-.584.375-.208 1.04-.208 1.04l.001.002l.001.001l.002.003l.005.008l.013.018l.036.048a2.542 2.542 0 0 0 .556.503c.394.263.975.501 1.76.501c1.622 0 2.73-.969 2.803-2.206a2.042 2.042 0 0 0-.573-1.544c.411-.427.606-.986.573-1.544C20.98 15.969 19.872 15 18.25 15c-.785 0-1.366.238-1.76.501a2.542 2.542 0 0 0-.556.503a1.432 1.432 0 0 0-.036.048l-.013.018l-.005.008l-.002.003l-.001.002l-.001.001a.75.75 0 0 0 1.238.847a1.046 1.046 0 0 1 .208-.182c.169-.112.463-.249.928-.249c1.023 0 1.29.531 1.305.794a.546.546 0 0 1-.18.44zm-2.253 2.847l-.005-.008v.002l.004.004l.001.002zm.002-3.664l-.007.01v-.002l.004-.004l.001-.002l.002-.002z" fill="currentColor"></path></g>', 1)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer324Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
