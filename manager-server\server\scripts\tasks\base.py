import sys
import asyncio
import warnings
from os.path import abspath, dirname
from tornado.options import parse_command_line

sys.path.insert(0, dirname(dirname(dirname(abspath(__file__)))))

from settings.config import load_config

load_config()
parse_command_line()


def async_test(coro):
    def wrapper(*args, **kwargs):
        warnings.simplefilter("ignore", ResourceWarning)
        loop = asyncio.new_event_loop()
        return loop.run_until_complete(coro(*args, **kwargs))
    return wrapper
