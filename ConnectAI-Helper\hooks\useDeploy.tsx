import { useState } from "react";
import type { FeishuLoginCookies } from "~utils/open-feishu-api/configuration";
import { Configuration } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";
import { DeployTool } from "~utils/open-feishu-api/deploy";
import { expect } from "vitest";


export const  defaultCode = {
    "name": "connectai-test",
    "desc": "test",
    "avatar": "https://s1-imfile.feishucdn.com/static-resource/v1/v2_b126c0fd-8f9d-49b0-9653-8c16caf0e28g",
    "events": [
        "im.message.message_read_v1",
        "im.message.receive_v1"
    ],
    "encryptKey":"e-fJKrqNbSz9NqSWL5",
    "verificationToken":"v-Ohw8k6KwVynNmzXX",
    "scopeIds": [
        "21001",
        "7",
        "21003",
        "21002",
        "20001",
        "20011",
        "3001",
        "20012",
        "6005",
        "20010",
        "3000",
        "20013",
        "20014",
        "20015",
        "20008",
        "1000",
        "1006",
        "1005",
        "20009"
    ],
    "cardRequestUrl":"https://ai-zhoulin.forkway.cn/feishu/64afc24d8cc7d8000190d6db/card",
    "verificationUrl":"https://ai-zhoulin.forkway.cn/feishu/64afc24d8cc7d8000190d6db/eventt"
};


export const useDeploy = () => {

  const [configNow, setConfigNow] = useState<string>("");

  const testConfig: FeishuLoginCookies = {
    lark_oapi_csrf_token: "bjDCGfwT/oYUc5cjnnGmkBtkUFW0Y8XKixpk3ktqGIc=",
    session: "XN0YXJ0-aa0g7686-6fe5-4582-bf08-c0760df940b2-WVuZA"
  };
  const configCookie = new Configuration(testConfig);
  const app = new OpenApp(configCookie);


  const initLoad = async () => {
  }

  const startDeploy = async (config:string) => {
    const configNowObj = JSON.parse(config);
    console.log(configNowObj);
    const deploy = new DeployTool(configNowObj as any);
    await deploy.loadOpenApi(app)
    await deploy.createAndDeploy()
    return true
  }
  return{
    configNow,
    setConfigNow,
    startDeploy,
  }

}
