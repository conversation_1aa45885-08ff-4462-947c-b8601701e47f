import { reactive, type Ref } from 'vue';
import type { PaginationProps } from 'naive-ui';

export default function usePagination({ itemCount }: { itemCount: Ref<number> }) {
  const paginationOptions = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    onUpdatePage: (page: number) => {
      paginationOptions.page = page;
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationOptions.pageSize = pageSize;
      paginationOptions.page = 1;
    },
    itemCount
  }) as PaginationProps;

  const pagination = reactive({
    get page() {
      return paginationOptions.page || 1;
    },
    get size() {
      return paginationOptions.pageSize || 10;
    }
  });

  return { pagination, paginationOptions };
}
