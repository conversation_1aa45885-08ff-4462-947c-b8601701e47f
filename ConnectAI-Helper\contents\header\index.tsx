import React from "react";
import LogoIcon from "react:~components/icon/logo.svg";
import { Button } from "@douyinfe/semi-ui";
import { IconClose } from "@douyinfe/semi-icons";
import { useStorage } from "@plasmohq/storage/dist/hook";
import { FEISHU_LOGIN } from "~constant";
import { openCenteredWindow } from "~utils/browser";

export const CustomHeader: React.FC = () => {
  const [isOpen, setIsOpen] = useStorage<boolean>("isOpen", (v) => v ?? false);

  const clickLogin = () => {
    openCenteredWindow(FEISHU_LOGIN)
  }
  return (
    <header>
      <div className="flex py-2 justify-between items-center px-4">
        <LogoIcon />
        <div className="flex items-center">
          {/*<div*/}
          {/*  style={{ marginRight: '8px' }}>*/}
          {/*  <Avatar*/}
          {/*    alt="beautiful cat"*/}
          {/*    src="https://lf3-static.bytednsdoc.com/obj/eden-cn/ptlz_zlp/ljhwZthlaukjlkulzlp/root-web-sites/dy.png"*/}
          {/*    size={'small'}*/}
          {/*  />*/}
          {/*</div>*/}
          <Button
            onClick={ clickLogin}
            theme='borderless' type='tertiary' style={{ marginRight: 8 }}>授权登录</Button>

          <Button
          onClick={ () => setIsOpen(false) }
          icon={ <IconClose /> }
          aria-label="关闭"
          theme="borderless" type="tertiary"
          className="flex justify-center items-center">
        </Button>
        </div>
      </div>
    </header>
  );
}

