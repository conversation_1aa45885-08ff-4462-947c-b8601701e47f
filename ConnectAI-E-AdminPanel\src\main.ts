import { createApp } from 'vue';
import { createHead } from '@vueuse/head';
import { isMobile } from '@/utils/common';
import App from './App.vue';
import AppLoading from './components/common/app-loading.vue';
import { setupDirectives } from './directives';
import { setupRouter } from './router';
import { setupAssets } from './plugins';
import { setupStore } from './store';
import { setupI18n, t, getLocale } from './locales';
import MobileLoginPrompt from './components/common/mobile-login-prompt.vue';

async function setupApp() {
  // import assets: js、css
  setupAssets();

  // app loading
  const appLoading = createApp(AppLoading);

  appLoading.mount('#appLoading');

  // 判断是否是手机登录
  const app = createApp(isMobile() ? MobileLoginPrompt : App);

  const lang = getLocale() as any;

  const head = createHead({
    title: t('message.system.title'),
    meta: [
      {
        name: 'description',
        content: t('message.system.description')
      }
    ],
    htmlAttrs: {
      lang: lang.value
    }
  });
  app.use(head);
  // store plugin: pinia
  setupStore(app);
  // vue custom directives
  setupDirectives(app);

  // vue router
  await setupRouter(app);

  setupI18n(app);

  if (import.meta.env.DEV) {
    app.mount('#app');
    return;
  }

  setTimeout(() => {
    app.mount('#app');
  }, 1100);
}

setupApp();
