import json
from langchain.agents.agent import Agent, Agent<PERSON>xe<PERSON>or, AgentOutputParser, BaseSingleActionAgent
from langchain.schema import AgentAction, AgentFinish, OutputParserException
from langchain.callbacks.base import BaseCallbackHandler
from langchain.callbacks.manager import CallbackManager
from langchain.chains.base import Chain
from langchain.llms.fake import FakeListLLM
from langchain.tools.base import BaseTool
from langchain.base_language import BaseLanguageModel
from langchain.schema import HumanMessage

from enum import Enum
from typing import Dict, Any


class ObjectDict(Dict[str, Any]):
    """Makes a dictionary behave like an object, with attribute-style access."""

    def __getattr__(self, name: str) -> Any:
        try:
            value = self[name]
            if isinstance(value, (list, tuple)):
                return [ObjectDict(i) if isinstance(i, dict) else i for i in value]
            return ObjectDict(value) if isinstance(value, dict) else value
        except KeyError:
            raise AttributeError(name)

    def __setattr__(self, name: str, value: Any) -> None:
        self[name] = value


class AppResult(Enum):
    Text = 1
    ReplyText = 2
    # streaming模式需要更新消息
    UpdateText = 3
    OpenAIMMenu = 4

    # mj
    CardWithButton = 11
    Image = 12


model = ObjectDict(
    streaming=True,
    openai_api_type="azure",
    openai_api_key='',
    openai_api_version='2023-03-15-preview',
    openai_api_base='https://proxy.forkway.cn',
    deployment_name='gpt-3.5-turbo',
)

platform = 'feishu'

def send_message(message_type, data):
    print('send_message', platform, message_type, data)


class CAgent(BaseSingleActionAgent):
    # 先继承一次
    @property
    def input_keys(self):
        return ["input"]

    def plan(self, intermediate_steps, **kwargs):
        if len(intermediate_steps) >= 1:
            action, result = intermediate_steps[-1]
        else:
            action, result = None, None
        # 这里将plan再抽象一次，变成一个next函数，主要是将最近一次action, result给抽取一下方便使用
        return self.next(action, result, intermediate_steps, **kwargs)

    async def aplan(self, intermediate_steps, **kwargs):
        return self.plan(intermediate_steps, **kwargs)


class CTool(BaseTool):
    # 先继承一下
    async def _arun(self, *args, **kwargs):
        return self._run(*args, **kwargs)


with open('/home/<USER>/connectai/manager-server/server/modules/application/openai.py', 'r') as f:
    content = f.read()
    print(content)
    g = ObjectDict(
        # model=model, # 这个在后面的globals里面有一份了
        chat_history=[
            # HumanMessage(content="Hi"),
        ],
        # TODO 处于安全考虑，后面这里还是进来先少量，做加法
        **globals(),
    )
    loc = {}
    # 加载app中的自定义逻辑
    exec(content, g, loc)

    platform = 'feishu'
    extra = {}
    data = '测试消息'
    agent, tools = None, []
    # 从locals拿到自定义app中定义的Agent以及Tool，并实例化
    for name in loc:
        value = loc[name]
        if issubclass(value, CAgent):
            agent = value(platform=platform, extra=extra, data=data)
        elif issubclass(value, CTool):
            tools.append(value())

    print('----------')
    print(agent, tools)

    # 从实例化之后的agent以及tools生成AgentExecutor
    agent_executor = AgentExecutor.from_agent_and_tools(
        agent=agent, tools=tools, verbose=True,
    )
    # 调用agent_executor
    result = agent_executor.run(data)
    print('result', result, type(result))

