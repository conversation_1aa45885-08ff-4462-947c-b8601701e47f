import json
import logging
import functools
import uuid
import pandas as pd
from io import Bytes<PERSON>
from inspect import getfullargspec, iscoroutinefunction
from typing import Union

import tornado.locale
from tornado.escape import utf8
from tornado.gen import is_coroutine_function
from tornado.options import options
from itertools import zip_longest
from sqlalchemy import and_, or_

from tornado.web import Request<PERSON><PERSON><PERSON>, Finish

from core import utils
from core.redisdb import redis_cli, pickle, stalecache
from settings.constant import SESSION_CACHE_EXPIRE
from .base_model import BaseModel, HandlerContext, MysqlModel
from .exception import (
    NotFound,
    ParametersError,
    FileTypeError,
    Duplicate,
    PermissionDenied,
    InternalError,
)
from .schema import User, ObjID, TenantAdmin
from .mysql import Session
from .dingding import DingDingCallbackError, TextMessage as DTextMessage


class WebSession(dict):

    def __init__(self, **kwargs):
        self.dirty = False
        super().__init__(**kwargs)

    def __setitem__(self, name, value):
        self.dirty = True
        super().__setitem__(name, value)

    def __delitem__(self, name, value):
        self.dirty = True
        super().__delitem__(name, value)

    def __getattr__(self, name):
        try:
            return self[name]
        except KeyError:
            raise AttributeError(name)


class SessionHandler(RequestHandler):

    session_id_prefix = 'sid'

    @property
    def session_id(self):
        return self.request.headers.get('X-Session-Id') or self.get_cookie('__sid__') or self.get_argument('__sid__', '')

    def _get_current_user_from_redis(self):
        if not hasattr(self, '_current_user_from_redis'):
            """根据cookie信息从redis中获取用户身份"""
            session_id = self.session_id
            if session_id:
                user_id = redis_cli().get('{}:{}:{}'.format(
                    options.REDIS_NAMESPACE,
                    self.session_id_prefix,
                    session_id
                ))
                if user_id:
                    try:
                        res = json.loads(user_id)
                        setattr(self, '_current_user_from_redis', res)
                        return getattr(self, '_current_user_from_redis')
                    except Exception as e:
                        logging.warning("error: %r user_id: %r", e, user_id)
                        setattr(self, '_current_user_from_redis', {
                            'user_id': user_id,
                        })
                        return getattr(self, '_current_user_from_redis')
        return getattr(self, '_current_user_from_redis', None)

    def get_current_user(self):
        user_id = None
        current_user = self._get_current_user_from_redis()
        if current_user:
            if ObjID.is_valid(current_user.get("user_id")):
                user_id = current_user.get("user_id")
        return user_id

    def gen_session_id(self, user_id=''):
        """生成session_id，存储于头部和redis"""
        expires_days = 7
        session_id = str(uuid.uuid4())
        session_key = '{}:{}:{}'.format(
            options.REDIS_NAMESPACE,
            self.session_id_prefix,
            session_id,
        )
        redis_cli().pipeline().set(
            # session_key, user_id or self.current_user
            session_key, json.dumps({
                'user_id': user_id or self.current_user,
            })
        ).expire(
            session_key, 3600 * 24 * expires_days
        ).execute()
        self.clear_all_cookies(path='/')
        self.set_cookie(
            name="__sid__",
            value=session_id,
            expires_days=expires_days,  # 过期时间设置为1个月
            # httponly=True,
            # secure=True,
        )
        self.set_header('X-Session-Id', session_id)
        return session_id

    def remove_session(self, session_id):
        """删除session"""
        if not session_id:
            session_id = self.session_id
            redis_cli().expire('{}:{}:{}'.format(
                options.REDIS_NAMESPACE,
                self.session_id_prefix,
                session_id
            ), -1)
        return session_id

    @property
    def session_key(self):
        return '{user_type}:user:{user_id}'.format(
            user_type=self.request.headers.get('X-Session-Type', 'shield'),
            user_id=self.current_user,
        )

    @property
    def session(self):
        if not hasattr(self, "_session"):
            self._session = WebSession(**self._get_user(self.current_user))
        return self._session

    @session.setter
    def session(self, value):
        self.set_session(value)

    def on_finish(self):
        if hasattr(self, "_session") and self._session.dirty:
            self.set_session()  # 这里不需要强行重新赋值，只需要检查自己的session.dirty就可以了
        super().on_finish()

    def set_session(self, value=None):
        if value:
            if not isinstance(value, WebSession):
                value = WebSession(**value)
            self._session = value  # need save
        else:
            if not self.session.dirty:
                return  # 没有传递参数的时候判断session.dirty是不是有变化
        session_key = "{}:{}".format(options.REDIS_NAMESPACE, self.session_key)
        redis_cli(False).pipeline().set(
            session_key, pickle.dumps(self._session)
        ).expire(
            session_key, self._session and SESSION_CACHE_EXPIRE or 0
        ).execute()

    @stalecache(stale=0, expire=SESSION_CACHE_EXPIRE, attr_key='session_key')
    def _get_user(self, current_user):
        return self.get_user(current_user)

    def get_user(self, current_user):
        raise NotImplementedError()

    @property
    def version(self):
        """获取版本号（由前端生成）"""
        return self.request.headers.get("X-Version", "")

    @property
    def real_ip(self):
        """获取真实ip地址"""
        return self.request.headers.get("X-Forwarded-For", self.request.headers.get("X-Real-Ip", self.request.remote_ip)).split(', ')[0]

    def get_user_locale(self):
        user_locale = self._get_locale()
        if user_locale in ['en', 'en_US']:
            return tornado.locale.get('en_US')
        if user_locale in ['zh', 'zh_CN']:
            return tornado.locale.get('zh_CN')
        return tornado.locale.get(options.DEFAULT_LOCALE)

    def _get_locale(self):
        user_locale = self.get_argument('lang', None) or self.get_cookie('__lang__')
        if self.current_user:
            locale = self.session.locale
            if not user_locale and locale:
                self.set_cookie(
                    name="__lang__",
                    value=locale,
                    expires_days=30,  # 过期时间设置为1个月
                    # httponly=True,
                    # secure=True,
                )
                return locale
            # 如果显式传进来的语言和session存的不一致，就更新一下
            # 确保不传的时候，使用session中的
            if user_locale and user_locale != locale:
                self.session['locale'] = user_locale
                self.set_cookie(
                    name="__lang__",
                    value=user_locale,
                    expires_days=30,  # 过期时间设置为1个月
                    # httponly=True,
                    # secure=True,
                )
        return user_locale



def authenticated(method):
    """检查是否登录"""
    @functools.wraps(method)
    def wrapper(self, *args, **kwargs):
        if not self.current_user:
            self.set_status(401, "请登录!")
            self.finish({"code": -1, "msg": "请登录!"})
            return None
        return method(self, *args, **kwargs)
    return wrapper


class JsonHandler(SessionHandler):
    """尝试从请求体中提取json"""

    def __init__(self, application, request, **kwargs):
        self._json_args = {}
        super().__init__(application, request, **kwargs)

    def set_default_headers(self):
        """设置默认头信息为json, 设置跨域方便调试"""
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        origin = self.request.headers.get('Origin')
        host = self.request.headers.get('Host')
        if origin and origin.split('/')[-1] != host:
            self.set_header("Access-Control-Allow-Origin", origin)
            self.set_header("Access-Control-Allow-Credentials", 'true')
            self.set_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE')
            self.set_header('Access-Control-Max-Age', '1728000')
            self.set_header("Access-Control-Allow-Headers",
                            "x-requested-with, x-device-id, x-version, x-session-id, x-app-id"
                            "content-encoding, content-length, content-type, "
                            "authorization, cache-control")

    def prepare(self):
        """尝试从请求体中提取json"""
        if self.request.method in ('GET', 'HEAD', 'OPTIONS'):
            return
        if "multipart/form-data" in self.request.headers.get("Content-Type", ""):
            return
        try:
            body = self.request.body.decode('utf8')
            self._json_args = body and json.loads(body) or {}
        except Exception as e:
            logging.info(e)

    def options(self, *args, **kwargs):
        pass


class BaseHandler(JsonHandler):
    """所有应用层Handler使用此类"""

    def get_user(self, current_user):
        session = Session()
        user = session.query(User).filter(User.id == current_user).first()
        is_admin = True if user and session.query(TenantAdmin.id).filter(
            TenantAdmin.tenant_id == user.tenant_id,
            TenantAdmin.user_id == current_user,
        ).scalar() else False
        session.close()
        if not user:
            raise NotFound('找不到用户')
        return {
            'id': user.id,
            'tenant_id': user.tenant_id,
            'is_admin': is_admin,
            'name': user.name,
            "user_info": utils.row2dict(user),
            "locale": self.get_argument('lang', 'zh_CN'),
        }

    def write(self, chunk: Union[str, bytes, dict]) -> None:
        """Writes the given chunk to the output buffer.

        To write the output to the network, use the `flush()` method below.

        If the given chunk is a dictionary, we write it as JSON and set
        the Content-Type of the response to be ``application/json``.
        (if you want to send JSON as a different ``Content-Type``, call
        ``set_header`` *after* calling ``write()``).

        Note that lists are not converted to JSON because of a potential
        cross-site security vulnerability.  All JSON output should be
        wrapped in a dictionary.  More details at
        http://haacked.com/archive/2009/06/25/json-hijacking.aspx/ and
        https://github.com/facebook/tornado/issues/1009
        """
        if self._finished:
            raise RuntimeError("Cannot write() after finish()")
        if isinstance(chunk, dict):
            # change our encode to fix Decimal type error
            # try translate 'msg' and 'content'
            _ = self.locale.translate
            if 'msg' in chunk:
                chunk['msg'] = _(chunk['msg'])
            if 'content' in chunk and isinstance(chunk['context'], str):
                chunk['content'] = _(chunk['content'])
            chunk = utils.json_encode(chunk)
            self.set_header("Content-Type", "application/json; charset=UTF-8")
        chunk = utf8(chunk)
        super().write(chunk)


class ExportHandler(BaseHandler):

    export = False
    def initialize(self, export=False):
        self.export = export

    def export_excel(self, rows, columns):
        data = [[row.get(name) for name, _ in columns] for row in rows]
        df = pd.DataFrame(data, columns=[title for _, title in columns])
        out = BytesIO()
        df.to_excel(out, index=True, index_label='序号')
        self.set_header('Content-Type', 'application/vnd.ms-excel')
        self.set_header('Content-Disposition', 'attachment; filename="download.xlsx"')
        self.finish(out.getvalue())

    def export_csv(self, rows, columns):
        data = [[row.get(name) for name, _ in columns] for row in rows]
        df = pd.DataFrame(data, columns=[title for _, title in columns])
        out = BytesIO()
        df.to_csv(out, index=True, index_label='序号')
        self.set_header('Content-Type', 'text/csv')
        self.set_header('Content-Disposition', 'attachment; filename="download.csv"')
        self.finish(out.getvalue())


def arguments(method):
    """从请求体自动装填被修饰方法的参数，自动类型转换，捕获异常"""
    @functools.wraps(method)
    async def wrapper(self, *args, **kwargs):
        spec = getfullargspec(method)
        filling_args = spec.args[len(args) + 1:]  # 切出需要填充的参数
        default_values = spec.defaults[-len(filling_args):] if spec.defaults else []  # 切出需要的默认值

        # 倒序，参数与默认值对齐
        for key, default in zip_longest(reversed(filling_args), reversed(default_values)):
            if key in kwargs:
                continue
            if isinstance(self._json_args, dict) and key in self._json_args:
                kwargs[key] = self._json_args.get(key)
                continue
            if isinstance(default, list):
                value = self.get_arguments(key, True) or default
            else:
                value = self.get_argument(key, default)
            kwargs[key] = value

        # 根据注解做类型转换
        model_dict = {}
        for key, value in kwargs.items():
            if key not in spec.annotations:
                continue
            annotations = spec.annotations.get(key)
            try:
                if issubclass(annotations, BaseModel):
                    model = annotations(context=HandlerContext(self))
                    kwargs[key] = model
                    model_dict[key] = model
                elif isinstance(value, list):
                    kwargs[key] = [annotations(item) for item in value if item != '']
                elif value:
                    kwargs[key] = annotations(value)
            except Exception as e:
                logging.warning(e)
                logging.info(f'{key} 字段期待类型为: {str(annotations)} 实际为: "{value}"')
                self.finish({'code': -1, 'msg': '参数错误'})
                return

        try:  # 捕获异常，关闭连接
            if iscoroutinefunction(method) or is_coroutine_function(method):
                response = await method(self, *args, **kwargs)
            else:
                response = method(self, *args, **kwargs)
            return response
        except ParametersError as e:
            logging.warn(e)
            if not self._finished:
                self.finish({'code': -1, 'msg': '参数错误'})
        except (NotFound, FileTypeError, Duplicate, PermissionDenied, InternalError) as e:
            if not self._finished:
                self.finish({'code': e.code, 'msg': e.msg})
        except DingDingCallbackError as e:
            # 钉钉机器人回调接口，可以直接返回错误消息，例如消息sign校验不通过等
            if not self._finished:
                self.finish(DTextMessage(content=str(e)))
        except Finish as e:
            raise e
        except Exception as e:
            logging.exception(e)
            if not self._finished:
                self.finish({'code': -1, 'msg': '内部错误'})
        finally:
            for key, model in model_dict.items():
                model.clear()

    return wrapper
