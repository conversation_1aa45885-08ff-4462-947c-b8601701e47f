#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Flask服务器
"""

try:
    print("导入模块...")
    import os
    import sys
    import json
    import sqlite3
    import hashlib
    from flask import Flask, request, jsonify, session
    from flask_cors import CORS
    print("模块导入成功")

    print("创建Flask应用...")
    app = Flask(__name__)
    CORS(app, supports_credentials=True, origins=['http://localhost:3200'])
    app.secret_key = 'test-secret-key'
    print("Flask应用创建成功")

    # 添加CORS预检请求处理
    @app.before_request
    def handle_preflight():
        if request.method == "OPTIONS":
            response = jsonify({})
            response.headers.add("Access-Control-Allow-Origin", "http://localhost:3200")
            response.headers.add('Access-Control-Allow-Headers', "Content-Type,Authorization")
            response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
            response.headers.add('Access-Control-Allow-Credentials', "true")
            return response

    @app.route('/')
    def index():
        return jsonify({"message": "Test Flask Server", "status": "running"})

    @app.route('/api/account/login', methods=['POST'])
    def login():
        try:
            data = request.get_json()
            email = data.get('email')
            passwd = data.get('passwd')

            print(f"收到登录请求: email={email}, passwd={passwd}")

            # 简单验证
            if email == "<EMAIL>" and passwd == "admin123":
                # 设置session
                session['user_id'] = 'admin-user-id'
                session['email'] = email
                session['tenant_id'] = 'default-tenant'

                return jsonify({
                    "code": 0,
                    "msg": "登录成功",
                    "type": "login"
                })
            else:
                return jsonify({
                    "code": -1,
                    "msg": "邮箱或密码错误"
                }), 401
        except Exception as e:
            print(f"登录错误: {e}")
            return jsonify({
                "code": -1,
                "msg": f"登录失败: {str(e)}"
            }), 500

    @app.route('/api/tenant/handler', methods=['GET'])
    def tenant_handler():
        """获取租户权限信息"""
        try:
            print("收到租户权限请求")

            # 模拟租户权限数据
            return jsonify({
                "code": 0,
                "msg": "success",
                "data": [
                    "app_management",
                    "user_management",
                    "log_management",
                    "sensitive_management",
                    "prompt_management"
                ],
                "product": "enterprise",
                "expired": "2025-12-31 23:59:59"
            })
        except Exception as e:
            print(f"租户权限错误: {e}")
            return jsonify({
                "code": -1,
                "msg": f"获取权限失败: {str(e)}"
            }), 500

    @app.route('/api/account/info', methods=['GET'])
    def account_info():
        """获取账户信息"""
        try:
            print("收到账户信息请求")

            return jsonify({
                "code": 0,
                "msg": "success",
                "data": {
                    "display_name": "ConnectAI管理员",
                    "email": "<EMAIL>"
                }
            })
        except Exception as e:
            print(f"账户信息错误: {e}")
            return jsonify({
                "code": -1,
                "msg": f"获取账户信息失败: {str(e)}"
            }), 500

    @app.route('/api/account/logout', methods=['DELETE'])
    def logout():
        """用户登出"""
        try:
            print("收到登出请求")
            session.clear()

            return jsonify({
                "code": 0,
                "msg": "success"
            })
        except Exception as e:
            print(f"登出错误: {e}")
            return jsonify({
                "code": -1,
                "msg": f"登出失败: {str(e)}"
            }), 500

    @app.route('/api/product', methods=['GET'])
    def get_products():
        """获取产品套餐"""
        try:
            print("收到产品套餐请求")

            return jsonify({
                "code": 0,
                "msg": "success",
                "data": [
                    {
                        "id": "enterprise",
                        "name": "企业版",
                        "price": 999,
                        "features": ["无限用户", "高级功能", "技术支持"]
                    }
                ]
            })
        except Exception as e:
            print(f"产品套餐错误: {e}")
            return jsonify({
                "code": -1,
                "msg": f"获取产品套餐失败: {str(e)}"
            }), 500

    print("启动Flask服务器...")
    app.run(host='0.0.0.0', port=3000, debug=True, use_reloader=False)

except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
