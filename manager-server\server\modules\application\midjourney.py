class MidjourneyChatTool(CTool):
    name: str = 'mj_chat'
    description: str = 'midjourney chat'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    self.result = token
                    if token:
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，%(progress)s%% ...', progress=token)))
                            )
                        )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # debug [[ChatGeneration(text='我是一个人工智能语言 型，被称作OpenAI GPT-3。我能够自动生成文章、回答问题、翻译语言等等。我具有自然语言处理的能力，可以与人类进行交流和互动。尽管我没有自己的想法和情感，但我可以根据输入的信息给出合理的回答和建议。希望我能够为您提供帮助。', generation_info=None, message=AIMessage(content='我是一个人工智能语言模型，被称作OpenAI GPT-3。我能够自动生成文章、回答问题、翻译语言等等。我具有自然语 处理的能力，可以与人类进行交流和互动。尽管我没有自己的想法和情感，但我可以根据输入的信息给出合理的回答和建议。希望我能够为您提供帮助。', additional_kwargs={}, example=False))]]
                # content='https://cdn.aigcfun.com/attachments/1092632390930808946/1127879667181944882/62660168-8cc6-430d-98f3-c5f84cf77104.png' additional_kwargs={'jobId': '443676', 'messageId': '1127879667970478102', 'imageURL': 'https://cdn.aigcfun.com/attachments/1092632390930808946/1127879667181944882/62660168-8cc6-430d-98f3-c5f84cf77104.png', 'status': 'completed'} example=False
                img_url = response.generations[0][0].text
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                is_upscale = True if 'action' in data.extra.extra and data.extra.extra.action.value.action == 'upscale' else False
                params = {
                    'jobId': additional_kwargs['jobId'],
                    'messageId': additional_kwargs['messageId'],
                }
                if platform == 'feishu':
                    # time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    # TODO feishu更换markdown，不用上传图片
                    with FeishuModel() as fmodel:
                        fmodel.init_by_bot_id(data.extra.bot_instance_id)
                        # feishu_client.upload_image('https://cdn.aigcfun.com/attachments/1092632390930808946/1127840952468381806/551a3df7-2791-4e7e-9243-1e6883d1ff4b.png')
                        try:
                            loop = asyncio.get_event_loop()
                        except Exception as e:
                            logging.error(e)
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                        img_key = loop.run_until_complete(functools.partial(fmodel.client.upload_image, img_url)())
                        contents = [
                            FeishuMessageImage(img_key=img_key, alt='图片'),
                        ]
                        if not is_upscale:
                            contents.append(
                                FeishuMessageAction(
                                    FeishuMessageButton('U1', value={'action': 'upscale', 'position': '1', **params}),
                                    FeishuMessageButton('U2', value={'action': 'upscale', 'position': '2', **params}),
                                    FeishuMessageButton('U3', value={'action': 'upscale', 'position': '3', **params}),
                                    FeishuMessageButton('U4', value={'action': 'upscale', 'position': '4', **params}),
                                ),
                            )
                            contents.append(
                                FeishuMessageAction(
                                    FeishuMessageButton('V1', value={'action': 'variations', 'position': '1', **params}),
                                    FeishuMessageButton('V2', value={'action': 'variations', 'position': '2', **params}),
                                    FeishuMessageButton('V3', value={'action': 'variations', 'position': '3', **params}),
                                    FeishuMessageButton('V4', value={'action': 'variations', 'position': '4', **params}),
                                ),
                            )
                            contents.append(
                                FeishuMessageAction(
                                    FeishuMessageButton('re-roll', value={'action': 'reroll', 'logId': data.extra.id}),
                                ),
                            )
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                *contents,
                                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                                header="Midjourney Bot🎉",
                            )
                        )
                else:
                    # 钉钉消息
                    # 之前尝试先上传图片在发卡片消息，但是卡片中的图，布局太奇怪，更换md格式布局正常，发送也更简单！！！
                    jobId = additional_kwargs['jobId']
                    messageId = additional_kwargs['messageId']
                    action_options = [
                        ('U1', 'upscale', 1),
                        ('U2', 'upscale', 2),
                        ('U3', 'upscale', 3),
                        ('U4', 'upscale', 4),
                        ('V1', 'variations', 1),
                        ('V2', 'variations', 2),
                        ('V3', 'variations', 3),
                        ('V4', 'variations', 4),
                        ('re-roll', 'reroll', 0),
                    ]
                    # 这些按钮，点击之后，直接在输入框回复，当前应用会解析字符串，变成和飞书那边统一的action格式的json数据
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            *[DingDingActionCardButton(
                                'dtmd://dingtalkclient/sendMessage?content={}'.format(
                                    quote('{}:{}:{}:{}'.format(action, data.extra.id, jobId, messageId)) if action == 'reroll' else quote('{}:{}:{}:{}'.format(action, position, jobId, messageId))
                                ),
                                title,
                            ) for title, action, position in action_options] if not is_upscale else [],
                            text='![图片]({})  \n🤖️：生成成功'.format(img_url),
                            title='Midjourney Bot🎉',
                        )
                    )

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [OpenAICallbackHandler()]
        chat = MJChat(**model)
        if 'action' in data.extra.extra:
            # "action": {"value": {"action": "reroll", "logid": "xxxxx"}}
            if data.extra.extra.action.value.action == 'reroll' and input:
                # 重新生成
                messages = [HumanMessage(content=input, additional_kwargs=dict(
                    action='imagine',
                    prompt=input,
                ))]
            else:
                # "action": {"value": {"action": "upscale", "jobId": "447593", "messageId": "117930018060832934", "position": "1"}}
                messages = [HumanMessage(content='', additional_kwargs=data.extra.extra.action.value)]
        else:
            messages = [HumanMessage(content=input, additional_kwargs=dict(
                action='imagine',
                prompt=input,
            ))]
        return chat(messages)


class DingdingCommand(CommandTool):

    next_tool_name: str = 'mj_chat'
    name: str = 'mj_chat_dingding_command'
    description: str = 'midjourney chat dingding command'

    def parse_command(self, input, action):
        # 这个函数不返回任何
        try:
            action, position, jobId, messageId = input.split(':')
            # 钉钉，只能回复字符串，如果符合格式，就模拟飞书点击按钮返回事件内容
            data['extra']['extra']['action'] = {'value': dict(
                action=action,
                position=position,
                jobId=jobId,
                messageId=messageId,
            )}
            if action == 'upscale' and app.duplicate(input):
                return 'duplicate',
        except Exception as e:
            pass
        return None,

    def on_duplicate(self):
        send_message(AppResult.ReplyText, _('🤖️：已放大图片，请勿重复操作...'))
        return 'duplicate upscale'


class FeishuCommand(CommandTool):

    next_tool_name: str = 'mj_chat'
    name: str = 'mj_chat_feishu_command'
    description: str = 'midjourney chat feishu command'

    def parse_command(self, input, action):
        try:
            if 'action' in data.extra.extra:
                action = data.extra.extra.action.value
                content = '{}:{}:{}:{}'.format(action.action, action.position, action.jobId, action.messageId)
                # "action": {"value": {"action": "upscale", "jobId": "447593", "messageId": "117930018060832934", "position": "1"}}
                app.update_content(content)
                if action.action == 'upscale' and app.duplicate(content):
                    return 'duplicate',
        except Exception as e:
            logging.error(e)

        return None,

    def on_duplicate(self):
        send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：已放大图片，请勿重复操作...'))),
                header='Midjourney Bot🎉',
            )
        )
        # 这个字符串会保存到chat_log里面
        return 'duplicate upscale'


class MJAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'AI绘画'
        name: str = 'midjourney'
        title: str = 'Midjourney绘图机器人'
        description: str = ''
        icon: str = 'https://pic1.forkway.cn/cdn/bot-logo4.svg'
        logo: str = 'https://mpic.forkway.cn/cdn/logo/midjourney.png'
        sorted: int = 3
        support_resource: List[object] = [dict(
            category=ModelCategory.Draw.value,
            scene=ModelCategory.Draw.value,
            title='AI绘画',
            tip='',
            required=True,
            resource=['Midjourney']
        )]
        support_bots: List[str] = ['feishu', 'dingding']

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='mj_chat_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='mj_chat_dingding_command', tool_input=kwargs, log="")
        # if platform == 'feishu' and self.parse_command(**kwargs):
        #     return AgentFinish({"output": "command"}, kwargs.get('input'))
        return AgentAction(tool='mj_chat', tool_input=kwargs, log="")


