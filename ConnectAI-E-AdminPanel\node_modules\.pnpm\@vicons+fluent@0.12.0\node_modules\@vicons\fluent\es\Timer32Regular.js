import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13 2a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-6zm3 9a1 1 0 0 1 1 1v6a1 1 0 1 1-2 0v-6a1 1 0 0 1 1-1zm12 7c0 6.627-5.373 12-12 12S4 24.627 4 18S9.373 6 16 6s12 5.373 12 12zm-2 0c0-5.523-4.477-10-10-10S6 12.477 6 18s4.477 10 10 10s10-4.477 10-10zm.708-11.707a1 1 0 1 0-1.414 1.414l2 2a1 1 0 1 0 1.414-1.415l-2-2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
