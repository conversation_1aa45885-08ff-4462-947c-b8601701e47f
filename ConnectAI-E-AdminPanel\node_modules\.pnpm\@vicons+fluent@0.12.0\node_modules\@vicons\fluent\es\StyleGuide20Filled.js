import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.068 5.993A2.005 2.005 0 0 1 6.484 3.54l5.46-1.47a1.995 1.995 0 0 1 2.447 1.414l2.54 9.522a2.005 2.005 0 0 1-1.415 2.452l-5.46 1.47a1.995 1.995 0 0 1-2.447-1.413l-2.54-9.522zM9 6.25a.75.75 0 1 0-1.5 0a.75.75 0 0 0 1.5 0zM5 15V9.618l1.772 6.64c.072.272.186.52.334.742H7a2 2 0 0 1-2-2zm-2.33-1.504L4 8.532V15c0 .338.056.662.159.965l-.075-.02a2 2 0 0 1-1.414-2.45z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'StyleGuide20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
