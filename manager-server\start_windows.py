#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows环境下的Manager Server启动脚本
解决Tornado在Windows上的兼容性问题
"""

import os
import sys
import asyncio
import platform

# 设置Windows兼容的事件循环
if platform.system() == 'Windows':
    # 在Windows上使用SelectorEventLoop
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# 添加server路径到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

def main():
    """主函数"""
    try:
        # 导入Tornado相关模块
        import tornado.ioloop
        import tornado.web
        import tornado.options
        from tornado.options import define, options
        
        # 设置基本配置
        define("SERVER_PORT", default=3000, help="Server port")
        define("DEBUG", default=True, help="Debug mode")
        
        # 解析命令行参数
        tornado.options.parse_command_line()
        
        # 创建简化的应用
        app = tornado.web.Application([
            (r"/", MainHandler),
            (r"/health", HealthHandler),
            (r"/api/login", LoginHandler),
            (r"/api/user/info", UserInfoHandler),
        ], debug=options.DEBUG)
        
        # 启动服务器
        print(f"🚀 Manager Server 启动中...")
        print(f"服务地址: http://localhost:{options.SERVER_PORT}")
        print(f"调试模式: {options.DEBUG}")
        print("按 Ctrl+C 停止服务器")
        
        app.listen(options.SERVER_PORT)
        tornado.ioloop.IOLoop.current().start()
        
    except KeyboardInterrupt:
        print("\\n⏹️  服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

class MainHandler(tornado.web.RequestHandler):
    """主页处理器"""
    def get(self):
        self.set_header("Content-Type", "application/json")
        self.write({
            "message": "ConnectAI Manager Server",
            "version": "1.0.0",
            "status": "running",
            "platform": platform.system()
        })

class HealthHandler(tornado.web.RequestHandler):
    """健康检查处理器"""
    def get(self):
        self.set_header("Content-Type", "application/json")
        self.write({"status": "healthy"})

class LoginHandler(tornado.web.RequestHandler):
    """登录处理器"""
    def post(self):
        try:
            import json
            import sqlite3
            import hashlib
            
            # 解析请求数据
            data = json.loads(self.request.body)
            email = data.get('email')
            password = data.get('password')
            
            if not email or not password:
                self.set_status(400)
                self.write({"error": "邮箱和密码不能为空"})
                return
            
            # 验证用户
            db_path = "../data/connectai.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            hashed_password = hashlib.md5(password.encode('utf-8')).hexdigest()
            cursor.execute("""
                SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name
                FROM account a
                JOIN tenant t ON a.tenant_id = t.id
                WHERE a.email = ? AND a.passwd = ? AND a.status = 0
            """, (email, hashed_password))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                self.set_header("Content-Type", "application/json")
                self.write({
                    "success": True,
                    "message": "登录成功",
                    "user": {
                        "id": user[0],
                        "name": user[1],
                        "email": user[2],
                        "tenant_id": user[3],
                        "tenant_name": user[4]
                    }
                })
            else:
                self.set_status(401)
                self.write({"error": "邮箱或密码错误"})
                
        except Exception as e:
            self.set_status(500)
            self.write({"error": str(e)})

class UserInfoHandler(tornado.web.RequestHandler):
    """用户信息处理器"""
    def get(self):
        try:
            import sqlite3
            
            # 简化版本，直接返回管理员信息
            db_path = "../data/connectai.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name, t.apikey
                FROM account a
                JOIN tenant t ON a.tenant_id = t.id
                WHERE a.email = '<EMAIL>'
            """)
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                self.set_header("Content-Type", "application/json")
                self.write({
                    "user": {
                        "id": user[0],
                        "name": user[1],
                        "email": user[2],
                        "tenant_id": user[3],
                        "tenant_name": user[4],
                        "tenant_apikey": user[5]
                    }
                })
            else:
                self.set_status(404)
                self.write({"error": "用户不存在"})
                
        except Exception as e:
            self.set_status(500)
            self.write({"error": str(e)})

if __name__ == "__main__":
    main()
