const knowledge: AuthRoute.Route = {
  name: 'knowledge',
  path: '/knowledge',
  component: 'basic',
  meta: {
    title: '知识库',
    icon: 'akar-icons:telescope',
    order: 5,
    hide: false,
    i18nTitle: 'message.routes.knowledge._value'
  },
  children: [
    {
      name: 'knowledge_app',
      path: '/knowledge/app',
      component: 'self',
      meta: { title: '知识库应用', requiresAuth: true, icon: 'akar-icons:shopping-bag', i18nTitle: '知识库应用' }
    },
    {
      name: 'knowledge_my',
      path: '/knowledge/my',
      component: 'self',
      meta: {
        title: '我的知识库',
        icon: 'akar-icons:infinity',
        i18nTitle: 'message.routes.knowledge.wdzsk',
        requiresAuth: true
      }
    },
    {
      name: 'knowledge_info',
      path: '/knowledge/info',
      component: 'self',
      meta: { title: '知识库管理', icon: 'mdi:document', hide: true, requiresAuth: true },
      children: []
    },
    {
      name: 'knowledge_appInfo',
      path: '/knowledge/appInfo',
      component: 'self',
      meta: {
        title: '知识库应用信息',
        icon: 'akar-icons:double-sword',
        hide: true,
        i18nTitle: '知识库应用信息',
        requiresAuth: true
      }
    }
  ]
};

export default knowledge;
