import { IconInfoCircle } from "@douyinfe/semi-icons";
import { Avatar, But<PERSON>, Card, Popover, Toast } from "@douyinfe/semi-ui";
import type { FC } from "react";
import { useDeploy } from "~hooks/useDeploy";
import { useState } from "react";

export interface AppCardProps {
  id: string
  title: string
  description: string
  avatar: string
}

const defaultCode = `{
    "name": "飞书-OpenAI",
    "desc": "一个飞书OpenAI机器人",
    "avatar": "https://s1-imfile.feishucdn.com/static-resource/v1/v2_b126c0fd-8f9d-49b0-9653-8c16caf0e28g",
    "events": [
        "im.message.message_read_v1",
        "im.message.receive_v1"
    ],
    "encryptKey":"UwM6vYMYyqLbTmNatwNjcfNT2T53U02n",
    "verificationToken":"eidEZjig7dbaQsgc9gADcwtny1YayigK",
    "scopeIds": [
        "21001",
        "7",
        "21003",
        "21002",
        "20001",
        "20011",
        "3001",
        "20012",
        "6005",
        "20010",
        "3000",
        "20013",
        "20014",
        "20015",
        "20008",
        "1000",
        "1006",
        "1005",
        "20009"
    ],
    "cardRequestUrl":"https://ai-feishu.forkway.cn/api/callback/lark/65/card",
    "verificationUrl":"https://ai-feishu.forkway.cn/api/callback/lark/65/event"
}`;

export const AppCard: FC = (data: AppCardProps) => {
  const [isLoading, setIsLoading] = useState(false)
  const { Meta } = Card
  const {configNow, setConfigNow, startDeploy} = useDeploy();
  const clickDeploy = () => {
    setIsLoading(true)
    startDeploy(defaultCode).then(() => {
      setIsLoading(false)
      Toast.success("部署成功");
    })
  };
  return (
    <div>
      <Card
        shadows="hover"
        style={{ minWidth: 400 }}
        bodyStyle={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between"
        }}>
        <Meta
          title={data.title}
          description={data.description}
          avatar={<Avatar alt={data.title} size="default" src={data.avatar} />}
        />
        <Button theme="borderless" loading={isLoading} type="primary" onClick={clickDeploy}>
          部署
        </Button>
        {/* <Popover
          position="left"
          showArrow
          content={<div style={{ padding: 6 }}>这是一个 Card</div>}>
          <IconInfoCircle style={{ color: "var(--semi-color-primary)" }} />
        </Popover> */}
      </Card>
    </div>
  )
}
