# 代码文档 - server/modules/account/handler.py

## 文件作用
账户管理模块的HTTP请求处理器，提供用户登录、注册、验证码、提示词管理等功能。

## 逐行代码解释

### 导入模块 (1-28行)
```python
#!/usr/bin/env python
# coding=utf-8
import asyncio                    # 异步编程支持
import json                       # JSON数据处理
import base64                     # Base64编码
import qrcode                     # 二维码生成
import logging                    # 日志记录
from time import time             # 时间戳
from io import BytesIO            # 字节流处理

from core.base_handler import (   # 基础处理器组件
    BaseHandler,                  # 基础处理器类
    ExportHandler,                # 导出处理器类
    arguments,                    # 参数装饰器
    authenticated,                # 认证装饰器
    WebSession,                   # Web会话类
)
from core.utils import ObjIDStr, DateStr  # 工具类型
from core.redisdb import delete   # Redis删除装饰器
from core.base_model import RedisModel  # Redis模型基类
from .model import (              # 账户相关模型
    AccountModel,                 # 账户模型
    PromptModel,                  # 提示词模型
    SensitiveModel,               # 敏感词模型
    ChatLogModel,                 # 聊天日志模型
    TenantPrivilegeModel,         # 租户权限模型
    ProductModel,                 # 产品模型
    TenantSeatModel,              # 租户座席模型
)
```

### AccountHandler类 - 账户处理器 (34-115行)
```python
class AccountHandler(BaseHandler):
    """账户登录、注册、信息管理处理器"""

    @arguments
    async def post(self, email: str = '', passwd: str = '', phone: str = '', code: str = '', model: AccountModel = None):
        """用户登录/注册接口 - 支持多种登录方式"""
        
        is_new = False  # 是否为新用户标志
        
        # 方式1: 邮箱+验证码+密码 --> 注册
        if email and code and passwd:
            user_id, is_new = await model.login_by_code(email, '', code, passwd=passwd, register=True)
            
        # 方式2: 邮箱/手机号+密码 --> 登录
        elif (email or phone) and passwd:
            user_id = await model.login(email, phone, passwd)
            
        # 方式3: 手机号+验证码 --> 快捷登录/注册
        elif phone and code:
            user_id, is_new = await model.login_by_code('', phone, code, passwd='', register=True)
            
        else:
            raise PermissionDenied('请输入正确的帐号')
        
        # 设置当前用户并生成会话
        self.current_user = user_id
        self.gen_session_id(user_id=user_id)

        # 根据是否为新用户返回不同响应
        if is_new:
            self.finish({'code': 0, 'msg': '注册成功', 'type': 'register'})
        else:
            self.finish({'code': 0, 'msg': '登录成功', 'type': 'login'})

    @authenticated
    @arguments
    async def delete(self, model: AccountModel = None):
        """用户登出接口"""
        # 清除所有Cookie，会话自动过期
        self.clear_all_cookies(path='/')
        self.finish({'code': 0, 'msg': 'success'})

    @authenticated
    @arguments
    async def put(self, display_name: str = '', model: AccountModel = None):
        """修改租户信息接口 - 当前只支持修改展示名"""
        
        # 检查展示名是否包含中文字符
        has_cn = False
        for _char in display_name:
            if '\u4e00' <= _char <= '\u9fa5':  # Unicode中文字符范围
                has_cn = True
                break

        # 验证展示名长度
        if has_cn and len(display_name) < 2:
            raise ParametersError('展示名不能小于2个字')
        elif not has_cn and len(display_name) < 1:
            raise ParametersError('Display name cannot be blank')

        # 更新租户信息
        await model.update(tenant_id=self.session.tenant_id, display_name=display_name)
        self.finish({'code': 0, 'msg': 'success'})

    @authenticated
    @arguments
    async def get(self, model: AccountModel = None):
        """获取租户信息接口"""
        tenant_info = await model.get_tenant_info(self.session.tenant_id)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': {
                "display_name": tenant_info.get('display_name', ''),
                "email": tenant_info.get('email', ''),
            },
        })
```

### AccountCodeHandler类 - 验证码处理器 (117-124行)
```python
class AccountCodeHandler(BaseHandler):
    """验证码发送处理器"""
    
    @arguments
    async def post(self, email: str = '', phone: str = '', model: AccountModel = None):
        """发送登录验证码接口"""
        # 发送验证码，使用Redis键防止重复发送
        await model.send_code(email, phone, key='send_login_code:{}:{}'.format(email, phone))
        self.finish({'code': 0, 'msg': 'success'})
```

### PromptCategoryHandler类 - 提示词分类处理器 (127-143行)
```python
class PromptCategoryHandler(BaseHandler):
    """提示词分类管理处理器"""

    @authenticated
    @arguments
    async def get(self, page: int = 1, size: int = 10, model: PromptModel = None):
        """获取提示词分类列表接口"""
        logging.info("debug session %r", self.session)
        
        # 获取当前租户和用户的提示词分类
        categories, total = await model.get_prompt_category(
            self.session.tenant_id,    # 租户ID
            self.session.id,           # 用户ID
            page, size,                # 分页参数
            lang=self._get_locale(),   # 语言设置
        )
        
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': categories,
            'total': total,
        })
```

### PromptHandler类 - 提示词处理器 (146-160+行)
```python
class PromptHandler(ExportHandler):
    """提示词管理处理器 - 继承自ExportHandler支持导出功能"""

    @authenticated
    @arguments
    async def get(self, prompt_id: ObjIDStr = '', category_id: ObjIDStr = '', keyword: str = '', 
                  page: int = 1, size: int = 20, model: PromptModel = None):
        """获取提示词列表接口"""
        
        prompts, total = await model.get_prompt_list(
            self.session.tenant_id,    # 租户ID
            self.session.id,           # 用户ID
            category_id,               # 分类ID过滤
            keyword,                   # 关键词搜索
            # 导出时忽略size限制，获取所有数据
            page, size if not self.is_export else 10000,
            lang=self._get_locale(),
        )
        
        # 根据是否为导出请求返回不同格式
        if self.is_export:
            return self.export_data(prompts)  # 导出Excel等格式
        else:
            self.finish({
                'code': 0,
                'msg': 'success',
                'data': prompts,
                'total': total,
            })
```

## 登录方式说明
1. **邮箱/手机号+密码登录**: 传统登录方式，适用于已注册用户
2. **手机号+验证码登录**: 快捷登录，支持自动注册
3. **邮箱+验证码+密码注册**: 邮箱注册方式，需要验证码验证

## 技术特点
- **多种认证方式**: 支持密码和验证码两种认证
- **自动注册**: 验证码登录时自动创建新用户
- **会话管理**: 基于Redis的分布式会话
- **国际化支持**: 根据用户语言返回相应内容
- **参数验证**: 自动验证和转换请求参数
- **导出功能**: 提示词支持Excel导出

## 安全特性
- **验证码防刷**: 使用Redis键防止重复发送验证码
- **会话保护**: 登出时清除所有Cookie
- **参数校验**: 严格的输入参数验证
- **权限控制**: 基于装饰器的认证和授权
