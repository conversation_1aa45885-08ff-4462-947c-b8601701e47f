import json
import logging
import hashlib
from base import *
from core.base_model import MysqlModel
from core.schema import (
    ApplicationWithActionTemplate,
    ApplicationImplementation,
    Bot, Resource,
    ApplicationSupportResource,
    ApplicationSupportBot,
    ApplicationCategory,
    text,
)
from tornado.ioloop import <PERSON><PERSON><PERSON>
from os.path import abspath, dirname
from modules.application.model import *
from core.mysql import get_engine_by_name
from settings.constant import ModelCategory


class InitApplication(MysqlModel):
    async def try_run_sql(self, sql):
        try:
            with get_engine_by_name('master').connect() as connection:
                connection.execute(text(sql))
                connection.commit()
        except Exception as e:
            logging.error(e)

    async def try_insert_record(self, Schema, uniq_key, id='', **data):
        if not isinstance(uniq_key, list):
            uniq_key = [uniq_key]
        filters = [getattr(Schema, k) == data.get(k, id) for k in uniq_key]
        record_id = self.session.query(Schema.id).filter(*filters).limit(1).scalar()
        if record_id:
            self.session.begin_nested()
            self.session.query(Schema).filter(Schema.id == record_id).update(data, synchronize_session=False)
            self.session.commit()
            return record_id
        record_id = id or ObjID.new_id()
        self.session.begin_nested()
        self.session.add(Schema(
            id=record_id,
            **data,
        ))
        self.session.commit()
        return record_id

    async def run(self):
        await self.init_application_by_file('openai.py')
        await self.init_application_by_file('openai1.py')
        await self.init_application_by_file('gpts.py')
        # await self.init_application_by_file('midjourney.py')  # deprecated
        await self.init_application_by_file('translate.py')
        await self.init_application_by_file('mjsd_prompt.py')
        await self.init_application_by_file('meeting_summary.py')
        await self.init_application_by_file('douyin_shop.py')
        await self.init_application_by_file('knowledge.py')
        await self.init_application_by_file('sdimage.py')
        await self.init_application_by_file('mjgoapi.py')
        await self.init_application_by_file('topic_summary.py')  # 18
        await self.init_application_by_file('chatpdf.py')  # 19
        await self.init_application_by_file('chat_video.py')
        await self.init_application_by_file('removebg.py')
        await self.init_application_by_file('removevocal.py')
        await self.init_application_by_file('upscaler.py')
        await self.init_application_by_file('removetext.py')
        # 客服
        await self.init_application_by_file('messenger.py')
        await self.init_application_by_file('bittosvg.py')
        await self.init_application_by_file('translate_document.py')
        await self.init_application_by_file('voice_friend.py')
        await self.init_application_by_file('snap_story.py')

    async def init_application_by_file(self, file='openai.py'):
        try:
            with open(dirname(dirname(abspath(__file__))) + '/modules/application/' + file, 'r') as f:
                code = f.read()
                g, loc = {**globals()}, {}
                exec(code, g, loc)
                for name in loc:
                    value = loc[name]
                    if issubclass(value, CAgent):
                        for attribute_name in value.__dict__:
                            attribute = value.__dict__[attribute_name]
                            if isinstance(attribute, type) and issubclass(attribute, BaseAppConfig):
                                await self.save_application_by_config(code, attribute())
        except Exception as e:
            logging.exception(e)

    async def save_application_by_config(self, code, config):
        hash = hashlib.md5((code).encode('utf-8')).hexdigest()
        app = {
            'name': config.name,
            'title': config.title,
            'title_en': config.title_en,
            'description': config.description,
            'description_en': config.description_en,
            'problem': config.problem,
            'problem_en': config.problem_en,
            'manual': config.manual,
            'manual_en': config.manual_en,
            'video': config.video,
            'video_en': config.video_en,
            'icon': config.icon,
            'logo': config.logo,
            'sorted': config.sorted,
            'feedback_url': config.feedback_url,
            'deploy': getattr(config, 'deploy', ''),
            'show': config.show,
            'action_template': getattr(config, 'action_template', ''),
            # 'wx_suite_id': config.wx_suite_id,
            # 'wx_suite_secret': config.wx_suite_secret,
        }
        logging.info('\nINIT application: %r %r', config.name, config.title)
        if getattr(config, 'category', ''):
            category_id = self.session.query(ApplicationCategory.id).filter(
                ApplicationCategory.name == config.category
            ).limit(1).scalar()
            if not category_id:
                raise Exception('app: {} category: {} not found!'.format(config.name, config.category))
            app['category_id'] = category_id
            logging.info('application_category %r %r', category_id, config.category)
        app_id = await self.try_insert_record(ApplicationWithActionTemplate, 'name', **app)
        logging.info("app_id %r", app_id)
        impl = {'application_id': app_id, 'version': 1, 'code': code, 'hash': hash}
        impl_id = await self.try_insert_record(ApplicationImplementation, 'application_id', **impl)
        logging.info("impl_id %r", impl_id)
        ## 支持的资源
        if len(config.support_resource) and isinstance(config.support_resource[0], str):
            resource_ids = [rid for rid, in self.session.query(Resource.id).filter(
                Resource.name.in_(config.support_resource),
            )]
            support_resource_ids = [await self.try_insert_record(
                ApplicationSupportResource, ['application_id', 'resource_id', 'scene'],
                application_id=app_id,
                resource_id=resource_id,
            ) for resource_id in resource_ids]
            logging.info("old support_resource_ids %r", support_resource_ids)
        else:
            """
            config.support_resource = {
                category: str,
                scene: str,
                title: str,
                tip: str,
                required: bool,
                resource: list[str],
            }
            """
            for i, r in enumerate(config.support_resource):
                resource_ids = [rid for rid, in self.session.query(Resource.id).filter(
                    Resource.name.in_(r['resource']),
                )]
                # # TODO
                # # 删除不在 support_resource 中的数据
                # delete_sql = 'delete from application_support_resource where resource_id not in {}'.format(
                #     json.dumps(resource_ids))
                # await self.try_run_sql(delete_sql)
                support_resource_ids = [await self.try_insert_record(
                    ApplicationSupportResource, ['application_id', 'resource_id', 'scene'],
                    application_id=app_id,
                    resource_id=resource_id,
                    category=r.get('category', ModelCategory.LLM.value),
                    scene=r.get('scene', ModelCategory.LLM.value),
                    title=r.get('title', '大语言模型'),
                    tip=r.get('tip', ''),
                    required=int(r.get('required', True)),
                    sorted=i,
                ) for resource_id in resource_ids]
                logging.info("support_resource_ids %r %r", r.get('scene', ModelCategory.LLM.value), support_resource_ids)

        ## 支持的平台
        bot_ids = [bid for bid, in self.session.query(Bot.id).filter(
            Bot.platform.in_(config.support_bots),
        )]
        support_bot_ids = [await self.try_insert_record(
            ApplicationSupportBot, ['application_id', 'bot_id'],
            application_id=app_id,
            bot_id=bot_id,
        ) for bot_id in bot_ids]
        logging.info("support_bot_ids %r", support_bot_ids)


async def main():
    model = InitApplication()
    await model.run()


if __name__ == "__main__":
    IOLoop.current().run_sync(main)


