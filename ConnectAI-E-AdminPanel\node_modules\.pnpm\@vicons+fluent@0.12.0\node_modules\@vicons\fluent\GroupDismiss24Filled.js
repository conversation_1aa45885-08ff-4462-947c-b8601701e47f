'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M7 5a2 2 0 1 1-4 0a2 2 0 0 1 4 0zm14 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0zM7 19a2 2 0 1 1-4 0a2 2 0 0 1 4 0zM8 5c0 .35-.06.687-.17 1h4.67a.5.5 0 0 1 .5.5V9h-1.5A2.5 2.5 0 0 0 9 11.5V13H6.5a.5.5 0 0 1-.5-.5V7.83a2.995 2.995 0 0 1-2 0v4.67A2.5 2.5 0 0 0 6.5 15H9v2.5a2.5 2.5 0 0 0 2.498 2.5A6.48 6.48 0 0 1 11 17.5v-6a.5.5 0 0 1 .5-.5h6c.886 0 1.73.177 2.5.498A2.5 2.5 0 0 0 17.5 9H15V6.5A2.5 2.5 0 0 0 12.5 4H7.83c.11.313.17.65.17 1zm15 12.5a5.5 5.5 0 1 1-11 0a5.5 5.5 0 0 1 11 0zm-7.146-2.354a.5.5 0 0 0-.708.708l1.647 1.646l-1.647 1.646a.5.5 0 0 0 .708.708l1.646-1.647l1.646 1.647a.5.5 0 0 0 .708-.708L18.207 17.5l1.647-1.646a.5.5 0 0 0-.708-.708L17.5 16.793l-1.646-1.647z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'GroupDismiss24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
