import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11 2.25a.75.75 0 0 1 .704.49l3.5 9.5a.75.75 0 0 1-1.408.52l-.924-2.51H9.128l-.924 2.51a.75.75 0 1 1-1.408-.52l3.5-9.5A.75.75 0 0 1 11 2.25zm0 2.92L9.68 8.75h2.64L11 5.17zM6.125 8.873v3.8c-.035.082-.121.26-.192.317a.623.623 0 0 1-.348.169h-.009l-.076.005c-.3 0-.55-.21-.61-.49c-.607.321-1.18.49-1.723.49c-1.292 0-2.292-.915-2.292-2.291c0-.605.209-1.138.595-1.54c.386-.401.94-.665 1.621-.747a5.1 5.1 0 0 1 1.775.102c-.026-.263-.109-.45-.244-.582c-.164-.16-.425-.256-.82-.276c-.633-.031-1.049.06-1.278.221a.625.625 0 0 1-.718-1.023c.469-.329 1.096-.467 1.863-.453h.002l.193.007c.676.034 1.229.246 1.62.619c.392.374.61.897.638 1.53v.002l.003.14zm-3.732 1.328c-.177.174-.268.407-.268.696c0 .338.109.595.286.768c.178.173.436.274.756.274c.417 0 .951-.195 1.598-.622l.11-.074v-1.235l-.112-.033a3.913 3.913 0 0 0-1.521-.124c-.393.047-.67.174-.849.35z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextChangeCase16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
