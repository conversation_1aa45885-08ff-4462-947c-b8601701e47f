import json
import hmac
import base64
import hashlib
import logging
from urllib.parse import quote
from typing import Dict, Any
from uuid import uuid4
from core.utils import ObjectDict
from core.redisdb import stalecache
from core.exception import InternalError
from tornado.httpclient import AsyncHTTPClient, HTTPRequest
from urllib3.filepost import encode_multipart_formdata


DING_HOST = 'https://api.dingtalk.com'


class BaseMessage(Dict):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._init_at()

    def __getattr__(self, name: str) -> Any:
        try:
            return self[name]
        except KeyError:
            raise AttributeError(name)

    def __setattr__(self, name: str, value: Any) -> None:
        self[name] = value

    def _init_at(self):
        if 'at' not in self:
            self['at'] = {
                'atMobiles': [],
                'atUserIds': [],
                'isAtAll': False,
            }

    def at(self, userid):
        self.at_user(userid)

    def at_user(self, userid):
        self['at']['atUserIds'].append(str(userid))

    def at_mobile(self, mobile):
        self['at']['atMobiles'].append(str(mobile))

    def __str__(self):
        return json.dumps(self)


class TextMessage(BaseMessage):

    def __init__(self, *args, content='', **kwargs):
        super().__init__(*args, **kwargs)
        self['msgtype'] = 'text'
        self['text'] = {
            'content': content
        }

    @property
    def content(self):
        return self['text']['content']

    def set_content(self, content):
        self['text']['content'] = content


class MarkdownMessage(BaseMessage):

    def __init__(self, *args, title='', text='', **kwargs):
        super().__init__(*args, **kwargs)
        self['msgtype'] = 'markdown'
        self['markdown'] = {
            'title': title,
            'text': text
        }

    @property
    def title(self):
        return self['markdown']['title']

    @property
    def text(self):
        return self['markdown']['text']

    def set_title(self, title):
        self['markdown']['title'] = title

    def set_text(self, text):
        self['markdown']['text'] = text


class DingDing(object):

    def __init__(self, agent_id, app_key, app_secret):
        self.agent_id = agent_id
        self.app_key = app_key
        self.app_secret = app_secret

    def calc_sign(self, timestamp):
        msg = "{}\n{}".format(timestamp, self.app_secret).encode()
        h = hmac.new(self.app_secret.encode(), msg=msg, digestmod=hashlib.sha256)
        return base64.b64encode(h.digest()).decode()

    def validate_sign(self, timestamp, sign):
        return sign == self.calc_sign(timestamp)

    async def reply(self, sessionWebhook, message):
        request = HTTPRequest(
            sessionWebhook,
            method='POST',
            body=json.dumps(message),
            headers = {
                'Content-Type': 'application/json',
            }
        )
        logging.info("Request: %r %r", request.headers, request.body)
        response = await AsyncHTTPClient().fetch(request)
        logging.info("Response: %r", response.body)
        return ObjectDict(json.loads(response.body.decode()))

    async def reply_text(self, sessionWebhook, text):
        return await self.reply(sessionWebhook, TextMessage(content=text))

    async def reply_markdown(self, sessionWebhook, title, text):
        return await self.reply(sessionWebhook, MarkdownMessage(title=title, text=text))

    @property
    def access_token_cache_key(self):
        return "connectai:ding:access_token:{}".format(self.app_key)

    @stalecache(expire=600, stale=1800, attr_key="access_token_cache_key", namespace='connectai')
    async def get_tenant_access_token(self):
        """获取 tenant_access_token"""
        # https://open.dingtalk.com/document/orgapp/obtain-the-access_token-of-an-internal-app?spm=ding_open_doc.document.0.0.66fe4a97E7FClg
        url = '{api}//v1.0/oauth2/accessToken'.format(
            api=DING_HOST,
        )
        request = HTTPRequest(url, method='POST', body=json.dumps({
            'appKey': self.app_key,
            'appSecret': self.app_secret,
        }), headers={'Content-Type': 'application/json'})
        try:
            response = await AsyncHTTPClient().fetch(request)
        except Exception as e:
            raise InternalError("获取 accessToken 失败")
        result = json.loads(response.body.decode())
        logging.info(result)
        if "accessToken" not in result:
            raise InternalError("获取 accessToken 失败")
        return result.get("accessToken")

    async def request(self, url, body=None, method=''):
        tenant_access_token = await self.get_tenant_access_token()
        headers = {
            'Content-Type': 'application/json',
            'x-acs-dingtalk-access-token': tenant_access_token,
        }
        if not method:
            method = 'POST' if body else 'GET'
        request = HTTPRequest(
            url,
            method=method,
            body=json.dumps(body) if body else None,
            headers=headers,
        )
        logging.info("Request: %r %r", request.headers, request.body)
        response = await AsyncHTTPClient().fetch(request, raise_error=False)
        logging.info("Response: %r %r", response.headers['Content-Type'], response.body[:200] if len(response.body) >= 1000 else response.body)
        if 'application/json' not in response.headers['Content-Type']:
            return response.body
        return ObjectDict(json.loads(response.body.decode()))

    async def send_card(
        self,
        cardTemplateId='TuWenCard04',
        openConversationId='',
        singleChatReceiver='',
        outTrackId='',
        robotCode='',
        callbackUrl='',
        cardData=dict(),
        sendOptions=dict(),
    ):
        body = dict(
            cardTemplateId=cardTemplateId,
            outTrackId=outTrackId or str(uuid4()),
            robotCode=robotCode,
            cardData=json.dumps(cardData),
            sendOptions=sendOptions,

        )
        if openConversationId:
            body['openConversationId'] = openConversationId
        elif singleChatReceiver:
            body['singleChatReceiver'] = json.dumps(singleChatReceiver)
        if callbackUrl:
            body['callbackUrl'] = callbackUrl
        if sendOptions:
            body['sendOptions'] = sendOptions

        return await self.request(DING_HOST + '/v1.0/im/interactiveCards/templates/send', body=body)

    async def upload_image(self, img_url):
        # https://open.dingtalk.com/document/orgapp/upload-media-files
        # https://oapi.dingtalk.com/media/upload
        file_response = await AsyncHTTPClient().fetch(img_url)
        file_content = file_response.body
        return await self.upload_image_binary(file_content)

    async def upload_image_binary(self, file_content):
        tenant_access_token = await self.get_tenant_access_token()
        url = 'https://oapi.dingtalk.com/media/upload?access_token={}'.format(tenant_access_token)
        encoded, content_type = encode_multipart_formdata([
            ('media', ('image', file_content)),
            ('type', 'file'),
        ])
        request = HTTPRequest(
            url,
            method='POST',
            body=encoded,
            headers={'Content-Type': content_type},
        )
        response = await AsyncHTTPClient().fetch(request, raise_error=False)
        logging.info("Response: %r %r", response.headers, response.body)
        result = ObjectDict(json.loads(response.body.decode()))
        return result.media_id

    async def get_message_resource(self, robotCode, downloadCode):
        # https://open.dingtalk.com/document/isvapp/download-the-file-content-of-the-robot-receiving-message
        try:
            # Body must be None for method GET (unless allow_nonstandard_methods is true)
            # # image | file
            response = await self.request('{}/v1.0/robot/messageFiles/download'.format(
                DING_HOST
            ), method='POST', body={
                'robotCode': robotCode,
                'downloadCode': downloadCode,
            })
            logging.info('logging dingding res: {} {}'.format(robotCode, downloadCode, response.downloadUrl))
            return response.downloadUrl
        except Exception as e:
            logging.error(e)
            if hasattr(e, 'response'):
                logging.error("response: %r", e.response.body)
            return ''


# 以下为钉钉消息
class DingDingCardHeader(Dict):

    def __init__(self, text='', icon='', **kwargs):
        if isinstance(text, str):
             text = dict(zh_Hans=text)
        if isinstance(icon, str):
             icon = dict(light=icon, dark=icon)
        super().__init__(text=text, icon=icon, **kwargs)


class DingDingTuWenCard(Dict):

    def __init__(self, *contents, header='', actions=list(), actionDirection='VERTICAL', **kwargs):
        params = {
            'contents': contents,
            'actions': actions,
            'actionDirection': actionDirection,
        }
        if header:
            if isinstance(header, str):
                header = DingDingCardHeader(header)
            params['header'] = header
        super().__init__(params)


class DingDingCardBaseText(Dict):

    def __init__(self, *args, text='', **kwargs):
        if isinstance(text, str):
             text = dict(zh_Hans=text)
        super().__init__(*args, text=text, **kwargs)


class DingDingCardText(DingDingCardBaseText):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, type='PARAGRAPH', **kwargs)


class DingDingCardTitle(DingDingCardBaseText):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, type='TITLE', **kwargs)


class DingDingCardDescription(DingDingCardBaseText):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, type='DESCRIPTION', **kwargs)


class DingDingCardImage(Dict):

    def __init__(self, image='', **kwargs):
        super().__init__(image=image, type='IMAGE')


class DingDingCardMarkdown(Dict):

    def __init__(self, markdown='', **kwargs):
        super().__init__(markdown=markdown, type='MARKDOWN')


class DingDingCardBaseAction(Dict):

    def __init__(self, text='', id='', status='NORMAL', **kwargs):
        if isinstance(text, str):
            text = dict(zh_Hans=text)
        super().__init__(id=id, text=text, status=status, **kwargs)


class DingDingCardUrlAction(DingDingCardBaseAction):

    def __init__(self, *args, url='', **kwargs):
        super().__init__(*args, actionUrl=url, actionType='URL', **kwargs)


class DingDingCardRequestAction(DingDingCardBaseAction):

    def __init__(self, *args, **kwargs):
        super().__init__(actionType='URL', *args, **kwargs)


class DingDingCardDtmdAction(DingDingCardBaseAction):

    def __init__(self, *args, dtmdLink='', **kwargs):
        super().__init__(*args, dtmdLink=dtmdLink, actionType='DTMD', **kwargs)


class DingDingCallbackError(Exception):
    """钉钉机器人回调异常"""
    pass


if __name__ == "__main__":

    m = TextMessage()
    m.at_mobile(15926399336)
    m.set_content('test Message')
    print(m)

    m = MarkdownMessage()
    m.at_mobile(15926399336)
    m.set_title('杭州天气')
    m.set_text('#### 杭州天气 @150XXXXXXXX \n> 9度，西北风1级，空气良89，相对温度73%\n> ![screenshot](https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png)\n> ###### 10点20分发布 [天气](https://www.dingalk.com) \n')
    print(m)



