const bot: AuthRoute.Route = {
  name: 'bot',
  path: '/bot',
  component: 'basic',
  children: [
    {
      name: 'bot_market',
      path: '/bot/market',
      component: 'self',
      meta: {
        title: '应用市场',
        requiresAuth: true,
        icon: 'akar-icons:shopping-bag',
        i18nTitle: 'message.routes.bot.yysc'
      },
      children: []
    },
    {
      name: 'bot_common',
      path: '/bot/common',
      component: 'self',
      meta: {
        title: '常用机器人',
        requiresAuth: true,
        icon: 'akar-icons:star',
        hide: true,
        i18nTitle: 'message.routes.bot.cyjqr'
      },
      children: []
    },
    {
      name: 'bot_my',
      path: '/bot/my',
      component: 'self',
      meta: {
        title: '我的应用',
        requiresAuth: true,
        icon: 'akar-icons:lifesaver',
        i18nTitle: 'message.routes.bot.wdyy'
      },
      children: []
    },
    {
      name: 'bot_info',
      path: '/bot/info',
      component: 'self',
      meta: {
        title: '应用信息',
        icon: 'akar-icons:double-sword',
        hide: true,
        i18nTitle: 'message.routes.bot.yyxx',
        requiresAuth: true
      },

      children: []
    }
  ],
  meta: {
    title: 'AI 应用',
    icon: 'akar-icons:grid',
    order: 3,
    i18nTitle: 'message.routes.bot._value',
    requiresAuth: true
  }
};

export default bot;
