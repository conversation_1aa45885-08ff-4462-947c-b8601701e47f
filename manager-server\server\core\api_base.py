
class ApiBase(object):

    def __init__(self, name='Azure', base='forkway.cn', schema='https'):
        self.name = name
        self.base = base
        self.schema = schema

    @property
    def url(self):
        name = self.name.lower()
        if self.name == '文心一言':
            name = 'wenxin'
        elif self.name == '百川大模型':
            name = 'baichuan'
        elif self.name == '360智脑':
            name = '360'
        elif self.name == 'Midjourney':
            name = 'mjf'
        elif self.name == '星火认知大模型':
            name = 'xinghuo'
        elif self.name in ['Stable Diffusion', 'SDImage']:
            name = 'sdimage'
        elif self.name == 'BibiGPT':
            name = 'bibigpt'
        elif self.name == '商汤日日新':
            name = 'sensenova'
        elif self.name == '通义千问':
            name = 'qwen'
        elif self.name == 'Vectorizer.AI':
            name = 'vectorizer'
        elif self.name == '紫东太初':
            name = 'taichu'

        stuff = ''
        if self.name == '文心一言':
            stuff = '/rpc/2.0/ai_custom/v1/wenxinworkshop'
        elif self.name == 'Midjourney':
            stuff = '/mj'
        elif self.name in ['ChatGLM', 'MiniMax','商汤日日新']:
            stuff = '/v1'
        elif self.name in ['Stable Diffusion', 'SDImage']:
            stuff = '/sd'

        return '{}://{}.{}{}'.format(self.schema, name, self.base, stuff)

    def __str__(self):
        return self.url


class NewApiBase(ApiBase):
    def __init__(self, name='Azure', base='ai2e.cn', schema='https'):
        super().__init__(name=name, base=base, schema=schema)


if __name__ == "__main__":
    print('Azure', ApiBase('Azure'))
    print('文心一言', ApiBase('文心一言'))
    print('ChatGLM', ApiBase('ChatGLM'))
    print('MiniMax', ApiBase('MiniMax'))
    print('星火认知大模型', ApiBase('星火认知大模型'))
    print('RWKV', ApiBase('RWKV'))
    print('Midjourney', ApiBase('Midjourney'))
    print('Claude', ApiBase('Claude'))
    print('OpenAI', ApiBase('OpenAI'))
    print('Stable Diffusion', ApiBase('Stable Diffusion'))
    print('360智脑', ApiBase('360智脑'))
    print('商汤日日新', ApiBase('商汤日日新'))
    print('百川大模型', ApiBase('百川大模型'))


