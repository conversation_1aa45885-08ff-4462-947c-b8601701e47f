import json
import httpx
import logging
import sys
import warnings
from typing import (
    Any,
    Dict,
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)
from tornado.options import options
from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage
from langchain.schema import AIMessage, SystemMessage, HumanMessage


logger = logging.getLogger(__name__)


class TaichuClient(object):

    def build_query(
        self,
        api_base='https://ai-maas.wair.ac.cn/maas',
        **kwargs
    ):
        headers = {
            'Content-Type': 'application/json',
        }
        if 'https://taichu' != api_base[:14]:
            headers['api-base'] = api_base
            from core.api_base import NewApiBase
            api_base = NewApiBase('紫东太初').url
        headers['api-key'] = kwargs['api_key']
        body = json.dumps(kwargs)
        return '{}/v1/model_api/invoke'.format(api_base), body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                if 'data:' == line[:5]:
                    yield line[5:].replace('\\n', '\n')
                elif 'event:finish' == line:
                    break
                # yield json.loads(line)

    def create(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)   # 没有stream模式 直接改成false
        # print(url, data, headers)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()['data']['content']

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield line[5:].replace('\\n', '\n')
                elif 'event:finish' == line:
                    break
                # yield json.loads(line)


    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class TaichuChat(SimpleChatModel):

    client: Any  #: :meta private:
    model_code: str = "taichu_llm_8b"    # 两种模型taichu_llm_8b/taichu_vqa_10b
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None

    api_base: Optional[str] = None

    max_retries: int = 3
    prefix_messages: List = Field(default_factory=list)

    streaming: bool = False

    picture: str = ""
    temperature: float = 0.8       # 默认0.9，范围 (0, 1.0]，不能为0
    top_p: float = 0.9             # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    repetition_penalty: float = 1.5

    def _llm_type(self) -> str:
        return "taichu_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': True,  # self.streaming,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'model_code': self.model_code,
            'top_p': self.top_p,
            # 'temperature': self.temperature,
            # 'repetition_penalty': self.repetition_penalty,
            # 'picture': self.picture,
        }

        if messages:
            last_message = messages[-1]
            if hasattr(last_message, 'additional_kwargs'):
                params.update(last_message.additional_kwargs)

        # 1. 一种模式是找出system message作为context，剩余消息全部是question
        '''
        context, question = '', ''
        for message in messages:
            if isinstance(message, SystemMessage):
                context += f"{message.content}\n\n"
            else:
                question += f"{message.type}: {message.content}\n\n" '''

        # 2. 第二种模式，只有最后一条消息作为question，前面的都是context
        context = '\n\n'.join([f"{message.type}: {message.content}" for message in messages[:-1]])
        question = messages.pop().content

        self.client = TaichuClient()

        response = ""
        # 默认只支持streaming模式
        for stream_resp in completion_with_retry(self, question=question, context=context, **params):
            token = stream_resp
            response += stream_resp
            if run_manager:
                run_manager.on_llm_new_token(token)
        return response


if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    async def main():
        from langchain.schema import HumanMessage
        from core.api_base import NewApiBase


        api_key = ''
        api_base = 'https://ai-maas.wair.ac.cn/maas'
        # api_base = NewApiBase('MiniMax').url

        chat = TaichuChat(
            api_base=api_base,
            api_key=api_key,
            streaming=False,
        )
        # messages = [HumanMessage(content='你是谁')]
        messages = [HumanMessage(content='推荐西藏旅游线路')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


