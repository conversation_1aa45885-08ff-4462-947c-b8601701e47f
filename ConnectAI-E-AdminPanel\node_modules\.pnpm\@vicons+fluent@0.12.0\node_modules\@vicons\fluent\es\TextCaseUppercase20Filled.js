import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.495 3.25a.75.75 0 0 1 .698.504l3.652 10.5a.75.75 0 1 1-1.417.492L8.647 12.5H4.06l-.86 2.266a.75.75 0 0 1-1.402-.532l3.984-10.5a.75.75 0 0 1 .712-.484zM4.63 11h3.495L6.454 6.195L4.63 11zM12 3.5a.75.75 0 0 0-.75.75V14.5c0 .414.336.75.75.75h2.75a3.5 3.5 0 0 0 1.714-6.552A3.125 3.125 0 0 0 14.125 3.5H12zm3.75 3.125c0 .897-.727 1.625-1.625 1.625H12.75V5h1.375c.898 0 1.625.728 1.625 1.625zm-1 7.125h-2v-4h2a2 2 0 1 1 0 4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseUppercase20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
