'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M13 24a1.998 1.998 0 0 0 2 2h5a2 2 0 0 0 2-2v-1l2.4 1.8A1 1 0 0 0 26 24v-7c0-.654-.593-1.086-1.168-.988a.997.997 0 0 0-.432.188L22 18v-1a2 2 0 0 0-2-2h-5a2 2 0 0 0-2 2v7zM2.332 19.5a12.028 12.028 0 0 0 7.6 6.104c-1.059-1.42-1.907-3.553-2.413-6.104H2.332zm9.367 5.852c.04.04.08.076.12.11A3.486 3.486 0 0 1 11.5 24v-4.5H9.05c.276 1.3.644 2.46 1.078 3.437c.492 1.107 1.039 1.91 1.57 2.415zM11.5 18H8.783a27.782 27.782 0 0 1-.283-4c0-.86.037-1.695.108-2.5h8.784c.057.648.092 1.316.104 2H15a3.5 3.5 0 0 0-3.5 3.5v1zm5.717-8H8.783c.28-1.913.75-3.599 1.345-4.937c.492-1.107 1.039-1.91 1.57-2.416C12.223 2.15 12.66 2 13 2c.34 0 .777.149 1.301.647c.532.506 1.079 1.31 1.57 2.416c.595 1.338 1.067 3.024 1.346 4.937zm-9.95 0c.443-3.214 1.405-5.914 2.665-7.604A12.026 12.026 0 0 0 1.682 10h5.586zm11.465 0c-.442-3.214-1.404-5.914-2.664-7.604A12.026 12.026 0 0 1 24.318 10h-5.586zm.264 3.5a30.046 30.046 0 0 0-.099-2h5.842a12.048 12.048 0 0 1 .251 2.995c-.507 0-1.026.157-1.49.505l-.428.321A3.5 3.5 0 0 0 20 13.5h-1.004zM7 14c0-.854.035-1.69.103-2.5H1.26C1.09 12.306 1 13.143 1 14c0 1.402.24 2.749.683 4h5.585A29.418 29.418 0 0 1 7 14z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'GlobeVideo28Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
