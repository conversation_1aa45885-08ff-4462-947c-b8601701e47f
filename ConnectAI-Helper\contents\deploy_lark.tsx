// @ts-nocheck
import { useCallback, useEffect, useMemo, useState } from "react";
import { LARK_DOMAIN as domain } from '~utils/browser'
import { sendToBackground } from "@plasmohq/messaging";
import cssText from "data-text:~/contents/deploy.css";
import type {
  PlasmoCSConfig,
  PlasmoGetOverlayAnchor,
  PlasmoWatchOverlayAnchor
} from "plasmo";
import { Toast } from "@douyinfe/semi-ui";

export const config: PlasmoCSConfig = {
    matches: ["<all_urls>"]
};

// Inject into the ShadowDOM
export const getStyle = () => {
  const style = document.createElement("style");
  style.textContent = cssText;
  return style;
};

export const watchOverlayAnchor: PlasmoWatchOverlayAnchor = (
  updatePosition
) => {
  const interval = setInterval(() => {
    updatePosition();
  }, 10);
  return () => clearInterval(interval);
};

const notify = () => {
  Toast.warning({
    content: "The current tenant does not have permission to update the application. Please manually deploy",
    duration: 3,
    zIndex: 9999
  });
};


export const getOverlayAnchor: PlasmoGetOverlayAnchor = async () =>
  document.querySelector(`.connectai-auto-deploy-lark`) as any;

const AutoDeploy = ({ anchor }) => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  // const rect = useRef({});
  const [style, setStyle] = useState({});
  const [state, setState] = useState({});
  const [user, setUser] = useState({});
  useEffect(() => {
    // 直接通过组件传递过来的anchor获取大小等信息，移除storage
    const r = anchor.element.getBoundingClientRect().toJSON();
    // console.log(rect.current.width);
    setTimeout(() => {
      setStyle({ width: r?.width, height: r?.height });
    }, 400);
    // if (rect?.width != r?.width) {
    //   rect = { ...r };
    //   setStyle({ width: r?.width, height: r?.height });
    // }
  });
  useEffect(() => {
    setState(anchor.element.dataset);
    sendToBackground({ name: "user-info", body: { domain }}).then(({ user }) => {
      console.log("userInfo", user);
      if (user) {
        setUser(user);
      }
    }).catch(e => {
      console.log("userInfo", e);
    });
  }, []);

  const saveBot = useCallback(async (app) => {
    const {
      encryptKey,
      secret,
      verificationToken,
      appId,
      id,
      botId,
      description,
        name,
        saveBotUrl
    } = app;
    let url =saveBotUrl?saveBotUrl: `/api/app/${ id }/client/${ botId }`;
    if (window.location.hostname.indexOf("localhost") > -1) {
      url = "/api" + url;
    }
    fetch(url, {
      method: "POST",
      body: JSON.stringify({
        name, description,
        app_id: appId,
        app_secret: secret
        // encript_key: encryptKey,
        // validation_token: verificationToken,
      }),
      mode: "cors",
        credentials: "include",
        headers: {
            "content-type": "application/json"
      } 
    }).then(res => {
      console.log("saveBot", res);
    });
  }, [state]);
  const handleDeploy = useCallback(async (e) => {
    if (loading) {
      return;
    }
    if (success) {
      const button = document.querySelector(`.connectai-auto-deploy-close`);
      if (button) {
        button.click();
      }
      setTimeout(() => {
        // 先关闭弹窗，再跳转审核
        window.open("https://larksuite.com/admin/appCenter/audit");
      }, 1000);
      return; // 机器人创建成功，跳转审核的时候这里需要return，否则会重复创建机器人
    }
    setLoading(true);
    try {
      console.log("deploy", state, user);
      const {
        appId,
        botId,
        id,
        cardCallback,
        eventCallback,
        name,
        description,
        logo,
        encryptKey,
          verificationToken,
          saveBotUrl
      } = state;
      // 判断是不是有效的appId
      const ifValid = validateAppId(appId);
      if (ifValid) {
        const app = await sendToBackground({ name: "get-app-info", body: { appId, domain } });
        console.log("app", app.error);
        if (app.error) {
          notify();
          throw new Error(app.error);
        }
        await saveBot({ ...app, id, appId, botId, name, description, saveBotUrl });
        const resp = await sendToBackground({
          name: "publish-app",
          body: { appId, eventCallback, cardCallback, encryptKey, verificationToken, domain },
        });
        console.log("resp", resp);
        if (resp.response) {
          setSuccess(true);
        }
      } else {
        // create app and get app info
        const app = await sendToBackground({
          name: "create-app",
          body: {
            name: name || "test bot",
            // 使用前端页面传递过来的头像
            avatar: logo || "https://s1-imfile.feishucdn.com/static-resource/v1/v2_8d04e97a-bc0d-4949-b858-20a260064b4g",
            desc: description || "A lark robot",
            domain,
          },
        });
        console.log("app", app.error);
        if (app.error) {
          notify();
          throw new Error(app.error);
        }
        await saveBot({ ...app, id, botId, name, description, saveBotUrl });
        const resp = await sendToBackground({
          name: "publish-app",
          body: {
            appId: app.appId,
            eventCallback,
            cardCallback,
            encryptKey,
            verificationToken,
            domain,
          },
        });
        console.log("resp", resp);
        if (resp.response) {
          setSuccess(true);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [state, user, success, loading]);
  const label = useMemo(() => {
    const { tenant = "" } = user || {};
    if (loading) {
      return "Deploying, please wait...";
    }
    if (success) {
      return tenant ? `Successfully deployed to (${ tenant }), Go to larksuite admin panel review` : "Successfully deployed, please notify the administrator for review";
    }
    if (state.appId) {
      return tenant ? `One click redeployment to (${ tenant })` : "Redeployment (need login to larksuite)";
    } else {
      return tenant ? `One click deployment to (${ tenant })` : "One click deployment (need login to larksuite)";
    }
  }, [user, state, success, loading]);
  // console.log('style', style, state, rect)
  return (
    <label style={ style }
           className="inline-flex items-center justify-center w-full p-5 border-2 rounded-lg cursor-pointer border-blue-600 text-blue-600 bg-gray-50"
           onClick={ handleDeploy }>

      <svg
        className="w-6 h-6 mr-2"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M11.5 19h1v-1.85l3.5-3.5V9H8v4.65l3.5 3.5V19Zm-2 2v-3L6 14.5V9q0-.825.588-1.413T8 7h1L8 8V3h2v4h4V3h2v5l-1-1h1q.825 0 1.413.588T18 9v5.5L14.5 18v3h-5Zm2.5-7Z"
        />
      </svg>
      <span className="w-full ml-2">{ label }</span>
      { loading ?
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="32"
          height="32"
          viewBox="0 0 200 200"
          fill="none"
          color="#3f51b5"
        >
          <defs>
            <linearGradient id="spinner-secondHalf">
              <stop offset="0%" stopOpacity="0" stopColor="currentColor" />
              <stop offset="100%" stopOpacity="0.5" stopColor="currentColor" />
            </linearGradient>
            <linearGradient id="spinner-firstHalf">
              <stop offset="0%" stopOpacity="1" stopColor="currentColor" />
              <stop offset="100%" stopOpacity="0.5" stopColor="currentColor" />
            </linearGradient>
          </defs>
          <g strokeWidth="8">
            <path stroke="url(#spinner-secondHalf)" d="M 4 100 A 96 96 0 0 1 196 100" />
            <path stroke="url(#spinner-firstHalf)" d="M 196 100 A 96 96 0 0 1 4 100" />
            <path
              stroke="currentColor"
              strokeLinecap="round"
              d="M 4 100 A 96 96 0 0 1 4 98"
            />
          </g>
          <animateTransform
            from="0 0 0"
            to="360 0 0"
            attributeName="transform"
            type="rotate"
            repeatCount="indefinite"
            dur="1300ms"
          />
        </svg>
        :
        <svg className="w-6 h-6 ml-3" fill="currentColor" viewBox="0 0 20 20"
             xmlns="http://www.w3.org/2000/svg">
          <path
            fillRule="evenodd"
            d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
            clipRule="evenodd"
          ></path>
        </svg> }
    </label>
  );
};

function validateAppId(appId) {
  if (!appId) {
    return false;
  }
  if (appId.length !== 20) {
    return false;
  }
  return true;
}

export default AutoDeploy;

