"success","success"
"内部错误","Internal Error"
"参数错误","Parameter Error"
"任务忙，请稍后再试","Busy now, try again later"
"请求次数超过限制","Rate limit"
"找不到用户","Can not find user"
"请登录","Login required!"
"帐号异常","Account Error"
"帐号或密码错误","Account and Password not completed"
"找不到应用","Can not find Application"
"找不到应用版本","Can not find version for Application"
"找不到机器人","Can not find <PERSON><PERSON>"
"暂无访问权限","Permission Denied"
"找不到资源版本","Can not find version for Resource"
"注册验证码","Registration verification code"
"无效消息","Invalid Message"
"获取 tenant_access_token 失败","Can not get tenant_access_token"
"正在上传，请稍等...","Uploading, please wait..."
"正在思考，请稍等...","Thinking, please wait..."
"正在生成，请稍等...","Generating, please wait..."
"正在生成，%(progress)s%% ...","Generating, %(progress)s%% ..."
"🤖️：正在生成，请稍等...","🤖️: Generating, please wait..."
"🤖️：正在生成，%(progress)s%% ...","🤖️: Generating, %(progress)s%% ..."
"🤖️：生成成功","🤖️: Generate success"
"生成成功","Generate success"
"文心一言","ERNIE"
"好的","Alright"
"文心千帆","ERNIE Thousand Sails"
"🆑 清除话题上下文","🆑 Clear Topic Context"
"🚀 AI模型切换","🚀 Switch AI Models"
"🤖 发散模式选择","🤖 Select AI Modes"
"🏠 内置角色列表","🏠 Built-in Role List"
"🎒 需要更多帮助","🎒 Need More Assistance"
"我是小飞机，一款基于%(tech)s技术的智能聊天机器人！","I'm Connect-AI, an intelligent chatbot based on %(tech)s technology!"
"**👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！**","**👋 Hello, I am an intelligent chatbot based on %(tech)s technology!**"
"帮助","Help"
"清除","Clear"
"模型","Model"
"发散模式","AI Mode"
"角色","Role"
"角色列表","Role List"
"🆑 机器人提醒","🆑 Bot Reminder"
"已删除此话题的上下文信息","Context information for this topic has been removed."
"我们可以开始一个全新的话题，继续找我聊天吧","We can start a fresh topic. Feel free to continue chatting with me."
"已删除此话题的上下文信息","Context information for this topic has been removed"
"🚀 机器人提醒","🚀 Bot Reminder"
"已选择模型","Model has been selected"
"无可用模型","No available models"
"选择以下模型：","Select from the following models:"
"🤖 机器人提醒","🤖 Bot Reminder"
"已选择模式：%(mode)s","Mode has been selected: %(mode)s"
"无可用模式","No available modes"
"选择以下模式","Select from the following modes"
"🤖 发散模式切换","🤖 Switch AI Mode"
"👺 已进入角色扮演模式","👺 Entered Role-Playing Mode"
"👺 机器人提醒","👺 Bot Reminder"
"无可用角色","No available roles"
"选择以下内置角色：","Select from the following built-in roles:"
"🏠 内置角色选择","🏠 Built-in Role Selection"
"👺 机器人提醒","👺 Bot Reminder"
"文本回复*角色扮演* 或 */system*+空格+角色信息","Reply with */system* + space + role information"
"立刻清除","Clear Now"
"您确定要清除对话上下文吗？","Are you sure you want to clear the conversation context?"
"请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息","Please note that this will start a new conversation, and you won't be able to refer to the history of the previous topic."
"选择模型","Select Model"
"您确定更改模型吗？","Are you sure you want to change the model?"
"选择模型，可以让AI更好的理解您的需求。","Selecting a model can help AI better understand your needs."
"选择模式","Select Mode"
"您确定更改发散模式吗？","Are you sure you want to change the AI mode?"
"选择内置模式，可以让AI更好的理解您的需求。","Selecting a built-in mode can help AI better understand your needs."
"选择内置角色","Select Built-in Role"
"您确定更改角色吗？","Are you sure you want to change the role?"
"选择内置场景，快速进入角色扮演模式。","Selecting a built-in scenario for quick entry into role-playing mode."
"** 🆑 清除话题上下文**","** 🆑 Clear Topic Context**"
"文本回复 *清除* 或 */clear*","Reply with */clear*"
"🚀 **AI模型切换**","🚀 **Switch AI Models**"
"文本回复 *模型* 或 */model*","Reply with */model*"
"🤖 **发散模式选择**","🤖 **Select AI Modes**"
"文本回复 *发散模式* 或 */ai_mode*","Reply with */ai_mode*"
"🏠 **内置角色列表**","🏠 **Built-in Role List**"
"文本回复 *角色列表* 或 */roles*","Reply with */roles*"
"👺 **角色扮演模式**","👺 **Role-Playing Mode**"
"🎒 **需要更多帮助**","🎒 **Need Help**"
"文本回复 *帮助* 或 */help*","Reply with */help*"
"🎒需要帮助吗？","Need help?"
"角色扮演","Role-Playing"
"无可用模型切换","No available model switch"
"提醒","Note"
"选择内置模型，让AI更好的理解您的需求。","Select a built-in model to help AI better understand your needs."
"无法选择模型","Unable to select a model"
"已选择模型：**%(model)s**","Model has been selected: **%(model)s**"
"已选择发散模式：**%(mode)s**","AI mode has been selected: **%(mode)s**"
"提醒：选择内置模式，让AI更好的理解您的需求。","Note: Select a built-in mode to help AI better understand your needs."
"无可用角色选择","No available role selection"
"提醒：选择内置场景，快速进入角色扮演模式。","Note: Select a built-in scenario for quick entry into role-playing mode."
"🏠 请选择角色","🏠 Select a Role"
"👺 **角色扮演模式**","👺 **Role-Playing Mode**"
"👺 已进入角色扮演模式","👺 Entered Role-Playing Mode"
"OpenAI聊天机器人","OpenAI Chatbot"
"Azure聊天机器人","Azure Chatbot"
"文心一言聊天机器人","ERNIE Chatbot"
"Claude聊天机器人","Claude Chatbot"
"RWKV聊天机器人","RWKV Chatbot"
"MiniMax聊天机器人","MiniMax Chatbot"
"讯飞星火","IFlytek Spark"
"讯飞星火聊天机器人","IFlytek Spark Chatbot"
"星火认知大模型","IFlytek Spark Model"
"ChatGLM聊天机器人","ChatGLM Chatbot"
"绘画","Artistry"
"生成式 AI 大模型，能够应用于各种用例，支持GPT-3.5、GPT-4 8K、GPT-4 32K模型","Generative AI mega model applicable across various use cases, supporting GPT-3.5, GPT-4 8K, GPT-4 32K models"
"具备OpenAI的所有能力，例如编写帮助、代码生成和数据推理，支持GPT-3.5、GPT-4 8K、GPT-4 32K模型，通过内置负责任的 AI 检测和缓解有害使用，并访问企业级 Azure 安全性。","Possesses all capabilities of OpenAI, such as writing help, generating code, and inferring data, supporting GPT-3.5, GPT-4 8K, GPT-4 32K models, with built-in responsible AI detection and mitigation for harmful usage, and access to enterprise-level Azure security."
"微软云","Microsoft Cloud"
"文心一言","ERNIE"
"作为扎根于中国市场的大语言模型，在5500亿事实知识图谱的赋能下，文心一言具备中文领域更先进的自然语言处理能力，在中文语言和中国文化上有更好的表现。","As a large language model deeply rooted in the Chinese market and empowered by a 550-billion factual knowledge graph, ERNIE possesses advanced natural language processing capabilities in the Chinese domain, demonstrating better performance in the Chinese language and culture."
"百度千帆","Baidu Thousand Sails"
"通过自然语言关键词描述，就能通过AI算法生成相应的图片，支持快速模式、慢速模式，支持最新的V5版本","Through natural language keywords, AI algorithms generate corresponding images, supporting fast mode, slow mode, and the latest V5 version."
"待上架","Coming Soon"
"Claude是一个由Anthropic开发的大语言模型，性能媲美GPT-3.5，最大特点是支持100K的超长上下文","Claude, developed by Anthropic, is a large language model comparable to GPT-3.5 in performance. Its standout feature is the support for ultra-long context of up to 100K."
"RWKV是一个具有Transformer水平的LLM性能的RNN模型。它可以像GPT一样直接进行训练，具有出色的性能、快速推理、快速训练、”无限”的上下文长度和超低费率。","RWKV is an RNN model with Transformer-level LLM performance. Like GPT, it can be trained directly, delivering exceptional performance, fast inference, quick training, 'infinite' context length, and ultra-low costs."
"基于MiniMax端到端自研多模态大语言模型，以自然语言交互的形式帮助企业用户或企业开发者提高文本相关的生产效率。","Based on MiniMax, an in-house, end-to-end, multimodal LLM, it enhances text-related productivity for enterprise users or developers through natural language interactions."
"星火认知大模型","IFlytek Spark Model"
"讯飞星火认知大模型，是以中文为核心的新一代认知智能大模型。","IFlytek Spark Schema Cognitive Mega Model, the new generation cognitive intelligence model with Chinese at its core."
"科大讯飞","IFLYTEK"
"ChatGLM是清华大学知识工程和数据挖掘小组发布的一个开源的对话机器人。","ChatGLM is an open-source conversational bot released by the Knowledge Engineering and Data Mining Group at Tsinghua University."
"目前最好用的开源AI绘图模型，可自由定制专属lora。","The best open-source AI drawing model currently available allows for the customization of exclusive Lora."
"CuteYukiMix(特化可爱风格adorable style）","CuteYukiMix(Specialize in adorable style)"
"majicMIX realistic 麦橘写实","majicMIX realistic"
"清华大学","Tsinghua University"
"OpenAI聊天机器人","OpenAI Chatbot"
"Midjourney绘图机器人","Midjourney Artistry Bot"
"文心一言聊天机器人","ERNIE Chatbot"
"Claude聊天机器人","Claude Chatbot"
"RWKV聊天机器人","RWKV Chatbot"
"MiniMax聊天机器人","MiniMax Chatbot"
"讯飞星火","IFlytek Spark"
"讯飞星火聊天机器人","IFlytek Spark Chatbot"
"ChatGLM聊天机器人","ChatGLM Chatbot"
"本地化翻译助手","Localization Translation Assistant"
"绘图提示词","Art Prompts"
"AI绘画提示词助手","AI Art Prompts Assistant"
"会议总结","Meeting Summaries"
"会议摘要助手","Meeting Abstracts Assistant"
"中文会议纪要整理小助手","Chinese Meeting Minutes Organizer"
"多比特","wedobest"
"Stable Diffusion文本生图","Stable Diffusion Text-to-Image"
"飞书机器人（MJ）","Lark Bot (MJ)"
"飞书机器人","Lark Bot"
"钉钉机器人","DingTalk Bot"
"MJ聊天机器人","MJ Chatbot"
"文案撰写","Copywriting"
"生活助手","Life Assistant"
"代码专家","Code Expert"
"角色扮演","Role-Playing"
"公文写作","Official Document Writing"
"各种材料汇报专业撰写，让你高效完成工作任务","Professional Writing of Various Report Materials to Help You Efficiently Complete Work Tasks"
"你是某机关单位办公室秘书，你熟悉各类公文写作格式，你喜欢撰写文字材料，请你文采过人地，条理清晰地跟我对话","You are a secretary in a government office, familiar with various official document formats, and you enjoy writing textual materials. Please engage in a conversation with eloquence and clear organization."
"你好，我是某某某，我想要你帮我写一份公文，内容是：团结一致，共同抗击疫情，全力以赴，共克时艰。","Hello, I am someone, and I would like you to help me write an official document. The content is: Unite as one, jointly combat the epidemic, give our all, and overcome challenges together."
"公司级别","Company Level"
"百度","Baidu"
"腾讯","Tencent"
"AI应用接入","AI Application Integration"
"无限制","Unrestricted"
"应用提示词","Application Prompts"
"通用AI机器人","General AI Bot"
"AI交互日志","AI Interaction Logs"
"内部知识库","Internal Knowledge Base"
"行业场景机器人","Industry-Specific Scenario Bots"
"低代码AI工作流","Low-Code AI Workflows"
"AI插件生态","AI Plugin Ecosystem"
"一对一服务支持","One-on-One Service Support"
"开源模型自部署","Self-Deployment of Open-Source Models"
"AI场景定制","Customized AI Scenarios"
"体验版","Trial Version"
"7天体验，送25w token","7-Day Trial, Includes 250,000 Tokens"
"个人版","Personal Edition"
"月付（3月起购）","Monthly payment (minimum 3 months to purchase)"
"年付（8折）","Annual payment (20%% off)"
"3月起订，限1坐席，送25w token","Order from March, Limited to 1 Seat, Includes 250,000 Tokens"
"1年起订，限1坐席，送85w token","Order for 1 Year, Limited to 1 Seat, Includes 850,000 Tokens"
"企业版","Enterprise Edition"
"3月起订，10坐席起购，送250w token","Order from March, Minimum 10 Seats, Includes 2,500,000 Tokens"
"1年起订，10坐席起购，送1000w token","Order for 1 Year, Minimum 10 Seats, Includes 10,000,000 Tokens"
"私有化部署版","Private Deployment Edition"
"沟通获取解决方案","Communicate to Obtain Solutions"
"抖音带货文案生成","TikTok Sales Copy Generation"
"抖音带货文案助手","TikTok Sales Copy Generation"
"根据销售产品的信息为您生成一份极具吸引力的抖音带货文案","Generate a highly appealing TikTok sales copy for you based on product information."
"知识库应用","Knowledge Base Application"
"会议总结","Meeting Summaries"
"会议摘要助手","Meeting Summary Assistant"
"Midjourney绘图机器人","Midjourney Drawing Bot"
"绘图提示词","Drawing Prompts"
"AI绘画提示词助手","AI Drawing Prompts Assistant"
"本地化翻译助手","Localization Translation Assistant"
"严谨","Rigorous"
"简洁","Concise"
"标准","Standard"
"发散","Divergent"
"订阅已失效，请联系本企业管理员处理","Subscription has expired. Please contact your company administrator for assistance."
"暂无访问权限","No Access Permission at the Moment"
"涉及敏感词，请调整提问","Sensitive words are involved. Please adjust your query."
"我是抖音带货文案生成器，可以帮您根据销售产品的信息为您生成一份极具吸引力的抖音带货文案","I'm the TikTok Sales Copy Generator that can craft an irresistibly attractive sales copy for your product based on its information."
"**我是抖音带货文案生成器，可以帮您根据销售产品的信息为您生成一份极具吸引力的抖音带货文案**","**I'm the TikTok Sales Copy Generator that can create an incredibly compelling sales copy for your product based on its information.**"
"我是本地翻译小助手，可以帮您以更符合当地表达习惯的方式进行内容生成翻译！","I'm the Local Translation Assistant that can help you generate content translations in a way that aligns with the local expression norms!"
"**我是本地翻译小助手，可以帮您以更符合当地表达习惯的方式进行内容生成翻译！**","**I'm the Local Translation Assistant that can aid you in producing translated content using expressions that resonate with the local norms!**"
"你好呀， 我是会议小秘书。 您可以将会议的文字稿发送给我，让我帮助整理会议纪要。","Hello there, I'm the Conference Secretary. Feel free to send me the meeting transcript, and I'll assist you in organizing meeting minutes."
"**嗨! 我是会议小秘书~ 告诉我会议的文字稿，我会帮你整理出精彩的会议纪要**","**Hey there! I'm the Meeting Secretary. Share the meeting transcript with me, and I'll help you craft engaging meeting minutes!**"
"**示例：会议文字稿**","**Example: Meeting Transcript**"
"提醒：示例中的会议内容，仅供参考。","Reminder: The meeting content in the example is for reference only."
"精炼后的会议纪要","Condensed Meeting Minutes"
"**精炼后的会议纪要**","**Condensed Meeting Minutes**"
"查看示例","View Example"
"📖 **查看示例**","📖 **View Example**"
"文本回复 *示例* 或 */example*","Reply with */example*"
"🤖️：已放大图片，请勿重复操作...","🤖️: Image has been enlarged. Please refrain from repetitive actions..."
"我是AI绘图小助手，可以帮您扩写优化AI生图提示词，更好的利用AI生图","I'm the AI Sketching Assistant that can assist you in expanding and enhancing AI-generated prompt phrases, making better use of AI-generated sketches."
"**我是AI绘图小助手，可以帮您扩写优化AI生图提示词，更好的利用AI生图**","**I'm the AI Sketching Assistant that can help you extend and refine AI-generated prompt phrases, maximizing the potential of AI-generated sketches.**"
"平台","Platform"
"已选择平台：%(platform)s","Selected platform: %(platform)s"
"选择平台","Choose Platform"
"您确定该平台吗？","Are you sure about this platform?"
"AI将帮您生成适合该平台的绘画提示词。","AI will assist in generating sketching prompts suitable for this platform."
"🤖 **选择绘图平台**","🤖 **Select Sketching Platform**"
"文本回复 *绘图平台* 或 */drawing*","Text reply */drawing*"
"已选择平台：**%(platform)s**","Selected platform: **%(platform)s**"
"我是本地翻译小助手，可以帮您以更符合当地表达习惯的方式进行内容生成翻译！","I'm the Local Translation Assistant, here to help you generate content translations in a way that aligns better with local expression norms!"
"语言","Language"
"已选择语言：%(language)s","Selected language: %(language)s"
"选择以下语言：","Choose from the following languages:"
"🤖 平台选择","🤖 Platform Selection"
"🤖 语言选择","🤖 Language Selection"
"🏠 内置语言选择","🏠 Built-in Language Selection"
"选择内置语言","Select Built-in Language"
"您确定选择该语言吗？","Are you sure about selecting this language?"
"AI将用更本地化的表述帮您翻译成该语言","AI will assist in translating it into a more localized expression in this language."
"**我是本地翻译小助手，可以帮您以更符合当地表达习惯的方式进行内容生成翻译！**","**I'm the Local Translation Assistant, here to assist you in content generation translation with expressions that resonate better with the local norms!**"
"🏠 **选择语言**","🏠 **Select Language**"
"文本回复 *语言* 或 */language*","Text reply */language*"
"👺 **输入语言**","👺 **Input Language**"
"文本回复*语言* 或 */language*+空格+需要翻译的语言，示例：/language 越南语","Text reply */language*+space+desired language for translation, e.g., /language Vietnamese"
"提醒：选择语言。","Reminder: Select a language."
"🏠 请选择语言","🏠 Please select a language"
"👺 已选择语言：%(language)s","👺 Language selected: %(language)s"
"还没有相关资源？不知道如何配置？点击👉","Don't have any relevant resources yet? Don't know how to configure it? click 👉"
"服务域名","Service Domain Name"
"请输入URL","Please enter URL"
"AppID","AppID"
"请输入AppID","Please enter AppID"
"API Key","API Key"
"请输入API Key","Please enter API Key"
"Secret Key","Secret Key"
"请输入Secret Key","Please enter Secret Key"
"Group ID","Group ID"
"请输入Group ID","Please enter Group ID"
"支持模型","Support Model"
"若您使用文心一言官方资源，请务必填写AppID、Secret Key","If you use the official resources of ERNIE Bot, please fill in the AppID and Secret Key"
"若您使用讯飞星火官方资源，请务必填写AppID、Secret Key","If you use the official resources of iFlytek Spark, please be sure to fill in the AppID and Secret Key"
"若您使用MiniMax官方资源，请务必填写GroupID","If you are using the official resources of MiniMax, please be sure to fill in the GroupID"
"请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错","Please select the model supported by the API you are using, do not check if it is not supported, otherwise an error will be reported when employees choose not to support model usage"
"1、若您使用的是Azure官方资源，请务必填写Deployname，且保持命名与Azure 后台设置的保持一致","1. If you are using Azure official resources, please be sure to fill in Deployname and keep the naming consistent with the Azure backend settings"
"2、请选择您所使用的API支持的模型，不支持的可不勾选，否则员工使用时选择不支持模型使用时会报错","2. Please select the model supported by the API you are using, do not check if it is not supported, otherwise an error will be reported when employees choose not to support model usage"
"你的飞书 AI 助手，助你提升工作效率","Your Lark AI assistant helps you improve work efficiency"
"正在读取，请稍等...","Reading, Please wait..."
"会议纪要","Meeting Summary"
"正在生成会议摘要，请稍等...","Generating, please wait..."
"重新生成","reroll"
"小","small"
"中","medium"
"大","large"
"清晰","clear"
"高清","High-definition"
"超高清","Ultra HD"
"🤖 内置图片尺寸选择","🤖 Built-in image size selection"
"🤖 图片尺寸选择","🤖 Select image size"
"我是Stable Diffusion AI生图小助手","I am Stable Diffusion AI's image generation assistant"
"尺寸","size"
"质量","quality"
"选择以下图片尺寸：","Select from the following image size:"
"选择图片尺寸","Select image size"
"👺 已选择图片尺寸：%(width)s*%(height)s","👺 Selected image size: %(width)s*%(height)s"
"选择以下图片质量：","Select from the following image quality:"
"🏠 内置图片质量选择","🏠 Built-in image quality selection"
"👺 已选择图片质量：%(quality)s","Selected image quality:%(quality)s"
"您确定选择该图片尺寸吗?","Are you sure you want to choose this image size?"
"AI将生成该尺寸的图片","AI will generate images of this size"
"选择图片质量","Select image quality"
"您确定选择该图片质量吗?","Are you sure you want to choose this image quality?"
"AI将生成该质量的图片","AI will generate images of this quality"
"**我是Stable Diffusion AI生图小助手**","**I am Stable Diffusion AI's image generator assistant**"
"🤖 **图片尺寸选择**","🤖 **Select Image Size**"
"文本回复 *尺寸* 或 */size*","Reply with */size*"
"提醒：选择图片尺寸。","Reminder: Select the image size."
"🏠 请选择图片尺寸","Please select the image size."
"接下来模型生成图片的尺寸为：宽度%(width)s 高度%(height)s","The size of the generated image will be: width %(width)s and height %(height)s."
"提醒：选择图片质量。","Reminder: Select the image quality."
"🏠 请选择图片质量","Please select the image quality."
"接下来模型生成图片的质量为：%(quality)s","The quality of the generated image will be: width %(width)s and height %(height)s."
"我是知识库小助手","I am a knowledge base assistant"
"**我是知识库小助手**","**I am a knowledge base assistant**"
"完成","complete"
"🤖️：输入不合法！","🤖️: Invalid input!"
"🤖️：请上传图片！","🤖️: Please upload a picture!"
"🤖️：Blend操作上传的图片数量应在2-5之间！","🤖️: The number of images uploaded for the Blend operation should be between 2 and 5!"
"🤖️：输入格式不合法，请参考帮助！","🤖️: Invalid input format, please refer to the help!"
"💥 **我是Midjourney AI绘图小助手**","💥 **I'm Midjourney AI Drawing Assistant**"
"通过提示词进行绘图，您可以直接在输入框输入提示词进行文生图，或者展开输入框后同时输入图片及文字进行图生图，比如: 图片+换行+提示词","Draw pictures based on prompt words. You can directly enter the prompt words in the input box to generate an image from text, or expand the input box to enter both an image and text for image-to-image generation, like: image + newline + prompt words."
"通过图片反推提示词，您可以在输入框的展开按钮中只上传1张图片，并输入文字 /describe","Using image reverse search to get suggested keywords, you can upload only 1 image in the expandable input box and enter the text /describe."
"将多张图片进行混合，您可以在输入框的展开按钮中上传2-5张图片","Blend multiple images. You can upload 2-5 images in the expanded input box."
"通过图片反推提示词，文本回复 /describe + 图片 url","Using image reverse search to get suggested keywords, text reply /describe + picture url."
"将多张图片进行混合，文本回复 /blend + 多个图片 url","Blend multiple images, text reply /blend + multiple image urls."
"对图片进行放大，您可以在图片生成后，点击图片下方的 U1、U2 等按钮进行操作","Enlarge images. After the image is generated, you can click on the buttons U1, U2, etc., below the image to operate."
"支持对图片进行变体，可点击图片下方的 V1、V2 等按钮进行操作。","Create variations of the image. You can click on the buttons V1, V2, etc., below the image to operate."
"支持高变体及低变体，可在 Upscale 操作后，点击Vary high 或 Vary low 进行操作","Supports high and low variations. After the Upscale operation, click on Vary high or Vary low to operate."
"对图片按比例进行扩充，需要先进行 Upscale 放大图片，支持1.5x 和 2x","Expand the image proportionally. You need to first Upscale the image. Supports 1.5x and 2x."
"在特定方向上对图片进行扩展，需要先进行 Upscale 放大图片","Expand the image in a specific direction, you need to Upscale the image first."
"按照当前提示词，重新生成图片","Regenerate the image according to the current prompt words."
"360智脑","360 AI Brain"
"首创大模型安全评估体系，近20年互联网内容能力积累审核","🧠 The first large-scale model security evaluation system, accumulated review of Internet content capabilities in the past 20 years"
"意见反馈","Feedback"
"小技巧","Tips"
"输入<帮助> 或 /help 即可获取帮助菜单","Input /help to get the help menu"
"输入<模型> 或 /model 即可切换AI模型","Input /model to switch AI models"
"输入<清除> 或 /clear 即可清除上下文","Input /clear to clear the context"
"输入<发散模式> 或 /ai_mode 即可选择发散模式","Input /ai_mode to select AI mode"
"输入<角色列表> 或 /roles 即可选择内置角色","Input /roles to select built-in roles"
"输入<角色扮演> 或 /system+空格+角色信息 即可进入角色扮演模式","Input /system + space + role information to enter roleplay mode"
"输入<示例> 或 /example 即可查看示例","Input /example to view examples"
"您可以直接在输入框输入提示词进行文生图，无需添加/imagine前缀","You can directly enter prompt words in the input box to generate image, no need to add the prefix /imagine"
"您可以直接在输入框输入提示词+图片进行图生图","You can directly enter prompt words + image in the input box to generate images with text"
"您可以直接在输入框输入图片 + /describe 进行图生文","You can directly enter image + /describe in the input box to generate text from images"
"您可以直接在输入框输入多张图片进行混合生图的操作","You can directly enter multiple images in the input box for mixed image generation"
"您可以直接在输入框输入 /describe + 图片 url 进行图生文","You can directly enter /describe + image url in the input box to generate text from images"
"您可以直接在输入框输入 /blend + 多个图片 url 进行混合生图的操作","You can directly enter /blend + multiple image URLs in the input box for mixed image generation"
"生成图片后，您可以点击图片下方的U1、U2等按钮放大图片","After generating an image, you can click on the buttons below the image like U1, U2 to zoom out the image"
"生成图片后，您可以点击图片下方的V1、V2等按钮生成变体图片","After generating an image, you can click on the buttons below the image like V1, V2 to generate variant images"
"放大图片后，您可以点击图片下方的Zoom out按钮扩展图片","After upscaling an image, you can click on the Zoom out button to expand the image"
"若您对当前生成的图片不满意，可进行Reroll操作重新生成","If you are not satisfied with the currently generated image, you can Reroll to generate a new one"
"输入<绘图> 或 /drawing 即可选择绘图平台","Input /drawing to select the drawing platform"
"输入<语言> 或 /language 即可切换语言","Input /language to switch languages"
"输入<语言> 或 /language +空格+需要翻译的语言，示例: /language 越南语","Input /language + space + language to translate, example: /language Vietnamese"
"详情","Desc"
"定制/私有部署版","Private Deployment Edition"
"内部成员","members"
"token 赠送","token"
"主流 AI 模型支持","AI Models"
"支持接入自有 AI 资源","Use Your Own AI Resources"
"开源 AI 模型私有化部署","Private Deployment of AI Models"
"AI 应用接入","AI Application"
"AI 应用提示词","Prompts"
"AI 知识库容量","AI Knowledge Storage"
"标准 AI 行业场景应用","Standard Industry AI Applications"
"定制 AI 行业场景应用","Customize Industry AI Applications"
"AI 交互日志","AI Interaction Logs"
"AI 应用及模型权限管控","AI Access Control"
"AI 插件能力生态","AI Plugin Capability Ecosystem"
"低代码 AI 工作流工具","Low-Code AI Workflow Tool"
"一对一服务支持","One-on-One Service Support"
"试用版","Trial"
"初创企业版","Essential"
"中小企业版","Advanced"
"定制数量","custom quantity"
"无限","infinity"
"是否显示emoji表情，默认显示","Display emoji expressions by default"
"您确定显示表情吗？","Are you sure you want to display emojis?"
"选择否后，总结内容中将不再显示emoji表情","If you choose no, emojis will no longer be displayed in the summary content"
"是否忽略缓存，默认不缓存","Ignore cache by default"
"您确定忽略缓存吗？","Are you sure you want to ignore cache?"
"💥 **我是ChatVideo，输入bilibili、抖音、youtube、小红书等网站链接帮你加速理解音视频内容**","💥 **I'm ChatVideo. Input links from websites like Bilibili, Douyin, YouTube, Xiaohongshu, articles, etc., and I'll help you quickly understand the audio and video content**"
"😁 **是否显示表情**","😁 Display emojis or not"
"🖌️️ **是否忽略缓存**","🖌 Ignore cache or not"
"解析后的总结内容中显示emoji表情","Emoji expressions are displayed in the parsed summary content"
"选择缓存后解析速度更快","Parsing will be faster after selecting cache"
"选择是否在解析后的总结内容中显示emoji表情","Choose whether to display emojis in the parsed summary content"
"选择缓存后解析速度更快，但是新的配置无法生效","Choose caching will result in faster parsing speed, but new configurations will not take effect"
"正在解析，请稍等...","Parsing, please wait..."
"接下来的总结内容将不再显示emoji表情","The upcoming summary content will no longer display emoji expressions"
"👺 已选择不显示表情","👺 Chosen not to display emojis"
"接下来的总结内容将会进行缓存","The upcoming summary content will be cached"
"👺 已选择缓存","👺 Chosen to cache"
"无效输入，请输入音视频网站的链接或分享链接","Invalid input, please enter the link or share link of an audio/video website"
"解析示例视频","Analyze sample video"
"跳转详细版(支持对话追问)","Switch to detailed version"
"🤖 示例输入","🤖 Example input"
"🤖 示例输出","🤖 Example output"
"输入<帮助> 或 /help 可选择是否显示emoji表情","Enter /help to choose whether to display emoji expressions"
"输入<帮助> 或 /help 可选择是否缓存","Enter /help to choose whether to cache"
"选择忽略缓存，相关配置才会生效","Choose to ignore cache, and the related settings will take effect"
"支持直接粘贴音视频的网站分享链接哦","Supports directly pasting audio and video website share links"
"查询知识库信息...","Query Knowledge Base Information..."
"优化关键词...","Optimize Keywords..."
"查询相关文档信息...","Query related document information..."
"超过坐席限制，暂时无法添加，请联系客服提升坐席数量","Exceeded the agent limit, unable to add at the moment. Please contact customer service to increase the number of agents."
"该成员已经存在，请勿重复添加","This member already exists, please do not add duplicates."
"ChatGPT AI 音视频一键总结，轻松学习哔哩哔哩丨YouTube丨播客丨小红书丨抖音等内容。BibiGPT 助力于成为最好的 AI 学习助理，支持免费试用！","ChatGPT AI: One-click summary of audio and video, making it easy to learn from Bilibili, YouTube, podcasts, Xiaohongshu, TikTok, and more. BibiGPT aims to be the best AI learning assistant, with free trials available!"
"BibiGPT AI音视频助理","BibiGPT AI Audio and Video Assistant"
"对话追问","Dialogue Follow-up"
"解析字幕","Subtitle analysis"
"音视频解析完成后，可通过创建话题进行对话追问","After the audio and video analysis is completed, you can create a topic for dialogue follow-up"
"音视频解析完成后，可获取相应字幕","After the audio and video analysis is completed, corresponding subtitles can be obtained"
"已切换为对话追问模式，您可输入问题对视频提问","The dialogue follow-up mode has been switched on, and you can input questions to ask the video"
"退出对话追问","Exit Dialogue Follow-up"
"暂不支持您上传的文档类型","The document type you uploaded is currently not supported"
"支持上传pdf、word、excel、ppt、txt格式文档","Supports uploading documents in PDF, Word, Excel, PowerPoint, and txt formats"
"处理文档成功...","Successfully processed document..."
"处理文档失败..","Processing document failed.."
"开始处理文档...","Start processing document..."
"请在对应文件话题下进行回复","Please reply under the corresponding file topic"
"1. 在对话框中上传文档","1. Upload documents in the dialog box"
"2. 使用文件消息创建话题","2. Create a topic using file messages"
"如何创建话题","How to create a topic"
"3. 在话题内进行回复，与文档对话","3. Reply within the topic and have a conversation with the document"
"**💡内容摘要**","**💡Content Summary**"
"**📖推荐问题**","**📖Recommendation Questions**"
"🎉 您可以参照以下内容进行提问对话","🎉 You can refer to the following content for questioning and dialogue"
"查询相关文档信息...","Query related document information..."
"**请在上方「文件消息」处点击「创建话题」并回复「摘要」后进行对话**","**Please click on \"Create Topic\" and reply to \"Summary\" in the \"File Message\" section above to start the conversation**"
"将鼠标悬浮至某条消息上，选择创建话题，在右侧话题详情页的输入框中即可回复该话题；发送回复后，针对该消息的话题即已创建完成。","Hover the mouse over a message, select Create Topic, and reply to the topic in the input box on the topic details page on the right; After sending a reply, the topic for the message has been created."
"查看如何创建话题","View how to create a topic"
"🎉 文件解析完成，可以开始跟文件对话啦","🎉 File parsing is complete, you can start a conversation with the file now"
"**请在上方「解析消息」或「链接消息」处点击「创建话题」后进行对话**","**Please click \"Create Topic\" in the \"Analyze Message\" or \"Link Message\" area above to start a conversation.**"
"字幕解析成功🎉","Subtitles parsed successfully🎉"
"去除背景","Remove background"
"🤖️：已传输，","🤖️: transmitted,"
"💥 **我是AI背景去除小助手，可以帮您去除图片背景**","💥 **I'm an AI background removal helper that can help you remove the background of your pictures**"
"🧹 **AI背景去除 使用**","🧹 **Usage of RemoveBg**"
"在聊天框中输入图片，BOT将回复已去除背景的图片","Enter a picture in the chat box, and the bot will reply with a picture with the background removed"
"🤖️：请一次上传一张图片！","🤖️: Please upload one image at a time!"
"输入图片，BOT回复去除背景图片","Enter a picture, and the bot will reply with a picture with the background removed"
"引用图片，BOT回复已去除背景的图片","Quote a picture, and the bot will reply with a picture with the background removed"
"点击图片右侧的快捷选项，去除背景","Click on the shortcut option to the right of the image to remove the background"
"🎒 需要帮助吗？","🎒 Need help?"
"AI背景去除小助手 🎉","RemoveBg BOT 🎉"
"获取提示词","get prompt"
"上传图片，BOT回复高清放大图片","Upload a picture, and the bot will reply with a high-definition enlarged picture"
"若您配置了该应用的快捷消息，可在图片消息的快捷消息中快速去除背景","If you have configured quick replies for this app, you can quickly remove the background in the quick reply for image messages"
"选择放大倍数","Select magnification"
"您确定选择此放大倍数吗？","Are you sure you want to select this magnification?"
"一键获取高清放大图","Get high-definition upscaled image with one click"
"**我是AI图片高清放大助手，支持对图片进行2，4，8倍按宽高比例放大**","**I am an AI image high-definition enlargement assistant that supports 2, 4, and 8 times enlargement of images according to the aspect ratio**"
"**🧹UpScaler 使用**","**🧹UpScaler usage**"
"🚀 **放大倍数选择**","🚀 **Magnification selection**"
"已选择放大倍数","Magnification selected"
"高清放大","upscale"
"转人工：","Contact customer service:"
"用户重新进入","User re-enter"
"用户离开","User Leaving"
"AI绘画","AI Drawing"
"日常办公","Daily Office"
"音视频","Audio and Video"
"大语言模型","Large Language Model"
"语音合成","Text To Speech"
"语音识别","Speech To Text"
"视觉","Vision"
"图片处理","Image Processing"
"音频处理","Audio Processing"
"视频处理","Video Processing"
"文档处理","Document Processing"
"不支持的消息类型","Unsupported message type"
"查看原图","view origin"
"请选择图片生成模式。","Please select image generation mode"
"已选择图片生成模式：%(mode)s","Image generation mode selected：%(mode)s"
"无可用提示词模板选择","No prompt word template selection available"
"选择提示词模板，获取更加精准的绘图提示词。","Select a prompt word template to get more accurate drawing prompt words."
"请替换提示词模板大括号中的内容，并保留大括号,"Please replace the content in the curly brackets of the prompt word template and keep the curly brackets."
"🤖 **图片生成模式选择**","🤖 **Image generation mode selection**"
"文本回复 *模式* 或 */mode*","Text reply */mode*"
"🏠 **内置提示词模板**","🏠 **Built-in prompt word template**"
"文本回复 *模板* 或 */template*","Text reply */template*"
"放大图片后，您可以点击图片下方的Pan按钮在特定方向上扩展图片","After enlarging the picture, you can click the Pan button below the picture to expand the picture in a specific direction."
"可以通过切换图片生成模式更快地生成图片，但消耗的token也更多","You can generate images faster by switching the image generation mode, but it consumes more tokens."
"您确定选择该模式吗","Are you sure you want to select this mode?"
"AI将使用该模式生成图片","The AI will use this pattern to generate the image."
"选择内置提示词模板","Choose a built-in prompt word template"
"请替换提示词模板大括号中的内容，并保留大括号","Please replace the content in the curly brackets of the prompt word template and keep the curly brackets"
"支持上传wav，mp3，m4a，flac，ogg格式音频文件","Supports uploading audio files in WAV, MP3, M4A, FLAC, and OGG formats"
"暂不支持您上传的文件类型","The file type you uploaded is currently not supported"
"AI人声去除小助手 🎉","AI Vocal Remove BOT 🎉"
"发送音频，BOT回复已分离的人声和背景音频","Send an audio, and the bot will reply with the separated vocals and background audio"
"💥 **我是AI人声分离小助手，可以帮您分离音频中的人声**","💥 **I'm an AI vocal remove bot that can help you separate the vocals from an audio**"
"🧹 **AI 人声分离 使用**","🧹 **AI Vocal Remove Usage**"
"选择音频文件发送给BOT，BOT将回复分离好的人声和背景音文件","Select an audio file and send it to the bot, and the bot will reply with the separated vocals and background audio"
"下载人声","Download vocal"
"下载背景音","Download background sounds"
"我是文件小助手，请直接向我发送文件，支持docx(doc)、xlsx(xls)、pptx(ppt)、pdf、txt、csv、markdown等文本文件类型，不支持自然语言对话哦!","I am a file assistant. Please send me files directly. It supports docx (doc), xlsx (xls), pptx (ppt), pdf, txt, csv, markdown and other text file types. Natural language dialogue is not supported!"
"不支持的消息，请更改发送文件/内容！","Unsupported message, please change the file/content to be sent!"
"👇👇请下载下面的文件查看","👇👇Please download the file below to view"
"🎉文件翻译成功！","🎉File translation successful!"
"文档翻译小助手 🎉","Document Translation Assistant 🎉"
" **点击右侧下拉框，选择想要翻译成的目标语言 **\n"," **Click the dropdown box on the right to select the target language you want to translate into **\n"
"选择目标语言","Select target language"
"已确认，开始翻译","Confirmed, starting translation"
"文件解析完毕，请选择目标语言","File parsing completed, please select target language"
"👋🏻 **你好呀，我是文件翻译小助手**","👋🏻 **Hello there, I am a file translation assistant**"
"🌏 **目标翻译语言选择**\n文本回复 *语言* 或 */language*","🌏 **Target Translation Language Selection**\nText reply *语言* or */language*"
"🚀 绘图平台选择","🚀 Drawing platform selection"
"提醒：选择绘图平台，让AI更好的理解您的需求。","Reminder: Choose a drawing platform to let AI better understand your needs."
"支持上传png，jpg，jpeg，bmp，exif，webp格式位图文件","Supports uploading bitmap files in PNG, JPG, JPEG, BMP, EXIF, and WebP formats"
"转换完成 🎉 请下载下方文件","Transformation complete 🎉 Please download the file below"
"AI转换矢量图小助手 🎉","AI Vectorize BOT 🎉"
"发送位图文件，BOT回复转换为矢量图的svg文件","Send a bitmap file, and the bot will reply with an SVG file converted to a vector image"
"💥 **我是AI矢量图转换小助手，可以帮您将位图转换为矢量图**","💥 **I'm an AI vectorize bot that can help you convert bitmap images to vector images**"
"🧹 **AI 矢量图转换**\n向BOT发送位图文件，BOT将回复已转换为高清矢量图的文件","🧹 **AI Vectorize**\nSend a bitmap file to the bot, and the bot will reply with a file converted to a high-res vector image"
"🤖️：生成中，视图像复杂度耗时3-5分不等，请耐心等待！","🤖️：Generating, depending on the complexity of the image, it may take 3-5 minutes. Please be patient!"
"输入“平台”或者“/platform”可选择切换默认的平台","Enter "/platform" to switch to the default platform"
"选择朋友圈/小红书","Select Moments/Little Red Book"
"💥 **你好呀，我是AI识图文案助手**","💥 **Hello, I am an AI image recognition copywriting assistant**"
"🌍 **选择分享平台**\n文本回复 *平台* 或 */platform*","🌍 **Select sharing platform**\nText reply *Platform* or */platform*"
"🤖 选择分享平台","🤖 Choose a sharing platform"
"已选择分享平台：%(platform)s","已选择分享平台：%(platform)s"
"我是AI识图文案小助手，请直接向我发送图片，支持jpg、jpeg、png格式，不支持自然语言对话哦!","I am an AI picture-recognizing copywriting assistant. Please send me pictures directly. I support jpg, jpeg, and png formats. Natural language dialogue is not supported!"
"不支持当前内容，请更改发送文件/内容格式！","The current content is not supported, please change the sending file/content format!"
"正在合成语音，请稍等...","Speech is being synthesized, please wait..."
"查看原文","View raw text"
"选择内置语音角色","Choose a built-in voice character"
"💥 **我是VoiceFriend**","💥 **I am VoiceFriend**"
"👺 **选择语音角色**","👺 **Select voice character**"
"已选择语音角色","Voice character selected"
"💥 **我是智能语音AI助手 VoiceFriend**","💥 **I am the intelligent voice AI assistant VoiceFriend**"
"正在转写语音，请稍等...","Transscribing speech, please wait..."
"语音转写失败，请尝试文本输入","Voice transcription failed, please try text input"
"输入您在官网的VoiceLab创建的Voice ID","Enter the Voice ID you created in VoiceLab on the official website"
"无效的Voice ID","Invalid Voice ID"
"我是VoiceFriend，请直接向我发送文字、音频、音视频文件，支持mp3、mp4、mpeg、m4a、wav、webm格式。","I am VoiceFriend, please send me text, audio, audio and video files directly, supporting mp3, mp4, mpeg, m4a, wav, webm formats."
"🎉 不支持当前内容，请更改发送文件/内容格式！","The current content is not supported, please change the sending file/content format!"
"去除文字","Remove Text"
"内容为空，请直接告诉用户你不知道答案","Content is empty just say you don't know answer"
"了解更多玩法技巧，请点击右侧「使用说明」查看👉","To learn more about how to play, please click "manual" on the right to view👉"
"使用说明","manual"
"该模型支持图文消息，请切换模型后使用!","The model do not support graphics messages, please switch the model before using!"
"请直接向我发送图文消息。","Please send me a graphics message directly."
"请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!","Please send me messages in natural language directly. Files, links, pictures and other formats are not supported!"
"不支持的消息，请更改发送文本内容！","Unsupported message, please change the sending text content!"
"请直接向我发送图片或文件，不支持自然语言（除帮助外）、链接等格式的消息哦!","Please send me pictures or files directly. Messages in natural language (except help), links and other formats are not supported!"
"不支持的消息，请更改发送图片/文件！","Unsupported message, please change the picture/file you send!"
"请直接向我发送视频链接，不支持自然语言（除帮助外）、文件、图片等格式的消息哦!","Please send me the video link directly. Messages in natural language (except help), files, pictures and other formats are not supported!"
"不支持的消息，请更改发送链接！","Unsupported message, please change the sending link!"
"请直接向我发送自然语言或文件(仅支持pdf、word、excel、ppt、txt格式），不支持链接、图片等格式的消息哦!","Please send me natural language or files directly (only pdf, word, excel, ppt, txt formats are supported). Messages in link, picture and other formats are not supported!"
"不支持的消息，请更改发送文本/文件！","Unsupported message, please change the sending text/file!"
"请直接向我发送自然语言或图文（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!","Please send me natural language or graphics directly (pictures only support png, gif, webp, jpg, jpeg). Messages in file, link and other formats are not supported!"
"不支持的消息，请更改发送文本/图文！","Unsupported message, please change sending text/graphics!"
"请直接向我发送自然语言或图片（仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!","Please send me natural language or pictures directly (only supports png, gif, webp, jpg, jpeg). Messages in file, link and other formats are not supported!"
"不支持的消息，请更改发送文本/图片！","Unsupported message, please change the sending text/image!"
"请直接向我发送图片，不支持自然语言（除帮助外）、文件、链接等格式的消息哦!","Please send me pictures directly. Messages in natural language (except help), files, links and other formats are not supported!"
"不支持的消息，请更改发送图片！","Unsupported message, please change the picture you send!"
"请直接向我发送文件（仅支持WAV，MP3，M4A，FLAC，OGG格式），不支持自然语言（除帮助外）、链接、图片等格式的消息哦!","Please send me files directly (only WAV, MP3, M4A, FLAC, OGG formats are supported). Messages in natural language (except help), links, pictures and other formats are not supported!"
"不支持的消息，请更改发送文件！","Unsupported message, please change the sending file!"
"💥 **我是文档翻译小助手**","💥 **I am a document translation assistant**"
"请直接向我发送自然语言或语音，不支持文件、链接、图片等格式的消息哦!","Please send me messages in natural language or voice directly. Files, links, pictures and other formats are not supported!"
"不支持的消息，请更改发送文本/语音！","Unsupported message, please change sending text/voice!"
"生成失败，请重试...","Generation failed, please try again..."
"上传失败，请重试...","Upload failed, please try again..."
"操作无效，该内容已被清除'","Invalid action, the content has been cleared"
"回答 %(i)s","answer %(i)s"
"该模型暂不支持","This model is not currently supported"
"输入<联网> 或 /web 即可开启或关闭联网搜索功能","Enter /web to turn on or off the web search function"
"开启","turn on"
"关闭","turn off"
"您确定更改联网搜索吗","Are you sure you want to change your web search?"
"开启联网搜索可以让AI查询最新的信息，并提供更多最新和准确的答复","Enabling web search allows AI to query the latest information and provide more up-to-date and accurate answers"
"**🌏 联网搜索**\n文本回复 *联网* 或 */web*","**🌏 Web search**\nText reply */web*"
"开启后，AI可以查询最新的信息，并提高更多最新和准确的答复","When turned on, AI can query the latest information and provide more up-to-date and accurate answers"
"🌏 联网搜索","🌏 web search"
"无法开启联网搜索，已在管理后台关闭","Unable to open web search, it has been closed in the management background"
"已开启联网搜索","Web search enabled"
"已关闭联网搜索","web search disabled"
"您确定更改联网搜索吗？","Are you sure you want to change your web search?"
"请直接向我发送自然语言或图片加文字一起发送（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!","Please send me natural language or pictures and text directly (pictures only support png, gif, webp, jpg, jpeg). Messages in file, link and other formats are not supported!"
"仅在纯语言模型中生效，不支持和生图/多模态模型混用","It only takes effect in pure language models and does not support mixed use with raw graph/multimodal models."
"上传成功，请回复上面的消息。","Upload successful, please reply to the message above."
