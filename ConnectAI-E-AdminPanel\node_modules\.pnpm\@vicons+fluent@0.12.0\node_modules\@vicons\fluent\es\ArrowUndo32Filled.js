import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.953 4.256a1.25 1.25 0 0 1 2.5 0v6.181L12.05 5.64c1.221-1.294 3.252-2.326 5.48-2.543c2.28-.22 4.817.408 6.93 2.52c2.148 2.15 2.712 4.66 2.5 6.83c-.205 2.105-1.143 3.959-2.119 4.936l-.002.002L13.633 28.68a1.25 1.25 0 0 1-1.775-1.76l11.21-11.3l.004-.004c.523-.523 1.245-1.818 1.4-3.412c.149-1.528-.228-3.268-1.78-4.82c-1.526-1.525-3.298-1.957-4.92-1.8c-1.67.163-3.128.947-3.907 1.773l-.007.008l-4.94 5.155h6.438a1.25 1.25 0 0 1 0 2.5H6.202c-.69 0-1.25-.56-1.25-1.25V4.256z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ArrowUndo32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
