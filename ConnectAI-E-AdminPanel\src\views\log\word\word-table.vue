<template>
  <n-data-table scroll-x="1500" :columns="columns" :data="data" :bordered="false" />
  <div class="flex justify-end mt-4"><n-pagination v-bind="paginationOptions" /></div>
</template>

<script setup lang="tsx">
import { ref, toRefs } from 'vue';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { NButton, NDivider, NPopconfirm, NTag, NSwitch } from 'naive-ui';
import { t } from '@/locales';

const props = defineProps<{
  data: ApiSensitive.SensitiveTypes[];
  paginationOptions: PaginationProps;
}>();
const { data, paginationOptions } = toRefs(props);
const emit = defineEmits(['handle-edit', 'handle-delete', 'handle-active']);
const popconfirm = ref();

const columns: DataTableColumns<ApiSensitive.SensitiveTypes> = [
  {
    title: t('message.log.id'),
    key: 'id',
    width: 100,
    align: 'center',
    render(row, index) {
      return <div>{index + 1}</div>;
    }
  },
  {
    title: t('message.log.fxzt'),
    key: 'category',
    width: 180,
    sorter: 'default',
    align: 'center'
  },
  {
    title: t('message.log.fx'),
    key: 'name',
    resizable: true,
    align: 'center',
    render({ name }) {
      return (
        <div class="i-flex-center gap-2">
          {name.map(str => {
            return <NTag>{str}</NTag>;
          })}
        </div>
      );
    }
  },
  {
    title: t('message.log.gjcl'),
    key: 'name',
    resizable: true,
    align: 'center',
    render({ name = [] }) {
      return name?.length || 0;
    }
  },
  {
    title: t('message.log.time'),
    key: 'created',
    resizable: true,
    align: 'center'
  },
  {
    title: t('message.log.zt'),
    key: 'status',
    resizable: true,
    align: 'center',
    render({ status, ...rest }) {
      return (
        <NSwitch
          defaultValue={Boolean(status)}
          onUpdateValue={(status: boolean) => emit('handle-active', status, { status, ...rest })}
        >
          {{
            checked: () => t('message.log.sx'),
            unchecked: () => t('message.log.xx')
          }}
        </NSwitch>
      );
    }
  },

  {
    title: t('message.log.cz'),
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',

    render({ category, ...rest }) {
      return (
        <div class="flex gap-2">
          <NButton tertiary size={'small'} onClick={() => emit('handle-edit', { category, ...rest })}>
            {t('message.log.bj')}
          </NButton>
          <NPopconfirm showIcon={false} negativeText={null} positiveText={null} ref={popconfirm}>
            {{
              action: () => (
                <div class="flex justify-start gap-1 items-center">
                  {t('message.log.jjsc') + '『'  + category + '』'+ t('message.log.jjsc2')}
                  <NDivider vertical />
                  <NButton
                    type={'error'}
                    tertiary
                    size={'small'}
                    onClick={() => [emit('handle-delete', { category, ...rest }), popconfirm.value.setShow(false)]}
                  >
                  {t('message.log.qrsc')}
                  </NButton>
                </div>
              ),
              trigger: () => (
                <NButton tertiary size={'small'}>
                  {t('message.log.sc')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
];
</script>
