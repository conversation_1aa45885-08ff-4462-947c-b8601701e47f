<template>
  <div>
    <div
      class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="flow-root">
        <h3 class="text-xl font-semibold dark:text-white">{{ $t('message.my.cjyh') }}</h3>
        <p class="text-xs font-normal text-gray-500 dark:text-gray-400">{{ $t('message.my.cyhbs') }}</p>
        <div v-if="loading">
          <n-skeleton class="mt-8px" height="40px" :sharp="false" />
          <n-skeleton class="mt-8px" height="40px" :sharp="false" />
        </div>
        <ul v-else class="divide-y divide-gray-200 dark:divide-gray-700">
          <li v-for="(user, index) in config?.adminUsers" :key="index" class="py-4">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <svg
                  class="w-6 h-6 dark:text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  ></path>
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-base font-semibold text-gray-900 truncate dark:text-white">river</p>
                <p class="text-sm font-normal text-gray-500 truncate dark:text-gray-400">{{ $t('message.my.mrgly') }}</p>
              </div>
              <div class="inline-flex items-center">
                <a
                  class="px-3 py-2 mb-3 mr-3 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  >{{ $t('message.my.jz') }}</a
                >
              </div>
            </div>
          </li>
          <li class="pt-4 pb-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <svg
                  class="w-6 h-6 dark:text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                  ></path>
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-base font-semibold text-gray-900 truncate dark:text-white">cyb</p>
                <p class="text-sm font-normal text-gray-500 truncate dark:text-gray-400">{{ $t('message.my.drgly') }}</p>
              </div>
              <div class="inline-flex items-center">
                <a
                  class="cursor-not-allowed px-3 py-2 mb-3 mr-3 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  >{{ $t('message.my.qy') }}</a
                >
              </div>
            </div>
          </li>
        </ul>
        <div>
          <n-skeleton v-if="loading" class="mt-8px" height="40px" width="33%" :sharp="false" />

          <button
            class="cursor-not-allowed text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-blue-800"
          >
          {{ $t('message.my.jjzc') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue';
import { inject } from 'vue';
const config = inject<Ref<ApiRobot.Robot>>('config');
const loading = inject<Ref<boolean>>('loading');
</script>

<style scoped></style>
