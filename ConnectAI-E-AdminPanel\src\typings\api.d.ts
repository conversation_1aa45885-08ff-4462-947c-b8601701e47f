// 后端接口返回的数据类型

/** 通用查询参数 */
declare namespace ApiCommon {
  /** 分页查询参数 */
  interface PaginationProps {
    page: number;
    pageSize: number;
    total?: number;
  }

  interface ListData<T> {
    total: number;
    list: T[];
  }
}

/** 后端返回的用户权益相关类型 */
declare namespace ApiAuth {
  /** 返回的token和刷新token */
  interface Token {
    token: string;
    // refreshToken: string;
  }

  /** 返回的用户信息 */
  type UserInfo = Auth.UserInfo;
}

/** 后端返回的路由相关类型 */
declare namespace ApiRoute {
  /** 后端返回的路由数据类型 */
  interface Route {
    /** 动态路由 */
    routes: AuthRoute.Route[];
    /** 路由首页对应的key */
    home: AuthRoute.AllRouteKey;
  }
}

declare namespace ApiUserManagement {
  interface User {
    /** 用户id */
    id: string;
    /** 用户名 */
    userName: string | null;
    /** 用户年龄 */
    age: number | null;
    /**
     * 用户性别
     * - 0: 女
     * - 1: 男
     */
    gender: '0' | '1' | null;
    /** 用户手机号码 */
    phone: string;
    /** 用户邮箱 */
    email: string | null;
    /**
     * 用户状态
     * - 1: 启用
     * - 2: 禁用
     * - 3: 冻结
     * - 4: 软删除
     */
    userStatus: '1' | '2' | '3' | '4' | null;
  }

  interface Seat {
    id: string;
    tenant_id: string;
    user_id: string;
    unionid: string;
    name: string;
    telephone: string;
    department: string;
    status: number;
    created: string;
    modified: string;
  }
  namespace Req {
    interface SeatList {
      page: number;
      size: number;
      keyword?: string;
    }
    interface SeatAdd {
      name: string;
      telephone: string;
      department: string;
    }
    interface SeatUpdate {
      seat_id: string;
      name: string;
      telephone: string;
      department: string;
    }
    interface SeatAction {
      seat_id: string;
      action: 'start' | 'stop';
    }
  }
  namespace Resp {
    interface SeatList {
      data: Seat[];
      total: number;
    }
  }
}

declare namespace ApiAIResource {
  interface AIResource {
    category_id: string;
    description: string;
    provider: string;
    config: Config;
    status: number;
    modified: string;
    name: string;
    bot_instance_count: number;
    icon: string;
    sorted: number;
    id: string;
    created: string;
    tenant_resource: Tenant_resource;
    api_key: string;
    api_base: string;
    tenant_status: number;
    amount: number;
    recharge_link: string;
  }
  interface Top {
    label: string;
    url: string;
  }

  interface Form {
    name: string;
    label: string;
    required: boolean;
    placeholder: string;
    value: string;
    size: string;
  }

  interface Config {
    title: string;
    top: Top;
    form: Form[];
  }

  interface Tenant_resource {
    extra: any;
    resource_id: string;
    api_key: string;
    status: number;
    modified: string;
    api_base: string;
    tenant_id: string;
    id: string;
    created: string;
  }

  namespace Req {
    interface AIResourceList {
      page?: number;
      size?: number;
      category_id?: string;
      keyword?: string;
    }

    interface UpdateAIResource {
      id: string;
      data: {
        api_key: string;
        api_base: string;
      };
    }
  }

  namespace Resp {
    interface AIResourceList {
      data: AIResource[];
      total: number;
    }
  }
}

declare namespace ApiApp {
  interface Application {
    icon: string;
    category_id?: any;
    tenant_status?: any;
    description: string;
    id: string;
    created: string;
    title: string;
    name: string;
    price: number;
    status: number;
    modified: string;
  }

  interface AppCategory {
    id: string;
    created: string;
    description: string;
    status: number;
    name: string;
    modified: string;
  }

  interface AppDetail extends Application {
    video?: string;
    manual?: string;
    problem?: string;

    supportBot?: string[];
  }

  interface Models {
    description: string;
    resource_id: string;
    id: string;
    status: number;
    modified: string;
    price: number;
    name: string;
    created: string;
  }

  interface AppResource {
    icon: string;
    name: string;
    id: string;
    created: string;
    category_id: string;
    description: string;
    provider: string;
    status: number;
    modified: string;
    models: Models[];
  }

  interface AppResources {
    category: string;
    required: boolean;
    scene: string;
    title: string;
    resource: AppResource[];
    tip?: string;
  }

  interface AppClient {
    description: string;
    status: number;
    platform: 'feishu' | 'dingding' | 'wework' | 'wxwork';
    modified: string;
    name: string;
    id: string;
    created: string;
    tenant_status?: number;
  }

  interface AppClientBot {
    tenant_id: string;
    bot_id: string;
    description: string;
    app_secret: string;
    validation_token: string;
    id: string;
    created: string;
    platform: 'feishu' | 'dingding' | 'wework' | 'wxwork' | 'messenger';
    tenant_name: string;
    user_id?: any;
    name: string;
    app_id: string;
    encript_key: string;
    agent_id?: any;
    status: number;
    modified: string;
    callback_url: {
      card: string;
      event: string;
    };
    logo?: string;
    instance_id: string;
  }

  interface APPSetting {
    group_permission: GroupPermission[];
    prompt_id: string[];
    resource_id: string;
    resource_ids: Record<string, string>; // 新增多资源模式
    sensitive_id: string[];
    user_permission: UserPermission[];
    collection_id: string[];
    prompt: string;
    chat_history: string;
    model_id?: string;
    web_search: string;
    artifacts?: string;
  }

  interface AppInfo {
    application_id: string;
    resource_id: string;
    description: string;
    id: string;
    created: string;
    name: string;
    tenant_id: string;
    icon: string;
    status: number;
    modified: string;
  }

  /**
   * GroupPermission
   */
  interface GroupPermission {
    allow_groups: string[];
    deny_groups: string[];
    model_id: string;
    name: string;
    resource_id: string;
    flag?: boolean; // 前度用于标记是否可用
    title?: string; // 模型名称
    description?: string; // 模型描述
  }

  /**
   * UserPermission
   */
  interface UserPermission {
    allow_users: string[];
    deny_users: string[];
    model_id: string;
    name: string;
    resource_id: string;
    flag?: boolean; // 前度用于标记是否可用
    title?: string; // 模型名称
    description?: string; // 模型描述
  }

  namespace Req {
    interface APPList {
      page?: number;
      size?: number;
      category_id?: string;
      keyword?: string;
    }

    interface APPDetail {
      id: string;
      data: any;
    }

    interface UpdateStatus {
      id: string;
      action: 'buy' | 'deploy';
    }

    interface GetAppResource {
      id: string;
    }

    interface GetAppClient {
      id: string;
    }

    interface GetAppClientBot {
      id: string;
      botId: string;
    }

    interface UpdateAppClientBot {
      id: string;
      botId: string;
      data: AppClientBot;
    }

    interface AppSetting {
      id: string;
    }

    interface AppInfo {
      id: string;
    }

    interface UpdateAppSetting {
      id: string;
      data: APPSetting;
    }
  }
  namespace Resp {
    interface APPDetail {
      data: AppDetail;
    }
    interface AppList {
      data: Application[];
      total: number;
    }

    interface AppCategoryList {
      data: AppCategory[];
      total: number;
    }

    interface AppResourceList {
      data: AppResource[];
      total: number;
    }

    interface AppResourcesList {
      data: AppResources[];
      total: number;
    }

    interface AppClientList {
      data: AppClient[];
      total: number;
    }

    interface AppClientBot {
      data: AppClientBot;
    }

    interface AppSetting {
      data: APPSetting;
    }

    interface AppInfo {
      data: AppInfo;
    }
  }
}

declare namespace ApiLog {
  interface ChatLog {
    tenant_id: string;
    user_id: string;
    name: string[];
    status: number;
    modified: string;
    category: string;
    id: string;
    created: string;
  }

  interface AtUser {
    dingtalkId: string;
  }

  interface Text {
    content: string;
  }

  interface Extra {
    atUsers: AtUser[];
    chatbotCorpId: string;
    chatbotUserId: string;
    isAdmin: boolean;
    senderStaffId: string;
    sessionWebhookExpiredTime: number;
    createAt: number;
    senderCorpId: string;
    conversationType: string;
    conversationTitle: string;
    isInAtList: boolean;
    sessionWebhook: string;
    text: Text;
    robotCode: string;
  }

  interface RootObject {
    model_id?: any;
    extra: Extra;
    sensitive_id?: any;
    reply_message_id: string;
    chat_id: string;
    result: any;
    tenant_id: string;
    message_id: string;
    id: string;
    user_id?: any;
    message_type: string;
    status: number;
    bot_instance_id: string;
    sender_id: string;
    created: string;
    instance_id: string;
    username: string;
    display_name: string;
    modified: string;
    content: string;
    sensitive?: any;
  }

  namespace Req {
    interface LogList {
      page: number;
      size: number;
      keyword?: string;
      app?: string;
      start_date?: string;
      end_date?: string;
    }

    interface Export {
      /**
       * 聊天内容，模糊搜索
       */
      content?: string;
      /**
       * 机器人名称，模糊搜索
       */
      instanceName?: string;
      /**
       * 开始时间
       */
      since: string;
      /**
       * 结束时间
       */
      until: string;
    }
  }
  namespace Resp {
    interface LogList {
      data: ChatLog[];
      total: number;
    }
  }
}

declare namespace ApiPrompt {
  interface PromptTypes {
    tenant_id: string;
    user_id: string;
    title: string;
    content: string;
    id: string;
    created: string;
    category_id: string;
    description: string;
    example: string;
    status: number;
    modified: string;
  }

  interface PromptCateTypes {
    user_id: string;
    id: string;
    created: string;
    status: number;
    tenant_id: string;
    name: string;
    modified: string;
  }

  namespace Req {
    interface PromptList {
      page?: number;
      size?: number;
      category_id?: string;
      keyword?: string;
    }

    interface Create {
      category_id: string;
      content: string;
      description: string;
      example: string;
      title: string;
    }

    interface Update {
      id: string;
      data: {
        category_id: string;
        content: string;
        description: string;
        example: string;
        title: string;
      };
    }

    interface Delete {
      id?: string;
    }

    interface PromptCateList {
      page: number;
      pageSize: number;
      title?: string;
    }

    interface Export {
      categoryId?: number;
      title?: string;
    }
  }
  namespace Resp {
    interface PromptList {
      data: PromptTypes[];
      total: number;
    }

    interface PromptCateList {
      data: PromptCateTypes[];
      total: number;
    }
  }
}

declare namespace ApiSensitive {
  interface SensitiveTypes {
    tenant_id: string;
    user_id: string;
    name: string[];
    status: number;
    modified: string;
    category: string;
    id: string;
    created: string;
  }

  namespace Req {
    interface List {
      keyword?: string;
      page: number;
      size: number;
    }

    interface Create {
      category: string;
      name: string[];
    }

    interface Update {
      id: string;
      data: {
        category: string;
        name: string[];
      };
    }

    interface Delete {
      id?: string;
    }

    interface Export {
      words?: string;
    }

    interface UpdateStatus {
      id?: string;
      action: 'start' | 'stop';
    }
  }
  namespace Resp {
    interface SensitiveList {
      data: SensitiveTypes[];
      total: number;
    }
  }
}

declare namespace ApiDataset {
  interface DatasetApp {
    application_id: string;
    resource_id: string | null;
    description: string;
    extra: Record<string, unknown>;
    status: number;
    created: string;
    tenant_id: string;
    name: string;
    icon: string;
    id: string;
    modified: string;
  }
  interface Dataset {
    created: number;
    description: string;
    id: string;
    name: string;
    document_count: number;
  }
  export interface Document {
    created: number;
    id: string;
    name: string;
    path: string;
    type: string;
  }

  namespace Req {
    interface List {
      keyword?: string;
      page: number;
      size: number;
    }

    interface Create {
      name: string;
      description?: string;
      space_id?: string;
      type?: string;
    }

    interface CreateApp {
      application_id: string;
      name: string;
      description?: string;
      resource_ids?: Record<string, string>;
    }

    interface Update {
      id: string;
      data: {
        name: string;
        description?: string;
      };
    }

    interface Delete {
      id: string;
    }

    interface UpoladDocument {
      id: string;
      data: any;
    }

    interface DocumentStatus {
      id: string;
      taskId: string;
    }
  }
  namespace Resp {
    interface DatasetAppList {
      data: DatasetApp[];
      total: number;
    }
    interface DatasetList {
      data: Dataset[];
      total: number;
    }

    interface DocumentsList {
      data: Document[];
      total: number;
    }
  }
}

declare namespace ApiMessenger {
  interface Messenger {
    instance_id: null;
    tenant_id: string;
    user_id: string;
    name: string;
    platform: string;
    id: string;
    created: string;
    seat_count: number;
    bot_instance_id: string;
    chat_id: string;
    description: string;
    ai: number;
    status: number;
    modified: string;
  }

  interface MessengerChat {
    avatar: string;
    chat_id: string;
    description: string;
    external: boolean;
    name: string;
    owner_id: string;
    owner_id_type: string;
    tenant_key: string;
  }
  interface MessengerSeat {
    end_time: string;
    open_id: string;
    status: number;
    modified: string;
    start_time: string;
    messenger_id: string;
    name: string;
    id: string;
    created: string;
  }

  interface MessengerChatMember {
    member_id: string;
    member_id_type: string;
    name: string;
    tenant_key: string;
  }

  interface MessengerBot extends ApiApp.AppClientBot {}

  interface MessengerChatInfoDetails {
    tenant_id: string;
    name: string;
    platform: string;
    id: string;
    created: string;
    modified: string;
    config: {
      form: number;
      links: {
        title: string;
        href: string;
      }[];
    };
    chat_id: string;
    user_id: string;
    bot_instance_id: string;
    description: string;
    ai: number;
    status: number;
    instance_id: string;
  }

  interface MessengerChatInfoDetails {
    tenant_id: string;
    name: string;
    platform: string;
    id: string;
    created: string;
    modified: string;
    config: {
      form: number;
      links: {
        title: string;
        href: string;
      }[];
    };
    chat_id: string;
    user_id: string;
    bot_instance_id: string;
    description: string;
    ai: number;
    status: number;
    instance_id: string;
  }
  namespace Req {
    interface MessengerList {
      page?: number;
      size?: number;
    }

    interface MessengerInfo {
      icon: string;
      category_id?: any;
      tenant_status?: any;
      description: string;
      id: string;
      created: string;
      title: string;
      name: string;
      price: number;
      status: number;
      modified: string;
    }

    interface MessengerWebConfig {
      id: string;
    }

    interface MessengerChatList {
      id: string;
    }

    interface UpdateMessengerWebConfig {
      id: string;
      data: Resp.MessengerWebConfig;
    }
    interface Create {
      name: string;
      description: string;
      platform: string;
    }

    interface Update {
      messenger_id: string;
      chat_id?: string;
      name?: string;
      description?: string;
      ai?: 0 | 1;
    }
    interface CreateSeatMember {
      id: string;
      data: {
        open_id: string;
        name: string;
        start_time: string;
        end_time: string;
      };
    }
    interface UpdateSeatMember {
      id: string;
      data: {
        open_id: string;
        name: string;
        start_time: string;
        end_time: string;
        seat_id: string;
      };
    }

    interface UpdateSeatMemberAction {
      id: string;
      data: {
        action: 'delete' | 'enable' | 'disable';
        seat_id: string;
      };
    }
    interface UpdateMessengerBot {
      id: string;
      data: MessengerBot;
    }
  }
  namespace Resp {
    interface MessengerList {
      data: Messenger[];
      total: number;
    }

    interface MessengerChatList {
      data: MessengerChat[];
    }

    interface MessengerChatInfo {
      data: MessengerChatInfoDetails;
    }

    interface MessengerChatMemberList {
      data: MessengerChatMember[];
    }
    interface MessengerWebConfig {
      form: 0 | 1;
      api: 0 | 1;
      access_token: string;
      links: Array<{
        title: string;
        href: string;
      }>;
      theme: {
        primaryColor?: string;
      };
      logo: string;
      welcome: string;
      lang: 'en_US' | 'zh_CN';
    }
    interface MessengerSeatList {
      data: MessengerSeat[];
      total: number;
    }
  }
}
