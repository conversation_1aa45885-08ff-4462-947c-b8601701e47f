import json
import httpx
from typing import Dict, Any

class Base(Dict):

    def __getattr__(self, name: str) -> Any:
        try:
            return self[name]
        except KeyError:
            raise AttributeError(name)

    def __setattr__(self, name: str, value: Any) -> None:
        self[name] = value


# https://open.dingtalk.com/document/isvapp/message-types-supported-by-enterprise-internal-robots-1
class DingDingAT(Base):
    def __init__(self, *atMobile, atMobiles=list(), atUserIds=list(), isAtAll=False):
        super().__init__(at=dict(
            atMobiles=atMobiles + list(atMobile),
            atUserIds=atUserIds,
            isAtAll=isAtAll,
        ))


class DingDingLinkMessage(Base):

    def __init__(self, messageUrl='', picUrl='', text='', title='', **kwargs):
        super().__init__(
            msgtype='link',
            link=dict(messageUrl=messageUrl, picUrl=picUrl, text=text, title=title)
        )


class DingDingTextMessage(Base):

    def __init__(self, content='', **kwargs):
        super().__init__(
            msgtype='text',
            text=dict(content=content)
        )

class DingDingFeedCardLink(Base):
    def __init__(self, title='', messageURL='', picURL=''):
        super().__init__(title=title, messageURL=messageURL, picURL=picURL)


class DingDingFeedCardMessage(Base):

    def __init__(self, *link, links=list(), **kwargs):
        super().__init__(
            msgtype="feedCard",
            feedCard=dict(links=links + list(link)),
            **kwargs,
        )


class DingDingMarkdownMessage(Base):

    def __init__(self, text='', title='', **kwargs):
        super().__init__(
            msgtype='markdown',
            markdown=dict(title=title, text=text),
            **kwargs
        )


class DingDingActionCardButton(Base):
    def __init__(self, actionURL='', title=''):
        super().__init__(actionURL=actionURL, title=title)


def DingDingActionURL(content=''):
    return 'dtmd://dingtalkclient/sendMessage?content={}'.format(content)


class DingDingActionCardMessage(Base):

    def __init__(self, *btn, btns=list(), title='', text='', btnOrientation="0", **kwargs):
        super().__init__(
            msgtype='actionCard',
            actionCard=dict(
                title=title,
                text=text,
                btnOrientation=btnOrientation,
                btns=btns + list(btn),
            )
        )


class DingDingBot(Base):

    def __init__(self, webhook='', **kwargs):
        super().__init__(webhook=webhook, **kwargs)

    async def asend(self, msg):
        return httpx.AsyncClient().post(self.webhook, data=json.dumps(msg)).json()

    def send(self, msg):
        return httpx.post(self.webhook, data=json.dumps(msg), headers={'Content-Type': 'application/json'}).json()




if __name__ == "__main__":

    # client = DingDingBot('https://oapi.dingtalk.com/robot/sendBySession?session=5e0563e5271bb4f23aa5a7c015310e44')
    client = DingDingBot('https://oapi.dingtalk.com/robot/sendBySession?session=4a0271acc69362ff92d7421478cb7dbb')
    response = client.send(DingDingMarkdownMessage("""
# Midjourney Bot🎉
![](https://cdn.aigcfun.com/attachments/1092632390930808946/1128151333590024284/3401b0c8-9acf-4eb9-89e9-d2a1cb9c2434.png)  
[U1](dtmd://dingtalkclient/sendMessage?content=U1)  
[U2](dtmd://dingtalkclient/sendMessage?content=U2)  
[U3](dtmd://dingtalkclient/sendMessage?content=U3)  
[U4](dtmd://dingtalkclient/sendMessage?content=U4)  
[V1](dtmd://dingtalkclient/sendMessage?content=V1)  
[V2](dtmd://dingtalkclient/sendMessage?content=V2)  
[V3](dtmd://dingtalkclient/sendMessage?content=V3)  
[V4](dtmd://dingtalkclient/sendMessage?content=V4)  
    """, title='测试dtmd链接'))

    print('response', response)
    message = DingDingActionCardMessage(
        DingDingActionCardButton('dtmd://dingtalkclient/sendMessage?content=内容不错', '内容不错'),
        DingDingActionCardButton('dtmd://dingtalkclient/sendMessage?content=不感兴趣', '不感兴趣'),
        title='我 20 年前想打造一间苹果咖啡厅，而它正是 Apple Store 的前身',
        text="![screenshot](https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png) \n\n #### 乔布斯 20 年前想打造的苹果咖啡厅 \n\n Apple Store 的设计正从原来满满的科技感走向生活化，而其生活化的走向其实可以追溯到 20 年前苹果一个建立咖啡馆的计划"
    )
    print('message', message)
    response = client.send(message)
    print('response', response)

