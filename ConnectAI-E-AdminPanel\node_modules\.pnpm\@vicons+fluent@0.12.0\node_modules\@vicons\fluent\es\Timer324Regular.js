import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<g fill="none"><path d="M20.5 13.5a8.5 8.5 0 1 0-5.672 8.018a1.75 1.75 0 0 1 .675-1.956a7 7 0 1 1 3.476-5.507a4.082 4.082 0 0 1 1.455.508A8.58 8.58 0 0 0 20.5 13.5z" fill="currentColor"></path><path d="M12.743 8.648a.75.75 0 0 0-1.493.102v4.5l.007.102a.75.75 0 0 0 1.493-.102v-4.5l-.007-.102z" fill="currentColor"></path><path d="M19.23 5.174l-.083-.06a.75.75 0 0 0-.877 1.212l1.159.965l.082.06a.75.75 0 0 0 .877-1.213l-1.158-.964z" fill="currentColor"></path><path d="M15 3.25a.75.75 0 0 0-.75-.75h-4.5l-.102.007A.75.75 0 0 0 9.75 4h4.5l.102-.007A.75.75 0 0 0 15 3.25z" fill="currentColor"></path><path d="M19.375 17.734c-.135.126-.4.266-.875.266a.75.75 0 0 0 0 1.5c.474 0 .74.14.875.266s.19.283.18.44c-.015.263-.282.794-1.305.794c-.465 0-.76-.137-.928-.249a1.046 1.046 0 0 1-.208-.182a.75.75 0 0 0-1.03-.193c-.584.375-.208 1.04-.208 1.04l.001.002l.001.001l.002.003l.005.008l.013.018l.036.048a2.542 2.542 0 0 0 .556.503c.394.263.975.501 1.76.501c1.622 0 2.73-.969 2.803-2.206a2.042 2.042 0 0 0-.573-1.544c.411-.427.606-.986.573-1.544C20.98 15.969 19.872 15 18.25 15c-.785 0-1.366.238-1.76.501a2.542 2.542 0 0 0-.556.503a1.432 1.432 0 0 0-.036.048l-.013.018l-.005.008l-.002.003l-.001.002l-.001.001a.75.75 0 0 0 1.238.847a1.046 1.046 0 0 1 .208-.182c.169-.112.463-.249.928-.249c1.023 0 1.29.531 1.305.794a.546.546 0 0 1-.18.44zm-2.253 2.847l-.005-.008v.002l.004.004l.001.002zm.002-3.664l-.007.01v-.002l.004-.004l.001-.002l.002-.002z" fill="currentColor"></path></g>', 1)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer324Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
