import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.737 2.53a1.75 1.75 0 0 0-2.475 0L2.53 10.262a1.75 1.75 0 0 0 0 2.475l.775.775c.407.407.986.337 1.346.14a1.25 1.25 0 0 1 1.696 1.696c-.197.36-.266.94.14 1.347l.775.774a1.75 1.75 0 0 0 2.475 0l7.732-7.732a1.75 1.75 0 0 0 0-2.475l-.775-.775c-.406-.406-.985-.337-1.345-.14a1.25 1.25 0 0 1-1.696-1.696c.196-.36.266-.94-.14-1.346l-.776-.775z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
