from core.base_handler import BaseHandler as CoreBaseHandler
from core.exception import NotFound
from core.mysql import Session
from core.schema import AdminAccount

class BaseHandler(CoreBaseHandler):

    def get_user(self, current_user):
        session = Session()
        user = session.query(AdminAccount).filter(AdminAccount.id == current_user).first()
        session.close()

        if not user:
            return NotFound("找不到用户")

        return {
            "id": user.id,
            "name": user.name,
            "locale": self.get_argument('lang', 'zh_CN'),
        }
