import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.268 4.66c.3.299.788.299 1.089 0c.3-.298.3-.782 0-1.081a.774.774 0 0 0-1.09 0a.76.76 0 0 0 0 1.081zM1.536 6.33a1.992 1.992 0 0 0 0 2.83l3.31 3.289a2.023 2.023 0 0 0 2.154.45v-1.177l-.019.02c-.393.39-1.03.39-1.423 0l-3.31-3.29a.996.996 0 0 1 0-1.415l4.76-4.73a1.01 1.01 0 0 1 .707-.293L10.979 2c.56-.002 1.015.451 1.01 1.008l-.014 2.03c.36.057.699.178 1.004.351l.017-2.374A2.007 2.007 0 0 0 10.975 1l-3.264.014a2.02 2.02 0 0 0-1.416.586l-4.76 4.73zM9.5 8v1H9a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-.5V8a2 2 0 1 0-4 0zm1 1V8a1 1 0 1 1 2 0v1h-2zm1 2.25a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagLock16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
