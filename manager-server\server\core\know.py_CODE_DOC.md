# 代码文档 - server/core/know.py

## 文件作用
知识库服务客户端，提供与DataChat-API知识库服务的HTTP通信接口，支持文档上传、查询、管理等功能。

## 逐行代码解释

### 导入模块 (1-7行)
```python
import time                              # 时间处理
import json                              # JSON数据处理
import httpx                             # 异步HTTP客户端
from uuid import uuid4                   # UUID生成
from core.redisdb import stalecache, redis_cli  # Redis缓存和客户端
from core.utils import syncify           # 异步转同步工具
from tornado.options import options      # Tornado配置选项
```

### KnowClient类定义 (10-19行)
```python
class KnowClient(object):
    """知识库服务客户端类"""

    def __init__(self, user_id=options.CHATPDF_USER):
        """初始化客户端，默认使用配置的CHATPDF用户ID"""
        self.user_id = user_id

    def __getattr__(self, name):
        """魔术方法：自动将异步方法转换为同步方法"""
        try:
            # 尝试获取async_前缀的异步方法并转换为同步
            return syncify(object.__getattribute__(self, 'async_' + name))
        except Exception as e:
            # 如果没有对应的异步方法，返回原属性
            return object.__getattribute__(self, name)
```

### 访问令牌管理 (21-43行)
```python
@stalecache(stale=0, expire=60)
async def fetch_access_token(self, tenant_id, t):
    """获取知识库服务的访问令牌（带缓存）"""
    code = str(uuid4())                          # 生成临时授权码
    key = 'code:{}'.format(code)                 # Redis键名
    
    # 构造用户信息
    user = {
        'openid': tenant_id,                     # 用户标识
        'permission': {
            'has_privilege': True,               # 拥有权限
            'expires': time.time() + 86400,      # 24小时后过期
        }
    }
    
    # 将用户信息存储到Redis，设置过期时间
    redis_cli().pipeline().set(key, json.dumps(user)).expire(key, 1000).execute()
    
    # 向知识库服务请求访问令牌
    response = await httpx.AsyncClient().get(
        '{}/api/access_token?code={}'.format(options.KNOW_SERVER, code),
        headers={
            # 设置系统回调URL
            'X-System-Url': f'{options.SCHEMA}://{options.DOMAIN}/api/code2session',
        }
    )
    return response.json().get('access_token')

@property
def access_token(self):
    """获取访问令牌的同步属性"""
    # 使用时间戳的百分位作为缓存键的一部分，实现定期刷新
    return syncify(self.fetch_access_token)(self.user_id, int(time.time() / 100))
```

### HTTP请求方法 (45-61行)
```python
async def post(self, path, **kwargs):
    """POST请求封装"""
    return await self.request('POST', path, **kwargs)

async def get(self, path, **kwargs):
    """GET请求封装"""
    return await self.request('GET', path, **kwargs)

async def delete(self, path, **kwargs):
    """DELETE请求封装"""
    return await self.request('DELETE', path, **kwargs)

async def request(self, method='GET', path='', **kwargs):
    """通用HTTP请求方法"""
    response = await httpx.AsyncClient().request(
        method=method, 
        url='{}{}'.format(options.KNOW_SERVER, path),  # 拼接完整URL
        headers={
            'Authorization': 'Bearer {}'.format(self.access_token),  # Bearer令牌认证
        }, 
        **kwargs
    )
    return response.json()                       # 返回JSON响应
```

### 文件上传方法 (63-66行)
```python
async def async_upload(self, file_name, bytes, content_type='application/octet-stream'):
    """异步上传文件到知识库服务"""
    files = {'file': (file_name, bytes, content_type)}  # 构造文件数据
    result = await self.post('/api/upload', files=files, timeout=60)  # 上传文件
    return result['url']                         # 返回文件URL
```

### 文档管理方法 (68-84行)
```python
async def async_add_document_to_collection(self, collection_id, file_name, url, file_type, uniqid=''):
    """将文档添加到知识库集合"""
    data = {
        'fileName': file_name,                   # 文件名
        'fileUrl': url,                          # 文件URL
        'fileType': file_type,                   # 文件类型
        'uniqid': uniqid,                        # 唯一标识符
    }
    path = '/api/collection/{}/documents'.format(collection_id)
    result = await self.post(path, json=data, timeout=20)
    return result['data']

async def async_fetch_task_result(self, collection_id, task_id):
    """获取异步任务结果"""
    result = await self.get('/api/collection/{}/task/{}'.format(
        collection_id, task_id,
    ), timeout=20)
    return result['data']
```

### 知识库查询方法 (86-96行)
```python
async def async_get_collection(self, collection_id):
    """获取知识库集合信息"""
    return await self.get('/api/collection/{}'.format(collection_id), timeout=20)

async def async_query(self, collection_id, keyword, size=4):
    """在知识库中查询关键词"""
    return await self.get('/api/collection/{}/query?q={}&size={}'.format(
        ','.join(collection_id) if isinstance(collection_id, list) else collection_id,  # 支持多个集合
        keyword,                                 # 查询关键词
        size,                                    # 返回结果数量
    ), timeout=20)
```

### 文档查询方法 (98-127行)
```python
async def async_query_document(self, document_id, keyword, size=4):
    """在指定文档中查询关键词"""
    return await self.get('/api/document/{}/query?q={}&size={}'.format(
        document_id, keyword, size,
    ), timeout=20)

async def get_docs_by_document_id_and_page(self, document_id, page=1, size=20):
    """分页获取文档内容"""
    return await self.get('/api/document/{}/query?page={}&size={}'.format(
        document_id, page, size,
    ), timeout=20)

async def get_docs_by_document_id(self, document_id, size=20):
    """生成器：获取文档的所有内容"""
    page = 1
    while True:
        result = await self.get_docs_by_document_id_and_page(document_id, page, size)
        docs = result.get('data', [])
        if len(docs) == 0:
            break                                # 没有更多数据时退出
        for doc in docs:
            yield doc                            # 逐个返回文档
        page += 1

async def async_get_document(self, document_id):
    """获取文档的完整内容"""
    result = []
    async for doc in self.get_docs_by_document_id(document_id):
        result.append(doc)
    return result
```

### 文档操作方法 (129-141行)
```python
async def async_save_summary(self, document_id, summary):
    """保存文档摘要"""
    return await self.post('/api/document/{}'.format(document_id), 
                          json=dict(summary=summary), timeout=20)

async def async_get_document_info(self, document_id):
    """获取文档信息"""
    return await self.get('/api/document/{}'.format(document_id), timeout=20)

async def async_get_client_info(self):
    """获取客户端信息"""
    return await self.get('/api/collection/client', timeout=20)
```

## 技术特点

### 异步/同步双重接口
- **异步优先**: 所有核心方法都是异步实现
- **同步转换**: 通过`__getattr__`魔术方法自动提供同步版本
- **灵活调用**: 支持`client.upload()`和`await client.async_upload()`两种调用方式

### 认证和安全
- **Bearer令牌**: 使用标准的Bearer令牌认证
- **临时授权**: 通过临时授权码获取访问令牌
- **权限控制**: 支持权限验证和过期时间控制
- **缓存优化**: 访问令牌带缓存，减少重复请求

### HTTP客户端封装
- **统一接口**: 封装GET、POST、DELETE等HTTP方法
- **自动认证**: 自动添加认证头
- **超时控制**: 为不同操作设置合适的超时时间
- **错误处理**: 统一的HTTP错误处理

### 知识库功能
- **文件上传**: 支持各种格式文件的上传
- **文档管理**: 文档的添加、查询、更新、删除
- **内容搜索**: 基于关键词的语义搜索
- **分页查询**: 支持大量数据的分页获取

## 使用场景
- **知识库集成**: 将外部文档导入知识库系统
- **智能问答**: 基于知识库的问答功能
- **文档检索**: 企业文档的智能检索
- **内容管理**: 知识内容的统一管理

## 配置依赖
- **KNOW_SERVER**: 知识库服务的地址
- **CHATPDF_USER**: 默认的用户ID
- **SCHEMA**: 协议类型（http/https）
- **DOMAIN**: 服务域名

## 使用示例
```python
# 同步调用
client = KnowClient()
result = client.query('collection_id', 'keyword')

# 异步调用
client = KnowClient()
result = await client.async_query('collection_id', 'keyword')

# 文件上传
with open('file.pdf', 'rb') as f:
    url = await client.async_upload('file.pdf', f.read())
```
