<template>
  <section class="h-full bg-blend-multiply">
    <div class="h-full py-8 px-4 mx-auto max-w-screen-xl lg:py-16 lg:px-6">
      <div class="h-full mx-auto max-w-screen-sm text-center">
        <div class="flex-center mb-32">
          <component :is="iconRender({ localIcon: 'logo' })" class="text-5xl mr-2" />
        </div>
        <p class="mb-4 text-2xl tracking-tight font-bold text-gray md:text-4xl dark:text-white">
          {{ t('message.global.mobiletip') }}
        </p>
        <p class="mb-4 text-lg font-light text-gray-400">{{ t('message.global.mobiletip2') }}</p>
        <n-tooltip placement="bottom" trigger="click">
          <template #trigger>
            <button
              class="copy-btn text-white inline-flex items-center right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
            >
              <icon-akar-icons-copy class="text-lg mr-2" />
              {{ t('message.global.fzlj') }}
            </button>
          </template>
          <span>{{ t('message.global.yfz') }}</span>
        </n-tooltip>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { onMounted } from 'vue';
import ClipboardJS from 'clipboard';
import { useIconRender } from '@/composables';
const { iconRender } = useIconRender();
import { t } from "@/locales";

onMounted(() => {
  // eslint-disable-next-line no-new
  new ClipboardJS('.copy-btn', {
    text: () => {
      return location.href;
    }
  });
});
</script>
