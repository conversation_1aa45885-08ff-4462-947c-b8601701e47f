# 代码文档 - server/modules/application/model.py

## 文件作用
应用管理模块的数据模型层，提供资源管理、应用配置、AI模型集成、知识库管理等核心业务逻辑。

## 逐行代码解释

### 导入模块 (1-78行)
```python
import re                                # 正则表达式
import hashlib                           # 哈希算法
import urllib                            # URL处理
import urllib3                           # HTTP客户端
import random                            # 随机数
import time                              # 时间处理
from datetime import datetime            # 日期时间
import httpx                             # 异步HTTP客户端
import requests                          # HTTP请求库
import asyncio                           # 异步编程
import logging                           # 日志记录
import json                              # JSON处理
import functools                         # 函数工具
import textwrap                          # 文本包装
from uuid import uuid4                   # UUID生成
from concurrent.futures import ThreadPoolExecutor, as_completed  # 线程池
from typing import Optional, List, Union # 类型提示
from tornado.options import options      # Tornado配置
from tornado.httpclient import AsyncHTTPClient, HTTPRequest, HTTPError  # HTTP客户端
from sqlalchemy import alias, and_, or_, select, func, distinct, text  # SQLAlchemy

# 核心模块导入
from core.base_model import MysqlModel   # MySQL基础模型
from core.exception import NotFound, PermissionDenied, InternalError, Duplicate  # 异常类
from core.utils import (                 # 工具函数
    row2dict, ObjectDict, _,
    get_event_loop, syncify,
    compress_image,
    parse_urls,
    upload_file,
    mp3_to_opus,
    get_image_info,
)

# AI模型客户端导入
from core.wenxin import WenXinChat, WenXinClient      # 文心一言
from core.baichuan import BaichuanChat, BaichuanClient # 百川
from core.rwkv import RWKVChat, RWKVClient            # RWKV
from core.minimax import MiniMaxChat, MiniMaxClient   # MiniMax
from core.sensenova import SenseNovaClient, SenseNovaChat  # 商汤
from core.xinghuo import XingHuoChat, XingHuoClient   # 讯飞星火
from core.chatglm import ChatGLMChat, ChatGLMClient   # ChatGLM
from core.midjourney import MJChat, MJClient          # Midjourney
from core.moonshot import MoonshotChat                # 月之暗面
from core.tongyi import TongYiChat                    # 通义千问
from core.hunyuan import HunyuanChat                  # 腾讯混元
from core.gemini import GeminiChat                    # Google Gemini

# 其他服务集成
from core.duckduckgo import DDGS                      # DuckDuckGo搜索
from core.downloader import ChunkedDownloader, download_bytes  # 文件下载
from core.feishu import *                             # 飞书集成
from core.dingding import *                           # 钉钉集成
from core.wework import *                             # 企业微信集成
from core.know import *                               # 知识库集成
from core.gpts import *                               # GPTs集成
from core.rabbitmq import MqSession                   # 消息队列
from core.redisdb import redis_cli, pickle, stalecache  # Redis缓存

# 数据模型导入
from core.schema import (
    ObjID,                               # ObjectID类型
    Application,                         # 应用模型
    ApplicationImplementation,           # 应用实现模型
    Bot,                                 # 机器人模型
    BotInstance,                         # 机器人实例模型
    AppInstance,                         # 应用实例模型
    UserAccessAppInstance, Policy,       # 用户访问和策略模型
    Resource, Model, TenantWithKey, TenantResource,  # 资源和租户模型
    ChatLog,                             # 聊天日志模型
    Prompt,                              # 提示词模型
    AppInstancePrompt,                   # 应用实例提示词模型
    ApplicationSupportResource,          # 应用支持资源模型
)
```

### ResourceModel类 - 资源管理模型 (82-150+行)
```python
class ResourceModel(MysqlModel):
    """资源管理数据模型"""

    async def get_resource_category(self, page=1, size=10, lang='zh'):
        """获取资源分类列表"""
        # 构建查询条件
        query = select(Resource).where(
            Resource.status == 0,            # 正常状态
            Resource.category != '',         # 有分类
        ).order_by(Resource.sort.desc())     # 按排序字段降序
        
        # 分页查询
        offset = (page - 1) * size
        resources = await self.fetch_all(query.offset(offset).limit(size))
        
        # 获取总数
        count_query = select(func.count(Resource.id)).where(
            Resource.status == 0,
            Resource.category != '',
        )
        total = await self.fetch_val(count_query)
        
        # 处理国际化
        result = []
        for resource in resources:
            resource_dict = row2dict(resource)
            if lang == 'en' and resource.name_en:
                resource_dict['name'] = resource.name_en
            if lang == 'en' and resource.description_en:
                resource_dict['description'] = resource.description_en
            result.append(resource_dict)
        
        return result, total

    async def get_resource_price(self, resource_id):
        """获取资源价格模型列表"""
        query = select(Model).where(
            Model.resource_id == resource_id,
            Model.status == 0,               # 正常状态
        ).order_by(Model.sort.desc())
        
        models = await self.fetch_all(query)
        return [row2dict(model) for model in models]

    async def update_tenant_resource(self, tenant_id, resource_id, api_key, api_base, extra):
        """更新租户资源配置"""
        # 查找现有配置
        query = select(TenantResource).where(
            TenantResource.tenant_id == tenant_id,
            TenantResource.resource_id == resource_id,
        )
        existing = await self.fetch_one(query)
        
        if existing:
            # 更新现有配置
            update_data = {
                'api_key': api_key,
                'api_base': api_base,
                'extra': json.dumps(extra),
                'modified': datetime.now(),
            }
            await self.execute(
                TenantResource.__table__.update().where(
                    TenantResource.id == existing.id
                ).values(**update_data)
            )
        else:
            # 创建新配置
            new_resource = TenantResource(
                tenant_id=tenant_id,
                resource_id=resource_id,
                api_key=api_key,
                api_base=api_base,
                extra=json.dumps(extra),
                status=0,
                created=datetime.now(),
                modified=datetime.now(),
            )
            await self.execute(TenantResource.__table__.insert().values(
                **row2dict(new_resource)
            ))
```

### AppModel类 - 应用管理模型 (主要方法)
```python
class AppModel(MysqlModel):
    """应用管理数据模型"""

    async def get_application_list(self, tenant_id, page=1, size=10, category='', keyword=''):
        """获取应用列表"""
        # 构建查询条件
        conditions = [
            Application.status == 0,         # 正常状态
            or_(
                Application.tenant_id == tenant_id,  # 租户自己的应用
                Application.public == 1,     # 或公开应用
            )
        ]
        
        if category:
            conditions.append(Application.category == category)
        
        if keyword:
            conditions.append(
                or_(
                    Application.name.like(f'%{keyword}%'),
                    Application.description.like(f'%{keyword}%'),
                )
            )
        
        query = select(Application).where(and_(*conditions)).order_by(
            Application.created.desc()
        )
        
        # 分页查询
        offset = (page - 1) * size
        applications = await self.fetch_all(query.offset(offset).limit(size))
        
        # 获取总数
        count_query = select(func.count(Application.id)).where(and_(*conditions))
        total = await self.fetch_val(count_query)
        
        return [row2dict(app) for app in applications], total

    async def create_application(self, tenant_id, name, description, category, config):
        """创建新应用"""
        # 检查应用名称是否重复
        existing_query = select(Application).where(
            Application.tenant_id == tenant_id,
            Application.name == name,
            Application.status == 0,
        )
        existing = await self.fetch_one(existing_query)
        
        if existing:
            raise Duplicate("应用名称已存在")
        
        # 创建新应用
        new_app = Application(
            id=ObjID.new_id(),
            tenant_id=tenant_id,
            name=name,
            description=description,
            category=category,
            config=json.dumps(config),
            status=0,
            created=datetime.now(),
            modified=datetime.now(),
        )
        
        await self.execute(Application.__table__.insert().values(
            **row2dict(new_app)
        ))
        
        return new_app.id

    async def get_application_by_id(self, app_id, tenant_id=None):
        """根据ID获取应用详情"""
        conditions = [
            Application.id == app_id,
            Application.status == 0,
        ]
        
        if tenant_id:
            conditions.append(
                or_(
                    Application.tenant_id == tenant_id,
                    Application.public == 1,
                )
            )
        
        query = select(Application).where(and_(*conditions))
        application = await self.fetch_one(query)
        
        if not application:
            raise NotFound("应用不存在")
        
        return row2dict(application)
```

## 技术特点

### 多AI模型集成
- **模型支持**: 集成20+主流AI模型（GPT、文心一言、通义千问等）
- **统一接口**: 为不同AI模型提供统一的调用接口
- **动态配置**: 支持运行时切换和配置不同的AI模型
- **负载均衡**: 支持多个API密钥的负载均衡

### 资源管理
- **分类管理**: 支持AI资源的分类和标签管理
- **价格模型**: 支持不同资源的价格和计费模式
- **租户隔离**: 基于租户的资源配置和权限控制
- **国际化**: 支持多语言的资源描述

### 应用生命周期
- **应用创建**: 支持自定义应用的创建和配置
- **版本管理**: 支持应用的版本控制和更新
- **部署管理**: 支持应用的部署和实例管理
- **权限控制**: 基于租户和用户的权限管理

### 知识库集成
- **文档管理**: 支持多种格式文档的上传和管理
- **向量检索**: 基于向量的语义检索功能
- **知识问答**: 基于知识库的智能问答
- **增量同步**: 支持知识库的增量更新

## 使用场景
- **AI应用开发**: 快速构建基于AI的应用
- **企业集成**: 为企业提供AI能力的集成
- **多租户SaaS**: 支持多租户的AI服务平台
- **知识管理**: 企业知识库的智能化管理

## 数据模型关系
- **Application**: 应用基础信息
- **AppInstance**: 应用实例（部署后的应用）
- **Resource**: AI资源（模型、API等）
- **TenantResource**: 租户资源配置
- **Bot**: 机器人配置
- **ChatLog**: 对话日志

## 扩展性设计
- **插件机制**: 支持新AI模型的插件式集成
- **配置驱动**: 通过配置文件控制功能开关
- **异步架构**: 基于异步的高性能架构
- **缓存优化**: 多层缓存提升性能
