class RemoveVcTool(CTool):
    name: str = 'RemoveVc'
    description: str = 'vocal remove tool'

    def get_file_type(self, file_name):
        file_type = file_name.split('.').pop()
        if file_type in ["wav", "mp3", "m4a", "flac", "ogg"]:
            return file_type
        else:
            send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('支持上传wav，mp3，m4a，flac，ogg格式音频文件'), tag='lark_md'),
                    header=FeishuMessageCardHeader(_('暂不支持您上传的文件类型'), template='blue'),
                )
            )
        raise Exception("NOT_SUPPORT")

    def _run(self, *args, run_manager=None, input='', **kwargs):
        logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class RemoveVcCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):

                logging.info("debug entering on_llm_start")
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            # FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_end(self, response, *args, **kwargs):
                #logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs

                vocal_url = additional_kwargs.get('url', None)
                other_url = additional_kwargs.get('url_others', None)
                if vocal_url is None or other_url is None:
                    raise Exception("NOT_FOUND")

                if platform == 'feishu':
                    time.sleep(1)

                    # # 分别上传两个文件。构建得到两个 file_key
                    # vocal_key = syncify(client.upload_file)(vocal_url, 'vocal.wav')
                    # other_key = syncify(client.upload_file)(other_url, 'other.wav')

                    # vocal_url  = vocal_url.replace('https://storage.googleapis.com', 'https://mpic.forkway.cn')
                    # other_url  = other_url.replace('https://storage.googleapis.com', 'https://mpic.forkway.cn')

                    proxies = {
                        "http://": options.PROXY_WEB,
                        "https://": options.PROXY_WEB,
                    } if options.DOMAIN != 'connectai-e.com' else None

                    def _download(url) -> bytes:
                        with httpx.Client(proxies=proxies) as client:
                            resp = client.get(url)
                            if resp.status_code != 200:
                                raise Exception("DOWNLOAD_ERROR")
                            return resp.content

                    vocal_binary = _download(vocal_url)
                    vocal_url = syncify(upload_file)({'body': vocal_binary, 'filename': 'vocal.wav'}) # 确认此处有 util.upload_file

                    other_binary = _download(other_url)
                    other_url = syncify(upload_file)({'body': other_binary, 'filename': 'other.wav'})

                    logging.info("DEBUG %r %r", vocal_url, other_url)

                    btns = [
                        FeishuMessageButton(_('下载人声'), url=vocal_url),
                        FeishuMessageButton(_('下载背景音'), url=other_url),
                    ]

                    contents = [
                        FeishuMessageAction(*btns),
                    ]

                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                            header=FeishuMessageCardHeader(content=_('AI人声去除小助手 🎉'), template='blue'),
                        )
                    )

                    # # 消息卡片不支持内嵌文件，因此分别发送两个文件
                    # send_message(
                    #     AppResult.File,
                    #     FeishuFileMessage(file_key=vocal_key),
                    # )

                    # send_message(
                    #     AppResult.File,
                    #     FeishuFileMessage(file_key=other_key),
                    # )

                else:
                    # 钉钉消息
                    # 暂时不支持钉钉
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title='AI人声去除小助手 🎉',
                        )
                    )
                    
            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""

                # TODO：这里也没处理钉钉
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))


        model.callbacks = [RemoveVcCallbackHandler()]
        chat = RVChat(**model)
        if platform == 'feishu':
            # TODO: 这里切换到拿文件（语音的接口）

            message_id = data.extra.message_id
            content = data.extra.extra.platform_content
            file_key = content.file_key
            file_name = content.file_name
            file_type = self.get_file_type(file_name)

            logging.info("debug %r %r %r", message_id, file_key, file_type)
            logging.info("debug %r", data.extra)

            # 这里要 file_key 拿 url

            file_url = f"https://{options.DOMAIN}/chat/feishu/message/file?message_id={message_id}&file_key={file_key}&file_type={file_type}"

            messages = [HumanMessage(content='', additional_kwargs=dict(url=file_url))]
            return chat.invoke(messages)
        elif platform == 'dingding':

            # TODO：暂不支持钉钉
            raise Exception("NOT_SUPPORT")

            # data_extra = data.get('extra', {})
            # input_kwargs = data_extra.get('input_kwargs', {})
            # with DingDingModel() as ddmodel:
            #     ddmodel.init_by_bot_id(data_extra.get('bot_instance_id'))
            #     robot_code = ddmodel.bot.app_id

            #     # TODO: 同理，这里切换到拿文件（语音的接口）
            #     img_url = syncify(client.get_message_resource)(robot_code, input_kwargs.get('img_key'))
            #     input_kwargs.update({'content': httpx.get(img_url).content})

            #     messages = [HumanMessage(content='', additional_kwargs=dict(input_kwargs))]
            #     return chat(messages=messages)


# class DingdingCommand(CommandTool):

#     next_tool_name: str = 'RemoveVc'
#     name: str = 'RemoveVc_dingding_command'
#     description: str = 'RemoveVc dingding command'
#     mode: List[str] = ['moises']

#     def send_usage(self):
        
#         # TODO 可使用ActionCardMessage替代，dtmdLink放到actionURL内即可
#         return send_message(
#             AppResult.ReplyActionCard,
#             DingDingMarkdownMessage(
#                 text=_('💥 **我是AI人声分离小助手，可以帮您分离音频中的人声**\n\n---\n🧹 \
#                        **AI 人声分离**\n\n选择音频文件发送给BOT，BOT将回复分离好的人声和背景音文件\n\n'),
#                 title=_('🎒 需要帮助吗？')
#             )
#         )

#     def parse_command(self, input, action):
#         if input[:5] == '/help' or input[:2] == '帮助':
#             return 'help',
#         if data.get('extra', {}).get('message_type', '') == 'post':
#             return 'post',
#         return 'help',
    

#     def on_post(self):
#         # 富文本消息
#         data_extra = data.get('extra', {})
#         rich_text = data_extra.get('extra', {}).get('platform_content', {}).get('richText', [])
#         input_img_keys = []
#         for msg in rich_text:
#             # TODO: 这里切换到拿文件（语音的接口）
#             if msg.get('type', '') == 'picture':
#                 input_img_keys.append(msg['downloadCode'])
#         input_kwargs = {}
#         if not input_img_keys:
#             send_message(AppResult.ReplyText, _('🤖️：请上传图片！'))
#             return None
#         elif len(input_img_keys) == 1:
#             input_kwargs = {'img_key': input_img_keys[0]}
#         else:
#             send_message(AppResult.ReplyText, _('🤖️：请一次上传一张图片！'))
#             return None

#         if not input_kwargs:
#             send_message(AppResult.ReplyText, _('🤖️：输入格式不合法，请参考帮助！'))
#             return None
#         data_extra['input_kwargs'] = input_kwargs
#         return self.next_tool_name


class FeishuCommand(CommandTool):

    next_tool_name: str = 'RemoveVc'
    name: str = 'RemoveVc_feishu_command'
    description: str = 'RemoveVc feishu command'
    mode: List[str] = ['moises']
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '发送音频，BOT回复已分离的人声和背景音频',
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是AI人声分离小助手，可以帮您分离音频中的人声**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🧹 **AI 人声分离 使用**\n选择音频文件发送给BOT，BOT将回复分离好的人声和背景音文件'),
                    tag='lark_md',
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒 需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        if data.extra.message_type in ['file']:
            return None,
        return 'note',

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送文件（仅支持WAV，MP3，M4A，FLAC，OGG格式），不支持自然语言（除帮助外）、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文件！')
        return send_note(text, title)


class RemoveVcAgent(CAgent):

    class AppConfig(BaseAppConfig):
        # TODO: 把所有项换掉
        name: str = 'RemoveVc'
        title: str = 'AI人声去除助手'
        category: str = '音视频'
        title_en: str = 'vocal remove helper'
        description: str = '🏹发送音频，利用AI算法轻松分离人声和背景音'
        description_en: str = '🏹 Quickly Remove Vocal With AI'
        problem: str = '如何快速分离人声和背景音'
        problem_en: str = 'How to quickly remove vocal and background voice.'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/VHRLdvq8Koi0d8xZwrHcDdfunff'
        manual_en: str = 'https://connect-ai.feishu.cn/docx/Mt5pdQ5Xko3g7axtqb0cQth2nXf'
        icon: str = 'https://pic1.forkway.cn/cdn/20231102140907.png'
        logo: str = 'https://pic1.forkway.cn/cdn/20231102140907.png?imageMogr2/thumbnail/720x'
        sorted: int = 108
        support_resource: List[object] = [dict(
            category=ModelCategory.Audio.value,
            scene=ModelCategory.Audio.value,
            title='音频处理',
            tip='',
            required=True,
            resource=['Moises.AI']
        )]
        support_bots: List[str] = ['feishu'] # DingDing 暂不支持
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnBb6w74sJOwI9zI04grgQsh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/Respwv08ui5fb5kT3TTcaltNn3c'
        show: int = 0
        action_template = "" # 先不做快捷应用（这个好像没什么做快捷应用的必要）

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='RemoveVc_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='RemoveVc_dingding_command', tool_input=kwargs, log="")
        # if platform == 'feishu' and self.parse_command(**kwargs):
        #     return AgentFinish({"output": "command"}, kwargs.get('input'))
        return AgentAction(tool='RemoveVc', tool_input=kwargs, log="")


