Metadata-Version: 2.4
Name: elasticsearch-dsl
Version: 8.18.0
Summary: Python client for Elasticsearch
Home-page: https://github.com/elasticsearch/elasticsearch-dsl-py
Author: Elastic Client Library Maintainers
Author-email: <EMAIL>
License: Apache-2.0
Classifier: Development Status :: 4 - Beta
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: python-dateutil
Requires-Dist: typing-extensions
Requires-Dist: elasticsearch<9.0.0,>=8.0.0
Requires-Dist: elastic-transport<9.0.0,>=8.0.0
Provides-Extra: async
Requires-Dist: elasticsearch[async]<9.0.0,>=8.0.0; extra == "async"
Provides-Extra: develop
Requires-Dist: elasticsearch[async]; extra == "develop"
Requires-Dist: unasync; extra == "develop"
Requires-Dist: jinja2; extra == "develop"
Requires-Dist: pytest; extra == "develop"
Requires-Dist: pytest-cov; extra == "develop"
Requires-Dist: pytest-mock; extra == "develop"
Requires-Dist: pytest-asyncio; extra == "develop"
Requires-Dist: pytz; extra == "develop"
Requires-Dist: coverage; extra == "develop"
Requires-Dist: nltk; extra == "develop"
Requires-Dist: sentence_transformers; extra == "develop"
Requires-Dist: tqdm; extra == "develop"
Requires-Dist: sphinx>2; extra == "develop"
Requires-Dist: sphinx-rtd-theme>0.5; extra == "develop"
Requires-Dist: mypy; extra == "develop"
Requires-Dist: pyright; extra == "develop"
Requires-Dist: types-python-dateutil; extra == "develop"
Requires-Dist: types-pytz; extra == "develop"
Requires-Dist: types-tqdm; extra == "develop"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Elasticsearch DSL
=================

Deprecation notice
------------------

As of release 8.18.0, the Elasticsearch DSL package is part of the official
`Elasticsearch Python client <https://github.com/elastic/elasticsearch-py>`_,
so a separate install is not needed anymore. To migrate, follow these steps:

- Uninstall the ``elasticsearch-dsl`` package
- Make sure you have version 8.18.0 or newer of the ``elasticsearch`` package
  installed
- Replace ``elasticsearch_dsl`` with ``elasticsearch.dsl`` in your imports

To prevent applications from breaking unexpectedly due to this change, the
8.18.0 release of this package automatically redirects all imports to the
corresponding modules of the Python client package.

The following instructions only apply to versions 8.17 and older of this
package.

Introduction
------------

Elasticsearch DSL is a high-level library whose aim is to help with writing and
running queries against Elasticsearch. It is built on top of the official
low-level client (`elasticsearch-py <https://github.com/elastic/elasticsearch-py>`_).

It provides a more convenient and idiomatic way to write and manipulate
queries. It stays close to the Elasticsearch JSON DSL, mirroring its
terminology and structure. It exposes the whole range of the DSL from Python
either directly using defined classes or a queryset-like expressions.

It also provides an optional wrapper for working with documents as Python
objects: defining mappings, retrieving and saving documents, wrapping the
document data in user-defined classes.

To use the other Elasticsearch APIs (eg. cluster health) just use the
underlying client.

Installation
------------

::

  pip install elasticsearch-dsl

Examples
--------

Please see the `examples
<https://github.com/elastic/elasticsearch-dsl-py/tree/master/examples>`_
directory to see some complex examples using ``elasticsearch-dsl``.

Compatibility
-------------

The library is compatible with all Elasticsearch versions since ``2.x`` but you
**have to use a matching major version**:

For **Elasticsearch 8.0** and later, use the major version 8 (``8.x.y``) of the
library.

For **Elasticsearch 7.0** and later, use the major version 7 (``7.x.y``) of the
library.

For **Elasticsearch 6.0** and later, use the major version 6 (``6.x.y``) of the
library.

For **Elasticsearch 5.0** and later, use the major version 5 (``5.x.y``) of the
library.

For **Elasticsearch 2.0** and later, use the major version 2 (``2.x.y``) of the
library.

The recommended way to set your requirements in your `setup.py` or
`requirements.txt` is::

    # Elasticsearch 8.x
    elasticsearch-dsl>=8.0.0,<9.0.0

    # Elasticsearch 7.x
    elasticsearch-dsl>=7.0.0,<8.0.0

    # Elasticsearch 6.x
    elasticsearch-dsl>=6.0.0,<7.0.0

    # Elasticsearch 5.x
    elasticsearch-dsl>=5.0.0,<6.0.0

    # Elasticsearch 2.x
    elasticsearch-dsl>=2.0.0,<3.0.0


The development is happening on ``main``, older branches only get bugfix releases

Search Example
--------------

Let's have a typical search request written directly as a ``dict``:

.. code:: python

    from elasticsearch import Elasticsearch
    client = Elasticsearch("https://localhost:9200")

    response = client.search(
        index="my-index",
        body={
          "query": {
            "bool": {
              "must": [{"match": {"title": "python"}}],
              "must_not": [{"match": {"description": "beta"}}],
              "filter": [{"term": {"category": "search"}}]
            }
          },
          "aggs" : {
            "per_tag": {
              "terms": {"field": "tags"},
              "aggs": {
                "max_lines": {"max": {"field": "lines"}}
              }
            }
          }
        }
    )

    for hit in response['hits']['hits']:
        print(hit['_score'], hit['_source']['title'])

    for tag in response['aggregations']['per_tag']['buckets']:
        print(tag['key'], tag['max_lines']['value'])



The problem with this approach is that it is very verbose, prone to syntax
mistakes like incorrect nesting, hard to modify (eg. adding another filter) and
definitely not fun to write.

Let's rewrite the example using the Python DSL:

.. code:: python

    from elasticsearch import Elasticsearch
    from elasticsearch_dsl import Search

    client = Elasticsearch("https://localhost:9200")

    s = Search(using=client, index="my-index") \
        .filter("term", category="search") \
        .query("match", title="python")   \
        .exclude("match", description="beta")

    s.aggs.bucket('per_tag', 'terms', field='tags') \
        .metric('max_lines', 'max', field='lines')

    response = s.execute()

    for hit in response:
        print(hit.meta.score, hit.title)

    for tag in response.aggregations.per_tag.buckets:
        print(tag.key, tag.max_lines.value)

As you see, the library took care of:

- creating appropriate ``Query`` objects by name (eq. "match")
- composing queries into a compound ``bool`` query
- putting the ``term`` query in a filter context of the ``bool`` query
- providing a convenient access to response data
- no curly or square brackets everywhere


Persistence Example
-------------------

Let's have a simple Python class representing an article in a blogging system:

.. code:: python

    from datetime import datetime
    from elasticsearch_dsl import Document, Date, Integer, Keyword, Text, connections

    # Define a default Elasticsearch client
    connections.create_connection(hosts="https://localhost:9200")

    class Article(Document):
        title = Text(analyzer='snowball', fields={'raw': Keyword()})
        body = Text(analyzer='snowball')
        tags = Keyword()
        published_from = Date()
        lines = Integer()

        class Index:
            name = 'blog'
            settings = {
              "number_of_shards": 2,
            }

        def save(self, ** kwargs):
            self.lines = len(self.body.split())
            return super(Article, self).save(** kwargs)

        def is_published(self):
            return datetime.now() > self.published_from

    # create the mappings in elasticsearch
    Article.init()

    # create and save and article
    article = Article(meta={'id': 42}, title='Hello world!', tags=['test'])
    article.body = ''' looong text '''
    article.published_from = datetime.now()
    article.save()

    article = Article.get(id=42)
    print(article.is_published())

    # Display cluster health
    print(connections.get_connection().cluster.health())


In this example you can see:

- providing a default connection
- defining fields with mapping configuration
- setting index name
- defining custom methods
- overriding the built-in ``.save()`` method to hook into the persistence
  life cycle
- retrieving and saving the object into Elasticsearch
- accessing the underlying client for other APIs

You can see more in the persistence chapter of the documentation.

Migration from ``elasticsearch-py``
-----------------------------------

You don't have to port your entire application to get the benefits of the
Python DSL, you can start gradually by creating a ``Search`` object from your
existing ``dict``, modifying it using the API and serializing it back to a
``dict``:

.. code:: python

    body = {...} # insert complicated query here

    # Convert to Search object
    s = Search.from_dict(body)

    # Add some filters, aggregations, queries, ...
    s.filter("term", tags="python")

    # Convert back to dict to plug back into existing code
    body = s.to_dict()

Development
-----------

Activate Virtual Environment (`virtualenvs <http://docs.python-guide.org/en/latest/dev/virtualenvs/>`_):

.. code:: bash

    $ virtualenv venv
    $ source venv/bin/activate

To install all of the dependencies necessary for development, run:

.. code:: bash

    $ pip install -e '.[develop]'

To run all of the tests for ``elasticsearch-dsl-py``, run:

.. code:: bash

    $ python setup.py test

Alternatively, it is possible to use the ``run_tests.py`` script in
``test_elasticsearch_dsl``, which wraps `pytest
<http://doc.pytest.org/en/latest/>`_, to run subsets of the test suite. Some
examples can be seen below:

.. code:: bash

    # Run all of the tests in `test_elasticsearch_dsl/test_analysis.py`
    $ ./run_tests.py test_analysis.py

    # Run only the `test_analyzer_serializes_as_name` test.
    $ ./run_tests.py test_analysis.py::test_analyzer_serializes_as_name

``pytest`` will skip tests from ``test_elasticsearch_dsl/test_integration``
unless there is an instance of Elasticsearch on which a connection can occur.
By default, the test connection is attempted at ``localhost:9200``, based on
the defaults specified in the ``elasticsearch-py`` `Connection
<https://github.com/elastic/elasticsearch-py/blob/master/elasticsearch
/connection/base.py#L29>`_ class. **Because running the integration
tests will cause destructive changes to the Elasticsearch cluster, only run
them when the associated cluster is empty.** As such, if the
Elasticsearch instance at ``localhost:9200`` does not meet these requirements,
it is possible to specify a different test Elasticsearch server through the
``TEST_ES_SERVER`` environment variable.

.. code:: bash

    $ TEST_ES_SERVER=my-test-server:9201 ./run_tests

Documentation
-------------

Documentation is available at https://elasticsearch-dsl.readthedocs.io.

Contribution Guide
------------------

Want to hack on Elasticsearch DSL? Awesome! We have `Contribution-Guide <https://github.com/elastic/elasticsearch-dsl-py/blob/master/CONTRIBUTING.rst>`_.

License
-------

Copyright 2013 Elasticsearch

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
