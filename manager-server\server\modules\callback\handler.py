#!/usr/bin/env python
# coding=utf-8
import hashlib
import httpx
import json
import logging
import random
import string
import time
from jinja2 import Template
from tempfile import NamedTemporaryFile
from tornado.options import options
from core.base_handler import (
    BaseHandler,
    arguments,
)
from core.exception import NotFound
from core.utils import ObjIDStr, ObjectDict, compress_image, _, upload_file
from core.dingding import DingDingCallbackError
from core.feishu import Event, decrypt_with_aes
from .model import (
    FeishuModel,
    DingDingModel,
    WXWorkModel,
    WEWorkModel,
    BotInstanceModel,
)
from settings.constant import VALIDATION_TOKEN


class FeishuCallbackHandler(BaseHandler):

    @arguments
    async def post(self, bot_id: ObjIDStr = '', model: FeishuModel = None):
        # 飞书可以使用event.header.app_id初始化应用

        logging.info("debug request %r", [self.request.body, list(self.request.headers.items())])
        try:
            # 如果bot_id存在的情况，优先使用bot_id初始化
            event = Event(self.request, '', '')
            # 飞书event会传一个app_id，使用这个参数，就可以把url中的bot_id去掉
            # 但是前提是移除Encrypt Key以及Verification Token
            # 飞书应用第一次创建的时候，只有Verification Token，没有Encrypt Key，不设置的时候就是明文的json数据
            if 'header' in event.data:
                if bot_id:
                    model.init_by_bot_id(bot_id)
                else:
                    model.init_by_app_id(event.data.header.app_id)
            elif 'event' in event.data:
                # v1版本event
                pass
            elif 'open_message_id' in event.data:
                # 如果是卡片消息回调，可以通过open_message_id找到消息，然后初始化成功
                model.init_by_message_id(event.data.open_message_id)
        except Exception as e:
            logging.error(e)
            model.init_by_bot_id(bot_id)
            event = Event(self.request, model.bot.validation_token, model.bot.encript_key)

        if event.data.get('type') == 'url_verification':
            self.finish(await model.url_verification(event))
            return

        logging.info("debug event %r", event.data)
        await model.handler(event)
        self.finish()



class DingDingCallbackHandler(BaseHandler):

    @arguments
    async def get(self, bot_id: ObjIDStr = '', model: FeishuModel = None):
        # GET方法没有什么用，只是在配置的时候看一下能不能调通
        pass

    @arguments
    async def post(self, bot_id: ObjIDStr = '', robotCode: str = '', model: DingDingModel = None):
        # 钉钉消息其实是会传一个robotCode的，所以，url里面可以去掉bot_id
        if bot_id:
            model.init_by_bot_id(bot_id)
        elif robotCode and 'normal' != robotCode:
            model.init_by_robot_code(robotCode)
        else:
            model.init_by_bot_id(bot_id)

        logging.info("debug %r", self.request.body)
        if not model.validate_sign(self.request.headers.get('Timestamp'), self.request.headers.get('Sign')):
            raise DingDingCallbackError('无效消息')

        message = ObjectDict(json.loads(self.request.body.decode()))
        reply_message = await model.handler(message)
        if reply_message:
            self.finish(reply_message)


class FeishuImageHandler(BaseHandler):
    @arguments
    async def get(self, message_id='', image_key='', bot_id='', app_id='', reply_message_id='', source='', compress=False, quality=80, save_format='', max_size=1024, auto_download=True):
        with FeishuModel() as model:
            if bot_id:
                model.init_by_bot_id(bot_id)
            elif app_id:
                model.init_by_app_id(app_id)
            elif reply_message_id:
                model.init_by_message_id(reply_message_id)
            else:
                model.init_by_origin_message_id(message_id)
            if not getattr(model, 'client'):
                # 无权限
                if source == 'shortcut':
                    return self.redirect('')
                return self.set_status(401)
            if reply_message_id:
                bytes = await model.client.download_image(image_key)
            else:
                bytes = await model.client.get_message_resource(message_id, image_key)
            if compress:
                bytes = compress_image(bytes, quality=int(quality), save_format=save_format.upper(), max_size=int(max_size))
            save_format = save_format or 'png'
            self.set_header('Content-Type', 'image/{}'.format(save_format.lower()))
            self.set_header('Cache-Control', 'public, max-age=864000000')
            if auto_download:
                self.set_header('Content-Disposition', 'attachment; filename="{}.{}"'.format(image_key, save_format.lower()))
            self.finish(bytes)


class DingDingImageHandler(BaseHandler):
    @arguments
    async def get(self, message_id, download_code):
        with DingDingModel() as model:
            model.init_by_origin_message_id(message_id)
            robotCode = model.bot.app_id
            url = await model.client.get_message_resource(robotCode, download_code)
            logging.info('logging cb dd: %r %r %r %r', message_id, robotCode, download_code, url)
            self.set_header('Content-Type', 'image/png')
            self.set_header('Content-Disposition', 'attachment; filename="{}.png"'.format(message_id))
            self.redirect(url)


class FeishuFileHandler(BaseHandler):
    @arguments
    async def get(self, message_id='', file_key='', bot_id='', app_id='', file_type=''):
        """
        Download required resource

        Params:
          - message_id: message id : required if bot_id is not provided
          - file_key: file key : required
          - bot_id: bot id : required if message_id is not provided
          - app_id: app id : required if message_id/bot_id is not provided
          - file_type: file type : optional

        Return:
          - file: bytes
        """
        with FeishuModel() as model:
            if bot_id:
                model.init_by_bot_id(bot_id)
            elif app_id:
                model.init_by_app_id(app_id)
            else:
                model.init_by_origin_message_id(message_id)
            bytes = await model.client.get_message_resource(message_id, file_key, type='file')
            if file_type == '':
                if '/message.' in self.request.uri:
                    file_type = self.request.uri.split('?')[0].split('.')[-1]
                else:
                    file_type = 'octet-stream'
            self.set_header('Content-Type', f'application/{file_type}')
            #self.set_header('Content-Disposition', 'attachment; filename="{}"'.format(message_id))
            self.set_header('Content-Disposition', 'attachment; filename="{}{}"'.format(message_id, ('.' + file_type) if '-' not in file_type else ''))
            self.finish(bytes)


class WXWorkCallbackHandler(BaseHandler):

    @arguments
    async def get(
        self,
        msg_signature: str = '',
        timestamp: str = '',
        nonce: str = '',
        echostr: str = '',
        model: WXWorkModel = None,
    ):
        # 这里是校验服务商的接口GET方式，校验成功输出echostr
        self.finish(model.check_signature(msg_signature, timestamp, nonce, echostr))

    @arguments
    async def post(
        self,
        msg_signature: str = '',
        timestamp: str = '',
        nonce: str = '',
        model: WXWorkModel = None,
    ):
        logging.debug("body %r", self.request.body)
        response = await model.handler(self.request.body.decode(), msg_signature, timestamp, nonce)
        self.finish(response or 'success')


class WXWorkAuthHandler(BaseHandler):

    @arguments
    async def get(
        self,
        instance_id: ObjIDStr,
        bot_instance_id: ObjIDStr,
        auth_code: str = '', expires_in: int = 0,
        state: str = '',  # 这里使用bot_instance_id
        model: WXWorkModel = None,
    ):
        logging.info("WXWorkAuthHandler")
        # 先强行设定不返回json数据
        model.init_by_bot_id(bot_instance_id)
        model.init_by_instance_id(instance_id)
        self.set_header("Content-Type", "text/html; charset=UTF-8")
        # 如果是授权后redirect回来，就调用父窗口，认证成功，然后关掉自己
        if auth_code and expires_in and state:
            await model.auth(auth_code, expires_in, state)
            # 这里检查能不能使用window.opener,或者window.parent
            return self.finish('''
<script>
try {{
    (window.opener || window.parent)['auth_callback_{}']()
}}catch(e) {{
    console.error(e)
}} finally {{
    // window.close()
    // Scripts may close only the windows that were opened by them.
    window.open("", '_self').window.close();
    // 如果关闭失败了，跳转到新的页面
    setTimeout(() => location.href = location.origin, 3000)
}}
</script>
                               '''.format(state))
        """
        1. https://open.work.weixin.qq.com/3rdapp/install?suite_id=SUITE_ID&pre_auth_code=PRE_AUTH_CODE&redirect_uri=REDIRECT_URI&state=STATE
        2. redirect_uri?auth_code=xxx&expires_in=600&state=xx
        """
        auth_url = await model.get_auth_url()
        self.redirect(auth_url)


class WEWorkCallbackHandler(BaseHandler):

    @arguments
    async def get(
        self,
        bot_id: ObjIDStr = '',
        msg_signature: str = '',
        timestamp: str = '',
        nonce: str = '',
        echostr: str = '',
        model: WEWorkModel = None,
    ):
        # 这里是校验的接口GET方式，校验成功输出echostr
        if bot_id:
            model.init_by_bot_id(bot_id)
            self.finish(model.check_signature(msg_signature, timestamp, nonce, echostr))

    @arguments
    async def post(
        self,
        bot_id: ObjIDStr = '',
        msg_signature: str = '',
        timestamp: str = '',
        nonce: str = '',
        model: WEWorkModel = None,
    ):
        logging.debug("body %r", self.request.body)
        if bot_id:
            model.init_by_bot_id(bot_id)
            response = await model.handler(self.request.body.decode(), msg_signature, timestamp, nonce)
            self.finish(response or 'success')


class FeishuActionHandler(BaseHandler):
    @arguments
    async def get(self, bot_id: str = '', model: FeishuModel = None):
        self.set_header("Content-Type", "text/html; charset=UTF-8")
        model.init_by_bot_id(bot_id)
        app_id = model.bot.app_id
        url = f"{options.SCHEMA}://{options.DOMAIN}{self.request.uri}"
        logging.info("url %r", url)
        ticket = await model.client.get_ticket()
        timestamp = int(time.time()) * 1000
        nonce_str = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(24))
        verify_str = "jsapi_ticket={}&noncestr={}&timestamp={}&url={}".format(
            ticket, nonce_str, timestamp, url
        )
        # 对字符串做sha1加密，得到签名signature
        signature = hashlib.sha1(verify_str.encode("utf-8")).hexdigest()

        if isinstance(model.bot.info, dict) and 'open_id' in model.bot.info:
            open_id = model.bot.info['open_id']
        else:
            info = await model.client.bot_info(app_id)
            open_id = info['open_id']
        bot_model = BotInstanceModel(model.bot)
        app_instance = bot_model.get_current_app_instance(open_id)
        # logging.info('app instance: %r %r %r', open_id, app_instance.application_id, app_instance.name)
        template = Template(bot_model.get_application_template(app_instance.application_id))
        logging.info('user agent %r', self.request.headers.get('User-Agent'))
        ua = self.request.headers.get('User-Agent')
        lang = ua.split('LarkLocale/').pop().split(' ')[0] or options.DEFAULT_LOCALE
        # 尝试做国际化
        template = template.render(lang=lang, _=_)
        # logging.info('app template: %r %r', app_instance.name, template)
        # 将鉴权所需参数返回给前端
        # 这里放一些全局的变量，例如bot_id, instance_id, app_id...
        context = {
            'bot_id': bot_id,
            'app_name': app_instance.name,
            # 将鉴权所需参数返回给前端
            'signature': {
                "appId": model.bot.app_id,
                "signature": signature,
                "nonceStr": nonce_str,
                "timestamp": timestamp,
                "jsApiList": [
                    'getUserInfo',
                    'getBlockActionSourceDetail'
                ],
            },
        }
        self.finish("""
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="UTF-8" />
            <title>""" + context['app_name'] + """</title>
            <link href="https://cdn.jsdelivr.net/npm/reset-css@5.0.2/reset.min.css" rel="stylesheet">
            <script type="text/javascript" src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.16.js"></script>
            <script type="module" src="https://unpkg.com/quarkd/lib/button/index.js"></script>
            <script type="module" src="https://unpkg.com/quarkd/lib/image/index.js"></script>
            <script type="text/javascript">
                (function(c,l,a,r,i,t,y){
                    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                    t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "jj20o0ve27");
            </script>
            <script type="module">
              // https://github.com/yyx990803/vue-lit/blob/master/index.js
              import { html, render } from 'https://unpkg.com/lit-html'
              import { shallowReactive, effect } from 'https://unpkg.com/@vue/reactivity/dist/reactivity.esm-browser.js'
              var context = """ + json.dumps(context) + """
              // 公共的从消息获取图片的函数
              function parseImageFromMessage(item) {
                const imageKeys = []
                if (item.messageType == "image") {
                  imageKeys.push({
                    image_key: item.content.url.split('image_key=').pop().split('&')[0],
                    message_id: item.openMessageId,
                    bot_id: context.bot_id,
                  })
                } else if (item.messageType == "post") {
                  item.content.content.forEach(x => {
                    x.attrs.forEach(y => {
                      if (y.tag == "img") {
                        imageKeys.push({
                          image_key: y.url.split('image_key=').pop().split('&')[0],
                          message_id: item.openMessageId,
                          bot_id: context.bot_id,
                        })
                      }
                    })
                  })
                } else if (item.messageType == "interactive") {
                  item.content.elements.forEach(x => {
                    x.forEach(y => {
                      if (y.tag == "img") {
                        imageKeys.push({
                          image_key: y.image_key,
                          message_id: item.openMessageId,
                        })
                      }
                    })
                  })
                } 
                return imageKeys.map(img => ({
                  ...img,
                  bot_id: img.bot_id || context.bot_id,
                  url: !img.bot_id
                    ? `/api/feishu/image/message?reply_message_id=${img.message_id}&image_key=${img.image_key}&compress=true&quality=50&save_format=jpeg&max_size=720`
                    : `/api/feishu/image/message?image_key=${img.image_key}&bot_id=${img.bot_id || context.bot_id}&compress=true&quality=50&save_format=jpeg&max_size=720`,
                }))
              }
              function parseTextFromMessage(item) {
                const texts = []
                if (item.messageType == "text") {
                  texts.push(item.content.text)
                } else if (item.messageType == "post") {
                  item.content.content.forEach(x => {
                    x.attrs.forEach(y => {
                      if (y.tag == "text") {
                        texts.push(y.text)
                      }
                    })
                  })
                } else if (item.messageType == "interactive") {
                  item.content.elements.forEach(x => {
                    x.forEach(y => {
                      // 卡片map层数多，只提取到这一层
                      if (y.tag == 'text') {
                        texts.push(y.text)
                      } else if (y.tag == "plain_text") {
                        texts.push(y.content)
                      }
                    })
                  })
                }
                return texts.filter(function(text) {return text.trim() !== ""})
              }
              if (window.h5sdk) {
                window.h5sdk.error((err) => {
                  console.error(err)
                  throw ("h5sdk error:", JSON.stringify(err));
                });
                // 接口鉴权
                let session_key = ""
                window.h5sdk.config({...context.signature, onSuccess: res => {
                  console.error(res)
                  session_key = res.session_key
                }, onFail: res => {
                  console.error(res)
                }})
                // 完成鉴权后，便可在 window.h5sdk.ready 里调用 JSAPI
                window.h5sdk.ready(() => {
                  let launchQuery = new URLSearchParams(location.search).get("bdp_launch_query");
                  if (!launchQuery) {
                    console.log("bdp_launch_query not found in URL");
                    return;
                  }
                  launchQuery = JSON.parse(launchQuery);
                  const triggerCode = launchQuery.__trigger_id__;
                  const user = shallowReactive({});
                  user["session_key"] = session_key
                  tt.getUserInfo({
                    withCredentials: true,
                    success(res) {
                      console.log(JSON.stringify(res));
                      Object.keys(res).forEach(key => user[key] = res[key])
                      // 获取用户信息成功后才能进行后续操作
                      tt.getBlockActionSourceDetail({
                        triggerCode: triggerCode,
                        success(res) {
                          console.log('success', res)
                          if (res.bizType == "message") {
                            res.content.messages.forEach(item => item.content = JSON.parse(item.content))
                            // document.write(JSON.stringify(res))
                          }
                          // 支持响应式，数据更改后，重新渲染
                          const detail = shallowReactive(res);
                          effect(() => {
                            const component = html`""" + template + """`;
                            render(component, document.body);
                          })
                        },
                        fail(res) {
                          console.log('fail', res)
                        }
                      });
                    },
                    fail(res) {
                      console.log(`getUserInfo fail: ${JSON.stringify(res)}`);
                    }
                  });
                })
              }
            </script>
          </head>
          <body style="width: 100%;overflow-x: hidden;background-color: black;">
          </body>
        </html>""")

    @arguments
    async def post(self, bot_id: str = '', model: FeishuModel = None):
        self.set_header("Content-Type", "text/html; charset=UTF-8")
        # logging.info("debug POST %r", [bot_id, self.request.body, list(self.request.headers.items())])
        data = {}
        for k in self.request.body_arguments:
            v = self.get_argument(k)
            try:
                v = json.loads(v)
            except Exception as e:
                pass
            data[k] = v
        # 缺少必要参数
        if 'messageId' not in data or 'action' not in data or 'user' not in data:
            logging.error('invalid parameter %r', data)
            return self.set_status(400)
        data['message'] = data.get('message', {})
        data = ObjectDict(data)
        # 解密 user 数据
        decrypted_data = decrypt_with_aes(data.user.session_key, data.user.iv, data.user.encryptedData)
        data.user = {
            **data.user,
            'decryptedData': decrypted_data
        }
        model.init_by_bot_id(bot_id)
        await model.handler_action(data)
        self.finish("""
<script type="module">
  window.close()
</script>
        """)
#         self.finish("""
# <div>Please go to bot to check result.</div>
# <script type="module">
# setTimeout(() => window.close(), 2000)
# </script>
#         """)


class FileUploadHandler(BaseHandler):

    async def post(self):
        """
        上传文件，获取一个下载地址
        1. 上传到本地上传目录
        2. 返回一个对象存储的地址（利用对象存储的回源机制自动拉取文件）
        """
        url = ''
        if 'file' in self.request.files:
            file_obj = self.request.files['file'][0]
            url = await upload_file(file_obj)
        if not url:
            raise NotFound('上传失败')
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': url,
        })


