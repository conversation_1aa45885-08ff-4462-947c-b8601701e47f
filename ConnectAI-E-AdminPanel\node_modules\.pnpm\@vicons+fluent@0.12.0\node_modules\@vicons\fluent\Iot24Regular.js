'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M19 4.491a2.5 2.5 0 0 1-3.012 2.448l-1.28 2.117A3.99 3.99 0 0 1 16 12.058l1.296.26a2.5 2.5 0 1 1-.279 1.475l-1.319-.266a4.015 4.015 0 0 1-1.744 1.964l.495 1.511h.051a2.5 2.5 0 1 1-1.472.48l-.497-1.517a3.994 3.994 0 0 1-3.752-1.593l-1.794.848a2.5 2.5 0 1 1-.608-1.372l1.754-.829a4.006 4.006 0 0 1 .784-3.566l-.991-1.13a2.5 2.5 0 1 1 1.164-.948l.982 1.12A3.982 3.982 0 0 1 12 8c.505 0 .989.093 1.434.265l1.245-2.06A2.5 2.5 0 1 1 19 4.491zm-1.5 0a1 1 0 1 0-2 0a1 1 0 0 0 2 0zM7 7a1 1 0 1 0 0-2a1 1 0 0 0 0 2zm5 7.5a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zm-6.5 1a1 1 0 1 0-2 0a1 1 0 0 0 2 0zm10 4.002a1 1 0 1 0-2 0a1 1 0 0 0 2 0zm4-5.002a1 1 0 1 0 0-2a1 1 0 0 0 0 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Iot24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
