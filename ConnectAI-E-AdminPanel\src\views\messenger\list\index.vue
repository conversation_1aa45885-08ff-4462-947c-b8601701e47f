<template>
  <div>
    <n-card
      class="h-full shadow-sm rounded-16px pt-2"
      content-style="overflow:hidden"
      header-style="padding:20px 20px 10px 40px"
    >
      <template #header>
        <div class="w-full flex justify-between items-center">
          <div class="text-2xl font-medium text-gray-500 dark:text-white">
            {{ t('message.messenger.title') }}
          </div>
          <div class="flex">
          <a :href="docUrl" target="_blank">
            <button
              type="button"
              class="text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center mr-2 dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
            >
              <icon-akar-icons-chat-dots class="mr-2" />
              {{ $t('message.bot.ckpz') }}
            </button>
          </a>
          <button
            type="button"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center mr-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            @click="handleCreate"
          >
            {{ t('message.messenger.xzaikf') }}
          </button>
        </div>
        </div>
        <n-divider class="p0 !mb-2" />
      </template>
      <div class="flex content-start justify-start flex-wrap mx-auto w-[100%] gap-[38px] h-full overflow-auto">
        <div
          v-for="item in data"
          :key="item.id"
          class="flex flex-wrap flex-col justify-between w-[470px] h-[200px] p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"
        >
          <div class="flex items-center justify-between">
            <h5 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
              {{ item.name }}
            </h5>
          </div>
          <p class="mb-3 font-normal text-gray-700 dark:text-gray-400 line-clamp-2" :title="item.description">
            {{ item.description }}
          </p>
          <div class="flex justify-between items-center">
            <div class="flex-center text-gray-400">
              <div>{{ item.platform }}</div>
              <n-divider v-if="item.platform" vertical />
              <div>{{ t('message.messenger.kf') }}: {{ item.seat_count }}</div>
            </div>
            <a
              href="#"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="(e) => handleConfig(e, item)"
            >
              {{ t('message.messenger.pz') }}
              <svg
                class="w-3.5 h-3.5 ml-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 10"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M1 5h12m0 0L9 1m4 4L9 9"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </n-card>
    <n-modal v-model:show="showModal">
      <n-card style="width: 500px" :title="t('message.messenger.xzaikf')" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <n-alert type="info" class="mb-4">
          <template #header>
            <div class="flex justify-between text-sm">
              <div>{{ t('message.messenger.alert') }}</div>
            </div>
          </template>
        </n-alert>
        <n-form ref="formRef" :label-width="80" :model="formValue">
          <n-form-item
            :label="t('message.messenger.kfmc')"
            path="name"
            :rule="{
              required: true,
              trigger: ['input', 'blur'],
              message: `${t('message.messenger.qsr') + t('message.messenger.kfmc')}`
            }"
          >
            <n-input v-model:value="formValue.name" />
          </n-form-item>
          <n-form-item
            :label="t('message.messenger.kfms')"
            path="description"
            :rule="{
              required: true,
              trigger: ['input', 'blur'],
              message: `${t('message.messenger.qsr') + t('message.messenger.kfms')}`
            }"
          >
            <n-input v-model:value="formValue.description" />
          </n-form-item>
          <n-form-item
            :label="t('message.messenger.kfqd')"
            path="platform"
            :rule="{
              required: true,
              trigger: ['input', 'blur'],
              message: `${t('message.messenger.qsr') + t('message.messenger.kfqd')}`
            }"
          >
            <n-select :options="options" v-model:value="formValue.platform" />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.ai.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              {{ t('message.ai.qd') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst } from 'naive-ui';
import { useTenantPrivilege } from '@/hooks';
import { getMessengerList, createMessenger, getMessengerInfo } from '@/service/api/messenger';
import { t } from '@/locales';
import { useRouterPush } from '@/composables';
import { routeName } from '@/router';

const { routerPush } = useRouterPush();

const { allow, deny } = useTenantPrivilege();
const showModal = ref(false);

const docUrl = ref('');

const message = useMessage();

const data = ref<ApiMessenger.Messenger[]>([]);
const formRef = ref<FormInst | null>(null);

const options = [
  {
    label: t('message.messenger.gw'),
    value: t('message.messenger.gw')
  },
  {
    label: t('message.messenger.dykfz'),
    value: t('message.messenger.dy'),
    disabled: true
  }
];

const formValue = ref<{
  name: string;
  description: string;
  platform: string;
  id?: string;
}>({
  name: '',
  description: '',
  platform: ''
});

function handleClose() {
  showModal.value = false;
  formValue.value = {
    name: '',
    description: '',
    platform: ''
  };
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (formValue.value.id) {
        const { id, ...data } = formValue.value;
        // await updateAiResource({ id, data });
      } else {
        await createMessenger(formValue.value);
      }
      message.success(t('message.msg.bccg'));
      getList();
      handleClose();
    }
  });
}

function handleCreate() {
  showModal.value = true;
}

function handleConfig(e: Event, item: ApiMessenger.Messenger) {
  e.preventDefault();
  routerPush({ name: routeName('messenger_info'), query: { id: item.id } });
}

async function getList() {
  try {
    const res = await getMessengerList({ page: 1, size: 99999 });
    data.value = res.data?.data || [];
  } catch (err) {}
}

async function getInfo() {
  try {
    const res = await getMessengerInfo();
    docUrl.value = res.data?.data.deploy;
  } catch (err) {}
}

onMounted(() => {
  getList();
  getInfo();
});
</script>
<style scoped lang="scss">
.n-alert:deep(.n-alert-body) {
  padding-top: 8px;
  padding-bottom: 8px;
}

.n-alert:deep(.n-alert__icon) {
  margin-top: 6px;
  margin-left: 10px;
}

.cardShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}
</style>
