import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.75 6c1.348 0 2.179.969 2.245 2.329l.005.18v2.115l2.219.403a3.25 3.25 0 0 1 2.602 3.857l-.043.18l-1.048 3.922a2.25 2.25 0 0 1-1.689 1.617l-.165.03l-2.418.347a2.25 2.25 0 0 1-2.28-1.124l-.08-.154l-.029-.063a3.464 3.464 0 0 0-1.026-1.283l-.194-.139l-1.884-1.255l-.093-.059l-.098-.052l-2.363-1.176a.75.75 0 0 1-.415-.655c-.025-1.108.465-1.963 1.418-2.44c.702-.351 1.636-.331 2.827.017l.259.079V8.508C9.5 7.055 10.343 6 11.75 6zm0 1.5c-.46 0-.713.275-.746.866L11 8.508v5.244a.75.75 0 0 1-1.036.694c-1.46-.603-2.441-.743-2.879-.524c-.254.127-.42.29-.51.518l-.038.12l1.905.948l.181.097l.175.108l1.883 1.256a4.966 4.966 0 0 1 1.626 1.793l.122.244l.03.063a.75.75 0 0 0 .677.434l.109-.008l2.418-.347a.75.75 0 0 0 .581-.444l.037-.105l1.048-3.923a1.75 1.75 0 0 0-1.239-2.142l-.07-.017l-2.904-.53a.75.75 0 0 1-.608-.63l-.008-.107V8.508c0-.69-.255-1.008-.75-1.008zm-.001-5a5.75 5.75 0 0 1 5.393 7.749l-.144-.053a5.601 5.601 0 0 0-.607-.175a4.988 4.988 0 0 0-.728-.112A4.25 4.25 0 1 0 8.5 10.99a5.66 5.66 0 0 0-.824.123c-.35.085-.588.17-.752.263A5.75 5.75 0 0 1 11.748 2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapSingle24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
