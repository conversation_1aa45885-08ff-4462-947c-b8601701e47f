
V?=2.0.1
CHANNEL?=privatization

all: package

package: build build-config docker-images images-base.tar.gz

build: build-know build-manager build-admin-panel build-messenger

build-know:
	docker build -t know-server:$(V)-$(CHANNEL) -f ./DataChat-API/docker/Dockerfile ./DataChat-API
	docker tag know-server:$(V)-$(CHANNEL) d.ai2e.cn:5000/know-server:$(V)-$(CHANNEL)
	docker push d.ai2e.cn:5000/know-server:$(V)-$(CHANNEL)

build-manager:
	docker build -t connectai-manager:$(V)-$(CHANNEL) -f ./manager-server/docker/Dockerfile ./manager-server
	docker tag connectai-manager:$(V)-$(CHANNEL) d.ai2e.cn:5000/connectai-manager:$(V)-$(CHANNEL)
	docker push d.ai2e.cn:5000/connectai-manager:$(V)-$(CHANNEL)

build-admin-panel:
	cd ./ConnectAI-E-AdminPanel && pnpm install && pnpm run build && cd ..

build-messenger:
	cd ./Lark-Messenger-Web && pnpm install && pnpm run build && cd ..

build-helper:
	cd ./ConnectAI-Helper && pnpm install && pnpm run zip:chrome && cat build/chrome-mv3-prod.zip | crx3 -o build/connectai.crx -p connectai.pem && cd ..

build-config:
	rm -rf build && cp -r ./deploy build
	cp ./DataChat-API/elasticsearch-analysis-ik-8.9.0.zip ./build/
	sed -i 's/know-server:es/d.ai2e.cn:5000\/know-server:$(V)-$(CHANNEL)/g' ./build/docker-compose.yml
	sed -i 's/connectai-manager:1.0/d.ai2e.cn:5000\/connectai-manager:$(V)-$(CHANNEL)/g' ./build/docker-compose.yml
	cp -r ./ConnectAI-E-AdminPanel/dist/* ./build/dist/ && cp -r ./Lark-Messenger-Web/dist/* ./build/dist/
	# cp ./ConnectAI-Helper/build/connectai.crx ./build/dist/
	mkdir -p ./build/dist/upload
	tar -zcvf deploy-$(V)-$(CHANNEL).tar.gz build/
	rm -rf build

images-base.tar.gz:
	docker save docker.elastic.co/elasticsearch/elasticsearch:8.9.0 lloydzhou/nchan jwilder/nginx-proxy:alpine mysql:5.7 rabbitmq:3.7-management-alpine redis:alpine | gzip > images-base.tar.gz

docker-images:
	docker save d.ai2e.cn:5000/know-server:$(V)-$(CHANNEL) d.ai2e.cn:5000/connectai-manager:$(V)-$(CHANNEL) | gzip > images-$(V)-$(CHANNEL).tar.gz

FORCE:
