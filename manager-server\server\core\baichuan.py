import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)   

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)
from tornado.options import options, define

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.adapters.openai import convert_dict_to_message, convert_message_to_dict
from langchain.schema import ChatGeneration, ChatResult, BaseMessage

logger = logging.getLogger(__name__)


class BaichuanClient(object):

    def build_query(
        self,
        messages=list(),
        model='Baichuan2-53B',
        stream=False,
        api_base='https://api.baichuan-ai.com',
        api_key='',
        secret_key='',
        **kwargs
    ):
        api = '/v1/stream/chat' if stream else '/v1/chat'
        headers = { 'api-key': api_key }
        if secret_key:
            headers = {
                'api-base': api_base,
                'api-key': api_key,
                'secret-key': secret_key,
            }
            from core.api_base import NewApiBase
            api_base = NewApiBase('Baichuan').url
        return '{}{}'.format(api_base, api), json.dumps(dict(messages=messages, model=model, parameters=kwargs)), headers

    def stream(self, url, data, headers, timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                yield json.loads(line)

    def create(self, messages=list(), model='Baichuan2-53B', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages, model=model, stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers, timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                yield json.loads(line)

    async def acreate(self, messages=list(), model='Baichuan2-53B', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages, model=model, stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


async def acompletion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the async completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    async def _completion_with_retry(**kwargs: Any) -> Any:
        # Use OpenAI's async api https://github.com/openai/openai-python#async-api
        return await llm.client.acreate(**kwargs)

    return await _completion_with_retry(**kwargs)


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class BaichuanChat(SimpleChatModel):
    """
    Baichuan2-53B
    """
    client: Any  #: :meta private:
    # 当前支持3个模型
    model_name: str = "Baichuan2-53B"  # 
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    # 支持官方的key
    secret_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 0.3     # 默认0.95，范围 (0, 1.0]，不能为0
    top_p: float = 0.85          # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    top_k: float = 5             # top_k 采样筛选策略，最大 20(超过 20 会被修正成 20)，缺省 5
    with_search_enhance = False  # 开启搜索增强，搜索增强会产生额外的费用, 缺省 False

    def _llm_type(self) -> str:
        return "baichuan_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'secret_key': self.secret_key,
            'model': self.model_name,
            'temperature': self.temperature,
            'top_p': self.top_p,
            'top_k': self.top_k,
        }

        message_dicts = [convert_message_to_dict(m) for m in messages]
        self.client = BaichuanClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, messages=message_dicts, **params):
                token = stream_resp.get("data", {}).get('messages', [{}])[0].get('content', '') if stream_resp.get("code", 0) == 0 else ''

                response += token
                if run_manager:
                    run_manager.on_llm_new_token(token)
            return response
        else:
            full_response = completion_with_retry(self, messages=message_dicts, **params)
            code = full_response.get("code", 0)
            if code != 0:
                raise Exception(full_response.get("message", ''))

            return full_response.get("data", {}).get('messages', [{}])[0].get('content', '')


if __name__ == "__main__":
    import asyncio
    from core.api_base import NewApiBase
    async def main():
        client = BaichuanClient()
        messages = [{"role":"user","content":"帮我推荐一条旅游路线"}]
        api_key = ''
        api_base = NewApiBase('Baichuan').url

        from langchain.schema import HumanMessage

        chat = BaichuanChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        messages = [HumanMessage(content='你是谁')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


