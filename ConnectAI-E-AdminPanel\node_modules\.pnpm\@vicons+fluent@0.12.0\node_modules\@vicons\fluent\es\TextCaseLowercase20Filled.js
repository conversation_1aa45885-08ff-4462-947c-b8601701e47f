import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.75 3.25a.75.75 0 0 1 .75.75v4.657A2.71 2.71 0 0 1 14.25 8c1.657 0 3 1.567 3 3.5s-1.343 3.5-3 3.5a2.71 2.71 0 0 1-1.75-.657v.157a.75.75 0 0 1-1.5 0V4a.75.75 0 0 1 .75-.75zm2.5 10.25c.62 0 1.5-.67 1.5-2s-.88-2-1.5-2s-1.5.67-1.5 2s.88 2 1.5 2zM5.654 8.937c-.458.018-.85.125-1.068.234a.75.75 0 0 1-.671-1.342c.447-.223 1.056-.367 1.681-.39c.63-.025 1.346.069 1.99.39c1.42.71 1.416 2.125 1.414 2.628V14.5a.75.75 0 0 1-1.498.056c-.871.539-1.89.85-2.945.57c-2.077-.555-2.801-3.365-.723-4.75c.774-.516 1.702-.652 2.526-.61c.379.02.753.077 1.105.162c-.055-.31-.196-.58-.55-.757c-.357-.178-.807-.251-1.261-.234zM7.5 11.5a4.508 4.508 0 0 0-1.216-.235c-.629-.032-1.201.082-1.619.36c-.923.616-.645 1.805.279 2.052c.75.2 1.668-.183 2.556-.996V11.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseLowercase20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
