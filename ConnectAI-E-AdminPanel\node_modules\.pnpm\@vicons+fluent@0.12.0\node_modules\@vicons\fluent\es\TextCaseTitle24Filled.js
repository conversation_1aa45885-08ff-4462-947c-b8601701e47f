import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8.443 3.416a1 1 0 0 0-1.875-.027l-6 15.5a1 1 0 0 0 1.865.722L3.83 16h6.955l1.272 3.584a1 1 0 0 0 1.885-.668l-5.5-15.5zM4.605 14l2.854-7.375L10.076 14H4.605zM16 3a1 1 0 0 1 1 1v7.619A3.578 3.578 0 0 1 19 11c2.21 0 4 2.07 4 4.625c0 2.554-1.79 4.625-4 4.625c-.753 0-1.458-.24-2.06-.66a1 1 0 0 1-1.94-.34V4a1 1 0 0 1 1-1zm3 15.25c.842 0 2-.893 2-2.625S19.842 13 19 13c-.842 0-2 .893-2 2.625s1.158 2.625 2 2.625z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseTitle24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
