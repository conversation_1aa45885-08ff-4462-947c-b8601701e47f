'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4.5 10a.5.5 0 0 1-.5-.5V3.928c-.331.341-.749.704-1.243 1a.5.5 0 1 1-.514-.857c.562-.337 1.021-.793 1.344-1.175a6.472 6.472 0 0 0 .48-.647a.5.5 0 0 1 .933.264V9.5a.5.5 0 0 1-.5.5zM12 4.5a2.5 2.5 0 0 1 5 0v3a2.5 2.5 0 0 1-5 0v-3zm4 0a1.5 1.5 0 0 0-3 0v3a1.5 1.5 0 0 0 3 0v-3zm-13 8a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1H4v2h1.5a.5.5 0 0 1 0 1H4v1.5a.5.5 0 0 1-1 0v-5zm5 0a.5.5 0 0 1 .5-.5H10a2 2 0 1 1 0 4H9v1.5a.5.5 0 0 1-1 0v-5zM9 15h1a1 1 0 1 0 0-2H9v2zm5.75-3a1.75 1.75 0 1 0 0 3.5h.5a.75.75 0 0 1 0 1.5h-.764a.486.486 0 0 1-.486-.486V16.5a.5.5 0 0 0-1 0v.014c0 .82.665 1.486 1.486 1.486h.764a1.75 1.75 0 1 0 0-3.5h-.5a.75.75 0 0 1 0-1.5h.764c.269 0 .486.217.486.486v.014a.5.5 0 0 0 1 0v-.014c0-.82-.665-1.486-1.486-1.486h-.764zM6.993 4.083v.003a.5.5 0 0 1-.575.407c-.477-.08-.411-.575-.411-.575v-.006l.002-.007l.005-.022l.015-.069a2.495 2.495 0 0 1 .414-.864C6.812 2.46 7.456 2 8.5 2c.862 0 1.59.291 2.052.878c.457.58.586 1.363.44 2.207c-.123.723-.526 1.173-1.007 1.482c-.333.214-.74.379-1.101.525c-.116.047-.227.092-.329.136c-.46.198-.833.406-1.1.725c-.195.234-.358.557-.424 1.047h3.47a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5c0-.968.252-1.669.687-2.189c.423-.506.979-.789 1.471-1.001c.152-.065.293-.123.426-.177c.333-.135.611-.248.86-.407c.303-.195.496-.425.562-.81c.113-.657-.009-1.125-.24-1.419C9.538 3.209 9.136 3 8.5 3c-.705 0-1.062.29-1.256.55a1.495 1.495 0 0 0-.25.534v-.001z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Fps12020Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
