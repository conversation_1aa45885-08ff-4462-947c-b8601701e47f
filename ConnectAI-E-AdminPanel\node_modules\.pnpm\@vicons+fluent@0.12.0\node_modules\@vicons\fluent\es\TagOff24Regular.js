import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.942 8.003L2.22 3.28a.75.75 0 0 1 1.06-1.06l18.5 18.5a.75.75 0 1 1-1.06 1.06L16 17.06l-3.452 3.453a3.255 3.255 0 0 1-4.596.001L3.49 16.06a3.25 3.25 0 0 1-.004-4.596l3.456-3.46zM14.94 16L8.003 9.064l-3.468 3.472a1.75 1.75 0 0 0 .015 2.462l4.461 4.454a1.755 1.755 0 0 0 2.477 0L14.939 16zm5.05-5.05l-2.99 2.989l1.061 1.06l2.99-2.99A3.25 3.25 0 0 0 22 9.713V4.25A2.25 2.25 0 0 0 19.75 2h-5.464a3.25 3.25 0 0 0-2.3.953L9.002 5.941l1.06 1.06l2.985-2.988a1.75 1.75 0 0 1 1.239-.513h5.465a.75.75 0 0 1 .75.75v5.462c0 .464-.184.91-.513 1.237zM17 5.501a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
