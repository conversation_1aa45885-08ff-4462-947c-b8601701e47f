{"name": "connect-ai-admin", "version": "0.1.0", "description": "企联ai管理中心", "author": {"name": "connect-ai-e", "email": "<EMAIL>", "url": "https://github.com/ConnectAI-E"}, "license": "MIT", "homepage": "https://github.com/ConnectAI-E", "repository": {"url": "https://github.com/ConnectAI-E/ConnectAI-E-AdminPanel"}, "bugs": {"url": "https://github.com/ConnectAI-E/ConnectAI-E-AdminPanel"}, "keywords": ["Vue3", "Vite", "TypeScript", "NaiveUI", "UnoCSS", "flowbite"], "scripts": {"dev": "cross-env VITE_SERVICE_ENV=dev vite", "dev:lark": "cross-env VITE_SERVICE_ENV=dev VITE_IS_LARK=true vite", "dev:test": "cross-env VITE_SERVICE_ENV=test vite", "dev:prod": "cross-env VITE_SERVICE_ENV=prod vite", "build": "cross-env VITE_SERVICE_ENV=prod vite build", "build:dev": "cross-env VITE_SERVICE_ENV=dev vite build", "build:lark": "cross-env VITE_SERVICE_ENV=prod VITE_IS_LARK=true vite build", "build:test": "cross-env VITE_SERVICE_ENV=test vite build", "build:prod": "cross-env VITE_SERVICE_ENV=prod vite build", "build:vercel": "cross-env VITE_SERVICE_ENV=prod VITE_HASH_ROUTE=Y VITE_VERCEL=Y vite build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --ext .js,.jsx,.mjs,.json,.ts,.tsx,.vue", "lint:fix": "npm run lint -- --fix", "format": "soy prettier-format", "commit": "soy git-commit", "cleanup": "soy cleanup", "update-pkg": "soy update-pkg", "tsx": "tsx", "logo": "tsx ./scripts/logo.ts", "release": "standard-version", "prepare": "husky install", "deploy:dev": "rsync -azvP --checksum --exclude='.data' --exclude='.git' dist ubuntu@*************:/data/www/manager-server-test/"}, "dependencies": {"@antv/data-set": "^0.11.8", "@antv/g2": "^4.2.10", "@better-scroll/core": "^2.5.1", "@soybeanjs/vue-materials": "^0.1.9", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^9.13.0", "@vueuse/head": "^1.3.1", "axios": "0.27.2", "clipboard": "^2.0.11", "colord": "^2.9.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts": "^5.4.2", "form-data": "^4.0.0", "less": "^4.1.3", "lodash-es": "^4.17.21", "naive-ui": "2.34.3", "pinia": "^2.0.33", "print-js": "^1.6.0", "prismjs": "^1.29.0", "qs": "^6.11.1", "query-string": "^8.1.0", "swiper": "^9.2.0", "ua-parser-js": "^1.0.34", "vditor": "^3.9.1", "vue": "3.3", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vuedraggable": "^4.1.0", "wangeditor": "^4.7.15", "xgplayer": "^2.32.2"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.13", "@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@iconify/json": "^2.2.42", "@iconify/vue": "^4.1.0", "@soybeanjs/cli": "^0.1.7", "@soybeanjs/vite-plugin-vue-page-route": "^0.0.5", "@types/bmapgl": "^0.0.6", "@types/crypto-js": "^4.1.1", "@types/node": "18.15.11", "@types/prismjs": "^1.26.1", "@types/qs": "^6.9.7", "@types/ua-parser-js": "^0.7.36", "@unocss/preset-uno": "^0.50.6", "@unocss/transformer-directives": "^0.50.6", "@unocss/vite": "^0.50.6", "@vicons/antd": "^0.12.0", "@vicons/fluent": "^0.12.0", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "conventional-changelog": "^3.1.25", "cross-env": "^7.0.3", "eslint": "^8.37.0", "eslint-config-soybeanjs": "^0.3.2", "flowbite": "^1.6.5", "http-proxy-middleware": "^2.0.6", "husky": "^8.0.0", "lint-staged": "12.5.0", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.60.0", "standard-version": "^9.5.0", "tsx": "^3.12.6", "typescript": "5.0.3", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "0.24.1", "unplugin-vue-define-options": "^1.3.2", "vite": "^4.3.5", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-pwa": "^0.14.7", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.2.0"}, "pnpm": {}, "lint-staged": {"*.{js,jsx,mjs,json,ts,tsx,vue}": "eslint . --fix"}}