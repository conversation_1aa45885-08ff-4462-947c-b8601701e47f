import { ref, onMounted, onBeforeUnmount } from 'vue';
import { isEmpty } from 'lodash-es';

const useOauthDialog = ({ url, option = 'left=500,top=300,width=1024,height=800', event, callback }) => {
  const dialog = ref<Window | null>();
  const eventListener = (e) => {
    if (e.data?.event === event && !isEmpty(e.data?.data)) {
      if (dialog.value) {
        dialog.value.close();
      }
      callback?.(e.data);
      console.log('Oauth message ~ e:', e);
      window.removeEventListener('message', eventListener);
    }
  };

  onMounted(() => {
    window.addEventListener('message', eventListener);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('message', eventListener);
  });

  return () => {
    dialog.value = window.open(url, '', option);
  };
};

export default useOauthDialog;
