<template>
  <div
    class="flex flex-wrap flex-col justify-between w-[370px] h-[200px] p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"
  >
    <div class="flex justify-between items-center">
      <h5 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
        {{ data.name }}
      </h5>
      <button
        id="dropdownButton"
        :data-dropdown-toggle="dropDownId"
        class="inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1.5"
        type="button"
      >
        <span class="sr-only">Open dropdown</span>
        <svg
          aria-hidden="true"
          class="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"
          ></path>
        </svg>
      </button>
      <!-- Dropdown menu -->
      <div
        :id="dropDownId"
        class="z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow w-24 dark:bg-gray-700"
      >
        <ul aria-labelledby="dropdownButton" class="py-2">
          <li>
            <a
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
              href="#"
              @click="(e) => handleEdit(e, data)"
              >{{ t('message.knowledge.rename') }}</a
            >
          </li>
          <li>
            <a
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
              href="#"
              @click="(e) => handleDelete(e, data)"
              >{{ t('message.knowledge.delete') }}</a
            >
          </li>
        </ul>
      </div>
    </div>

    <p class="mb-3 font-normal text-gray-700 dark:text-gray-400 line-clamp-2" :title="data.description">
      {{ data.description }}
    </p>

    <div class="flex justify-between items-center">
      <div class="flex-center text-gray-400">
        <!-- <div class="flex-center">
          <component :is="iconRender({ icon: 'mdi:document' })" class="text-2xl mr-2" />

          {{ '资源' }}
        </div> -->
      </div>
      <a
        href="#"
        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
        @click="(e) => handleConfig(e, data)"
      >
        {{ t('message.knowledge.gl') }}
        <svg
          class="w-3.5 h-3.5 ml-2"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 14 10"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M1 5h12m0 0L9 1m4 4L9 9"
          />
        </svg>
      </a>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import { initDropdowns } from 'flowbite';
import { routeName } from '@/router';
import { useIconRender, useRouterPush } from '@/composables';
import { t } from '@/locales';

const { routerPush } = useRouterPush();

const props = defineProps<{ data: ApiDataset.DatasetApp }>();

const dropDownId = computed(() => {
  return `dropdown-${props.data.id}`;
});

const emit = defineEmits(['handle-edit', 'handle-delete']);

const { iconRender } = useIconRender();

function handleEdit(e: Event, data: ApiDataset.DatasetApp) {
  e.preventDefault();

  emit('handle-edit', data);
}

function handleDelete(e: Event, data: ApiDataset.DatasetApp) {
  e.preventDefault();

  emit('handle-delete', data);
}

function handleConfig(e: Event, { id }: ApiDataset.DatasetApp) {
  e.preventDefault();
  routerPush({ name: routeName('knowledge_appInfo'), query: { id } });
}

onMounted(() => {
  initDropdowns();
});
</script>

<style scoped lang="scss">
.cardShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
  }
}
</style>
