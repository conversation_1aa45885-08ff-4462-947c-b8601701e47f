# 代码文档 - server/app.py

## 文件作用
Flask应用的主配置文件，设置Web服务器、会话管理、CORS和API文档。

## 核心组件

### Flask应用配置
- **app**: Flask应用实例
- **RegexConverter**: 自定义URL正则转换器
- **CORS**: 跨域资源共享配置
  - 支持Authorization和X-Requested-With头
  - 启用凭证支持

### 会话管理
- **Redis会话存储**:
  - 主机: redis:6379
  - 会话有效期: 86400分钟 (60天)
  - 键前缀: "know"

### 自定义SessionInterface
- **继承**: RedisSessionInterface
- **会话ID获取优先级**:
  1. URL参数 `__sid__`
  2. Cookie `__sid__`
  3. Cookie `session`
  4. Authorization头 (Bearer token)

#### 会话处理逻辑
- **首次访问**: 生成新的UUID会话ID
- **签名验证**: 支持会话ID签名验证
- **数据恢复**: 从Redis恢复会话数据
- **异常处理**: 数据损坏时创建新会话

### API文档
- **Swagger**: 自动生成API文档
- **配置**: 使用默认Swagger配置

### 日志配置
- **Gunicorn日志**: 继承Gunicorn的日志配置
- **OpenAI日志**: 配置OpenAI库的日志级别
- **统一处理**: 所有日志使用相同的处理器

## 环境配置
- **前缀环境变量**: 支持 `FLASK_` 前缀的环境变量
- **DELTA配置**: 默认值 0.25

## 技术特点
- 基于Redis的分布式会话
- 多种会话ID传递方式
- 自动API文档生成
- 统一日志管理
- 支持正则URL路由
