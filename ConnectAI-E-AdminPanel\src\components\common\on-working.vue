<template>
  <div class="flex-col-center gap-24px min-h-520px wh-full overflow-hidden">
    <div class="flex text-680px text-primary">
      <icon-local-on-working v-if="type === '1'" />
      <icon-local-on-working-2 v-if="type === '2'" />
      <icon-local-on-working-3 v-if="type === '3'" />
      <icon-local-on-working-4 v-if="type === '4'" />
    </div>
    <h1 class="text-40px">{{ $t('message.system.zznc') }}</h1>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'OnWorking' });

type ExceptionType = '1' | '2' | '3' | '4';

interface Props {
  /** 异常类型 403 404 500 */
  type: ExceptionType;
  desc?: string;
}

withDefaults(defineProps<Props>(), {
  type: '1',
  desc: '正在内测，敬请期待'
});
</script>

<style scoped></style>
