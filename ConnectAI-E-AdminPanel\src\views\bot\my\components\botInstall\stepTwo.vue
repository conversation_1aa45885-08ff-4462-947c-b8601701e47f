<template>
  <div
    class="min-w-[350px] w-full max-w-sm p-4 bg-white border border-gray-200 rounded-lg shadow sm:p-6 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="mb-2 flex-center">
      <h5 class="text-base font-semibold text-gray-900 md:text-xl dark:text-white">{{ t('message.my.bsfs') }}</h5>
      <button
        type="button"
        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
        @click="closeModel"
      >
        <svg
          aria-hidden="true"
          class="w-5 h-5"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          ></path>
        </svg>
        <span class="sr-only">Close modal</span>
      </button>
    </div>
    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">{{ t('message.my.xzbsfs') }}</p>
    <ul class="mt-4 space-y-3">
      <li>
        <a
          href="#"
          class="flex items-center p-3 text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
          @click="handleNextStep"
        >
          <component :is="iconRender({ localIcon: 'robot' })" class="text-28px" />
          <span class="flex-1 ml-3 whitespace-nowrap">{{ t('message.my.qyzjyy') }}</span>
        </a>
      </li>
      <li>
        <a
          href="#"
          class="flex items-center cursor-not-allowed p-3 text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white"
        >
          <component :is="iconRender({ localIcon: 'store' })" class="text-28px" />
          <span class="flex-1 ml-3 whitespace-nowrap">插件一键安装</span>
          <n-tag :bordered="false" type="success"> coming soon </n-tag>
        </a>
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, inject } from 'vue';
import { useVModel } from '@vueuse/core';
import { useIconRender } from '@/composables';
import {t} from '@/locales';

const close = inject<() => void>('close');

const { iconRender } = useIconRender();

const props = defineProps<{
  step: number;
}>();
const emit = defineEmits(['update:step']);

const step = useVModel(props, 'step', emit);

async function handleNextStep() {
  step.value += 1;
}

function closeModel() {
  close?.();
}
onMounted(async () => {});
</script>
