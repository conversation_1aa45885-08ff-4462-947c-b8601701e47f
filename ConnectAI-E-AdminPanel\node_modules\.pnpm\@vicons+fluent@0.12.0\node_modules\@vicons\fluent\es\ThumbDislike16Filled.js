import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.094 4.62a3.5 3.5 0 0 0-4.377-2.727l-4.95 1.375a2 2 0 0 0-1.43 1.558l-.224 1.196c-.165.879.46 1.647 1.157 1.936c.255.106.546.253.853.458c1.68 1.119 2.48 2.636 3.016 3.925c.096.232.188.47.277.699c.146.376.284.73.416 1.001c.111.227.245.455.42.63c.186.188.435.329.748.329c.357 0 .666-.124.912-.34c.235-.207.393-.481.502-.761c.214-.554.281-1.251.29-1.893A14.73 14.73 0 0 0 10.577 10h.512a2.5 2.5 0 0 0 2.457-2.961l-.453-2.42z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ThumbDislike16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
