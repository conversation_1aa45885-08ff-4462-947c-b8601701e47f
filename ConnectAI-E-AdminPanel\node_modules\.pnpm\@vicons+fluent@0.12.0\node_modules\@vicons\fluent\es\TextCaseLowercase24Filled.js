import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15 4a1 1 0 1 0-2 0v15.25a1 1 0 0 0 1.996.092A3.756 3.756 0 0 0 17.11 20c2.27 0 4.111-2.07 4.111-4.625c0-2.554-1.84-4.625-4.11-4.625c-.773 0-1.495.24-2.112.656V4zm2.111 14c-.95 0-2.111-.947-2.111-2.625s1.16-2.625 2.111-2.625c.951 0 2.111.947 2.111 2.625S18.062 18 17.112 18zM11 19.26v-5.5l-.005-.21v-.006c-.042-.961-.375-1.763-.976-2.337c-.6-.572-1.444-.894-2.47-.945l-.29-.01h-.004c-1.158-.022-2.114.186-2.832.69a1 1 0 1 0 1.149 1.637c.326-.229.932-.367 1.878-.32c.585.03.959.172 1.189.396c.175.17.291.41.337.748a7.69 7.69 0 0 0-2.596-.135c-1.033.125-1.88.526-2.47 1.14c-.59.615-.91 1.43-.91 2.352c0 2.1 1.53 3.5 3.5 3.5c.806 0 1.653-.244 2.544-.707a1 1 0 0 0 .956.707h.008l.119-.008h.008A1 1 0 0 0 11 19.26zm-6-2.463c0-.42.132-.753.383-1c.256-.252.658-.437 1.237-.508a5.805 5.805 0 0 1 2.257.185l.122.037v1.771l-.137.093c-.965.638-1.753.922-2.362.922c-.466 0-.837-.146-1.09-.393c-.251-.246-.41-.615-.41-1.107z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseLowercase24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
