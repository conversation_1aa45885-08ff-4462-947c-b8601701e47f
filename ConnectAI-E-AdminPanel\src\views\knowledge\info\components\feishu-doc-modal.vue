<template>
  <n-modal v-model:show="showTips" @after-leave="handleAfterLeave">
    <div>
      <n-card
        style="width: 600px"
        :title="t('安装飞书文档助手应用')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        v-if="step === 0"
      >
        <n-p depth="3" style="margin: 8px 0 0 0">{{
          t(
            '安装「企联 AI 飞书助手」应用后方可添加飞书知识库&云文档让大模型学习及调用，否则大模型无法获取知识库&云文档'
          )
        }}</n-p>
        <template #footer>
          <div class="text-right">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="showTips = false"
            >
              {{ t('message.ai.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="step = 1"
            >
              {{ t('下一步') }}
            </button>
          </div>
        </template>
      </n-card>
      <Config v-if="step === 1" v-model:data="data" @close="showTips = false" />
    </div>
  </n-modal>
  <n-modal v-model:show="showLarkDoc" @after-leave="handleLarkDocAfterLeave">
    <n-card style="width: 600px" :title="t('添加云文档')" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form ref="formRef" :model="model" :rules="rules" :label-width="80">
        <n-form-item label="飞书云文档链接" path="fileUrl">
          <n-input v-model:value="model.fileUrl" placeholder="请输入飞书云文档链接" />
        </n-form-item>
      </n-form>
      <n-p depth="3" style="margin: 8px 0 0 0">{{
        $t(
          '添加前，请确保「企联 AI 飞书助手」应用具有该云文档的阅读权限，以及「企联 AI 飞书助手」应用权限配置正确否则会导致添加失败'
        )
      }}</n-p>
      <a
        href="https://connect-ai.feishu.cn/docx/Bw5vdz4XHoS3NWxRRRScGc0AnPh"
        target="_blank"
        class="font-medium text-primary-600 hover:underline"
        >{{ $t('如何赋予「企联 AI 飞书助手」知识库/云文档的阅读权限') }}</a
      >
      <template #footer>
        <div class="flex justify-between items-center">
          <div>
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="
                step = 1;
                showTips = true;
              "
            >
              {{ t('修改「企联 AI 飞书助手」配置') }}
            </button>
          </div>
          <div>
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="showLarkDoc = false"
            >
              {{ t('message.ai.qx') }}
            </button>
            <button
              :disabled="larkUploadButtonLoading"
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleUploadLarkDoc"
            >
              {{ t('确定') }}
            </button>
          </div>
        </div>
      </template>
    </n-card>
  </n-modal>
  <n-modal v-model:show="showLarkWiki" @after-leave="handleLarkWikiAfterLeave">
    <n-card style="width: 600px" :title="t('添加知识库')" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form ref="formRef" :model="model" :rules="rules" :label-width="80">
        <n-form-item label="飞书知识库" path="spaceId">
          <n-select v-model:value="model.spaceId" :options="spaces" placeholder="请选择飞书知识库" />
        </n-form-item>
      </n-form>
      <n-p depth="3" style="margin: 8px 0 0 0">{{
        $t(
          '添加前，请确保「企联 AI 飞书助手」应用具有该知识库的阅读权限，以及「企联 AI 飞书助手」应用权限配置正确否则会导致添加失败'
        )
      }}</n-p>
      <a
        href="https://connect-ai.feishu.cn/docx/Bw5vdz4XHoS3NWxRRRScGc0AnPh"
        target="_blank"
        class="font-medium text-primary-600 hover:underline"
        >{{ $t('如何赋予「企联 AI 飞书助手」知识库/云文档的阅读权限') }}</a
      >
      <template #footer>
        <div class="flex justify-between items-center">
          <div>
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="
                step = 1;
                showTips = true;
              "
            >
              {{ t('修改「企联 AI 飞书助手」配置') }}
            </button>
          </div>
          <div>
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="showLarkWiki = false"
            >
              {{ t('message.ai.qx') }}
            </button>
            <button
              :disabled="larkUploadButtonLoading"
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleUploadLarkWiki"
            >
              {{ t('确定') }}
            </button>
          </div>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { t } from '@/locales';
import { uploadDocument, createDataset } from '@/service/api/knowledge';
import Config from './config.vue';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { useRoute } from 'vue-router';

const route = useRoute();

const datasetId = route.query.id as string;

const message = useMessage();

const step = ref(0);
const larkUploadButtonLoading = ref(false);

const formRef = ref();

const model = ref({
  fileUrl: '',
  spaceId: '',
});
const rules = {
  fileUrl: {
    type: 'url',
    required: true,
    message: t('请输入正确的飞书云文档链接'),
    trigger: ['input']
  },
  spaceId: {
    required: true,
    message: t('请选择飞书知识库'),
  }
};

const props = defineProps<{
  showTips: boolean;
  showLarkDoc: boolean;
  showLarkWiki: boolean;
  data: any;
  spaces: any;
}>();

const emit = defineEmits(['update:showTips', 'update:showLarkDoc', 'update:showLarkWiki', 'update:data', 'after-upload']);

const showTips = useVModel(props, 'showTips', emit);

const showLarkDoc = useVModel(props, 'showLarkDoc', emit);
const showLarkWiki = useVModel(props, 'showLarkWiki', emit);

const data = useVModel(props, 'data', emit);
const spaces = useVModel(props, 'spaces', emit);

function handleAfterLeave() {
  step.value = 0;
}

function handleLarkDocAfterLeave() {
  model.value.fileUrl = '';
}

async function handleUploadLarkDoc() {
  await formRef.value?.validate();
  larkUploadButtonLoading.value = true;
  try {
    const res = await uploadDocument({
      id: datasetId,
      data: {
        fileUrl: model.value.fileUrl,
        fileType: 'feishudoc'
      }
    });
    if (res.error) {
      throw Error(t(res.error.msg || '「企联 AI 飞书助手」应用权限配置不正确，请检查以后重新配置'));
    }
    emit('after-upload', { name: '飞书云文档', taskId: res.data.data?.task_id });
    message.success(t('提交成功，内容同步解析中，解析完成之后即可使用'));
    showLarkDoc.value = false;
    larkUploadButtonLoading.value = false;
  } catch (error) {
    console.log('error', error);
    message.error(error.message);
    larkUploadButtonLoading.value = false;
  }
}

async function handleUploadLarkWiki() {
  await formRef.value?.validate();
  larkUploadButtonLoading.value = true;
  try {
    console.log('feishuwiki', model.value)
    const res = await createDataset({
      space_id: model.value.spaceId,
      type: 'feishuwiki'
    });
    if (res.error) {
      throw Error(t(res.error.msg || '「企联 AI 飞书助手」应用权限配置不正确，请检查以后重新配置'));
    }
    emit('after-upload', { name: '飞书知识库', taskId: res.data.data?.task_id });
    message.success(t('提交成功，内容同步解析中，解析完成之后即可使用'));
    showLarkDoc.value = false;
    larkUploadButtonLoading.value = false;
  } catch (error) {
    console.log('error', error);
    message.error(error.message);
    larkUploadButtonLoading.value = false;
  }
}

onMounted(() => {});
</script>
