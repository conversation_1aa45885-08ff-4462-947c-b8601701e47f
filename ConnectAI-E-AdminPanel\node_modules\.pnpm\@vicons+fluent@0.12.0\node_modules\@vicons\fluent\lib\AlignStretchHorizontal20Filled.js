'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createStaticVNode)('<g fill="none"><path d="M3 17.5a.5.5 0 0 0 1 0v-15a.5.5 0 0 0-1 0v15z" fill="currentColor"></path><path d="M16 17.5a.5.5 0 0 0 1 0v-15a.5.5 0 0 0-1 0v15z" fill="currentColor"></path><path d="M13 16a2 2 0 0 0 2-2v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v1a2 2 0 0 0 2 2h6z" fill="currentColor"></path><path d="M15 7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1z" fill="currentColor"></path></g>', 1)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'AlignStretchHorizontal20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
