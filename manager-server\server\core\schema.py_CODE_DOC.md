# 代码文档 - server/core/schema.py

## 文件作用
定义SQLAlchemy数据模型和自定义数据类型，提供完整的数据库表结构定义。

## 逐行代码解释

### 导入模块 (1-12行)
```python
import bson                    # MongoDB ObjectId支持
import json                    # JSON数据处理
import logging                 # 日志记录
from sqlalchemy import (       # SQLAlchemy ORM组件
    Column, TIMESTAMP, Integer, String, Text,
    text, BINARY, Time,
    Float, DECIMAL, ForeignKey,
)
from sqlalchemy.ext.declarative import declarative_base  # 声明式基类
from sqlalchemy.ext.hybrid import hybrid_property        # 混合属性
from datetime import datetime  # 日期时间处理
```

### 基础声明 (14行)
```python
B = declarative_base()  # 创建SQLAlchemy声明式基类
```

### ObjID自定义类型 (17-38行)
```python
class ObjID(BINARY):
    """基于bson.ObjectId用于mysql主键的自定义类型"""
    
    def bind_processor(self, dialect):
        """数据写入数据库时的处理器"""
        def processor(value):
            # 如果是有效的ObjectId字符串，转换为二进制存储
            return bson.ObjectId(value).binary if bson.ObjectId.is_valid(value) else value
        return processor

    def result_processor(self, dialect, coltype):
        """从数据库读取数据时的处理器"""
        def processor(value):
            # 将二进制数据转换回ObjectId字符串
            return str(bson.ObjectId(value)) if bson.ObjectId.is_valid(value) else value
        return processor

    @staticmethod
    def new_id():
        """生成新的ObjectId字符串"""
        return str(bson.ObjectId())

    @staticmethod
    def is_valid(value):
        """验证ObjectId是否有效"""
        return bson.ObjectId.is_valid(value)
```

### JSONStr自定义类型 (40-71行)
```python
class JSONStr(String):
    """自动转换 str 和 dict 的自定义类型"""
    
    def bind_processor(self, dialect):
        """数据写入时的JSON序列化处理"""
        def processor(value):
            try:
                # 检查是否为LIKE查询的通配符
                if isinstance(value, str) and (value[0] == '%' or value[-1] == '%'):
                    return value  # 直接返回，不进行JSON处理
                # 将Python对象序列化为JSON字符串
                return json.dumps(value, ensure_ascii=False)
            except Exception as e:
                logging.error(e)
                return value
        return processor

    def result_processor(self, dialect, coltype):
        """数据读取时的JSON反序列化处理"""
        def processor(value):
            try:
                # 将JSON字符串反序列化为Python对象
                return json.loads(value)
            except Exception as e:
                logging.error(e)
                return value
        return processor

    @staticmethod
    def is_valid(value):
        """验证JSON字符串是否有效"""
        try:
            json.loads(value)
            return True
        except Exception as e:
            logging.error(e)
            return False
```

### JSONText自定义类型 (73-104行)
```python
class JSONText(Text):
    """自动转换 str 和 dict 的自定义类型（Text版本）"""
    # 实现逻辑与JSONStr相同，但基于Text类型
    # 用于存储更大的JSON数据
```

### Base基础模型类 (106-114行)
```python
class Base(B):
    """公共字段"""
    __abstract__ = True  # 抽象基类，不创建对应表
    
    # 主键：使用12字节的ObjectId
    id = Column(ObjID(12), primary_key=True)
    
    # 状态字段：0-正常，1-禁用，-1-删除
    status = Column(Integer, nullable=True, server_default=text("'0'"), comment="状态")
    
    # 创建时间：自动设置为当前时间
    created = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间")
    
    # 修改时间：创建时设置，更新时自动更新
    modified = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment="修改时间")
```

### 租户相关模型 (116-137行)
```python
class Tenant(Base):
    """租户信息表"""
    __tablename__ = 'tenant'
    
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    display_name = Column(String(128), nullable=True, server_default=text("''"), comment="展示名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="备注")
    level = Column(Integer, nullable=True, server_default=text("'0'"), comment="账户等级")  # 0-默认，1-付费
    auto_add_seat = Column(Integer, nullable=True, server_default=text("'1'"), comment="自动添加座席")

class TenantAdmin(Base):
    """租户管理员表"""
    __tablename__ = 'tenant_admin'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="管理员ID")

class TenantWithKey(Tenant):
    """带API密钥的租户（继承自Tenant）"""
    apikey = Column(String(128), nullable=True, server_default=text("''"), comment="apikey")
```

### 用户相关模型 (139-160行)
```python
class User(Base):
    """用户信息表"""
    __tablename__ = 'user'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    email = Column(String(128), nullable=True, server_default=text("''"), comment="邮箱")
    telephone = Column(String(128), nullable=True, server_default=text("''"), comment="手机号")
    department = Column(String(128), nullable=True, server_default=text("''"), comment="部门")
    remark = Column(String(512), nullable=True, server_default=text("''"), comment="备注")

class Account(User):
    """账户表（继承自User，添加密码字段）"""
    passwd = Column(String(128), nullable=True, server_default=text("''"), comment="登录密码")

class AdminAccount(Base):
    """后台管理用户信息表"""
    __tablename__ = 'admin_account'
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    password = Column(String(128), nullable=True, server_default=text("''"), comment="登录密码")
    is_on = Column(String(128), nullable=True, server_default=text("''"), comment="管理员状态")  # yes or no
```

## 技术特点
- **自定义类型**: ObjID支持MongoDB风格的主键
- **JSON支持**: 自动序列化/反序列化JSON数据
- **继承体系**: 合理的模型继承关系
- **外键关联**: 完整的表间关系定义
- **时间戳**: 自动管理创建和修改时间
- **软删除**: 通过status字段实现软删除

## 数据模型关系
- Tenant (租户) ← TenantAdmin (租户管理员) → User (用户)
- Tenant (租户) ← TenantSeat (座席) → User (用户)
- ResourceCategory (资源分类) ← Resource (资源) ← Model (模型)
- Tenant (租户) ← TenantResource (租户资源) → Resource (资源)
