<template>
  <div>
    <div
      v-if="showTip"
      class="overflow-y-auto overflow-x-hidden fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-99"
    >
      <div class="relative p-4 w-full max-w-lg h-full md:h-auto">
        <div
          class="relative p-4 bg-white rounded-lg shadow bg-gray-100 dark:bg-gray-800 md:p-8"
          style="box-shadow: rgba(0, 0, 0, 0.25) 0px 14px 28px, rgba(0, 0, 0, 0.22) 0px 10px 10px"
        >
          <div class="mb-4 text-sm font-light text-gray-500 dark:text-gray-400">
            <h3 v-if="hasPayed" class="mb-3 text-2xl font-bold text-gray-900 dark:text-white" style="min-width: 350px">
              {{ $t('message.tip.tip1') }}
            </h3>
            <h3 v-else class="mb-3 text-2xl font-bold text-gray-900 dark:text-white">{{ $t('message.tip.wdy') }}</h3>
            <p v-if="hasPayed">{{ $t('message.tip.sjbb') }}</p>
            <p v-else>{{ $t('message.tip.dyhsy') }}</p>
          </div>
          <div class="justify-between items-center pt-0 space-y-4 sm:flex sm:space-y-0">
            <a
              v-if="!hasPayed"
              class="font-medium text-primary-600 dark:text-primary-500 hover:underline"
              href="https://www.connectai-e.com/contact"
              target="_blank"
            >
              {{ $t('message.tip.lxkf') }}
            </a>
            <span v-else />
            <div class="items-center space-y-4 sm:space-x-4 sm:flex sm:space-y-0">
              <button
                id="close-modal"
                type="button"
                class="py-2 px-4 w-full text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-200 sm:w-auto hover:bg-gray-100 focus:outline-none focus:ring-primary-300 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"
                @click="hideModal"
              >
                {{ $t('message.tip.qx') }}
              </button>
              <button
                id="confirm-button"
                type="button"
                class="py-2 px-4 w-full text-sm font-medium text-center text-white rounded-lg bg-primary-700 sm:w-auto hover:bg-primary-800 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                @click="purchase"
              >
                {{ hasPayed ? t('message.tip.qwsj') : t('message.tip.qwdy') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouteStore, useAuthStore } from '@/store';
import { useRouterPush } from '@/composables';
import { useTenantPrivilege } from '@/hooks';
import { t } from '@/locales';

const { isSubscribe, needUpgrade } = useTenantPrivilege();
const routeStore = useRouteStore();
const { routerPush } = useRouterPush();

defineOptions({ name: 'PurchaseTip' });

interface Props {
  /** 是否勾选 */
  value?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  value: true
});

interface Emits {
  (e: 'update:value', value: boolean): void;
}

const emit = defineEmits<Emits>();

// pay/upgrage，没有购买的情况，或者已经购买需要升级的情况
const hasPayed = computed(() => isSubscribe());

const showTip = computed({
  get() {
    return props.value;
  },
  set(newValue: boolean) {
    emit('update:value', newValue);
  }
});
const hideModal = () => {
  showTip.value = false;
};

const purchase = () => {
  routerPush({ name: 'dashboard_pricing' }, false);
  hideModal();
};
</script>

<style scoped></style>
