#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Manager Server - 使用Flask
解决Windows兼容性问题
"""

import os
import sys
import json
import sqlite3
import hashlib
from flask import Flask, request, jsonify, session
from flask_cors import CORS

app = Flask(__name__)
CORS(app)
app.secret_key = 'connectai-manager-secret-key'

def hash_password(password):
    """密码哈希函数"""
    return hashlib.md5(password.encode('utf-8')).hexdigest()

def get_db():
    """获取数据库连接"""
    db_path = "../data/connectai.db"
    return sqlite3.connect(db_path)

@app.route('/')
def index():
    """主页"""
    return jsonify({
        "message": "ConnectAI Manager Server",
        "version": "1.0.0",
        "status": "running",
        "framework": "Flask"
    })

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({"status": "healthy"})

@app.route('/api/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')

        if not email or not password:
            return jsonify({"error": "邮箱和密码不能为空"}), 400

        # 验证用户
        conn = get_db()
        cursor = conn.cursor()

        hashed_password = hash_password(password)
        cursor.execute("""
            SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name, t.apikey
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.email = ? AND a.passwd = ? AND a.status = 0
        """, (email, hashed_password))

        user = cursor.fetchone()
        conn.close()

        if user:
            # 设置session
            session['user_id'] = user[0]
            session['user_email'] = user[2]
            session['tenant_id'] = user[3]

            return jsonify({
                "success": True,
                "message": "登录成功",
                "user": {
                    "id": user[0],
                    "name": user[1],
                    "email": user[2],
                    "tenant_id": user[3],
                    "tenant_name": user[4],
                    "tenant_apikey": user[5]
                }
            })
        else:
            return jsonify({"error": "邮箱或密码错误"}), 401

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/user/info', methods=['GET'])
def user_info():
    """获取用户信息"""
    try:
        # 简化版本，直接返回管理员信息
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name, t.apikey
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.email = '<EMAIL>'
        """)

        user = cursor.fetchone()
        conn.close()

        if user:
            return jsonify({
                "user": {
                    "id": user[0],
                    "name": user[1],
                    "email": user[2],
                    "tenant_id": user[3],
                    "tenant_name": user[4],
                    "tenant_apikey": user[5]
                }
            })
        else:
            return jsonify({"error": "用户不存在"}), 404

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/applications', methods=['GET'])
def get_applications():
    """获取应用列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT a.id, a.name, a.title, a.description, c.title as category
            FROM application a
            LEFT JOIN application_category c ON a.category_id = c.id
            WHERE a.status = 0
            ORDER BY a.sorted, a.created_at
        """)

        apps = cursor.fetchall()
        conn.close()

        applications = []
        for app in apps:
            applications.append({
                "id": app[0],
                "name": app[1],
                "title": app[2],
                "description": app[3],
                "category": app[4] or "未分类"
            })

        return jsonify({"applications": applications})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/categories', methods=['GET'])
def get_categories():
    """获取分类列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取应用分类
        cursor.execute("""
            SELECT id, name, title, description
            FROM application_category
            WHERE status = 0
            ORDER BY sorted
        """)
        app_categories = cursor.fetchall()

        # 获取资源分类
        cursor.execute("""
            SELECT id, name, title, description
            FROM resource_category
            WHERE status = 0
            ORDER BY sorted
        """)
        resource_categories = cursor.fetchall()

        conn.close()

        return jsonify({
            "application_categories": [
                {
                    "id": cat[0],
                    "name": cat[1],
                    "title": cat[2],
                    "description": cat[3]
                } for cat in app_categories
            ],
            "resource_categories": [
                {
                    "id": cat[0],
                    "name": cat[1],
                    "title": cat[2],
                    "description": cat[3]
                } for cat in resource_categories
            ]
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/logout', methods=['POST'])
def logout():
    """用户登出"""
    session.clear()
    return jsonify({"message": "登出成功"})

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 统计各种数据
        stats = {}

        # 租户数量
        cursor.execute("SELECT COUNT(*) FROM tenant WHERE status = 0")
        stats['tenant_count'] = cursor.fetchone()[0]

        # 用户数量
        cursor.execute("SELECT COUNT(*) FROM account WHERE status = 0")
        stats['user_count'] = cursor.fetchone()[0]

        # 应用分类数量
        cursor.execute("SELECT COUNT(*) FROM application_category WHERE status = 0")
        stats['app_category_count'] = cursor.fetchone()[0]

        # 资源分类数量
        cursor.execute("SELECT COUNT(*) FROM resource_category WHERE status = 0")
        stats['resource_category_count'] = cursor.fetchone()[0]

        conn.close()

        return jsonify({"stats": stats})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    # 设置UTF-8编码，避免Windows下的编码问题
    import sys
    import io
    if sys.platform.startswith('win'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

    print("启动ConnectAI Manager Server (Flask版本)...")
    print("服务将在 http://localhost:3000 运行")
    print("按 Ctrl+C 停止服务器")

    try:
        app.run(host='0.0.0.0', port=3000, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
