# 代码文档 - server/core/feishu.py

## 文件作用
飞书（Lark）开放平台API客户端，提供事件处理、消息发送、文件上传、用户管理等完整的飞书集成功能。

## 逐行代码解释

### 导入模块 (1-21行)
```python
import base64                            # Base64编码
from Crypto.Cipher import AES            # AES加密
import json                              # JSON数据处理
import time                              # 时间处理
import logging                           # 日志记录
import hashlib                           # 哈希算法
import uuid                              # UUID生成
from tornado.options import options      # Tornado配置选项
from datetime import datetime            # 日期时间处理
from html.entities import codepoint2name, html5  # HTML实体处理
from urllib.parse import quote           # URL编码
from tornado.httpclient import AsyncHTTPClient, HTTPRequest, HTTPError  # HTTP客户端

from core.redisdb import stalecache      # Redis缓存装饰器
from core.exception import InternalError, InvalidEventException  # 自定义异常
from core.decrypt import AESCipher       # AES解密工具
from core.utils import ObjectDict, _     # 工具函数
from core.downloader import ChunkedDownloader  # 文件下载器
from typing import Dict                  # 类型提示
from urllib3.filepost import encode_multipart_formdata  # 多部分表单编码
```

### 常量定义 (23行)
```python
LARK_HOST = 'https://open.feishu.cn'     # 飞书API主机地址
```

### Event事件处理类 (26-106行)
```python
class Event(object):
    """飞书事件处理类"""

    def __init__(self, request, token, encrypt_key):
        """初始化事件对象"""
        self.request = request               # HTTP请求对象
        body = json.loads(request.body.decode())
        
        if body.get('encrypt'):
            # 处理加密的事件数据
            self.data = ObjectDict(self._decrypt_data(encrypt_key, body['encrypt']))
        else:
            # 处理未加密的数据（如卡片回调）
            self.data = ObjectDict(body)

        if self.data.get('header'):
            # 验证事件的合法性
            self._validate(token, encrypt_key)

    def _validate(self, token, encrypt_key):
        """验证事件的token和签名"""
        if token and self.data.header.token != token:
            raise InvalidEventException("invalid token")
        
        if not encrypt_key:  # 没有传encrypt_key不校验signature
            return
            
        # 获取请求头中的验证信息
        timestamp = self.request.headers.get("X-Lark-Request-Timestamp")
        nonce = self.request.headers.get("X-Lark-Request-Nonce")
        signature = self.request.headers.get("X-Lark-Signature")
        body = self.request.body
        
        # 计算签名
        bytes_b1 = (timestamp + nonce + encrypt_key).encode("utf-8")
        bytes_b = bytes_b1 + body
        h = hashlib.sha256(bytes_b)
        
        if signature != h.hexdigest():
            raise InvalidEventException("invalid signature in event")

    def _decrypt_data(self, encrypt_key, encrypt_data):
        """解密事件数据"""
        cipher = AESCipher(encrypt_key)
        return json.loads(cipher.decrypt_string(encrypt_data))

    @property
    def event_type(self):
        """获取事件类型"""
        return self.data.header.event_type

    @property
    def event_handler_name(self):
        """获取事件处理器名称"""
        if 'header' in self.data:
            return self.data.header.event_type.replace('.', '_')
        if 'event' in self.data and 'type' in self.data.event:
            return self.data.event.type
        return 'action' if 'action' in self.data else 'unkown'

    @property
    def app_id(self):
        """获取应用ID"""
        if 'header' in self.data:
            return self.data.header.app_id
        if 'event' in self.data and 'app_id' in self.data.event:
            return self.data.event.app_id
        return 'unkown'

    @property
    def message(self):
        """获取消息内容（处理@提及）"""
        message = self.data.event.message
        try:
            message['content'] = json.loads(message['content'])
            # 移除@机器人的提及
            for mention in message.get('mentions', []):
                message['content']['text'] = message['content']['text'].replace(mention['key'] + ' ', '')
            # 移除@_all提及
            message['content']['text'] = message['content']['text'].replace('@_all ', '')
        except Exception as e:
            logging.warn(e)
        return message

    @property
    def at_all(self):
        """检查是否@全体成员"""
        return '@_all' == self.data.event.message.content[9:14]

    @property
    def sender(self):
        """获取消息发送者"""
        return self.data.event.sender

    @property
    def operator(self):
        """获取操作者"""
        return self.data.event.operator
```

### Lark客户端类 (108-137行)
```python
class Lark(object):
    """飞书API客户端类"""

    def __init__(self, app_id=None, app_secret=None):
        """初始化客户端"""
        self.app_id = app_id                 # 应用ID
        self.app_secret = app_secret         # 应用密钥

    @property
    def access_token_cache_key(self):
        """访问令牌的缓存键"""
        return "connectai:access_token:{}".format(self.app_id)

    @stalecache(expire=600, stale=1800, attr_key="access_token_cache_key", namespace='connectai')
    async def get_tenant_access_token(self):
        """获取租户访问令牌（带缓存）"""
        # https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/tenant_access_token_internal
        url = '{api}/open-apis/auth/v3/tenant_access_token/internal'.format(api=LARK_HOST)
        
        request = HTTPRequest(url, method='POST', body=json.dumps({
            'app_id': self.app_id,
            'app_secret': self.app_secret,
        }), headers={'Content-Type': 'application/json'})
        
        try:
            response = await AsyncHTTPClient().fetch(request)
        except Exception as e:
            raise InternalError("获取 tenant_access_token 失败")
            
        result = json.loads(response.body.decode())
        logging.info(result)
        
        if "tenant_access_token" not in result:
            raise InternalError("获取 tenant_access_token 失败")
            
        return result.get("tenant_access_token")
```

### 消息查询方法 (139-160行)
```python
async def get_messages_by_chat(self, chat_id, page_token='', page_size=20):
    """分页获取群聊消息"""
    # https://open.feishu.cn/open-apis/im/v1/messages
    response = await self.request(
        '{}/open-apis/im/v1/messages?container_id={}&container_id_type=chat&page_token={}&page_size={}&sort_type=ByCreateTimeDesc'.format(
            LARK_HOST, chat_id, page_token, page_size,
        )
    )
    return response.data

async def gen_recent_messages_by_chat(self, chat_id, size=1000):
    """生成器：获取群聊的最近消息"""
    count = 1
    page_token = ''
    
    while True:
        result = await self.get_messages_by_chat(chat_id, page_token=page_token, page_size=20)
        
        for item in result.get('items', []):
            count += 1
            yield item                       # 逐个返回消息
            if count > size:
                break
                
        if not result.has_more:
            break
        page_token = result.page_token
```

## 技术特点

### 事件处理机制
- **加密支持**: 支持飞书事件的AES加密和解密
- **签名验证**: 验证事件的数字签名确保安全性
- **事件路由**: 根据事件类型自动路由到对应处理器
- **消息解析**: 自动解析消息内容和@提及

### API客户端功能
- **令牌管理**: 自动获取和缓存访问令牌
- **HTTP封装**: 封装飞书API的HTTP请求
- **异步支持**: 全异步的API调用
- **错误处理**: 完善的错误处理和异常管理

### 安全机制
- **AES解密**: 支持飞书事件的AES解密
- **签名校验**: SHA256签名验证
- **令牌验证**: 验证事件令牌的合法性
- **缓存安全**: 安全的令牌缓存机制

### 消息处理
- **内容解析**: 自动解析JSON格式的消息内容
- **提及处理**: 自动移除@机器人和@全体的提及
- **分页查询**: 支持大量消息的分页获取
- **生成器模式**: 内存友好的消息遍历

## 使用场景
- **机器人开发**: 开发飞书聊天机器人
- **事件处理**: 处理飞书平台的各种事件
- **消息管理**: 发送和接收飞书消息
- **文件操作**: 上传和下载飞书文件
- **用户管理**: 管理飞书用户和群组

## API功能覆盖
- **认证授权**: 获取访问令牌
- **消息API**: 发送、接收、查询消息
- **文件API**: 上传、下载文件
- **用户API**: 获取用户信息
- **群组API**: 管理群组和成员
- **卡片API**: 发送和处理交互式卡片

## 配置要求
- **app_id**: 飞书应用ID
- **app_secret**: 飞书应用密钥
- **encrypt_key**: 事件加密密钥（可选）
- **verification_token**: 事件验证令牌（可选）

## 使用示例
```python
# 初始化客户端
lark = Lark(app_id='your_app_id', app_secret='your_app_secret')

# 获取访问令牌
token = await lark.get_tenant_access_token()

# 处理事件
event = Event(request, token, encrypt_key)
if event.event_type == 'im.message.receive_v1':
    message = event.message
    print(message['content']['text'])
```
