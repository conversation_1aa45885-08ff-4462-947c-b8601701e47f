# 代码文档 - server/server.py

## 文件作用
管理服务器的主入口文件，基于 Tornado 框架启动 HTTP 服务器。

## 关键组件

### 框架依赖
- **Tornado**: 异步Web框架
  - `tornado.web`: Web应用框架
  - `tornado.ioloop`: 事件循环
  - `tornado.httpserver`: HTTP服务器
  - `tornado.autoreload`: 自动重载

### 配置管理
- **load_config()**: 加载应用配置
- **parse_command_line()**: 解析命令行参数
- **tornado_settings**: Tornado应用配置
  - DEBUG: 调试模式
  - GZIP: 压缩支持
  - XHEADERS: 代理头支持
  - COOKIE_SECRET: Cookie加密密钥

### 日志过滤
- **NoHealthLoggingFilter**: 自定义日志过滤器
  - 过滤健康检查请求 (`/_healthz`)
  - 减少日志噪音

### 服务器启动
- **main()**: 主启动函数
  - 导入 API 路由配置
  - 创建 Tornado 应用实例
  - 配置 HTTP 服务器 (最大缓冲区 100MB)
  - 监听指定端口
  - 启动事件循环

## 技术特点
- 异步非阻塞架构
- 支持大文件上传 (100MB)
- 健康检查端点
- 可配置的服务器参数
- 生产级日志管理
