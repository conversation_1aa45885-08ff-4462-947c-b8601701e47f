import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.704 2.74a.75.75 0 0 0-1.408 0l-3.5 9.5a.75.75 0 0 0 1.408.52l.924-2.51h3.744l.924 2.51a.75.75 0 1 0 1.408-.52l-3.5-9.5zm.615 6.01H3.681l1.32-3.58l1.318 3.58zm4.181-6.5a.75.75 0 0 1 .75.75v4.294A2.24 2.24 0 0 1 12.354 7c1.438 0 2.604 1.4 2.604 3.125c0 1.726-1.166 3.125-2.604 3.125c-.438 0-.851-.13-1.214-.36a.75.75 0 0 1-1.39-.39V3a.75.75 0 0 1 .75-.75zm.75 7.875c0 1.165.739 1.625 1.104 1.625c.366 0 1.104-.46 1.104-1.625S12.72 8.5 12.354 8.5c-.365 0-1.104.46-1.104 1.625z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseTitle16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
