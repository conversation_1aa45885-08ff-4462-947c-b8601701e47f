#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录API
"""

import requests
import json

def test_login():
    """测试登录API"""
    url = "http://localhost:3000/api/account/login"
    data = {
        "email": "<EMAIL>",
        "passwd": "admin123"
    }
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"测试登录API: {url}")
    print(f"请求数据: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_login()
