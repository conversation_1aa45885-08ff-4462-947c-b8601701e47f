import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M20.25 5c.966 0 1.75.784 1.75 1.75v2.26c0 .39-.3.716-.688.748a2.25 2.25 0 0 0 0 4.484a.75.75 0 0 1 .688.748v2.26A1.75 1.75 0 0 1 20.25 19H3.75A1.75 1.75 0 0 1 2 17.25v-2.26c0-.39.3-.716.689-.748a2.25 2.25 0 0 0 0-4.484A.75.75 0 0 1 2 9.01V6.75C2 5.784 2.784 5 3.75 5h16.5zm.25 3.385V6.75a.25.25 0 0 0-.25-.25H3.75a.25.25 0 0 0-.25.25v1.635a3.752 3.752 0 0 1 0 7.23v1.635c0 .138.112.25.25.25h16.5a.25.25 0 0 0 .25-.25v-1.635a3.752 3.752 0 0 1-.189-7.173l.19-.057z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketHorizontal24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
