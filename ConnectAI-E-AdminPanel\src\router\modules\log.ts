const log: AuthRoute.Route = {
  name: 'log',
  path: '/log',
  component: 'basic',
  children: [
    {
      name: 'log_word',
      path: '/log/word',
      component: 'self',
      meta: { title: '风险词管理', requiresAuth: true, icon: 'akar-icons:chat-error', i18nTitle: 'message.routes.log.fxcgl' },
      children: []
    },
    {
      name: 'log_chat',
      path: '/log/chat',
      component: 'self',
      meta: { title: '对话日志', requiresAuth: true, icon: 'akar-icons:newspaper', i18nTitle: 'message.routes.log.dhrz' },
      children: []
    }
  ],
  meta: { title: '对话管理', icon: 'akar-icons:chat-dots', order: 3, i18nTitle: 'message.routes.log._value' }
};

export default log;
