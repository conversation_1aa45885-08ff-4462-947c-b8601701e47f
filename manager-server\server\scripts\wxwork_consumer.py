# -*- coding: utf-8 -*-
import json
import logging
from base import *
from tornado.options import options, parse_command_line
from core.consumer import Base<PERSON>onsumer, LOGGER, LOG_FORMAT
from core.rabbitmq import MqSession
from core.dingding import TextMessage, MarkdownMessage, DingDing
from modules.callback.model import WXWorkModel
from settings.constant import AppResult


class WXWorkConsumer(BaseConsumer):

    async def process_message(self, tag, app_id, data, **kwargs):
        try:
            logging.info("DEBUG %r", data)
            # TODO 
            logging.info("DEBUG result_type %r", data.result_type)
            with WXWorkModel() as model:
                model.init_by_bot_id(data.extra.bot_instance_id)
                model.init_by_instance_id(data.extra.instance_id)
                if data.result_type == AppResult.ReplyText.value:
                    model.client.message.send_text(data.extra.chat_id, data.extra.sender_id, data.result_content)
                elif data.result_type == AppResult.ReplyCard.value:
                    model.client.message.send(data.extra.chat_id, data.extra.sender_id, msg=data.result_content)
                elif data.result_type == AppResult.ReplyTemplateCard.value:
                    model.client.message.send(data.extra.chat_id, data.extra.sender_id, msg=data.result_content)
            self.acknowledge_message(tag)
        except Exception as e:
            LOGGER.exception(e)
            self.reject_message(tag, requeue=False)
            return False


def main():
    logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)

    logging.info("DEBUG %r", options.RABBIT_MQ_URI)
    consumer = WXWorkConsumer(
        options.RABBIT_MQ_URI,
        queue=options.QUEUE_WXWORK,
        exchange=options.RABBIT_MQ_EXCHANGE,
        prefetch_count=10,
    )
    try:
        consumer.run()
    except KeyboardInterrupt:
        consumer.stop()


if __name__ == '__main__':
    main()

