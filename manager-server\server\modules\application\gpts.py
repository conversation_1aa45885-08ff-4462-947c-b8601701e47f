class ChatTool(CTool):
    name: str = 'openai_chat'
    description: str = 'openai chat'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class OpenAICallbackHandler(BaseCallbackHandler):
            result = GPTSResult()
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageMarkdown(''),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('正在思考，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result.write(token)
                if model.streaming and platform == 'feishu':
                    content, status, header = self.result.content()
                    # 如果status不为空的时候不需要跳过
                    if not status and len(content) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(content), self.send_length)
                        return

                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(content)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageMarkdown(content),
                            FeishuMessageNote(FeishuMessagePlainText(
                                _('正在生成，请稍等...') if content else status
                            ))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # 同时添加当前用户输入的问题，以及ai回复问题
                # content = response.generations[0][0].text
                content, status, header = self.result.content()
                session.add_message({'role': 'human', 'content': input.strip()})
                session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageMarkdown(content),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))


        class GPT4AllCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageMarkdown(''),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('正在思考，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                logging.info('response: %r', response)
                content = response.generations[0][0].text
                input_content = data.extra.extra.get('vision_content', input.strip())
                session.add_message({'role': 'human', 'content': input_content})
                session.add_message({'role': 'ai', 'content': content})
                if platform == 'feishu':
                    pattern = r'(!\[.*?\]\(.*?\))'
                    images = re.findall(pattern, content)
                    for image in images:
                        image_url = image.split('](').pop()[:-1]
                        try:
                            image_bytes = syncify(download_bytes)(image_url, True)
                            image_key = syncify(client.upload_image_binary)(image_bytes)
                            new_image = image.replace(image_url, image_key)
                            content = content.replace(image, new_image)
                        except Exception as e:
                            logging.error(e)
                    action_eles = []
                    content_split = content.split('\n\n')
                    pattern = r'!\[.*?\]\(img_.*?\)'
                    for text in content_split:
                        if not text.strip():
                            continue
                        if re.search(pattern, text):
                            image_key = text.split('](').pop()[:-1]
                            action_eles.append(FeishuMessageImage(img_key=image_key, alt="图片"))
                        else:
                            action_eles.append(FeishuMessageMarkdown(text))
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *action_eles,
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        )
                    )
                elif platform == 'dingding':
                    pattern = r'(!\[.*?\]\(.*?\))'
                    images = re.findall(pattern, content)
                    for image in images:
                        try:
                            image_url = image.split('](').pop()[:-1]
                            image_name, image_type = image_url.split('/')[-1].split('.')
                            if image_type != 'webp':
                                continue
                            image_bytes = syncify(download_bytes)(image_url, True)
                            image_bytes = compress_image(image_bytes, quality=90, save_format='JPEG', max_size=1024)
                            new_image_url = syncify(upload_file)({'body': image_bytes, 'filename': image_name + '.jpeg'})
                            new_image = image.replace(image_url, new_image_url)
                            content = content.replace(image, new_image)
                        except Exception as e:
                            logging.error(e)
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title='OpenAI 🎉'.capitalize(),
                            text='{}  \n{}'.format(content, _('🤖️：生成成功')),
                        )
                    )

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))

        model.callbacks = [OpenAICallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        # 这里尝试从session里面读取gizmo，再尝试从session.model_id读取
        model_name = m.get(session.model_id, ai_model[0][1])
        model_name = session.get_extra('gizmo', model_name) or model_name
        del model['openai_api_type']

        content = input.strip()
        if model_name in ['gpt-4-all']:
            model.callbacks = [GPT4AllCallbackHandler()]
            # 改成 url + text 的格式输入
            content = data.extra.extra.get('input_kwargs', {}).get('text', content)
            if 'input_kwargs' in data.extra.extra:
                del data['extra']['extra']['input_kwargs']
            logging.info('chat contents: %r', content)
            data['extra']['extra']['vision_content'] = content
            model['max_retries'] = 10
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                max_tokens=800,
                presence_penalty=0,
                top_p=0.95,
                **model,
            )
        else:
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )

        if session.system_role:
            system_message = [SystemMessage(content=session.system_role)]
        else:
            system_message = []
        messages = system_message + chat_history + [HumanMessage(content=content)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class DingdingCommand(CommandTool):

    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_dingding_command'
    description: str = 'openai chat dingding command'

    def send_usage(self):
        tech = 'GPTs'
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/clear',
                    _('🆑 清除话题上下文')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/gizmo',
                    _('🚀 GPTs应用切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/ai_mode',
                    _('🤖 发散模式选择')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！", tech=tech)
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            # 如果"/ai_mode {mode}"，就发送成功消息，否则发送选项
            mode_name = input.replace('/ai_mode', '').replace('发散模式', '').strip()
            return 'ai_mode', mode_name
        elif input[:6] == '/gizmo':
            gpts_link = input.replace('/gizmo', '').strip()
            return 'gizmo', gpts_link
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        elif data.extra.message_type in ['richText']:
            return data.extra.message_type, data.extra.extra.platform_content
        return 'note',

    def on_clear(self):
        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_("🆑 机器人提醒"),
                text=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧"),
            )
        )

    def on_ai_mode(self, mode_name=None):
        # 如果"/ai_mode {mode}"，就发送成功消息，否则发送选项
        m = {content: value for value, content in ai_mode}
        if mode_name and mode_name in m:
            action_value = m[mode_name]
            session['temperature'] = float(action_value)
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🤖 机器人提醒'),
                    text=_("已选择模式：%(mode)s", mode=mode_name),
                ),
            )
        else:
            if len(ai_mode) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🤖 机器人提醒'),
                        text=_('无可用模式'),
                    )
                )
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/ai_mode ' + content)),
                        content
                    ) for _, content in ai_mode],
                    text=_('选择以下模式：'),
                    title=_('🤖 发散模式切换'),
                ),
            )

    def on_gizmo(self, gpts_link=None):
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        if not gpts_link:
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/gizmo ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下GPTs应用：'),
                    title=_('🚀 GPTs应用切换'),
                ),
            )

        # 这里走选择逻辑
        model_name = gpts_link
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            action_value = m[model_name]
            session['model_id'] = action_value
            session['chat_history'] = []  # 清除历史
            session.set_extra('gizmo', model_name)
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择应用：%(model)s", model=model_name),
                )
            )

        try:
            gizmo = '-'.join(gpts_link.split('g/').pop().split('-')[:2])
        except Exception as e:
            logging.error(e)
            gizmo = ''
        if not gizmo:
            # 没有输入的时候
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('👺 机器人提醒'),
                    text=_('👺 **自定义GPTs应用**\n文本回复*/gizmo*+空格+GPTs链接'),
                )
            )
        # 设置gizmo
        session['chat_history'] = []  # 清除历史
        session.set_extra('gizmo', f"gpt-4-gizmo-{gizmo}")
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                text=gizmo,
                title=_('👺 已进入新GPTs应'),
            )
        )

    def on_richText(self, platform_content):
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        if model_name not in ['gpt-4-all']:
            return self.on_note(text=_('该模型不支持图文消息，请切换模型后使用!'), title=_('🚀 机器人提醒'))
        robot_code = bot.app_id
        input_text, input_image_keys = '', []
        for msg in platform_content['richText']:
            if 'text' in msg and '@' not in msg and not input_text:
                input_text = msg['text'].strip()
            elif msg.get('type', '') == 'picture':
                input_image_keys.append(msg['downloadCode'])
        input_text = input_text.strip()
        if not input_image_keys:
            return self.on_note(text=_('请直接向我发送图文消息。'))
        image_urls = get_event_loop().run_until_complete(asyncio.gather(*[functools.partial(client.get_message_resource, robot_code, download_code)() for download_code in input_image_keys]))
        for image_url in image_urls:
            if not self.check_image_format(image_url):
                return self.on_note(text=_('请直接向我发送图文消息。'))
        data['extra']['extra']['input_kwargs'] = {'text': ' '.join(image_urls) + ' ' + input_text}
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或图文（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/图文！')
        return send_note(text, title)

    def check_image_format(self, image_url):
        image_type = image_url.split('?')[0].split('.')[-1]
        if not image_type:
            return True
        return image_type.lower() in ['png', 'jpeg', 'jpg', 'webp', 'gif']


class WEWorkCommand(CommandTool):

    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_wework_command'
    description: str = 'openai chat wework command'

    def send_usage(self):
        tech = 'GPTs'
        return send_message(
            AppResult.ReplyTemplateCard,
            ButtonInteractionCardMessage(
                WXCardButton(_('🆑 清除话题上下文'), '/clear'),
                WXCardButton(_("🚀 GPTs应用切换"), '/model'),
                WXCardButton(_('🤖 发散模式选择'), '/ai_mode'),
                main_title=WXCardMainTitle(
                    _("🎒 需要帮助吗？"),
                    desc=_("👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！", tech=tech)
                ),
                source=WXCardSource(instance.name, instance.icon or app.icon)
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            # 如果"/ai_mode {mode}"，就发送成功消息，否则发送选项
            mode_name = input.replace('/ai_mode', '').replace('发散模式', '').strip()
            return 'ai_mode', mode_name
        elif input[:6] == '/gizmo':
            gpts_link = input.replace('/gizmo', '').strip()
            return 'gizmo', gpts_link
        elif input[0] == '/' and not input.replace('/', ''):
            return 'help',
        elif data.extra.extra.get('SelectedItems', {}):
            selected_items = data.extra.extra.get('SelectedItems', {}).get('SelectedItem', {})
            question_key = selected_items.get('QuestionKey')
            option_id = selected_items.get('OptionIds', {}).get('OptionId')
            if question_key and option_id:
                return question_key, option_id

        return None,

    def on_clear(self):
        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyCard,
            WXTextCard(
                title=_("🆑 机器人提醒"),
                description=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧"),
                url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                btntxt=_('详情'),
            )
        )

    def on_ai_mode(self, action_value=None):
        if action_value:
            session['temperature'] = float(action_value)
            m = {str(value): content for value, content in ai_mode}
            mode_name = m.get(action_value, action_value)
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('🤖 机器人提醒'),
                    description=_("已选择模式：%(mode)s", mode=mode_name),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                ),
            )
        else:
            if len(ai_mode) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('🤖 机器人提醒'),
                        description=_('无可用模式'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, text=content) for value, content in ai_mode],
                        question_key='ai_mode',
                        title=_('选择以下模式：'),
                    ),
                    main_title=_('🤖 发散模式切换'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )

    def on_gizmo(self, gpts_link=None):
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        if not gpts_link:
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, content) for value, content in ai_model_options],
                        question_key='gizmo',
                        title=_('选择以下应用：'),
                    ),
                    main_title=_('🚀 GPTs应用切换'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )

        # 这里是走选择逻辑
        action_value = gpts_link
        m = {value: content for value, content in ai_model_options}
        if gpts_link in m:
            model_name = m[action_value]
            session['model_id'] = action_value
            session['chat_history'] = []  # 清除历史
            session.set_extra('gizmo', model_name)
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('🚀 机器人提醒'),
                    description=_("已选择应用：%(model)s", model=model_name),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )

        try:
            gizmo = '-'.join(gpts_link.split('g/').pop().split('-')[:2])
        except Exception as e:
            logging.error(e)
            gizmo = ''
        if not gizmo:
            # 没有输入的时候
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('👺 机器人提醒'),
                    description=_('👺 **自定义GPTs应用**\n文本回复*/gizmo*+空格+GPTs链接'),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        # 设置gizmo
        session['chat_history'] = []  # 清除历史
        session.set_extra('gizmo', f"gpt-4-gizmo-{gizmo}")
        return send_message(
            AppResult.ReplyCard,
            WXTextCard(
                description=gizmo,
                title=_('👺 已进入新GPTs应用'),
                url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                btntxt=_('详情'),
            )
        )


class FeishuCommand(CommandTool):

    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_feishu_command'
    description: str = 'openai chat feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /gizmo 即可切换GPTs应用',
        '输入<清除> 或 /clear 即可清除上下文',
        '输入<发散模式> 或 /ai_mode 即可选择发散模式',
    ]

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择应用'),
            initial_option=session.extra.get('gizmo', session.model_id or ai_model[0][0]),
            value={'command': 'gizmo'},
            confirm=FeishuMessageConfirm(
                title=_('您确定选择应用吗？'),
                text=_('选择GPTs应用，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def ai_mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in ai_mode],
            placeholder=_('选择模式'),
            initial_option=str(float(session.temperature)),
            value={'command': 'ai_mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改发散模式吗？'),
                text=_('选择内置模式，可以让AI更好的理解您的需求。'),
            )
        )

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        tech = 'GPTs'
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉', tech=tech),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **GPTs应用切换**\n文本回复*/gizmo*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **发散模式选择**\n文本回复 *发散模式* 或 */ai_mode*'),
                    tag='lark_md',
                    extra=self.ai_mode_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('👺 **自定义GPTs应用**\n文本回复*/gizmo*+空格+GPTs链接'),
                    tag='lark_md',
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            return 'ai_mode',
        elif input[:6] == '/gizmo':
            gpts_link = input.replace('/gizmo', '').strip()
            return 'gizmo', gpts_link
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        if data.extra.message_type in ['text']:
            return data.extra.message_type, input
        elif data.extra.message_type in ['post', 'image', 'file']:
            return data.extra.message_type, data.extra.extra.platform_content
        return 'note',

    def on_clear(self, flag=None, reply_log_id=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')),
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.SendCard if not reply_log_id else AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            ),
            **({'reply_log_id': reply_log_id} if reply_log_id else {})
        )

    def on_ai_mode(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模式，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 发散模式选择'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_mode}
        session['temperature'] = float(mode_name)
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('已选择发散模式：**%(mode)s**', mode=m.get(mode_name, mode_name)),
                    tag="lark_md",
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_gizmo(self, gpts_link=None):
        if not gpts_link:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用GPTs应用")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置应用，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 GPTs应用切换'), template='blue'),
                )
            )
        # 这里走之前选择模型的逻辑
        model_name = gpts_link
        m = {str(value): content for value, content in ai_model if value in data.models}
        if gpts_link in m:
            session['model_id'] = model_name
            session['chat_history'] = []  # 清除历史
            real_model_name = m.get(model_name, model_name)
            session.set_extra('gizmo', real_model_name)
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择应用：**%(model)s**', model=real_model_name),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

        try:
            gizmo = '-'.join(gpts_link.split('g/').pop().split('-')[:2])
        except Exception as e:
            logging.error(e)
            gizmo = ''
        if not gizmo:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('👺 **自定义GPTs应用**\n文本回复*/gizmo*+空格+GPTs链接', tag='lark_md')),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('👺 **自定义GPTs应用'), template='blue'),
                )
            )

        # 设置gizmo
        session['chat_history'] = []  # 清除历史
        session.set_extra('gizmo', f"gpt-4-gizmo-{gizmo}")
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(gizmo),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'))
                ),
                header=FeishuMessageCardHeader(_('👺 已进入新GPTs应用'), template='blue'),
            )
        )

    def on_text(self, text):
        if data.extra.extra.parent_id:
            parent_id = data.extra.extra.parent_id
            root_message = syncify(client.get_messages_by_id)(parent_id)
            if root_message['deleted']:
                return self.on_note(_('该消息已被删除'))
            root_content = json.loads(root_message['body']['content'])
            if 'image_key' in root_content:
                image_url = self.get_resource_url(parent_id, root_content['image_key'])
                data['extra']['extra']['input_kwargs'] = {'text': image_url + ' ' + text}
            elif 'file_key' in root_content:
                file_url = self.get_resource_url(parent_id, root_content['file_key'], root_content['file_name'])
                data['extra']['extra']['input_kwargs'] = {'text': file_url + ' ' + text}
        return self.next_tool_name

    def on_post(self, platform_content):
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        if model_name not in ['gpt-4-all']:
            return self.on_note(text=_('该模型不支持图文消息，请切换模型后使用!'), title=_('🚀 机器人提醒'))
        message_id = data.extra.message_id
        input_text, input_image_keys = '', []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg['tag'] == 'text' and not input_text:
                    input_text = msg['text'].strip()
                elif msg['tag'] == 'img':
                    input_image_keys.append(msg['image_key'])
        input_text = input_text.strip()
        if not input_image_keys:
            return self.on_note(text=_('请直接向我发送图文消息。'))
        image_urls = [self.get_resource_url(message_id, image_key) for image_key in input_image_keys]
        data['extra']['extra']['input_kwargs'] = {'text': ' '.join(image_urls) + ' ' + input_text}
        return self.next_tool_name

    def on_image(self, platform_content):
        return self.on_note(_('上传成功，请回复上面的消息。'))

    def on_file(self, platform_content):
        return self.on_note(_('上传成功，请回复上面的消息。'))

    def on_note(self, text='', title=''):
        text = text or _('不支持的消息类型')
        title = title or _('🤖 机器人提醒')
        return send_note(text, title)

    def get_resource_url(self, message_id, key, name=''):
        # gpts 无法识别用 message_id 拼接的图片或文件的 url
        bin = syncify(client.get_message_resource)(message_id, key, 'file' if name else 'image')
        url = syncify(upload_file)({'body': bin, 'filename': name or f'{message_id}.png'})
        return url


class OpenaiAgent(CAgent):

    class GPTsAppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'gpts'
        title: str = 'GPTs 聊天机器人'
        title_en: str = 'ChatGPTs'
        description: str = '🚀   所有GPT！直接使用！无需开通 ChatGPT Plus 即可直接使用GPTs'
        description_en: str = '🚀  All GPTs！Use directly！Use GPts directly without activating ChatGPT Plus'
        problem: str = '解决用户无法使用GPTs 的问题，让用户可以在协同办公平台中，直接使用GPTs'
        problem_en: str = 'Solve the problem of users not being able to use GPTs, allowing users to directly use GPTs in collaborative office platforms'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/wiki/KaZ8wBjCci50rGkQeujcc3e2nGZ?from=from_copylink'
        manual_en: str = 'https://connect-ai.feishu.cn/wiki/JBOdwr6w6iwkSIkkxIRcRI8onfg?from=from_copylink'
        icon: str = 'https://pic.forkway.cn/cdn/20231129154235.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic.forkway.cn/cdn/20231129154235.png?imageMogr2/thumbnail/720x'
        sorted: int = 3
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='GPTs',
            tip='',
            required=True,
            resource=['GPTs']
        )]
        support_bots: List[str] = ['feishu', 'dingding', 'wework']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnbNTu98ASACpGaHgA4kXOqd'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/EiLHw1SeniGA81kWoxVcWPy6nAh'


    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        if platform == 'feishu':
            return AgentAction(tool='openai_chat_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='openai_chat_dingding_command', tool_input=kwargs, log="")
        elif platform in ['wework', 'wxwork']:
            return AgentAction(tool='openai_chat_wework_command', tool_input=kwargs, log="")
        return AgentAction(tool='openai_chat', tool_input=kwargs, log="")


