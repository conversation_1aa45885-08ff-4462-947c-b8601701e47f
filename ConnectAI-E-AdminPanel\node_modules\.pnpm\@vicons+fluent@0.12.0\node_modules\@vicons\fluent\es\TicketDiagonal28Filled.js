import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M18.584 2.914a2.562 2.562 0 0 0-3.714-.095L2.801 14.887a2.563 2.563 0 0 0 .072 3.694l1.375 1.271c.714.66 1.692.513 2.29.27a1.667 1.667 0 0 1 2.216 2.05c-.195.615-.266 1.602.448 2.262l1.02.944a2.562 2.562 0 0 0 3.535-.053l11.528-11.322a2.563 2.563 0 0 0 .107-3.545l-1.075-1.192c-.635-.703-1.592-.664-2.2-.494a1.669 1.669 0 0 1-2.002-2.217c.232-.589.369-1.537-.266-2.24l-1.265-1.4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
