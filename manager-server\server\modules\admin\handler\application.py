from core.base_handler import (
    arguments,
    authenticated
)

from core.utils import ObjIDStr
from modules.account.model import NewAdminModel

from .base_handler import BaseHandler

class AppListHandler(BaseHandler):

    #get /admin/application
    @authenticated
    @arguments
    async def get(
        self,
        page: int = '',
        page_size: int = '',
        query: str = '',
        category: str = '',
        model: NewAdminModel = None,
    ):
        if not page:
            page = 1
        if not page_size:
            page_size = 10

        app_list, total = await model.get_app_list(query, category, page, page_size)
        self.finish({
            "code": 0,
            "msg": "success",
            "page": page,
            "page_size": page_size,
            "total": total,
            "data": [
                {
                    "application_id": app["id"],
                    "name": app["name"],
                    "description": app["description"],
                    "installations": app["count"],
                    "category": app["category"],
                    "document_link": app["manual"],
                    "share_page_title": app["problem"],
                    "sorted": app["sorted"],
                } for app in app_list
            ]
        })

class AppUpdateH<PERSON>ler(BaseHandler):

    #put /admin/application/{application_id}
    @authenticated
    @arguments
    async def put(
        self,
        application_id: str = '',
        name: str = '',
        description: str = '',
        category: str = '',
        document_link: str = '',
        share_page_title: str = '',
        model: NewAdminModel = None,
    ):
        for arg in [name, description, category, document_link, share_page_title]:
            if arg == "" or arg is None:
                self.finish({
                    "code": 1,
                    "msg": "参数不能为空",
                })
                return

        await model.update_app(application_id, name, description, category, document_link, share_page_title)

        self.finish({
            "code": 0,
            "msg": "success",
        })

