import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.5 2.5a.5.5 0 0 1 .5.5v4.6c.418-.377.937-.6 1.5-.6c1.38 0 2.5 1.343 2.5 3s-1.12 3-2.5 3c-.563 0-1.082-.223-1.5-.6v.1a.5.5 0 0 1-1 0V3a.5.5 0 0 1 .5-.5zm2 9.5c.665 0 1.5-.717 1.5-2s-.835-2-1.5-2s-1.5.717-1.5 2s.835 2 1.5 2zM6.997 8.697L7 8.834v3.675a.5.5 0 0 1-.432.487L6.5 13a.5.5 0 0 1-.495-.432L6 12.5v-.07c-.66.378-1.268.57-1.833.57C2.94 13 2 12.137 2 10.834c0-1.15.792-2.004 2.106-2.163A5.017 5.017 0 0 1 6 8.81c-.007-.739-.37-1.104-1.19-1.145c-.642-.031-1.093.059-1.357.244a.5.5 0 0 1-.574-.819c.439-.307 1.036-.444 1.789-.43l.191.007c1.307.065 2.085.817 2.139 2.03zm-1.2 1.119a4.038 4.038 0 0 0-1.57-.128c-.822.1-1.227.536-1.227 1.17c0 .731.475 1.167 1.167 1.167c.453 0 1.012-.21 1.667-.643L6 11.27V9.876l-.202-.06z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseLowercase16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
