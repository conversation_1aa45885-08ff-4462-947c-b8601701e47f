import AceEditor from "react-ace";


import "ace-builds/src-noconflict/mode-json5";
import "ace-builds/src-noconflict/theme-textmate";
import "ace-builds/src-noconflict/ext-language_tools";


import { IconBolt, IconCopy, IconFeishuLogo, IconSave } from "@douyinfe/semi-icons";
import { Button, Notification, Toast } from "@douyinfe/semi-ui";
import { type FC, useState } from "react";
import { useUpdateEffect } from "ahooks";
import { useStorage } from "@plasmohq/storage/dist/hook";
import type { FeishuLoginCookies } from "~utils/open-feishu-api/configuration";
import { Configuration } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";
import { DeployTool } from "~utils/open-feishu-api/deploy";
import {useDeploy} from "~hooks/useDeploy";

const defaultCode = `{
    "name": "飞书-OpenAI",
    "desc": "一个飞书OpenAI机器人",
    "avatar": "https://s1-imfile.feishucdn.com/static-resource/v1/v2_b126c0fd-8f9d-49b0-9653-8c16caf0e28g",
    "events": [
        "im.message.message_read_v1",
        "im.message.receive_v1",
        "20"
    ],
    "encryptKey":"UwM6vYMYyqLbTmNatwNjcfNT2T53U02n",
    "verificationToken":"eidEZjig7dbaQsgc9gADcwtny1YayigK",
    "scopeIds": [
        "21001",
        "7",
        "21003",
        "21002",
        "20001",
        "20011",
        "3001",
        "20012",
        "6005",
        "20010",
        "3000",
        "20013",
        "20014",
        "20015",
        "20008",
        "1000",
        "1006",
        "1005",
        "20009"
    ],
    "cardRequestUrl":"https://ai-feishu.forkway.cn/api/callback/lark/65/card",
    "verificationUrl":"https://ai-feishu.forkway.cn/api/callback/lark/65/event"
}`;


// @ts-ignore
export const CodeEditor: FC = () => {
  const [repLoading, setRepLoading] = useState(false);
  const [config, setConfig, {
    setRenderValue,
    setStoreValue,
    remove
  }] = useStorage("configCustom",defaultCode);
  //
  useUpdateEffect(() => {
    //3s后关闭loading
    if (!repLoading) {
      // 部署成功后显示提示
      Toast.success("部署成功");
    }
  }, [repLoading]);

  const saveConfig = (ifTip = false) => {
    setStoreValue(config).then(
      () => {
        ifTip && Notification.info({
          title: "配置文件保存成功",
          // content: "已成功保存",
          duration: 3,
          icon: <IconFeishuLogo />,
          zIndex: 1010,
          position: "bottomRight"
        });
      }
    );

  };

  const {configNow, setConfigNow, startDeploy} = useDeploy();
  const clickDeploy = () => {
    setRepLoading(true);
    saveConfig();
    console.log(defaultCode)
    startDeploy(defaultCode).then(() => {
      setRepLoading(false);
    })
  };

  return (
    <div className="w-full flex justify-items-start flex-col items-center px-1 py-2">
      <div className="relative w-full h-[640px] mt-2">

        <div
          className={ "absolute top-[0px] right-[32px] z-10 flex flex-row justify-start items-center gap-1" }>

          <Button theme="borderless" size={ "small" } type="tertiary"
                  icon={ <IconCopy /> } aria-label="复制">
          </Button>

          <Button theme="borderless" size={ "small" } type="tertiary"
                  icon={ <IconSave /> } aria-label="保存"
                  onClick={ () => saveConfig(true) }
          >
          </Button>
        </div>

        <AceEditor
          className={ "shadow-lg rounded-md py-2 position-center" }
          mode="json5"
          theme="textmate"
          placeholder="请填入App部署信息"
          wrapEnabled={ true }
          tabSize={ 4 }
          height="640px"
          width="94%"
          focus={ true }
          enableBasicAutocompletion={ true }
          enableLiveAutocompletion={ true }
          showGutter={ false }
          fontSize={ 14 }
          scrollMargin={ [0, 0, 0, 0] }
          showPrintMargin={ false }
          value={ config }
          onChange={ setRenderValue }
          name="UNIQUE_ID_OF_DIV"
          editorProps={ { $blockScrolling: true } }
          setOptions={ {
            showLineNumbers: true,
            tabSize: 2,
            enableSnippets: true
          } }
        />
      </div>

      <div className="flex justify-end items-center gap-2 mt-8">
        <div style={ { width: 200, display: "inline-block" } }>
          <Button icon={ <IconBolt /> } loading={ repLoading } type="primary" size="large"
                  theme="solid" block onClick={ clickDeploy }>
            { repLoading ? "正在部署" : "一键部署" }
          </Button>
        </div>
      </div>
    </div>
  );
};
