<template>
  <n-data-table scroll-x="1800" :columns="columns" :data="data" :bordered="false" />
  <div class="flex justify-end mt-4"><n-pagination v-bind="paginationOptions" /></div>
</template>

<script setup lang="tsx">
import { ref, toRefs } from 'vue';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { NButton, NDivider, NPopconfirm, NTag, NSwitch } from 'naive-ui';
import { t } from '@/locales';

const props = defineProps<{
  data: ApiSensitive.SensitiveTypes[];
  paginationOptions: PaginationProps;
}>();
const { data, paginationOptions } = toRefs(props);
const emit = defineEmits(['handle-edit', 'handle-delete', 'handle-active']);
const popconfirm = ref();

const columns: DataTableColumns<ApiSensitive.SensitiveTypes> = [
  {
    title: t('message.log.id'),
    key: 'id',
    align: 'center',
    render(row, index) {
      return <div>{index + 1}</div>;
    }
  },
  {
    title: t('message.dashboard.name_label'),
    key: 'name',
    sorter: 'default',
    align: 'center'
  },
  {
    title: t('message.dashboard.telephone_label'),
    key: 'telephone',
    resizable: true,
    sorter: 'default',
    align: 'center',
    render({telephone}, index) {
      return telephone ? telephone : '-';
    }
  },
  {
    title: t('message.dashboard.department_label'),
    key: 'department',
    resizable: true,
    sorter: 'default',
    align: 'center',
    render({department}, index) {
      return department ? department : '-';
    }
  },
  {
    title: t('message.dashboard.status_label'),
    key: 'status',
    align: 'center',
    render({ status }) {
      if (status == 1) {
        return t('message.dashboard.status_actived')
      }
      if (status == 0) {
        return t('message.dashboard.status_paused')
      }
      return t('message.dashboard.status_removed')
    }
  },
  {
    title: t('message.log.cz'),
    key: 'actions',
    width: 1,
    align: 'center',
    fixed: 'right',

    render({ name, status, ...rest }) {
      return (
        <div class="flex gap-2">
          <NButton tertiary size={'small'} onClick={() => emit('handle-edit', { name, ...rest })}>
            {t('message.log.bj')}
          </NButton>
          <NButton tertiary size={'small'} onClick={() => emit('handle-active', status == 0, { ...rest })}>
            {status == 0 ? t('message.dashboard.status_active') : t('message.dashboard.status_pause')}
          </NButton>
          <NPopconfirm showIcon={false} negativeText={null} positiveText={null} ref={popconfirm}>
            {{
              action: () => (
                <div class="flex justify-start gap-1 items-center">
                  {t('message.dashboard.remove_seat') + '『'  + name + '』'}
                  <NDivider vertical />
                  <NButton
                    type={'error'}
                    tertiary
                    size={'small'}
                    onClick={() => [emit('handle-delete', { name, ...rest }), popconfirm.value.setShow(false)]}
                  >
                  {t('message.log.qrsc')}
                  </NButton>
                </div>
              ),
              trigger: () => (
                <NButton tertiary size={'small'}>
                  {t('message.log.sc')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
];
</script>
