# ConnectAI 本地环境搭建指南

## 📋 概述

本指南将帮助您在本地环境中搭建和运行ConnectAI项目，无需Docker。我们使用轻量级的替代方案来模拟生产环境。

## 🎯 已完成的工作

### ✅ 后端环境搭建
- **manager-server**: Python虚拟环境已创建，依赖已安装
- **DataChat-API**: Python虚拟环境已创建，依赖已安装
- **数据库**: 配置为使用SQLite替代MySQL
- **缓存**: 配置为使用内存存储替代Redis
- **搜索**: 配置为使用文件存储替代Elasticsearch
- **消息队列**: 配置为使用内存队列替代RabbitMQ

### 📁 项目结构
```
project-manager/
├── manager-server/          # 主服务器 (Tornado)
│   ├── venv/               # Python虚拟环境 ✅
│   ├── requirements.txt    # 依赖列表 ✅
│   └── server/             # 服务器代码
├── DataChat-API/           # 知识库API (Flask)
│   ├── venv/               # Python虚拟环境 ✅
│   ├── requirements.txt    # 依赖列表 ✅
│   └── server/             # API代码
├── ConnectAI-E-AdminPanel/ # 前端管理面板 (Vue3)
├── ConnectAI-Helper/       # 浏览器扩展 (React)
├── data/                   # 本地数据存储 ✅
└── 启动脚本/               # 自动化脚本 ✅
```

## 🚀 快速启动

### 1. 启动后端服务
```bash
# 在项目根目录运行
python setup_and_start.py
```

这个脚本会自动：
- 检查环境要求
- 启动DataChat API (端口5000)
- 启动Manager Server (端口3000)
- 初始化数据库和存储

### 2. 访问服务
- **Manager Server**: http://localhost:3000
- **DataChat API**: http://localhost:5000
- **API健康检查**: http://localhost:5000/health

## 🔧 前端环境设置

### 需要安装Node.js

#### Windows安装Node.js:
1. 访问 https://nodejs.org/
2. 下载LTS版本 (推荐18.x或20.x)
3. 运行安装程序，按默认设置安装
4. 重启命令行工具

#### 验证安装:
```bash
node --version
npm --version
```

#### 安装前端依赖:
```bash
# 管理面板
cd ConnectAI-E-AdminPanel
npm install
npm run dev

# 浏览器扩展
cd ConnectAI-Helper
npm install
npm run dev
```

## 📊 服务架构

### 本地环境 vs 生产环境对比

| 组件 | 生产环境 | 本地环境 | 状态 |
|------|----------|----------|------|
| 数据库 | MySQL | SQLite | ✅ 已配置 |
| 缓存 | Redis | 内存存储 | ✅ 已配置 |
| 搜索引擎 | Elasticsearch | 文件存储 | ✅ 已配置 |
| 消息队列 | RabbitMQ | 内存队列 | ✅ 已配置 |
| 容器化 | Docker | 本地进程 | ✅ 已配置 |

## 🗂️ 数据存储

### 本地数据目录结构
```
data/
├── connectai.db           # SQLite数据库文件
├── files/                 # 上传文件存储
├── search_index/          # 搜索索引文件
│   └── documents.json     # 文档索引
└── logs/                  # 日志文件
```

## 🔍 功能测试

### 1. 测试DataChat API
```bash
# 健康检查
curl http://localhost:5000/health

# 搜索测试
curl -X POST http://localhost:5000/api/search \
  -H "Content-Type: application/json" \
  -d '{"query": "test"}'
```

### 2. 测试Manager Server
```bash
# 访问主页
curl http://localhost:3000/
```

## 🛠️ 开发工具

### 推荐的开发工具
- **代码编辑器**: VS Code
- **API测试**: Postman 或 curl
- **数据库查看**: DB Browser for SQLite
- **日志查看**: 终端或VS Code集成终端

### VS Code扩展推荐
- Python
- Vue Language Features (Volar)
- SQLite Viewer
- REST Client

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# 杀死占用进程 (Windows)
taskkill /PID <PID> /F
```

#### 2. 虚拟环境问题
```bash
# 重新创建虚拟环境
cd manager-server
rmdir /s venv
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

#### 3. 依赖安装失败
```bash
# 更新pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 📝 开发注意事项

### 1. 数据持久化
- SQLite数据库文件位于 `./data/connectai.db`
- 上传文件存储在 `./data/files/`
- 搜索索引存储在 `./data/search_index/`

### 2. 配置修改
- Manager Server配置: `manager-server/server/settings/config.py`
- DataChat API配置: 环境变量或配置文件

### 3. 日志查看
- Manager Server: 控制台输出
- DataChat API: Flask开发服务器日志

## 🔄 下一步计划

### 待完成的工作
1. **前端服务启动** - 需要安装Node.js
2. **完整功能测试** - 端到端测试
3. **数据库迁移** - 从SQLite到MySQL (可选)
4. **生产环境部署** - Docker容器化

### 扩展功能
1. **监控面板** - 服务状态监控
2. **日志聚合** - 集中日志管理
3. **性能优化** - 缓存和数据库优化
4. **安全加固** - 认证和授权

## 📞 支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 虚拟环境是否正确激活
3. 依赖是否完全安装
4. 端口是否被占用
5. 数据目录权限是否正确

---

**🎉 恭喜！您的ConnectAI本地开发环境已经搭建完成！**
