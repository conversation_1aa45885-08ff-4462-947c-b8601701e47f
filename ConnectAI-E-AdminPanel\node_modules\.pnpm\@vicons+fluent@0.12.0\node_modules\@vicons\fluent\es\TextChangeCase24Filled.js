import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.514 2.75a1 1 0 0 1 .928.666l5.5 15.5a1 1 0 0 1-1.884.668L19.786 16H12.83l-1.397 3.611a1 1 0 0 1-1.866-.722l6-15.5a1 1 0 0 1 .947-.639zm-.055 3.875L13.605 14h5.471l-2.617-7.375zM8.25 20.26a1 1 0 0 1-.956-.707c-.891.463-1.738.707-2.544.707c-1.97 0-3.5-1.4-3.5-3.5c0-.922.32-1.737.91-2.352c.59-.614 1.437-1.015 2.47-1.14a7.69 7.69 0 0 1 2.596.135c-.046-.338-.162-.579-.337-.748c-.23-.224-.604-.367-1.189-.396c-.946-.047-1.552.091-1.878.32a1 1 0 1 1-1.148-1.637c.717-.504 1.673-.712 2.83-.69h.005l.29.01c1.026.05 1.87.373 2.47.945c.601.574.933 1.376.976 2.337v.006l.005.21v5.5a1 1 0 0 1-.865.991l-.008.001l-.119.008H8.25zm-3.38-4.97c-.58.07-.981.255-1.237.507c-.25.247-.383.58-.383 1c0 .492.159.861.41 1.107c.253.247.624.393 1.09.393c.609 0 1.397-.284 2.362-.922l.137-.093v-1.771l-.122-.037a5.805 5.805 0 0 0-2.257-.184z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextChangeCase24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
