import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5 2.5a.5.5 0 0 1 .47.327l3.5 9.5a.5.5 0 0 1-.939.346L7.046 10H2.954l-.985 2.673a.5.5 0 0 1-.938-.346l3.5-9.5a.5.5 0 0 1 .47-.327zm0 1.946L3.322 9h3.356L5 4.446zM10.5 2.5a.5.5 0 0 1 .5.5v4.6c.418-.377.937-.6 1.5-.6c1.38 0 2.5 1.343 2.5 3s-1.12 3-2.5 3c-.563 0-1.082-.223-1.5-.6v.1a.5.5 0 0 1-1 0V3a.5.5 0 0 1 .5-.5zm2 9.5c.665 0 1.5-.717 1.5-2s-.835-2-1.5-2s-1.5.717-1.5 2s.835 2 1.5 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseTitle16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
