# 代码文档 - src/main.ts

## 文件作用
Vue应用的主入口文件，负责应用初始化、插件配置、路由设置和应用挂载。

## 逐行代码解释

### 导入模块 (1-11行)
```typescript
import { createApp } from 'vue';                    // Vue 3应用创建函数
import { createHead } from '@vueuse/head';          // 头部管理插件
import { isMobile } from '@/utils/common';          // 移动端检测工具
import App from './App.vue';                        // 主应用组件
import AppLoading from './components/common/app-loading.vue';  // 加载组件
import { setupDirectives } from './directives';     // 自定义指令设置
import { setupRouter } from './router';             // 路由设置
import { setupAssets } from './plugins';            // 资源插件设置
import { setupStore } from './store';               // 状态管理设置
import { setupI18n, t, getLocale } from './locales'; // 国际化设置
import MobileLoginPrompt from './components/common/mobile-login-prompt.vue'; // 移动端登录提示组件
```

### setupApp异步函数 (13-58行)
```typescript
async function setupApp() {
  // 设置静态资源：JavaScript、CSS文件
  setupAssets();

  // 创建并挂载应用加载界面
  const appLoading = createApp(AppLoading);
  appLoading.mount('#appLoading');  // 挂载到appLoading元素

  // 根据设备类型选择不同的根组件
  // 移动端显示登录提示，桌面端显示主应用
  const app = createApp(isMobile() ? MobileLoginPrompt : App);

  // 获取当前语言设置
  const lang = getLocale() as any;

  // 配置HTML头部信息
  const head = createHead({
    title: t('message.system.title'),        // 页面标题（国际化）
    meta: [
      {
        name: 'description',
        content: t('message.system.description')  // 页面描述（国际化）
      }
    ],
    htmlAttrs: {
      lang: lang.value                       // HTML语言属性
    }
  });
  app.use(head);  // 注册头部管理插件

  // 设置Pinia状态管理
  setupStore(app);
  
  // 设置Vue自定义指令
  setupDirectives(app);

  // 设置Vue路由（异步操作）
  await setupRouter(app);

  // 设置国际化
  setupI18n(app);

  // 开发环境立即挂载应用
  if (import.meta.env.DEV) {
    app.mount('#app');
    return;
  }

  // 生产环境延迟1.1秒挂载（等待加载动画）
  setTimeout(() => {
    app.mount('#app');
  }, 1100);
}
```

### 应用启动 (60行)
```typescript
setupApp();  // 执行应用设置和启动
```

## 初始化流程

### 1. 资源加载阶段
- **setupAssets()**: 加载必要的JavaScript和CSS资源
- **AppLoading**: 显示加载动画，提升用户体验

### 2. 应用创建阶段
- **设备检测**: 使用`isMobile()`检测设备类型
- **组件选择**: 移动端显示登录提示，桌面端显示完整应用
- **头部配置**: 设置页面标题、描述和语言属性

### 3. 插件配置阶段
- **状态管理**: 配置Pinia store
- **自定义指令**: 注册Vue指令
- **路由系统**: 异步配置Vue Router
- **国际化**: 设置多语言支持

### 4. 应用挂载阶段
- **开发环境**: 立即挂载到DOM
- **生产环境**: 延迟挂载，确保加载动画完成

## 技术特点

### 响应式设计
- **设备适配**: 自动检测移动端和桌面端
- **组件切换**: 根据设备类型加载不同组件
- **用户体验**: 移动端提供专门的登录提示

### 性能优化
- **异步加载**: 路由配置采用异步方式
- **延迟挂载**: 生产环境延迟挂载避免闪烁
- **加载动画**: 提供平滑的加载过渡效果

### 国际化支持
- **多语言**: 支持标题和描述的国际化
- **语言属性**: 自动设置HTML语言属性
- **动态切换**: 支持运行时语言切换

### 模块化架构
- **插件分离**: 各功能模块独立配置
- **依赖管理**: 清晰的模块依赖关系
- **可维护性**: 易于扩展和维护

## 环境区分
- **开发环境**: `import.meta.env.DEV`为true时立即挂载
- **生产环境**: 延迟挂载，等待加载动画完成
- **构建优化**: Vite环境变量支持
