import "./chunk-CUFIAZ4C.js";
import {
  Fragment,
  computed,
  createVNode,
  defineComponent,
  mergeProps,
  vShow,
  withDirectives,
  withModifiers
} from "./chunk-MYYYCDVO.js";
import "./chunk-TFWDKVI3.js";

// node_modules/.pnpm/@soybeanjs+vue-materials@0.1.9_vue@3.3.0/node_modules/@soybeanjs/vue-materials/dist/index.mjs
var ye = { grad: 0.9, turn: 360, rad: 360 / (2 * Math.PI) };
var _ = function(e) {
  return typeof e == "string" ? e.length > 0 : typeof e == "number";
};
var c = function(e, t, a) {
  return t === void 0 && (t = 0), a === void 0 && (a = Math.pow(10, t)), Math.round(a * e) / a + 0;
};
var y = function(e, t, a) {
  return t === void 0 && (t = 0), a === void 0 && (a = 1), e > a ? a : e > t ? e : t;
};
var te = function(e) {
  return (e = isFinite(e) ? e % 360 : 0) > 0 ? e : e + 360;
};
var E = function(e) {
  return { r: y(e.r, 0, 255), g: y(e.g, 0, 255), b: y(e.b, 0, 255), a: y(e.a) };
};
var V = function(e) {
  return { r: c(e.r), g: c(e.g), b: c(e.b), a: c(e.a, 3) };
};
var ge = /^#([0-9a-f]{3,8})$/i;
var N = function(e) {
  var t = e.toString(16);
  return t.length < 2 ? "0" + t : t;
};
var ae = function(e) {
  var t = e.r, a = e.g, n = e.b, r = e.a, i = Math.max(t, a, n), l = i - Math.min(t, a, n), u = l ? i === t ? (a - n) / l : i === a ? 2 + (n - t) / l : 4 + (t - a) / l : 0;
  return { h: 60 * (u < 0 ? u + 6 : u), s: i ? l / i * 100 : 0, v: i / 255 * 100, a: r };
};
var ne = function(e) {
  var t = e.h, a = e.s, n = e.v, r = e.a;
  t = t / 360 * 6, a /= 100, n /= 100;
  var i = Math.floor(t), l = n * (1 - a), u = n * (1 - (t - i) * a), s = n * (1 - (1 - t + i) * a), b = i % 6;
  return { r: 255 * [n, u, l, l, s, n][b], g: 255 * [s, n, n, u, l, l][b], b: 255 * [l, l, s, n, n, u][b], a: r };
};
var F = function(e) {
  return { h: te(e.h), s: y(e.s, 0, 100), l: y(e.l, 0, 100), a: y(e.a) };
};
var P = function(e) {
  return { h: c(e.h), s: c(e.s), l: c(e.l), a: c(e.a, 3) };
};
var q = function(e) {
  return ne((a = (t = e).s, { h: t.h, s: (a *= ((n = t.l) < 50 ? n : 100 - n) / 100) > 0 ? 2 * a / (n + a) * 100 : 0, v: n + a, a: t.a }));
  var t, a, n;
};
var S = function(e) {
  return { h: (t = ae(e)).h, s: (r = (200 - (a = t.s)) * (n = t.v) / 100) > 0 && r < 200 ? a * n / 100 / (r <= 100 ? r : 200 - r) * 100 : 0, l: r / 2, a: t.a };
  var t, a, n, r;
};
var me = /^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i;
var ve = /^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i;
var _e = /^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i;
var xe = /^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i;
var R = { string: [[function(e) {
  var t = ge.exec(e);
  return t ? (e = t[1]).length <= 4 ? { r: parseInt(e[0] + e[0], 16), g: parseInt(e[1] + e[1], 16), b: parseInt(e[2] + e[2], 16), a: e.length === 4 ? c(parseInt(e[3] + e[3], 16) / 255, 2) : 1 } : e.length === 6 || e.length === 8 ? { r: parseInt(e.substr(0, 2), 16), g: parseInt(e.substr(2, 2), 16), b: parseInt(e.substr(4, 2), 16), a: e.length === 8 ? c(parseInt(e.substr(6, 2), 16) / 255, 2) : 1 } : null : null;
}, "hex"], [function(e) {
  var t = _e.exec(e) || xe.exec(e);
  return t ? t[2] !== t[4] || t[4] !== t[6] ? null : E({ r: Number(t[1]) / (t[2] ? 100 / 255 : 1), g: Number(t[3]) / (t[4] ? 100 / 255 : 1), b: Number(t[5]) / (t[6] ? 100 / 255 : 1), a: t[7] === void 0 ? 1 : Number(t[7]) / (t[8] ? 100 : 1) }) : null;
}, "rgb"], [function(e) {
  var t = me.exec(e) || ve.exec(e);
  if (!t)
    return null;
  var a, n, r = F({ h: (a = t[1], n = t[2], n === void 0 && (n = "deg"), Number(a) * (ye[n] || 1)), s: Number(t[3]), l: Number(t[4]), a: t[5] === void 0 ? 1 : Number(t[5]) / (t[6] ? 100 : 1) });
  return q(r);
}, "hsl"]], object: [[function(e) {
  var t = e.r, a = e.g, n = e.b, r = e.a, i = r === void 0 ? 1 : r;
  return _(t) && _(a) && _(n) ? E({ r: Number(t), g: Number(a), b: Number(n), a: Number(i) }) : null;
}, "rgb"], [function(e) {
  var t = e.h, a = e.s, n = e.l, r = e.a, i = r === void 0 ? 1 : r;
  if (!_(t) || !_(a) || !_(n))
    return null;
  var l = F({ h: Number(t), s: Number(a), l: Number(n), a: Number(i) });
  return q(l);
}, "hsl"], [function(e) {
  var t = e.h, a = e.s, n = e.v, r = e.a, i = r === void 0 ? 1 : r;
  if (!_(t) || !_(a) || !_(n))
    return null;
  var l = function(u) {
    return { h: te(u.h), s: y(u.s, 0, 100), v: y(u.v, 0, 100), a: y(u.a) };
  }({ h: Number(t), s: Number(a), v: Number(n), a: Number(i) });
  return ne(l);
}, "hsv"]] };
var D = function(e, t) {
  for (var a = 0; a < t.length; a++) {
    var n = t[a][0](e);
    if (n)
      return [n, t[a][1]];
  }
  return [null, void 0];
};
var Ce = function(e) {
  return typeof e == "string" ? D(e.trim(), R.string) : typeof e == "object" && e !== null ? D(e, R.object) : [null, void 0];
};
var k = function(e, t) {
  var a = S(e);
  return { h: a.h, s: y(a.s + 100 * t, 0, 100), l: a.l, a: a.a };
};
var B = function(e) {
  return (299 * e.r + 587 * e.g + 114 * e.b) / 1e3 / 255;
};
var G = function(e, t) {
  var a = S(e);
  return { h: a.h, s: a.s, l: y(a.l + 100 * t, 0, 100), a: a.a };
};
var A = function() {
  function e(t) {
    this.parsed = Ce(t)[0], this.rgba = this.parsed || { r: 0, g: 0, b: 0, a: 1 };
  }
  return e.prototype.isValid = function() {
    return this.parsed !== null;
  }, e.prototype.brightness = function() {
    return c(B(this.rgba), 2);
  }, e.prototype.isDark = function() {
    return B(this.rgba) < 0.5;
  }, e.prototype.isLight = function() {
    return B(this.rgba) >= 0.5;
  }, e.prototype.toHex = function() {
    return t = V(this.rgba), a = t.r, n = t.g, r = t.b, l = (i = t.a) < 1 ? N(c(255 * i)) : "", "#" + N(a) + N(n) + N(r) + l;
    var t, a, n, r, i, l;
  }, e.prototype.toRgb = function() {
    return V(this.rgba);
  }, e.prototype.toRgbString = function() {
    return t = V(this.rgba), a = t.r, n = t.g, r = t.b, (i = t.a) < 1 ? "rgba(" + a + ", " + n + ", " + r + ", " + i + ")" : "rgb(" + a + ", " + n + ", " + r + ")";
    var t, a, n, r, i;
  }, e.prototype.toHsl = function() {
    return P(S(this.rgba));
  }, e.prototype.toHslString = function() {
    return t = P(S(this.rgba)), a = t.h, n = t.s, r = t.l, (i = t.a) < 1 ? "hsla(" + a + ", " + n + "%, " + r + "%, " + i + ")" : "hsl(" + a + ", " + n + "%, " + r + "%)";
    var t, a, n, r, i;
  }, e.prototype.toHsv = function() {
    return t = ae(this.rgba), { h: c(t.h), s: c(t.s), v: c(t.v), a: c(t.a, 3) };
    var t;
  }, e.prototype.invert = function() {
    return h({ r: 255 - (t = this.rgba).r, g: 255 - t.g, b: 255 - t.b, a: t.a });
    var t;
  }, e.prototype.saturate = function(t) {
    return t === void 0 && (t = 0.1), h(k(this.rgba, t));
  }, e.prototype.desaturate = function(t) {
    return t === void 0 && (t = 0.1), h(k(this.rgba, -t));
  }, e.prototype.grayscale = function() {
    return h(k(this.rgba, -1));
  }, e.prototype.lighten = function(t) {
    return t === void 0 && (t = 0.1), h(G(this.rgba, t));
  }, e.prototype.darken = function(t) {
    return t === void 0 && (t = 0.1), h(G(this.rgba, -t));
  }, e.prototype.rotate = function(t) {
    return t === void 0 && (t = 15), this.hue(this.hue() + t);
  }, e.prototype.alpha = function(t) {
    return typeof t == "number" ? h({ r: (a = this.rgba).r, g: a.g, b: a.b, a: t }) : c(this.rgba.a, 3);
    var a;
  }, e.prototype.hue = function(t) {
    var a = S(this.rgba);
    return typeof t == "number" ? h({ h: t, s: a.s, l: a.l, a: a.a }) : c(a.h);
  }, e.prototype.isEqual = function(t) {
    return this.toHex() === h(t).toHex();
  }, e;
}();
var h = function(e) {
  return e instanceof A ? e : new A(e);
};
var X = [];
var pe = function(e) {
  e.forEach(function(t) {
    X.indexOf(t) < 0 && (t(A, R), X.push(t));
  });
};
var v = function(e, t, a) {
  return t === void 0 && (t = 0), a === void 0 && (a = 1), e > a ? a : e > t ? e : t;
};
var H = function(e) {
  var t = e / 255;
  return t < 0.04045 ? t / 12.92 : Math.pow((t + 0.055) / 1.055, 2.4);
};
var L = function(e) {
  return 255 * (e > 31308e-7 ? 1.055 * Math.pow(e, 1 / 2.4) - 0.055 : 12.92 * e);
};
var T = 96.422;
var Z = 100;
var W = 82.521;
var ze = function(e) {
  var t, a, n = { x: 0.9555766 * (t = e).x + -0.0230393 * t.y + 0.0631636 * t.z, y: -0.0282895 * t.x + 1.0099416 * t.y + 0.0210077 * t.z, z: 0.0122982 * t.x + -0.020483 * t.y + 1.3299098 * t.z };
  return a = { r: L(0.032404542 * n.x - 0.015371385 * n.y - 4985314e-9 * n.z), g: L(-969266e-8 * n.x + 0.018760108 * n.y + 41556e-8 * n.z), b: L(556434e-9 * n.x - 2040259e-9 * n.y + 0.010572252 * n.z), a: e.a }, { r: v(a.r, 0, 255), g: v(a.g, 0, 255), b: v(a.b, 0, 255), a: v(a.a) };
};
var we = function(e) {
  var t = H(e.r), a = H(e.g), n = H(e.b);
  return function(r) {
    return { x: v(r.x, 0, T), y: v(r.y, 0, Z), z: v(r.z, 0, W), a: v(r.a) };
  }(function(r) {
    return { x: 1.0478112 * r.x + 0.0228866 * r.y + -0.050127 * r.z, y: 0.0295424 * r.x + 0.9904844 * r.y + -0.0170491 * r.z, z: -92345e-7 * r.x + 0.0150436 * r.y + 0.7521316 * r.z, a: r.a };
  }({ x: 100 * (0.4124564 * t + 0.3575761 * a + 0.1804375 * n), y: 100 * (0.2126729 * t + 0.7151522 * a + 0.072175 * n), z: 100 * (0.0193339 * t + 0.119192 * a + 0.9503041 * n), a: e.a }));
};
var I = 216 / 24389;
var w = 24389 / 27;
var J = function(e) {
  var t = we(e), a = t.x / T, n = t.y / Z, r = t.z / W;
  return a = a > I ? Math.cbrt(a) : (w * a + 16) / 116, { l: 116 * (n = n > I ? Math.cbrt(n) : (w * n + 16) / 116) - 16, a: 500 * (a - n), b: 200 * (n - (r = r > I ? Math.cbrt(r) : (w * r + 16) / 116)), alpha: t.a };
};
var Se = function(e, t, a) {
  var n, r = J(e), i = J(t);
  return function(l) {
    var u = (l.l + 16) / 116, s = l.a / 500 + u, b = u - l.b / 200;
    return ze({ x: (Math.pow(s, 3) > I ? Math.pow(s, 3) : (116 * s - 16) / w) * T, y: (l.l > 8 ? Math.pow((l.l + 16) / 116, 3) : l.l / w) * Z, z: (Math.pow(b, 3) > I ? Math.pow(b, 3) : (116 * b - 16) / w) * W, a: l.alpha });
  }({ l: v((n = { l: r.l * (1 - a) + i.l * a, a: r.a * (1 - a) + i.a * a, b: r.b * (1 - a) + i.b * a, alpha: r.alpha * (1 - a) + i.alpha * a }).l, 0, 400), a: n.a, b: n.b, alpha: v(n.alpha) });
};
function Ie(e) {
  function t(a, n, r) {
    r === void 0 && (r = 5);
    for (var i = [], l = 1 / (r - 1), u = 0; u <= r - 1; u++)
      i.push(a.mix(n, l * u));
    return i;
  }
  e.prototype.mix = function(a, n) {
    n === void 0 && (n = 0.5);
    var r = a instanceof e ? a : new e(a), i = Se(this.toRgb(), r.toRgb(), n);
    return new e(i);
  }, e.prototype.tints = function(a) {
    return t(this, "#fff", a);
  }, e.prototype.shades = function(a) {
    return t(this, "#000", a);
  }, e.prototype.tones = function(a) {
    return t(this, "#808080", a);
  };
}
pe([Ie]);
function re(e, t) {
  e.component(t.name, t);
}
function K(e) {
  let t = null;
  return e.some(([a, n]) => (a && (t = n), a)), t;
}
function j(e, t) {
  return h(e).alpha(t).toHex();
}
function Q(e, t, a = "#ffffff") {
  const n = j(e, t), { r, g: i, b: l } = h(n).toRgb(), { r: u, g: s, b } = h(a).toRgb();
  function z(d, x, $) {
    return x + (d - x) * $;
  }
  const M = {
    r: z(r, u, t),
    g: z(i, s, t),
    b: z(l, b, t)
  };
  return h(M).toHex();
}
function Ne(e) {
  return {
    "--soy-header-height": `${e.headerHeight}px`,
    "--soy-header-z-index": e.headerZIndex,
    "--soy-tab-height": `${e.tabHeight}px`,
    "--soy-tab-z-index": e.tabZIndex,
    "--soy-sider-width": `${e.siderWidth}px`,
    "--soy-sider-collapsed-width": `${e.siderCollapsedWidth}px`,
    "--soy-sider-z-index": e.siderZIndex,
    "--soy-footer-height": `${e.footerHeight}px`,
    "--soy-footer-z-index": e.footerZIndex
  };
}
var g = {
  "layout-header": "_layout-header_nhzen_3",
  "layout-header-placement": "_layout-header-placement_nhzen_4",
  "layout-tab": "_layout-tab_nhzen_12",
  "layout-tab-placement": "_layout-tab-placement_nhzen_18",
  "layout-sider": "_layout-sider_nhzen_22",
  "layout-sider_collapsed": "_layout-sider_collapsed_nhzen_27",
  "layout-footer": "_layout-footer_nhzen_32",
  "layout-footer-placement": "_layout-footer-placement_nhzen_33",
  "left-gap": "_left-gap_nhzen_41",
  "left-gap_collapsed": "_left-gap_collapsed_nhzen_45",
  "sider-padding-top": "_sider-padding-top_nhzen_49",
  "sider-padding-bottom": "_sider-padding-bottom_nhzen_53"
};
var je = (e, {
  slots: t
}) => {
  var a;
  return e.visible && createVNode(Fragment, null, [withDirectives(createVNode("header", {
    class: [g["layout-header"], "soybeanjs-gpr0x9", e.class, {
      "soybeanjs-ihf5pz": e.fixed
    }]
  }, [(a = t.default) == null ? void 0 : a.call(t)]), [[vShow, !e.hide]]), withDirectives(createVNode("div", {
    class: [g["layout-header-placement"], "soybeanjs-hg8qlw"]
  }, null), [[vShow, !e.hide && e.fixed]])]);
};
var Me = (e, {
  slots: t
}) => {
  var a;
  return e.visible && createVNode(Fragment, null, [withDirectives(createVNode("div", {
    class: [g["layout-tab"], "soybeanjs-gpr0x9", e.class, {
      "soybeanjs-elvq0l": e.fixed
    }]
  }, [(a = t.default) == null ? void 0 : a.call(t)]), [[vShow, !e.hide]]), withDirectives(createVNode("div", {
    class: [g["layout-tab-placement"], "soybeanjs-hg8qlw"]
  }, null), [[vShow, !e.hide && e.fixed]])]);
};
var $e = (e, {
  slots: t
}) => {
  var a;
  return e.visible && withDirectives(createVNode("aside", {
    class: ["soybeanjs-sbowzg", e.class, e.collapse ? g["layout-sider_collapsed"] : g["layout-sider"]]
  }, [(a = t.default) == null ? void 0 : a.call(t)]), [[vShow, !e.hide]]);
};
var Ve = (e, {
  slots: t
}) => {
  var a;
  return createVNode("main", {
    id: e.overScroll ? e.scrollId : void 0,
    class: ["soybeanjs-fg4g4j", e.class, {
      "soybeanjs-n12do3": e.overScroll
    }]
  }, [(a = t.default) == null ? void 0 : a.call(t)]);
};
var ke = (e, {
  slots: t
}) => {
  var a;
  return e.visible && createVNode(Fragment, null, [withDirectives(createVNode("footer", {
    class: [g["layout-footer"], "soybeanjs-gpr0x9", e.class, {
      "soybeanjs-muaizb": e.fixed
    }]
  }, [(a = t.default) == null ? void 0 : a.call(t)]), [[vShow, !e.hide]]), withDirectives(createVNode("div", {
    class: [g["layout-footer-placement"], "soybeanjs-hg8qlw"]
  }, null), [[vShow, !e.hide && e.fixed]])]);
};
var Be = "__SCROLL_EL_ID__";
var U = 100;
var He = defineComponent({
  name: "AdminLayout",
  props: {
    mode: {
      type: String,
      default: "vertical"
    },
    scrollMode: {
      type: String,
      default: "content"
    },
    scrollElId: {
      type: String,
      default: Be
    },
    scrollWrapperClass: {
      type: String,
      default: ""
    },
    commonClass: {
      type: String,
      default: "transition-all-300"
    },
    fixedTop: {
      type: Boolean,
      default: true
    },
    maxZIndex: {
      type: Number,
      default: U
    },
    headerVisible: {
      type: Boolean,
      default: true
    },
    headerClass: {
      type: String,
      default: ""
    },
    headerHeight: {
      type: Number,
      default: 56
    },
    tabVisible: {
      type: Boolean,
      default: true
    },
    tabClass: {
      type: String,
      default: ""
    },
    tabHeight: {
      type: Number,
      default: 48
    },
    siderVisible: {
      type: Boolean,
      default: true
    },
    siderClass: {
      type: String,
      default: ""
    },
    siderCollapse: {
      type: Boolean,
      default: false
    },
    siderWidth: {
      type: Number,
      default: 220
    },
    siderCollapsedWidth: {
      type: Number,
      default: 64
    },
    contentClass: {
      type: String,
      default: ""
    },
    fullContent: {
      type: Boolean,
      default: false
    },
    footerVisible: {
      type: Boolean,
      default: true
    },
    footerClass: {
      type: String,
      default: ""
    },
    fixedFooter: {
      type: Boolean,
      default: true
    },
    footerHeight: {
      type: Number,
      default: 48
    },
    rightFooter: {
      type: Boolean,
      default: false
    }
  },
  setup(e, {
    slots: t
  }) {
    const a = computed(() => {
      const {
        mode: d,
        maxZIndex: x = U,
        headerHeight: $,
        tabHeight: ie,
        siderWidth: oe,
        siderCollapsedWidth: le,
        footerHeight: ue
      } = e, de = x - 2, ce = x - 4, se = d === "vertical" ? x - 1 : x - 3, fe = x - 4;
      return Ne({
        headerHeight: $,
        headerZIndex: de,
        tabHeight: ie,
        tabZIndex: ce,
        siderWidth: oe,
        siderZIndex: se,
        siderCollapsedWidth: le,
        footerHeight: ue,
        footerZIndex: fe
      });
    }), n = computed(() => e.scrollMode === "wrapper"), r = computed(() => e.scrollMode === "content"), i = computed(() => e.mode === "vertical"), l = computed(() => e.mode === "horizontal"), u = computed(() => e.fixedTop || l.value && n.value), s = computed(() => K([[Boolean(e.siderVisible && !e.fullContent && e.siderCollapse), g["left-gap_collapsed"]], [Boolean(e.siderVisible && !e.fullContent && !e.siderCollapse), g["left-gap"]]]) || ""), b = computed(() => i.value ? s.value : ""), z = computed(() => K([[i.value, s.value], [l.value && n.value && !e.fixedFooter, s.value], [Boolean(l.value && e.rightFooter), s.value]]) || ""), M = computed(() => {
      let d = "";
      return e.headerVisible && !b.value && (d += g["sider-padding-top"]), e.footerVisible && !z.value && (d += ` ${g["sider-padding-bottom"]}`), d;
    });
    return () => createVNode("div", {
      class: ["soybeanjs-qyp971", e.commonClass],
      style: {
        ...a.value
      }
    }, [createVNode("div", {
      id: n.value ? e.scrollElId : void 0,
      class: ["soybeanjs-jpgwa8", e.commonClass, e.scrollWrapperClass, {
        "soybeanjs-n12do3": n.value
      }]
    }, [createVNode(je, {
      visible: e.headerVisible,
      class: [e.commonClass, e.headerClass, b.value],
      hide: e.fullContent,
      fixed: u.value
    }, {
      default: () => {
        var d;
        return [(d = t.header) == null ? void 0 : d.call(t)];
      }
    }), createVNode(Me, {
      visible: e.tabVisible,
      class: [e.commonClass, e.tabClass, {
        "top-0!": !e.headerVisible
      }, s.value],
      hide: e.fullContent,
      fixed: u.value
    }, {
      default: () => {
        var d;
        return [(d = t.tab) == null ? void 0 : d.call(t)];
      }
    }), createVNode($e, {
      visible: e.siderVisible,
      class: [e.commonClass, e.siderClass, M.value],
      hide: e.fullContent,
      collapse: e.siderCollapse
    }, {
      default: () => {
        var d;
        return [(d = t.sider) == null ? void 0 : d.call(t)];
      }
    }), createVNode(Ve, {
      scrollId: e.scrollElId,
      overScroll: r.value,
      class: [e.commonClass, e.contentClass, s.value]
    }, {
      default: () => {
        var d;
        return [(d = t.default) == null ? void 0 : d.call(t)];
      }
    }), createVNode(ke, {
      visible: e.footerVisible,
      class: [e.commonClass, e.footerClass, z.value],
      hide: e.fullContent,
      fixed: e.fixedFooter
    }, {
      default: () => {
        var d;
        return [(d = t.footer) == null ? void 0 : d.call(t)];
      }
    })])]);
  }
});
He.install = re;
function Le(e) {
  return {
    "--soy-primary-color": e.primaryColor,
    "--soy-primary-color1": e.primaryColor1,
    "--soy-primary-color2": e.primaryColor2,
    "--soy-primary-color-opacity1": e.primaryColorOpacity1,
    "--soy-primary-color-opacity2": e.primaryColorOpacity2,
    "--soy-primary-color-opacity3": e.primaryColorOpacity3
  };
}
var m = {
  "button-tab": "_button-tab_15sm7_3",
  "button-tab_dark": "_button-tab_dark_15sm7_7",
  "button-tab_active": "_button-tab_active_15sm7_16",
  "button-tab_active_dark": "_button-tab_active_dark_15sm7_22",
  "icon-close": "_icon-close_15sm7_26",
  "chrome-tab": "_chrome-tab_15sm7_36",
  "chrome-tab_active": "_chrome-tab_active_15sm7_40",
  "chrome-tab__bg": "_chrome-tab__bg_15sm7_45",
  "chrome-tab_active_dark": "_chrome-tab_active_dark_15sm7_53",
  "chrome-tab_dark": "_chrome-tab_dark_15sm7_65",
  "chrome-tab-divider": "_chrome-tab-divider_15sm7_87"
};
var Re = (e, {
  slots: t
}) => {
  var a, n, r;
  return createVNode("div", {
    class: ["soybeanjs-x463fz", e.class, m["button-tab"], {
      [m["button-tab_dark"]]: e.darkMode
    }, {
      [m["button-tab_active"]]: e.active
    }, {
      [m["button-tab_active_dark"]]: e.active && e.darkMode
    }],
    style: e.style
  }, [(a = t.prefix) == null ? void 0 : a.call(t), createVNode("span", null, [(n = t.default) == null ? void 0 : n.call(t)]), (r = t.suffix) == null ? void 0 : r.call(t)]);
};
var Ae = () => createVNode("svg", {
  style: "width: 100%; height: 100%"
}, [createVNode("defs", null, [createVNode("symbol", {
  id: "geometry-left",
  viewBox: "0 0 214 36"
}, [createVNode("path", {
  d: "M17 0h197v36H0v-2c4.5 0 9-3.5 9-8V8c0-4.5 3.5-8 8-8z"
}, null)]), createVNode("symbol", {
  id: "geometry-right",
  viewBox: "0 0 214 36"
}, [createVNode("use", {
  "xlink:href": "#geometry-left"
}, null)]), createVNode("clipPath", null, [createVNode("rect", {
  width: "100%",
  height: "100%",
  x: "0"
}, null)])]), createVNode("svg", {
  width: "51%",
  height: "100%"
}, [createVNode("use", {
  "xlink:href": "#geometry-left",
  width: "214",
  height: "36",
  fill: "currentColor"
}, null)]), createVNode("g", {
  transform: "scale(-1, 1)"
}, [createVNode("svg", {
  width: "51%",
  height: "100%",
  x: "-100%",
  y: "0"
}, [createVNode("use", {
  "xlink:href": "#geometry-right",
  width: "214",
  height: "36",
  fill: "currentColor"
}, null)])])]);
var Oe = (e, {
  slots: t
}) => {
  var a, n, r;
  return createVNode("div", {
    class: ["soybeanjs-yxkfns", e.class, m["chrome-tab"], {
      [m["chrome-tab_dark"]]: e.darkMode
    }, {
      [m["chrome-tab_active"]]: e.active
    }, {
      [m["chrome-tab_active_dark"]]: e.active && e.darkMode
    }],
    style: e.style
  }, [createVNode("div", {
    class: ["soybeanjs-pr5008", m["chrome-tab__bg"]]
  }, [createVNode(Ae, null, null)]), (a = t.prefix) == null ? void 0 : a.call(t), createVNode("span", null, [(n = t.default) == null ? void 0 : n.call(t)]), (r = t.suffix) == null ? void 0 : r.call(t), createVNode("div", {
    class: ["soybeanjs-714u3q", m["chrome-tab-divider"]]
  }, null)]);
};
var Te = () => createVNode("svg", {
  width: "1em",
  height: "1em",
  viewBox: "0 0 1024 1024"
}, [createVNode("path", {
  fill: "currentColor",
  d: "m563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8L295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512L196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1l216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
}, null)]);
var Ze = (e) => createVNode("div", {
  class: ["soybeanjs-bj4ztj", e.class],
  onClick: e.onClose && withModifiers(e.onClose, ["stop"])
}, [createVNode(Te, null, null)]);
var Y = "#1890ff";
var We = defineComponent({
  name: "AdminTab",
  props: {
    darkMode: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: "chrome"
    },
    commonClass: {
      type: String,
      default: "transition-all-300"
    },
    buttonClass: {
      type: String,
      default: ""
    },
    chromeClass: {
      type: String,
      default: ""
    },
    active: {
      type: Boolean,
      default: false
    },
    activeColor: {
      type: String,
      default: Y
    },
    closable: {
      type: Boolean,
      default: true
    },
    onClose: {
      type: Function,
      default: () => {
      }
    }
  },
  setup(e, {
    slots: t
  }) {
    const a = computed(() => {
      const {
        activeColor: i = Y
      } = e, l = {
        primaryColor: i,
        primaryColor1: Q(i, 0.1, "#ffffff"),
        primaryColor2: Q(i, 0.3, "#000000"),
        primaryColorOpacity1: j(i, 0.1),
        primaryColorOpacity2: j(i, 0.15),
        primaryColorOpacity3: j(i, 0.3)
      };
      return Le(l);
    }), n = computed(() => e.mode === "chrome" ? Oe : Re), r = computed(() => e.mode === "chrome" ? e.chromeClass : e.buttonClass);
    return () => createVNode(n.value, mergeProps({
      class: [e.commonClass, r.value],
      style: {
        ...a.value
      }
    }, e), {
      default: () => {
        var i;
        return (i = t.default) == null ? void 0 : i.call(t);
      },
      prefix: () => {
        var i;
        return (i = t.prefix) == null ? void 0 : i.call(t);
      },
      suffix: () => {
        var i;
        return ((i = t.suffix) == null ? void 0 : i.call(t)) || e.closable && createVNode(Ze, {
          class: m["icon-close"],
          onClose: e.onClose
        }, null);
      }
    });
  }
});
We.install = re;
export {
  He as AdminLayout,
  We as AdminTab,
  Be as SCROLL_EL_ID
};
//# sourceMappingURL=@soybeanjs_vue-materials.js.map
