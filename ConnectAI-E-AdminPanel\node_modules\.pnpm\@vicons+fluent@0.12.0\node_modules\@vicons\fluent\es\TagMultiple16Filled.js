import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.295 1.6l-4.76 4.727a1.99 1.99 0 0 0 0 2.829l3.31 3.288c.787.78 2.061.78 2.847 0l4.69-4.658c.374-.372.585-.875.59-1.4l.024-3.372A2.007 2.007 0 0 0 11.974 1l-3.264.014a2.02 2.02 0 0 0-1.415.585zm4.058 3.058a.774.774 0 0 1-1.09 0a.762.762 0 0 1 0-1.082a.774.774 0 0 1 1.09 0c.3.3.3.784 0 1.082zM1.998 9.75a2 2 0 0 0 .46 2.115l1.964 1.964a4 4 0 0 0 5.657 0l3.482-3.482a1.5 1.5 0 0 0 .44-1.06v-.78l-3.922 3.922c-.069.069-.14.135-.212.197l-.495.496a3 3 0 0 1-4.243 0l-.499-.499a4.08 4.08 0 0 1-.208-.194l-1.964-1.964a1.993 1.993 0 0 1-.46-.715z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagMultiple16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
