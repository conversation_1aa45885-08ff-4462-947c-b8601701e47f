import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 1c.2 0 .38.12.46.303l2.884 6.731l-.753.754a.502.502 0 0 1-.05-.091L6.313 7H2.687L1.96 8.697a.5.5 0 0 1-.92-.394l3-7A.5.5 0 0 1 4.5 1zm1.385 5L4.5 2.77L3.116 6h2.769zm8.427.19c.002-.063.004-.128.004-.193c0-1.82-.957-2.997-2.428-2.997c-.795 0-1.499.392-1.816 1.015H10V1.5a.5.5 0 1 0-1 0v5.378l1-1v-.394c.149-1.032.762-1.651 1.673-1.651c.853 0 1.426.526 1.617 1.44c.17.102.33.227.478.373l.544.545zM9.5 15a1.5 1.5 0 0 1-1.562-.354l-1.586-1.585a1.5 1.5 0 0 1 0-2.122l4.586-4.585a1.5 1.5 0 0 1 2.121 0l1.586 1.585a1.5 1.5 0 0 1 0 2.122l-3.285 3.284l-.007.009l-.009.008l-.638.638H12.5a.5.5 0 0 1 0 1h-3zm.792-2L8 10.707l-.94.94a.5.5 0 0 0 0 .707l1.586 1.585a.5.5 0 0 0 .707 0l.94-.94zm.707-.707l2.94-2.94a.5.5 0 0 0 0-.707l-1.587-1.585a.5.5 0 0 0-.707 0L8.707 10L11 12.293z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextClearFormatting16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
