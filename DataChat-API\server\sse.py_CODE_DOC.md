# 代码文档 - server/sse.py

## 文件作用
Server-Sent Events (SSE) 的简单Flask实现，用于实现服务器到客户端的实时数据推送，无需Redis pub/sub。

## 逐行代码解释

### 文件头注释和导入 (1-12行)
```python
#
#  A simple implementation of Server-Sent Events for Flask
#  that doesn't require Redis pub/sub.
#  Created On 21 November 2022
#
import logging                           # 日志记录
from time import time                    # 时间戳
from re import search                    # 正则表达式搜索
from json import dumps                   # JSON序列化
from queue import Queue, Full            # 队列和队列满异常
from flask import Response              # Flask响应对象
```

### ServerSentEvents类定义 (14-71行)
```python
class ServerSentEvents:
    """Flask的Server-Sent Events简单实现"""

    msg_id: int = 0                      # 消息ID计数器
    listeners = []                       # 监听器列表（存储客户端队列）
```

### response方法 - 创建SSE响应 (20-38行)
```python
def response(self):
    """返回可传递给Flask服务器的响应对象"""

    def stream():
        """生成器函数，产生SSE数据流"""
        has_finished = False             # 标记流是否结束

        queue = Queue(1000)              # 创建容量为1000的队列
        self.listeners.append(queue)     # 将队列添加到监听器列表

        while has_finished == False:
            msg = queue.get()            # 从队列获取消息（阻塞）

            # 检查是否为结束或错误事件
            if search("event: end", msg) or search("event: error", msg):
                yield 'data: [DONE]\n\n'  # 发送结束标记
                has_finished = True       # 标记流结束
            else:
                yield msg                 # 发送普通消息

    # 返回SSE响应，设置正确的MIME类型
    return Response(stream(), mimetype="text/event-stream")
```

### send方法 - 发送事件 (40-70行)
```python
def send(self, payload = None, event: str = "data"):
    """向已打开的通道发送新事件"""

    self.msg_id = self.msg_id + 1        # 递增消息ID

    if event == 'data' or event == 'role':
        # 处理数据和角色事件（OpenAI兼容格式）
        if event == 'role':
            logging.info("event role %r", payload)
        
        # 构造OpenAI兼容的流式响应格式
        msg = 'data: ' + dumps({
            'id': 'cmpl-{}'.format(self.msg_id),     # 完成ID
            'object': 'text_completion',             # 对象类型
            'created': int(time()),                  # 创建时间戳
            'choices': [{
                # 根据事件类型设置delta内容
                'delta': {'role': payload} if event == 'role' else {'content': payload},
                'index': 0,                          # 选择索引
                'finish_reason': None,               # 完成原因
            }]
        }) + '\n\n'
    else:
        # 处理其他类型的事件
        msg_str = dumps(payload) if payload else "{}"
        msg = f"id: {self.msg_id}\nevent: {event}\ndata: {msg_str}\n\n"

    # 向所有监听器发送消息
    for i in reversed(range(len(self.listeners))):
        try:
            self.listeners[i].put_nowait(msg)    # 非阻塞方式放入队列
        except Full as e:
            # 队列满时移除该监听器
            logging.error("error %r", e)
            del self.listeners[i]

    return self                              # 返回self支持链式调用
```

## 技术特点

### Server-Sent Events (SSE)
- **单向通信**: 服务器向客户端推送数据的单向通信协议
- **HTTP协议**: 基于标准HTTP协议，无需WebSocket
- **自动重连**: 客户端断线后自动重连
- **事件流**: 支持命名事件和数据流

### 无依赖实现
- **无Redis依赖**: 不需要Redis pub/sub机制
- **内存队列**: 使用Python内置Queue实现消息传递
- **轻量级**: 简单的实现，适合小到中等规模应用

### OpenAI兼容
- **流式响应**: 兼容OpenAI的流式API响应格式
- **标准格式**: 使用标准的completion响应结构
- **角色支持**: 支持角色（role）和内容（content）事件

### 错误处理
- **队列满处理**: 自动移除无法接收消息的客户端
- **连接管理**: 自动清理断开的连接
- **异常捕获**: 完善的异常处理机制

## 使用场景

### 实时聊天
- **AI对话**: 实时流式输出AI生成的回复
- **打字效果**: 模拟逐字输出的打字效果
- **多轮对话**: 支持连续的对话交互

### 实时通知
- **系统通知**: 向客户端推送系统通知
- **状态更新**: 实时更新任务或处理状态
- **事件广播**: 向多个客户端广播事件

### 数据流
- **日志流**: 实时推送日志信息
- **监控数据**: 实时推送监控指标
- **进度更新**: 实时更新任务进度

## SSE消息格式

### 标准SSE格式
```
id: 1
event: message
data: {"content": "Hello World"}

```

### OpenAI兼容格式
```
data: {"id": "cmpl-1", "object": "text_completion", "created": 1234567890, "choices": [{"delta": {"content": "Hello"}, "index": 0, "finish_reason": null}]}

```

### 结束标记
```
data: [DONE]

```

## 客户端使用示例

### JavaScript EventSource
```javascript
const eventSource = new EventSource('/api/chat/stream');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.choices && data.choices[0].delta.content) {
        // 处理流式内容
        console.log(data.choices[0].delta.content);
    }
};

eventSource.onerror = function(event) {
    console.error('SSE error:', event);
};
```

### 手动处理
```javascript
fetch('/api/chat/stream')
    .then(response => {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        function readStream() {
            return reader.read().then(({ done, value }) => {
                if (done) return;
                
                const chunk = decoder.decode(value);
                // 处理SSE数据
                console.log(chunk);
                
                return readStream();
            });
        }
        
        return readStream();
    });
```

## 优势和限制

### 优势
- **简单易用**: 基于HTTP协议，实现简单
- **自动重连**: 客户端自动处理连接断开
- **跨域支持**: 支持CORS跨域请求
- **轻量级**: 无需额外的依赖和基础设施

### 限制
- **单向通信**: 只能服务器向客户端推送
- **连接数限制**: 浏览器对同域名连接数有限制
- **内存使用**: 每个连接占用服务器内存
- **扩展性**: 单机实现，不适合大规模分布式部署

## 扩展建议
- **Redis集成**: 对于分布式部署可集成Redis pub/sub
- **连接池**: 实现连接池管理大量客户端
- **心跳机制**: 添加心跳检测及时清理死连接
- **压缩支持**: 添加数据压缩减少带宽使用
