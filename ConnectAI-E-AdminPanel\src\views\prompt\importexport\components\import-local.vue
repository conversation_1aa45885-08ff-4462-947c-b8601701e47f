<template>
  <div>
    <n-card class="max-w-[480px] rounded-16px pt-4 pb-2 commonShadow">
      <div>
        <div class="mb-8">
          <p class="mb-4 text-sm font-extrabold leading-none tracking-tight text-gray-900 dark:text-white">
            {{ $t('message.prompt.djxz') }}:<mark
              class="px-1 text-white py-1 bg-blue-600 rounded dark:bg-blue-500 ml-2"
            >
              <a class="cursor-pointer" @click="handleExample"> {{ $t('message.prompt.mbbg') }} </a>
            </mark>
          </p>
        </div>

        <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white" for="user_avatar">
          {{ $t('message.prompt.tzxz') }}</label
        >
        <input
          id="user_avatar"
          ref="fileInputRef"
          class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
          aria-describedby="user_avatar_help"
          type="file"
        />

        <div v-show="false" class="flex items-start mb-4 mt-10">
          <div class="flex items-center h-5">
            <input
              id="terms"
              ref="privacyRef"
              type="checkbox"
              value=""
              class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800"
              required
            />
          </div>
          <label for="terms" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
            {{ $t('message.prompt.wtyzs') }}
            <a href="https://connect-ai.forkway.cn/terms" class="text-blue-600 hover:underline dark:text-blue-500">{{
              $t('message.prompt.cpfwxy')
            }}</a>
            {{ t('message.system.and') }}
            <a href="https://connect-ai.forkway.cn/privacy" class="text-blue-600 hover:underline dark:text-blue-500">{{
              $t('message.prompt.ystk')
            }}</a>
          </label>
        </div>

        <div class="h-30px" />

        <button
          class="text-white w-full bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          @click="handleImport"
        >
          <svg
            v-if="Loading"
            aria-hidden="true"
            role="status"
            class="inline w-4 h-4 mr-3 text-white animate-spin"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="#E5E7EB"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentColor"
            />
          </svg>
          {{ $t('message.prompt.ksdr') }}
        </button>
        <p class="text-sm font-light text-gray-500 mt-4">
          {{ $t('message.prompt.czwc') }}
          <button class="font-medium text-primary-600 hover:underline" @click="handleGoToPromptList">
            {{ $t('message.prompt.qwwdtsc') }}
          </button>
        </p>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMessage } from 'naive-ui';
import { useRouterPush } from '@/composables';
import { importExample, importPromptList } from '~/src/service/api/prompt';
import { exportCSV } from '~/src/utils/common/export';
import { t } from '@/locales';
const message = useMessage();
defineOptions({ name: 'ImportLocal' });
const fileInputRef = ref<HTMLInputElement | null>(null);
const privacyRef = ref<HTMLInputElement | null>(null);
const Loading = ref(false);
const startLoading = () => {
  Loading.value = true;
};
const stopLoading = () => {
  Loading.value = false;
};
async function handleExample() {
  // 直接前端下载即可
  window.open('/import_example.csv');
  // try {
  //   const res = await importExample();
  //   exportCSV(res.data!);
  // } catch (err) {}
}
async function handleImport() {
  startLoading();
  if (!fileInputRef.value?.files?.[0]) {
    message.error(t('message.msg.wxzwj'));
    stopLoading();
    return;
  }
  const fd = new FormData();
  fd.append('file', fileInputRef.value?.files?.[0] as Blob);
  try {
    const res = await importPromptList(fd);
    if (res.error) {
      message.error(t('message.msg.drsb'));
      return;
    }
    message.success(t('message.msg.drwc'));
  } catch (err) {}
  stopLoading();
}
const { routerPush } = useRouterPush();
const handleGoToPromptList = () => {
  routerPush({
    name: 'prompt_my'
  });
};
</script>

<style scoped></style>
