<template>
  <div class="flex justify-center pages">
    <nav aria-label="Page navigation example">
      <ul class="flex list-style-none">
        <li class="page-item disabled">
          <a
            class="page-link relative block py-1.5 px-3 rounded border-0 bg-transparent outline-none transition-all duration-300 rounded hover:text-gray-800 hover:bg-gray-200 focus:shadow-none"
            :class="curPage <= 1 ? 'text-gray-500  cursor-not-allowed ' : ' text-gray-800'"
            href="#"
            tabindex="-1"
            aria-disabled="true"
            @click="e => handlePage(curPage - 1)"
            >前一页
          </a>
        </li>
        <li v-for="pg in pages" :key="pg" class="page-item" :class="{ active: pg === curPage }">
          <a
            class="page-link relative block py-1.5 px-3 rounded border-0 bg-transparent outline-none transition-all duration-300 rounded hover:bg-gray-200 dark:hover:bg-#333 focus:shadow-none"
            href="#"
            @click="e => handlePage(pg)"
            >{{ pg }}</a
          >
        </li>

        <li class="page-item">
          <a
            class="page-link relative block py-1.5 px-3 rounded border-0 bg-transparent outline-none transition-all duration-300 rounded hover:text-gray-800 hover:bg-gray-200 focus:shadow-none"
            :class="curPage >= maxPage ? 'text-gray-500 cursor-not-allowed ' : ' text-gray-800'"
            href="#"
            @click="e => handlePage(curPage + 1)"
            >后一页</a
          >
        </li>
      </ul>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs } from 'vue';

const props = defineProps<{
  total: number;
  pageSize: number;
  page: number;
  onUpdate?: (page: number) => void;
}>();
const { total, pageSize, page, onUpdate } = toRefs(props);
const curPage = ref(page.value);
const maxPage = computed(() => Math.ceil(total.value / pageSize.value) || 1);
const pages = computed(() => Array.from({ length: maxPage.value }, (_, i) => i + 1));
function handlePage(pg: number) {
  if (pg < 1 || pg > maxPage.value) return;
  curPage.value = pg;
  onUpdate?.value?.(pg);
}
</script>

<style scoped>
.pages {
  padding-top: 20px;
}
.active {
  //border: 1px solid #ddd;
  color: #999999;
}
</style>
