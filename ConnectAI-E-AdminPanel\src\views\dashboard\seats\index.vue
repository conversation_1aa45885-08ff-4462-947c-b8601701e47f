<template>
  <div>
    <NoPermission v-if="deny('page.dashboard.seats')" />
    <n-card v-else class="shadow-sm rounded-16px h-full" flex flex-col>
      <template #header>
        <div class="flex flex-wrap justify-between w-full items-center gap-4 mb-4">
          <n-space class="flex justify-start items-center gap-8">
            <span class="text-sm">{{t('message.dashboard.max_seats_label')}} {{currentSeats}}/{{maxSeats}}</span>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-1 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="moreSeats"
            >
              {{t('message.dashboard.more_seats_label')}}
            </button>
          </n-space>
          <div />
          <div class="flex center">
            <n-popover trigger="hover">
              <template #trigger>
                <icon-local-info-out class="mb-0.2" />
              </template>
              <span>{{t('message.dashboard.auto_add_seat')}}</span>
            </n-popover>
            <label for="autoaddseat" class="block text-sm mr-2 ml-2">{{
              $t('message.dashboard.disable_auto_add_seat')
            }}</label>
            <n-switch v-bind:value="autoAddSeat" @update:value="toggleSeatConfig" name="autoaddseat" :rail-style="railStyle">
              <template #checked>
                
              </template>
              <template #unchecked>
                
              </template>
            </n-switch>
          </div>
        </div>
        <div class="flex flex-wrap justify-between w-full items-center gap-4">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
              >Search</label
            >
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[300px] p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="t('message.dashboard.search_placeholder')"
              />
              <button
                type="submit"
                class="text-white absolute right-1 bottom-1.25 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-1 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ t('message.prompt.ss') }}
              </button>
            </div>
          </form>
          <div />
          <n-space class="flex justify-start items-center gap-8">
            <button
              type="button"
              class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 min-w-130px dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
              @click="handleImport"
            >
              <icon-akar-icons-cloud-upload class="mr-2" />
              {{ t('message.dashboard.pldr') }}
            </button>
            <button
              type="button"
              :data-label="t('message.dashboard.import_by_extension')"
              class="connectai-batch-import text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 min-w-130px dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-450 flex"
              @click="handleImportFromExtention"
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="M11.5 19h1v-1.85l3.5-3.5V9H8v4.65l3.5 3.5V19Zm-2 2v-3L6 14.5V9q0-.825.588-1.413T8 7h1L8 8V3h2v4h4V3h2v5l-1-1h1q.825 0 1.413.588T18 9v5.5L14.5 18v3h-5Zm2.5-7Z"
                />
              </svg>
              {{ t('message.dashboard.import_by_extension') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAdd"
            >
              <icon-akar-icons-person-add class="mr-2" />
              {{ t('message.dashboard.addseat') }}
            </button>
          </n-space>
        </div>
      </template>
      <loading-empty-wrapper class="min-h-350px" :loading="loading" :empty="empty">
        <SeatTable
          v-if="!loading && !empty"
          :data="data"
          :pagination-options="paginationOptions"
          @handle-edit="handleEdit"
          @handle-delete="handleDelete"
          @handle-active="handleActive"
        />
      </loading-empty-wrapper>
    </n-card>
    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        :title="formValue.id ? t('message.log.bj') : t('message.log.add')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form ref="formRef" :label-width="80" :model="formValue" :rules="rules">
          <n-form-item :label="t('message.dashboard.name_label')" path="name">
            <n-input v-model:value="formValue.name" :placeholder="t('message.log.qsr')" />
          </n-form-item>
          <n-form-item :label="t('message.dashboard.telephone_label')" path="telephone">
            <n-input v-model:value="formValue.telephone" :placeholder="t('message.log.qsr')" />
          </n-form-item>
          <n-form-item :label="t('message.dashboard.department_label')" path="department">
            <n-select
              filterable
              tag
              :placeholder="t('message.log.qsr')"
              class="col-span-4"
              v-model:value="formValue.department"
              :options="departmentList"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.log.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              {{ t('message.log.qd') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showUpload">
      <n-card
        v-if="showUploadConfirm"
        style="width: 600px"
        :title="t('message.dashboard.upload_confirm')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-transfer
          v-model:value="selected"
          :options="options"
          :render-source-list="renderSourceList"
          source-filterable
        />
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.log.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleUploadOk"
            >
              {{ t('message.log.qd') }}
            </button>
          </div>
        </template>
      </n-card>
      <n-card
        v-else
        style="width: 600px"
        :title="t('message.dashboard.upload_title')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-upload
          :show-remove-button="false"
          multiple
          directory-dnd
          :action="action"
          :custom-request="customRequest"
          :max="1"
        >
          <n-upload-dragger>
            <icon-akar-icons-file class="text-6xl" />
            <br />
            <n-text style="font-size: 16px"> {{ t('message.dashboard.upload_tip') }} </n-text>
          </n-upload-dragger>
        </n-upload>
        <div class="flex text-16px">
          <n-text> {{ t('message.dashboard.template_lable') }} </n-text><span class="ml-1 mr-1"> | </span>
          <a class="text-blue cursor-pointer" @click="handleExample"> {{ $t('message.prompt.mbbg') }} </a>
        </div>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.log.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleNext"
            >
              {{ t('message.log.next') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref, watch, computed, h, CSSProperties } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { NSpace, NTree } from 'naive-ui';
import type { UploadCustomRequestOptions } from 'naive-ui';
import axios from 'axios';
import dayjs from 'dayjs';
import { useLoadingEmpty, usePagination, useTenantPrivilege } from '@/hooks';
import {
  fetchSeatList,
  AddSeat, UpdateSeat, RemoveSeat,
  SeatAction, ImportSeats, SeatDepartment,
  SeatConfig, UpdateSeatConfig,
} from '@/service/api/management';
import SeatTable from './seat-table.vue';
import { PLUGIN_DOWNLOAD_URL } from '@/constants';
import { t } from '@/locales';
const data = ref<ApiLog.ChatLog[]>([]);
const route = useRoute();
const message = useMessage()

const showUpload = ref(false);
const showUploadConfirm = ref(false);
const selected = ref([]);
const parsed = ref([]);

const options = computed(() => parsed.value.map(i => ({ value: i.name, label: i.name })));
const treeData = computed(() => {
  const temp = parsed.value.reduce((s, i) => {
    (s[i.department]=s[i.department]||[]).push({value: i.name, label: i.name});
    return s 
  }, {})
  console.log('temp', temp)
  return Object.entries(temp).map(([k, v]) => {
    const value = k === 'undefined' || !k ? t('message.dashboard.unkown') : k
    return {children: v, label: value, value}
  })
})
const formRef = ref<FormInst | null>(null);
const showModal = ref(false);
const formValue = ref<{
  id?: string;
  name: string;
  telephone: string;
  department: string;
}>({
  name: '',
  telephone: '',
  department: '',
});
const rules: FormRules = {
  name: {
    required: true,
    message: t('message.dashboard.name_validate'),
    trigger: ['input']
  },
  name: {
    required: true,
    validator(rule: FormItemRule, value: []) {
      if (!value || value.length === 0) {
        return new Error(t('message.dashboard.name_validate'));
      }
      return true;
    },
    trigger: ['input', 'blur']
  }
};

const railStyle = ({ checked }: { checked: boolean }) => {
  const style: CSSProperties = {}
  if (checked) {
    style.background = '#1d4ed8'
  } else {
    style.background = ''
  }
  return style
}

const { deny } = useTenantPrivilege();
const { loading, startLoading, endLoading, empty, setEmpty } = useLoadingEmpty();
const itemCount = ref<number>(0);
const maxSeats = ref<number>(0);
const currentSeats = ref<number>(0);
const autoAddSeat = ref<number>(false);
const keyword = ref<string>('');

const { pagination, paginationOptions } = usePagination({ itemCount });
const departmentList = ref([])

watch(pagination, () => {
  getSeatList();
});

function getDepartment() {
  SeatDepartment().then(res => {
    if (res.data) {
      departmentList.value = res.data.data.map(d => ({ value: d, label: d }))
    }
  })
}

async function getSeatList() {
  startLoading();
  try {
    const res = await fetchSeatList({ keyword: keyword.value, ...pagination});
    data.value = res.data?.data || [];
    itemCount.value = res.data!.total;
    maxSeats.value = res.data!.max_seats;
    currentSeats.value = res.data!.current_seats;
    endLoading();
    setEmpty(res.data?.total === 0);
  } catch (err) {
    console.error(err)
  }
}

function handleImportFromExtention() {
  open(PLUGIN_DOWNLOAD_URL);
}

function handleImport() {
  // 批量导入，新增一个文件上传
  showUpload.value = true
}
function handleAdd() {
  // 手动导入，新增一个弹窗
  showModal.value = true
  formValue.value = {
    id: '',
    name: '',
    telephone: '',
    department: '',
  };
}
function handleClose() {
  showUpload.value = false;
  showUploadConfirm.value = false;
  showModal.value = false;
  selected.value = []
  formValue.value = {
    id: '',
    name: '',
    telephone: '',
    department: '',
  };
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      let res
      if (formValue.value.id) {
        res = await UpdateSeat(formValue.value);
      } else {
        res = await AddSeat(formValue.value);
      }
      // console.log('res', res)
      if (res.data) {
        message.success(t('message.msg.bccg'));
        handleClose();
        getSeatList();
        getDepartment();
      }
    }
  });
}

function handleEdit({ name, telephone, department, id }) {
  formValue.value = {
    name,
    telephone,
    department,
    id
  };
  showModal.value = true;
}

async function handleDelete({ id }) {
  await RemoveSeat(id);
  message.success(t('message.msg.sccg'));
  getSeatList();
}

async function handleActive(status: boolean, { id }) {
  const action = status ? 'start' : 'stop';
  await SeatAction({ id, action });
  message.success(t('message.msg.gxztcg'));
  getSeatList();
}

const action = import.meta.env.PROD === false ? '/api/api/seats/parse' : '/api/seats/parse';

async function customRequest(options: UploadCustomRequestOptions) {
  const { file, onFinish, onError, onProgress } = options;
  const formData = new FormData();
  formData.append('file', file.file as File);
  try {
    const { data } = await axios.post(action, formData, {
      withCredentials: true,
      onUploadProgress(e) {
        if (e.lengthComputable) {
          const percent = Math.round((e.loaded * 100) / e.total);
          onProgress({ percent });
        }
      }
    });
    const d = data?.data;
    console.log('data', data, typeof data, d)
    if (d) {
      parsed.value = d
    }
    onFinish();
  } catch (err) {
    onError();
  }
}

function handleNext() {
  showUploadConfirm.value = true
}

const renderSourceList = function ({
  onCheck,
  pattern
}) {
  return h(NTree, {
    style: 'margin: 0 4px;',
    keyField: 'value',
    checkable: true,
    selectable: false,
    blockLine: true,
    checkOnClick: true,
    data: treeData.value,
    cascade: true,
    pattern,
    checkedKeys: selected.value,
    onUpdateCheckedKeys: (checkedKeys: Array<string | number>) => {
      onCheck(checkedKeys)
    }
  })
}

async function handleExample() {
  // 直接前端下载即可
  window.open('/seat_example.xlsx');
}

function handleUploadOk() {
  const seats = parsed.value.filter(i => selected.value.indexOf(i.name)> -1)
  if (seats.length > 0) {
    ImportSeats(seats).then(res => {
      if (res.data) {
        showUpload.value = false
        showUploadConfirm.value = false
        selected.value = []
        getSeatList()
        getDepartment()
      }
    })
  }
}

function moreSeats() {
  window.open('https://www.connectai-e.com/contact')
}

function getSeatConfig() {
  SeatConfig().then(res => {
    if (res.data) {
      // console.log('autoAddSeat', res.data?.data, res.data?.data == 1)
      autoAddSeat.value = res.data?.data == 1
    }
  })
}

function toggleSeatConfig() {
  // 更改值
  UpdateSeatConfig(autoAddSeat.value ? 0 : 1).then(res => {
    getSeatConfig()
  })
}

function handleSearch(e: Event) {
  e.preventDefault();
  getSeatList();
}

onMounted(() => {
  getSeatList();
  getDepartment();
  getSeatConfig();
});
</script>

<style scoped></style>
