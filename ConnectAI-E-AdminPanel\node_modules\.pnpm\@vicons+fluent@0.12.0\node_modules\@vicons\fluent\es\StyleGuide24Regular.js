import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.036 2.778a2.75 2.75 0 0 1 3.368 1.945l2.718 10.142a2.75 2.75 0 0 1-1.945 3.368L11.9 19.915a2.75 2.75 0 0 1-3.368-1.944L5.813 7.828A2.75 2.75 0 0 1 7.758 4.46l6.278-1.682zm-8.233 8.88l1.762 6.571a3.732 3.732 0 0 0 1.002 1.713l-.443-.023a2.75 2.75 0 0 1-2.602-2.89l.281-5.37zm8.621-7.431L8.146 5.909a1.25 1.25 0 0 0-.884 1.531l2.717 10.142a1.25 1.25 0 0 0 1.531.884l6.279-1.682a1.25 1.25 0 0 0 .884-1.531L15.955 5.111a1.25 1.25 0 0 0-1.53-.884zM4.878 10.18l-.355 6.796c-.037.699.12 1.363.424 1.94l-.414-.16a2.75 2.75 0 0 1-1.582-3.553l1.927-5.023zm4.863-3.145a1 1 0 1 1 .518 1.931a1 1 0 0 1-.518-1.931z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'StyleGuide24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
