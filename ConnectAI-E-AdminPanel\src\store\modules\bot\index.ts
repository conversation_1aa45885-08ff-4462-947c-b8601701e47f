import { defineStore } from 'pinia';

export const useBotStore = defineStore('bot', {
  state: (): {
    installBotShow: boolean;
    app: ApiApp.Application;
    client: ApiApp.AppClient[];
    resource: ApiApp.AppResource[];
    resources: ApiApp.AppResources[];
  } => ({
    installBotShow: false,
    app: {} as ApiApp.Application,
    client: [],
    resource: [],
    resources: []
  }),
  getters: {
    getInstallBotShow(): boolean {
      return this.installBotShow;
    },
    getAppClient(): ApiApp.AppClient[] {
      return this.client;
    },
    getAppResource(): ApiApp.AppResource[] {
      return this.resource;
    },
    getAppResources(): ApiApp.AppResources[] {
      return this.resources;
    },
    getApp(): ApiApp.Application {
      return this.app;
    }
  },

  actions: {
    setInstallBotShow(show: boolean) {
      this.installBotShow = show;
    },
    setAppClient(client: ApiApp.AppClient[]) {
      this.client = client;
    },
    setAppResource(resource: ApiApp.AppResource[]) {
      this.resource = resource;
    },
    setAppResources(resources: ApiApp.AppResources[]) {
      this.resources = resources;
    },
    setApp(app: ApiApp.Application) {
      this.app = app;
    }
  }
});
