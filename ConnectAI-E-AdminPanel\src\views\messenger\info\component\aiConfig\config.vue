<template>
  <div>
    <section class="py-8 dark:bg-gray-900 bg-white lg:py-0 rounded-8px">
      <div class="flex">
        <div class="hidden w-full max-w-md p-12 md:h-800px lg:block bg-blue-600">
          <div class="flex items-center mb-8 space-x-4">
            <div class="flex items-center text-xl font-semibold text-white">
              <component :is="iconRender({ localIcon: 'feishu' })" class="w-8 h-8 mr-2" />
              {{ data.name }}
            </div>
            <div
              class="inline-flex items-center text-sm font-medium text-blue-100 hover:text-white cursor-pointer connectai-auto-deploy-close"
              @click="closeModel"
            >
              <svg class="w-6 h-6 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              <span class="w-10"> {{ $t('message.my.fh') }} </span>
            </div>
          </div>
          <TutiorBotFeishu :step="step" />
        </div>
        <div class="flex items-center mx-auto md:w-[800px] px-8 py-8">
          <div class="w-full">
            <DeploySteper :step="step" />
            <DeployStepOne v-if="step === 1" v-model:data="data" @step-go="(s) => (step = s)" :ai-info="aiInfo" />
            <DeployStepTwo v-else-if="step === 2" v-model:data="data" :ai-info="aiInfo" @step-go="(s) => (step = s)" />
            <DeployStepThree v-else-if="step === 3" :data="data" />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, inject } from 'vue';
import { useIconRender } from '@/composables';
import DeploySteper from './deploySteper.vue';
import DeployStepOne from './deployStepOne.vue';
import DeployStepTwo from './deployStepTwo.vue';
import DeployStepThree from './deployStepThree.vue';
import TutiorBotFeishu from '@/views/bot/my/components/botInstall/stepFour/tutiorBotFeishu.vue';

const { iconRender } = useIconRender();
const props = defineProps<{
  data: ApiMessenger.MessengerBot;
  aiInfo: ApiApp.AppInfo;
}>();
const close = inject<() => void>('close');

const { data } = toRefs(props);

const step = ref(1);

function closeModel() {
  close?.();
}
</script>
