import { request } from '~/src/service/request';

export function fetchDatasetList(params: ApiDataset.Req.List) {
  return request.get<ApiDataset.Resp.DatasetList>('/api/collection', { params });
}

export function fetchDatasetInfo(datasetId: string) {
  return request.get<ApiDataset.Dataset>(`/api/collection/${datasetId}`);
}

export function createDataset(data: ApiDataset.Req.Create) {
  return request.post('/api/collection', data);
}

export function updateDataset({ id, data }: ApiDataset.Req.Update) {
  return request.put(`/api/collection/${id}`, data);
}

export function deleteDataset({ id }: ApiDataset.Req.Delete) {
  return request.delete(`/api/collection/${id}`);
}

export function fetchDocuments(datasetId: string, params: ApiDataset.Req.List) {
  return request.get<ApiDataset.Resp.DocumentsList>(`/api/collection/${datasetId}/documents`, { params });
}

export function uploadDocument({ id, data }: ApiDataset.Req.UpoladDocument) {
  return request.post(`/api/collection/${id}/documents`, data);
}

export function removeDocument({ collection_id, id }) {
  return request.delete(`/api/collection/${collection_id}/document/${id}`);
}

export function getDocumentStatus({ id, taskId }: ApiDataset.Req.DocumentStatus) {
  return request.get(`/api/collection/${id}/task/${taskId}`);
}

export function fetchDatasetBotConfig() {
  return request.get<ApiDataset.Resp.DatasetList>('/api/collection/client');
}

export function updateDatasetBotConfig({ data }) {
  return request.post<ApiDataset.Resp.DatasetList>('/api/collection/client', data);
}
export function fetchDatasetYuQueConfig() {
  return request.get<ApiDataset.Resp.DatasetList>('/api/collection/yuque');
}
export function updateDatasetYuQueConfig({ data }) {
  return request.post<ApiDataset.Resp.DatasetList>('/api/collection/yuque', data);
}
export function fetchDatasetNotionConfig() {
  return request.get<ApiDataset.Resp.DatasetList>('/api/collection/notion');
}
export function updateDatasetNotionConfig({ data }) {
  return request.post<ApiDataset.Resp.DatasetList>('/api/collection/notion', data);
}
export function getFeishuWiki() {
  return request.get<ApiDataset.Resp.DatasetList>('/api/collection/feishu/wiki');
}

/**
 *
 *知识库应用
 */

export function fetchDatasetAppInfo() {
  return request.get<ApiApp.Application>('/api/app/knowledge');
}

export function fetchDatasetAppList(params: ApiDataset.Req.List) {
  return request.get<ApiDataset.Resp.DatasetAppList>('/api/knowledge', { params });
}

export function createDatasetApp(data: ApiDataset.Req.CreateApp) {
  return request.post<any>('/api/knowledge', data);
}
