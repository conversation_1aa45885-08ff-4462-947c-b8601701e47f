import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.002 4.001a2 2 0 0 1 2-2H12a2 2 0 0 1 2 2v6.13l-1-1.696V4.001a1 1 0 0 0-1-1H4.002a1 1 0 0 0-1 1V12a1 1 0 0 0 1 1h2.306l-.04.068c-.167.283-.26.604-.267.932h-2a2 2 0 0 1-2-2V4zm5.19 7.499l.59-1h-.778a.5.5 0 0 0 0 1h.189zm1.77-3l.308-.523a1.96 1.96 0 0 1 .395-.477H7.004a.5.5 0 0 0 0 1h1.958zM5.5 5A.75.75 0 1 1 4 5a.75.75 0 0 1 1.5 0zm-.75 3.752a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5zM5.5 11A.75.75 0 1 1 4 11a.75.75 0 0 1 1.5 0zm1.504-6.5a.5.5 0 0 0 0 1h4.474a.5.5 0 1 0 0-1H7.004zm3.731 3.534c.251-.066.526-.039.767.095c.154.086.28.21.367.356l3.002 5.09c.134.228.16.484.095.716a.956.956 0 0 1-.462.58c-.152.084-.325.129-.501.129H7.998c-.28 0-.53-.11-.71-.285a.93.93 0 0 1-.159-1.14l3.003-5.09a.988.988 0 0 1 .603-.45zm.766 1.468a.5.5 0 0 0-1 0v1.996a.5.5 0 1 0 1 0V9.502zM11 14.5a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListSquareWarning16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
