# -*- coding: utf-8 -*-
import json
import asyncio
import logging
from functools import partial
from base import *
from tornado.options import options, parse_command_line
from core.consumer import BaseConsumer, LOGGER, LOG_FORMAT
from core.rabbitmq import MqSession
from settings.constant import AppResult, AppAction
from modules.application.model import ApplicationModel
from langchain.schema import AIMessage
from langchain.adapters.openai import convert_message_to_dict


class OpenAIConsumer(BaseConsumer):

    queue_map = {
        'feishu': options.QUEUE_FEISHU,
        'dingding': options.QUEUE_DINGDING,
        'wework': options.QUEUE_WEWORK,
        'wxwork': options.QUEUE_WXWORK,
        'messenger': options.QUEUE_MESSENGER,
    }

    @property
    def platforms(self):
        return list(self.queue_map.keys())

    async def process_message(self, tag, app_id, data, **kwargs):
        try:
            logging.info("DEBUG %r", data)
            # TODO 暂时不处理，将结果放后面队列
            if data.platform not in self.platforms:
                logging.error("unkown platform %r", data.platform)
                self.acknowledge_message(tag)
                return

            with ApplicationModel(**data) as model:
                agent_executor = model.get_agent(data)

                logging.info("DEBUG agent_executor %r", agent_executor)
                # result = agent_executor.run(data)
                # 子进程异步执行
                func = partial(agent_executor.run, data)
                result = await asyncio.get_event_loop().run_in_executor(None, func)
                if isinstance(result, dict) and 'output' in result:
                    result = result['output']
                if isinstance(result, AIMessage):
                    additional_kwargs = result.additional_kwargs
                    result = convert_message_to_dict(result)
                    # 如果有additional_kwargs，的时候，需要处理一下
                    result.update(additional_kwargs=additional_kwargs)
                    model.save_result(result, True)
                else:
                    # 这种通常是app中的控制命令，隐藏不显示
                    model.save_result(result, False)
                logging.info("DEBUG result %r", result)
            self.acknowledge_message(tag)
        except Exception as e:
            LOGGER.exception(e)
            self.reject_message(tag, requeue=False)
            return False


def main():
    logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)

    logging.info("DEBUG %r", options.RABBIT_MQ_URI)
    consumer = OpenAIConsumer(
        options.RABBIT_MQ_URI,
        queue=options.QUEUE_APPLICATION,
        exchange=options.RABBIT_MQ_EXCHANGE,
        prefetch_count=10,
    )
    try:
        consumer.run()
    except KeyboardInterrupt:
        consumer.stop()


if __name__ == '__main__':
    main()

