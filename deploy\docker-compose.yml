version: '2'
services:
  manager:
    restart: always
    image: connectai-manager:1.0
    volumes:
      - ./dist:/dist
      - ./etc/web_config.conf:/etc/web_config.conf
    ports:
      - "3000"
    environment:
      - VIRTUAL_HOST=manager.connect.ai

  admin:
    restart: always
    image: connectai-manager:1.0
    volumes:
      - ./etc/web_config.conf:/etc/web_config.conf
    environment:
      - VIRTUAL_HOST=admin.connect.ai
    ports:
      - "3000"
    command: bash -c 'pip3 install gradio -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn && python3 /server/admin.py'

  appconsumer:
    restart: always
    image: connectai-manager:1.0
    volumes:
      - ./etc/web_config.conf:/etc/web_config.conf
    command: python3 /server/scripts/application_consumer.py

  feishuconsumer:
    extends: appconsumer
    command: python3 /server/scripts/feishu_consumer.py

  dingdingconsumer:
    extends: appconsumer
    command: python3 /server/scripts/dingding_consumer.py

  weworkconsumer:
    extends: appconsumer
    command: python3 /server/scripts/wework_consumer.py

  messengerconsumer:
    extends: appconsumer
    command: python3 /server/scripts/messenger_consumer.py

  nchan:
    restart: always
    image: lloydzhou/nchan
    ports:
      - "80"
    environment:
      - VIRTUAL_HOST=nchan.connect.ai
    volumes:
      - ./nchan.conf:/etc/nginx/conf.d/default.conf

  redis:
    restart: always
    image: redis:alpine
    ports:
      - "6379"

  rabbitmq:
    restart: always
    image: rabbitmq:3.7-management-alpine
    environment:
      RABBITMQ_ERLANG_COOKIE: "SWQOKODSQALRPCLNMEQG"
      RABBITMQ_DEFAULT_USER: "rabbitmq"
      RABBITMQ_DEFAULT_PASS: "rabbitmq"
      RABBITMQ_DEFAULT_VHOST: "/"
      VIRTUAL_HOST: rabbitmq-manager.connectai.ai
      VIRTUAL_PORT: 15672
    ports:
      - "15672"
      - "5672"
    volumes:
      - ./data/rabbitmq:/data/mnesia

  mysql:
    restart: always
    image: mysql:5.7
    volumes:
      - ./data/mysql/data:/var/lib/mysql
      - ./data/mysql/conf.d:/etc/mysql/conf.d
    environment:
      MYSQL_ROOT_PASSWORD: 'connectai2023'
      MYSQL_DATABASE: 'connectai-manager'
      TZ: 'Asia/Shanghai'
    ports:
      - "3306"
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  elasticsearch:
    restart: always
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    environment:
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - ./data/elasticsearch:/usr/share/elasticsearch/data
    ports:
      - "9200"
      - "9300"

  know-server:
    image: know-server:es
    ports:
      - "80"
    volumes:
      - ./files:/data/files
    environment:
      - VIRTUAL_HOST=know.connect.ai
      - FLASK_OPENAI_API_KEY=
      - FLASK_OPENAI_API_BASE=https://azure.forkway.cn
      # - FLASK_OPENAI_API_PROXY=
      - FLASK_OPENAI_API_VERSION=2023-03-15-preview
      - FLASK_SYSTEM_DOMAIN=http://**************:8081
      - FLASK_SYSTEM_LOGIN_URL=http://**************:8085/login
      - FLASK_SYSTEM_URL=http://manager:3000/api/code2session
      - FLASK_UPLOAD_PATH=/data/files
      - FLASK_DOMAIN=http://know-server
      - FLASK_ES_HOST=elasticsearch
      - FLASK_ES_PORT=9200
      - FLASK_MAX_CONTENT_LENGTH=104867600

  proxy:
    image: jwilder/nginx-proxy:alpine
    ports:
      - "8080:80"
      - "8081:81"
      - "8082:82"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./proxy.conf:/etc/nginx/proxy.conf
      - ./dist:/var/www/html:ro


