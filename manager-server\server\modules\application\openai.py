class ChatTool(CTool):
    name: str = 'openai_chat'
    description: str = 'openai chat'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                if platform == 'feishu':
                    if data.extra.extra.get('action', {}).get('value', {}).get('index'):
                        return
                    send_message(
                        AppResult.ReplyCard if 'open_message_id' not in data.extra.extra else AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                        ),
                        **{'reply_message_id': data.extra.extra.get('open_message_id')}
                    )
                else:
                    send_message(AppResult.ReplyText, _('正在思考，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        ),
                        **{'reply_message_id': data.extra.extra.get('open_message_id')}
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                content = response.generations[0][0].text
                if 'action' not in data.extra.extra:
                    history_count = -1 if instance.extra.get('chat_history', '') != 'enable' else 0
                    user_message_id = data.extra.message_id
                    input_content = input.strip()
                    action_index = 0
                    session.add_message({'role': 'human', 'content': input_content, 'additional_kwargs': {'id': user_message_id}})
                    session.add_message({'role': 'ai', 'content': content, 'additional_kwargs': {'choices': [content]}})
                else:
                    action_value = data.extra.extra.action['value']
                    user_message_id, input_content, action_index, history_index = action_value['id'], action_value['input'], action_value['index'], action_value['history_index']
                    choices = chat_history[history_index].additional_kwargs['choices']
                    if action_index:
                        content = choices[action_index - 1]
                    else:
                        choices.append(content)
                    history_count = len(choices)
                    session.update_message(history_index, {'role': 'ai', 'content': content, 'additional_kwargs': {'choices': choices}})
                actions = []
                for i in range(1, min(history_count, 4) + 1):
                    selected = (i == action_index) or (i == history_count and action_index == 0)
                    actions.append((_('回答 %(i)s', i=i), {'id': user_message_id, 'index': i, 'input': input_content}, selected))
                if 0 <= history_count < 4:
                    actions.append((_('重新生成'), {'id': user_message_id, 'index': 0, 'input': input_content}, False))

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            *([FeishuMessageAction(*[FeishuMessageButton(content=content, value=value, type='primary' if selected else 'default') for content, value, selected in actions])] if actions else []),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                        **{'reply_message_id': data.extra.extra.get('open_message_id')}
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))

        model.callbacks = [OpenAICallbackHandler()]
        if 'action' in data.extra.extra and 'index' in data.extra.extra.action['value']:
            action_value = data.extra.extra.action['value']
            history_index = -1
            for i in range(len(chat_history)):
                if chat_history[i].additional_kwargs.get('id', '') == action_value['id']:
                    history_index = i
                    break
            if history_index < 0 or not chat_history[history_index + 1].additional_kwargs.get('choices'):
                if platform == 'feishu':
                    return send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageNote(FeishuMessagePlainText(_('操作无效，该内容已被清除')))
                        ),
                        **{'reply_message_id': action_value['id']}
                    )
            action_value['history_index'] = history_index + 1
            if action_value['index']:
                return ForwardChat(callbacks=model.callbacks).invoke([])
            else:
                input = action_value['input']
                app_model.update_content(input)

        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])

        api_type = model.openai_api_type
        if api_type == 'Gemini' and model_name == 'gemini-pro-vision':
            return send_note(_('该模型暂不支持'))
        llm_chat_map = {
            '文心一言': WenXinChat,
            '百川大模型': BaichuanChat,
            'ChatGLM': ChatGLMChat,
            'minimax': MiniMaxChat,
            'xinghuo': XingHuoChat,
            'rwkv': RWKVChat,
            '商汤日日新': SenseNovaChat,
            '通义千问': TongYiChat,
            '紫东太初': TaichuChat,
            'Moonshot': MoonshotChat,
            'Hunyuan': HunyuanChat,
            'Gemini': GeminiChat,
            '': ChatOpenAI,
        }
        if api_type in ['claude', 'anthropic']:
            del model['openai_api_type']
            chat = ChatAnthropic(
                max_tokens_to_sample=60000,  # 数据库存的是Text，支持64k，这里需要确保不超出字段保存范围
                temperature=temperature,
                model=model_name,
                **model,
            )
        else:
            if api_type not in llm_chat_map or api_type in ['Gemini']:
                del model['openai_api_type']
            chat = llm_chat_map[api_type if api_type in llm_chat_map else ''](
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        if session.system_role:
            if api_type in ['文心一言', 'Hunyuan', 'Gemini']:
                system_message = [HumanMessage(content=session.system_role), AIMessage(content='好的')]
            else:
                system_message = [SystemMessage(content=session.system_role)]
        else:
            system_message = []
        messages = system_message + chat_history + [HumanMessage(content=input)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class DingdingCommand(CommandTool):
    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_dingding_command'
    description: str = 'openai chat dingding command'

    def send_usage(self):
        tech_map = {
            '文心一言': '文心千帆',
            '百川大模型': '百川大模型',
            '360智脑': '360智脑',
            'ChatGLM': 'ChatGLM',
            'minimax': 'MiniMax',
            'xinghuo': '星火认知大模型',
            'rwkv': 'RWKV',
            '商汤日日新': '商汤日日新',
            'claude': 'Claude',
            'Moonshot': 'MoonshotAI',
            '通义千问': '通义千问',
            '紫东太初': '紫东太初',
            'Hunyuan': '腾讯混元',
            'Gemini': 'Google Gemini',
        }
        tech = tech_map.get(model.openai_api_type, 'ChatGpt')

        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/clear',
                    _('🆑 清除话题上下文')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/model',
                    _('🚀 AI模型切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/ai_mode',
                    _('🤖 发散模式选择')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/roles',
                    _('🏠 内置角色列表')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！", tech=tech)
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            mode_name = input.replace('/ai_mode', '').replace('发散模式', '').strip()
            return 'ai_mode', mode_name
        elif input[:6] == '/roles' or input[:2] == '角色':
            role_name = input.replace('/roles', '').replace('角色列表', '').replace('角色', '').strip()
            return 'roles', role_name
        elif input[:7] == '/system' or input[:4] == '角色扮演':
            system_role = input.replace('/system', '').replace('角色扮演', '').strip()
            return 'system', system_role
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_clear(self):
        session['chat_history'] = []  # 清除历史
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_("🆑 机器人提醒"),
                text=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧"),
            )
        )

    def on_model(self, model_name=None):
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            action_value = m[model_name]
            session['model_id'] = action_value
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择模型：%(model)s", model=model_name),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🚀 机器人提醒'),
                        text=_('无可用模型'),
                    )
                )
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/model ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下模型：'),
                    title=_('🚀 AI模型切换'),
                ),
            )

    def on_ai_mode(self, mode_name=None):
        m = {content: value for value, content in ai_mode}
        if mode_name and mode_name in m:
            action_value = m[mode_name]
            session['temperature'] = float(action_value)
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🤖 机器人提醒'),
                    text=_("已选择模式：%(mode)s", mode=mode_name),
                ),
            )
        else:
            if len(ai_mode) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🤖 机器人提醒'),
                        text=_('无可用模式'),
                    )
                )
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/ai_mode ' + content)),
                        content
                    ) for _, content in ai_mode],
                    text=_('选择以下模式：'),
                    title=_('🤖 发散模式切换'),
                ),
            )

    def on_roles(self, role_name=None):
        m = {content: value for value, content in prompts}
        if role_name and role_name in m:
            action_value = m[role_name]
            prompt = get_prompt_by_id(action_value)
            session['prompt_id'] = action_value
            session['system_role'] = prompt.content
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('👺 已进入角色扮演模式'),
                    text=prompt.content,
                )
            )
        else:
            if len(prompts) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('👺 机器人提醒'),
                        text=_('无可用角色'),
                    )
                )
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/roles ' + content)),
                        content
                    ) for _, content in prompts],
                    text=_('选择以下内置角色：'),
                    title=_('🏠 内置角色选择'),
                )
            )

    def on_system(self, system_role=None):
        if not(system_role):
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('👺 机器人提醒'),
                    text=_('文本回复*角色扮演* 或 */system*+空格+角色信息'),
                )
            )
        session['prompt_id'] = ''
        session['system_role'] = system_role
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                text=system_role,
                title=_('👺 已进入角色扮演模式'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class WEWorkCommand(CommandTool):
    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_wework_command'
    description: str = 'openai chat wework command'

    def send_usage(self):
        tech_map = {
            '文心一言': '文心千帆',
            '百川大模型': '百川大模型',
            '360智脑': '360智脑',
            'ChatGLM': 'ChatGLM',
            'minimax': 'MiniMax',
            'xinghuo': '星火认知大模型',
            'rwkv': 'RWKV',
            '商汤日日新': '商汤日日新',
            'claude': 'Claude',
            'Moonshot': 'MoonshotAI',
            '通义千问': '通义千问',
            '紫东太初': '紫东太初',
            'Hunyuan': '腾讯混元',
            'Gemini': 'Google Gemini',
        }
        tech = tech_map.get(model.openai_api_type, 'ChatGpt')

        return send_message(
            AppResult.ReplyTemplateCard,
            ButtonInteractionCardMessage(
                WXCardButton(_('🆑 清除话题上下文'), '/clear'),
                WXCardButton(_("🚀 AI模型切换"), '/model'),
                WXCardButton(_('🤖 发散模式选择'), '/ai_mode'),
                WXCardButton(_('🏠 内置角色列表'), '/roles'),
                main_title=WXCardMainTitle(
                    _("🎒 需要帮助吗？"),
                    desc=_("👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！", tech=tech)
                ),
                source=WXCardSource(instance.name, instance.icon or app.icon)
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            mode_name = input.replace('/ai_mode', '').replace('发散模式', '').strip()
            return 'ai_mode', mode_name
        elif input[:6] == '/roles' or input[:2] == '角色':
            role_name = input.replace('/roles', '').replace('角色列表', '').replace('角色', '').strip()
            return 'roles', role_name
        elif input[:7] == '/system' or input[:4] == '角色扮演':
            system_role = input.replace('/system', '').replace('角色扮演', '').strip()
            return 'system', system_role
        elif input[0] == '/' and not input.replace('/', ''):
            return 'help',
        elif data.extra.extra.get('SelectedItems', {}):
            selected_items = data.extra.extra.get('SelectedItems', {}).get('SelectedItem', {})
            question_key = selected_items.get('QuestionKey')
            option_id = selected_items.get('OptionIds', {}).get('OptionId')
            if question_key and option_id:
                return question_key, option_id

        return None,

    def on_clear(self):
        session['chat_history'] = []  # 清除历史
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyCard,
            WXTextCard(
                title=_("🆑 机器人提醒"),
                description=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧"),
                url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                btntxt=_('详情'),
            )
        )

    def on_model(self, action_value=None):
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        if action_value:
            session['model_id'] = action_value
            m = {value: content for value, content in ai_model_options}
            model_name = m[action_value]
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('🚀 机器人提醒'),
                    description=_("已选择模型：%(model)s", model=model_name),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('🚀 机器人提醒'),
                        description=_('无可用模型'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, content) for value, content in ai_model_options],
                        question_key='model',
                        title=_('选择以下模型：'),
                    ),
                    main_title=_('🚀 AI模型切换'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )

    def on_ai_mode(self, action_value=None):
        if action_value:
            session['temperature'] = float(action_value)
            m = {str(value): content for value, content in ai_mode}
            mode_name = m.get(action_value, action_value)
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('🤖 机器人提醒'),
                    description=_("已选择模式：%(mode)s", mode=mode_name),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                ),
            )
        else:
            if len(ai_mode) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('🤖 机器人提醒'),
                        description=_('无可用模式'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, text=content) for value, content in ai_mode],
                        question_key='ai_mode',
                        title=_('选择以下模式：'),
                    ),
                    main_title=_('🤖 发散模式切换'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )

    def on_roles(self, action_value=None):
        if action_value:
            prompt = get_prompt_by_id(action_value)
            session['prompt_id'] = action_value
            session['system_role'] = prompt.content
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('👺 已进入角色扮演模式'),
                    description=prompt.content,
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        else:
            if len(prompts) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('👺 机器人提醒'),
                        description=_('无可用角色'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, content) for value, content in prompts[:10]],
                        question_key='roles',
                        title=_('选择以下内置角色：'),
                    ),
                    main_title=_('🏠 内置角色选择'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                )
            )

    def on_system(self, system_role=None):
        if not system_role:
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('👺 机器人提醒'),
                    description=_('文本回复*角色扮演* 或 */system*+空格+角色信息'),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        session['prompt_id'] = ''
        session['system_role'] = system_role
        return send_message(
            AppResult.ReplyCard,
            WXTextCard(
                description=system_role,
                title=_('👺 已进入角色扮演模式'),
                url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                btntxt=_('详情'),
            )
        )


class FeishuCommand(CommandTool):

    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_feishu_command'
    description: str = 'openai chat feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<清除> 或 /clear 即可清除上下文',
        '输入<发散模式> 或 /ai_mode 即可选择发散模式',
        '输入<角色列表> 或 /roles 即可选择内置角色',
        '输入<角色扮演> 或 /system+空格+角色信息 即可进入角色扮演模式'
    ]

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def voice_options(self):
        return ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer']

    @property
    def voice_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(voice, voice) for voice in self.voice_options],
            placeholder=_('选择声音'),
            initial_option=session.extra.get('voice', self.voice_options[0]),
            value={'command': 'voice'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改声音吗？'),
                text=_('选择声音，找到一种与你想要的音调和听众相匹配的声音。'),
            )
        )

    @property
    def ai_mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in ai_mode],
            placeholder=_('选择模式'),
            initial_option=str(float(session.temperature)),
            value={'command': 'ai_mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改发散模式吗？'),
                text=_('选择内置模式，可以让AI更好的理解您的需求。'),
            )
        )

    @property
    def prompt_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in prompts],
            placeholder=_('选择内置角色'),
            initial_option=session.prompt_id or None,
            value={'command': 'prompt'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改角色吗？'),
                text=_('选择内置场景，快速进入角色扮演模式。'),
            )
        ) if len(prompts) > 0 else None

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        tech_map = {
            '文心一言': '文心千帆',
            '百川大模型': '百川大模型',
            '360智脑': '360智脑',
            'ChatGLM': 'ChatGLM',
            'minimax': 'MiniMax',
            'xinghuo': '星火认知大模型',
            'rwkv': 'RWKV',
            '商汤日日新': '商汤日日新',
            'claude': 'Claude',
            'Moonshot': 'MoonshotAI',
            '通义千问': '通义千问',
            '紫东太初': '紫东太初',
            'Hunyuan': '腾讯混元',
            'Gemini': 'Google Gemini',
        }
        tech = tech_map.get(model.openai_api_type, 'ChatGpt')
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉', tech=tech),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **发散模式选择**\n文本回复 *发散模式* 或 */ai_mode*'),
                    tag='lark_md',
                    extra=self.ai_mode_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🏠 **内置角色列表**\n文本回复 *角色列表* 或 */roles*'),
                    tag='lark_md',
                    extra=self.prompt_select
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('👺 **角色扮演模式**\n文本回复*角色扮演* 或 */system*+空格+角色信息'),
                    tag='lark_md',
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/voice' or input[:2] == '声音':
            return 'voice',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            return 'ai_mode',
        elif input[:6] == '/roles' or input[:4] == '角色列表':
            return 'roles',
        elif input[:7] == '/system' or input[:4] == '角色扮演':
            system_role = input.replace('/system', '').replace('角色扮演', '').strip()
            return 'system', system_role
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
                elif 'index' in action['value']:
                    return None,
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_clear(self, flag=None, reply_log_id=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')),
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.SendCard if not reply_log_id else AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            ),
            **({'reply_log_id': reply_log_id} if reply_log_id else {})
        )

    def on_voice(self, voice_name=None):
        if voice_name in self.voice_options:
            session.set_extra('voice', voice_name)
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择声音：**%(voice)s**', voice=voice_name),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择声音'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            real_model_name = m.get(model_name, model_name)
            if real_model_name in ['tts-1', 'tts-1-hd']:
                return send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageAction(self.voice_select),
                        FeishuMessageNote(
                            FeishuMessagePlainText(_('提醒：选择不同的声音，找到一种与你想要的音调和听众相匹配的声音。'))
                        ),
                        header=FeishuMessageCardHeader(_('🚀 声音切换'), template='blue'),
                    )
                )
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择模型：**%(model)s**', model=real_model_name),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_ai_mode(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模式，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 发散模式选择'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_mode}
        session['temperature'] = float(mode_name)
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('已选择发散模式：**%(mode)s**', mode=m.get(mode_name, mode_name)),
                    tag="lark_md",
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_prompt(self, *args):
        return self.on_roles(*args)

    def on_roles(self, role_name=None):
        if not role_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.prompt_select) if len(prompts) else FeishuMessageDiv(_("无可用角色选择")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置场景，快速进入角色扮演模式。'))
                    ),
                    header=FeishuMessageCardHeader(_('🏠 请选择角色'), template='blue'),
                )
            )
        prompt = get_prompt_by_id(role_name)
        # 设置system_role以及prompt_id
        session['prompt_id'] = role_name
        session['system_role'] = prompt.content
        return send_message(
            AppResult.SendCard,  # 这里还使用replycard会引用一大短文字
            FeishuMessageCard(
                FeishuMessageDiv(prompt.content),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'))
                ),
                header=FeishuMessageCardHeader(_('👺 已进入角色扮演模式'), template='blue'),
            )
        )

    def on_system(self, system_role=None):
        if not(system_role):
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('文本回复*角色扮演* 或 */system*+空格+角色信息', tag='lark_md')),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_("👺 **角色扮演模式**"), template='blue'),
                )
            )
        # 设置system_role
        session['prompt_id'] = ''
        session['system_role'] = system_role
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(system_role),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'))
                ),
                header=FeishuMessageCardHeader(_('👺 已进入角色扮演模式'), template='blue'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class OpenaiAgent(CAgent):
    class WenXinAppConfig(BaseAppConfig):
        category = 'LLM'
        name = '文心一言'
        title = '文心一言聊天机器人'
        title_en = 'Wenxin Chatbot'
        description = '🥰 最受关注的中文AI引擎，释放汉字的独特魅力'
        description_en = '🥰 The most sought-after Chinese AI engine, unleashing the unique charm of Chinese characters.'
        problem = '需要一个更懂中文的大语言模型'
        problem_en = 'We need a LLMthat understands Chinese better'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/HNk1dxEGool7umxDanBciUxdnLb'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/Q6ODdzV5aoYezYxotN8lCTjEgMd'
        icon = 'https://pic1.forkway.cn/cdn/202308202025123.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308202025123.png?imageMogr2/thumbnail/720x'
        sorted = 3
        support_resource = [dict(
            resource=['文心一言']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnhEOBrRXMhw89xmiZEHxlmf'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/SUdEwuwCNicPAXkiIZgc7bwfnOy'

    class BaichuanAppConfig(BaseAppConfig):
        category = 'LLM'
        name = '百川大模型'
        title = '百川大模型聊天机器人'
        title_en = 'Baichuan Chatbot'
        description = '🌹 汇聚世界知识，创作妙笔生花'
        description_en = '🌹 Unleashing Global Knowledge, Crafting Masterpieces.'
        problem = '一款融合意图理解、信息检索以及强化学习的中文大模型'
        problem_en = 'A Chinese llm: Unleashing Power with Intent Understanding, Information Retrieval, and Reinforced Learning'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/RrsPdYCozoTvpbxwo8FcrCGyndd?from=from_copylink'
        manual_en = 'https://connect-ai.feishu.cn/docx/BYP6dQuUNoqRWUxXXKAclnavnAh'
        icon = 'https://pic1.forkway.cn/cdn/202309231155362.png'
        logo = 'https://pic1.forkway.cn/cdn/202309231155362.png?imageMogr2/thumbnail/720x'
        sorted = 214
        support_resource = [dict(
            resource=['百川大模型']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnD5nLRoTOKQcpOUTNu1SF1f'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/IOKewS4f4iE0CgkfUqMchv4jnhf'

    class ClaudeAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'Claude'
        title = 'Claude聊天机器人'
        title_en = 'Calude Chatbot'
        description = '🏗为您的任务量身打造的下一代人工智能助手，无论规模大小。'
        description_en = '🏗 next-generation AI assistant for your tasks, no matter the scale'
        problem = '一次读完整本三体？他是如何做到的'
        problem_en = 'Read the whole book at once? How did he do it?'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/XXDvdIsYBoHNoPxSZ22cEj8WnBb?from=from_copylink'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/X5ajdCRWZofu8pxTmJOlLprng2d'
        icon = 'https://pic1.forkway.cn/cdn/202308202018508.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308202018508.png?imageMogr2/thumbnail/720x'
        sorted = 201
        support_resource = [dict(
            resource=['Claude']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnDkZFhDYlZh2qV1mi5JQNsc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/ZEBpwJxu1ij4ijk0NqtcwWbqnod'

    class RWKVAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'RWKV'
        title = 'RWKV聊天机器人'
        title_en = 'RWKV Chatbot'
        description = '⚡ 最快的LLM，具有出色的性能、”无限”的上下文长度和超低费率。'
        description_en = '⚡The fastest LLM with excellent performance, unlimited context length and ultra-low rates.'
        problem = '推荐一个小而美的大语言模型'
        problem_en = 'a small and beautiful big language model'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/TgQidO7BZoQ22gxjHdfcfoeknMf?from=from_copylink'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/L9hNdtsMqoAUZVxpfBSlRhYPgAh'
        icon = 'https://pic1.forkway.cn/cdn/202308211421010.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308211421010.png?imageMogr2/thumbnail/720x'
        sorted = 213
        support_resource = [dict(
            resource=['RWKV']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnqce59QG58In0L56oTxu3Be'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/HM94wR3YhiUqiWkgIBgcqGIrnsh'

    class MiniMaxAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'MiniMax'
        title = 'MiniMax聊天机器人'
        title_en = 'MiniMax Chatbot'
        description = '👍 国内首个同时拥有文本、语音、视觉多种模态融合的通用大模型引擎'
        description_en = '👍 The first general large model engine with text, speech and visual multi-modal fusion in China.'
        problem = '㊙️ WPS 背后的秘密 AI 引擎'
        problem_en = '㊙️ The secret AI engine behind WPS'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/IivRdFJmRo0Zp8xfGuucD8B9n5g?from=from_copylink'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/ULQxd2iU9oJut2xGpBdlbH6rgdf'
        icon = 'https://pic1.forkway.cn/cdn/202308211414021.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308211414021.png?imageMogr2/thumbnail/720x'
        sorted = 212
        support_resource = [dict(
            resource=['MiniMax']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcncjFE4QCzVBK2HvjmldVOld'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/Ku6uwXNewiSvdGkpVW3cbVWtnOh'

    class XingHuoAppConfig(BaseAppConfig):
        category = 'LLM'
        name = '讯飞星火'
        title = '讯飞星火聊天机器人'
        title_en = 'Spark Chatbbot'
        description = '🔥世界上最大的中文预训练语言模型，来自科大讯飞'
        description_en = '🔥The world\'s largest Chinese pre-trained language model, from HKUST Xunfei'
        problem = '推荐一个中文参数最多的大语言模型'
        problem_en = 'Recommend a large language model with the most Chinese parameters'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/X3gtdw0TcotEjwxfYBKcEm4in0f?from=from_copylink'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/NBuLdCzw4ohlPDxxfTWlyAcwg7b'
        icon = 'https://pic1.forkway.cn/cdn/202308202037036.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308202037036.png?imageMogr2/thumbnail/720x'
        sorted = 202
        support_resource = [dict(
            resource=['星火认知大模型']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnmelcyN86uaYNjflb1lokgh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/SSLZwrjxgiaaaxkcQ58cWIPvnPf'

    class ChatGLMAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'ChatGLM'
        title = 'ChatGLM聊天机器人'
        title_en = 'ChatGlm Chatbot'
        description = '🎒千亿参数对话模型，来自清华大学'
        description_en = '🎒100 Billion Parameter Dialogue Model, from Tsinghua University'
        problem = '国产最优秀的开源大语言模型是谁'
        problem_en = 'Who is the best open source big language model made in China?'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/HMTvdr70PoL52ZxTVfGcRL1Bnae?from=from_copylink'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/Nj0mdzDn0o8kILxJoBjlcomAgqd'
        icon = 'https://pic1.forkway.cn/cdn/202308211359867.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308211359867.png?imageMogr2/thumbnail/720x'
        sorted = 203
        support_resource = [dict(
            resource=['ChatGLM']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnFNDwcqswciCE7ncUG9HOPd'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/QL6twxaVZitwBOkwNiocXGyYnPc'

    class GPT360AppConfig(BaseAppConfig):
        category = 'LLM'
        name = '360智脑'
        title = '360智脑聊天机器人'
        title_en = '360 AI Brain'
        description = '🧠  首创大模型安全评估体系，近20年互联网内容能力积累审核'
        description_en = '🧠 The first large-scale model security evaluation system, accumulated review of Internet content capabilities in the past 20 years'
        problem = '首创大模型安全评估体系，内容审核能力强'
        problem_en = 'The first large-scale model security evaluation system with strong content review capabilities'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/P9TmdtNauop66mxikQ1ch54Jngd?from=from_copylink'
        manual_en = 'https://connect-ai.feishu.cn/docx/RIaTdyjfOonxaixwq7lcswqZnOb?from=from_copylink'
        icon = 'https://pic1.forkway.cn/cdn/202308281729654.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308281729654.png?imageMogr2/thumbnail/720x'
        sorted = 219
        support_resource = [dict(
            resource=['360智脑']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcn4BrhFJQRwPwxcM5CrHPisc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/W06owOvyii0IzOkB7BNckEu4nnh'
        wx_suite_id = ''
        wx_suite_secret = ''

    class SenseNovaAppConfig(BaseAppConfig):
        category = 'LLM'
        name = '商汤日日新'
        title = '商汤聊天机器人'
        title_en = 'SenseTime Chatbot'
        description = '🐾 商汤自研的超千亿参数语言大模型应用平台 (只支持中文)'
        description_en = '🐾 SenseTime’s self-developed large-scale language model application platform with over 100 billion parameters'
        problem = '商量商量，都能解决'
        problem_en = 'We can all solve it if we discuss it'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/An0DdewxnovhQExYf29cymr1nng'
        manual_en = 'https://connect-ai.feishu.cn/docx/C9hpd7xFsoF0RYxe31OcQ7m2n0e'
        icon = 'https://pic1.forkway.cn/cdn/202309231200565.png'
        logo = 'https://pic1.forkway.cn/cdn/202309231200565.png?imageMogr2/thumbnail/720x'
        sorted = 217
        support_resource = [dict(
            resource=['商汤日日新']
        )]
        support_bots = ['feishu', 'dingding']  # wework?
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcn4BrhFJQRwPwxcM5CrHPisc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/KdINwDwkliXZ92kZNKEcZiHinic'


    class TongYiAppConfig(BaseAppConfig):
        category = 'LLM'
        name = '通义千问'
        title = '通义千问聊天机器人'
        title_en = 'qianwen ChatBot'
        description = '🐾 阿里云研发的大规模视觉语言模型，支持中文多模态对话及多图对话'
        description_en = '🐾 A large-scale visual language model developed by Aliyun to support Chinese multimodal dialog and multi-graph dialogue'
        problem = '阿里云研发的大规模视觉语言模型，支持中文多模态对话及多图对话'
        problem_en = 'A large-scale visual language model developed by Aliyun to support Chinese multimodal dialog and multi-graph dialogue'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/wiki/WlDPwMOfLiwXwOk0Bb1cphQAnee'
        manual_en = 'https://connect-ai.feishu.cn/wiki/H4dgwJdP0iSWAikXG0JcNhycnIh'
        icon = 'https://pic1.forkway.cn/cdn/20231114141340.png'
        logo = 'https://pic1.forkway.cn/cdn/20231114141340.png?imageMogr2/thumbnail/720x'
        sorted = 215
        support_resource = [dict(
            resource=['通义千问']
        )]
        support_bots = ['feishu', 'dingding']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcn4BrhFJQRwPwxcM5CrHPisc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/RJruw9uhFi4hzSkcl4OcrTEonDg'

    class TaichuAppConfig(BaseAppConfig):
        category = 'LLM'
        name = '紫东太初'
        title = '紫东太初聊天机器人'
        title_en = 'taichu ChatBot'
        description = '🐾 中科院自动化所和武汉人工智能研究院联合推出新一代大模型'
        description_en = '🐾 CAS Institute of Automation and Wuhan Institute of Artificial Intelligence Jointly Launch New Generation of Big Models'
        problem = '中科院自动化所和武汉人工智能研究院联合推出新一代大模型'
        problem_en = 'CAS Institute of Automation and Wuhan Institute of Artificial Intelligence Jointly Launch New Generation of Big Models'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/wiki/OI0zwRhCGiX8Fek0vWgcn68znRd'
        manual_en = ''
        icon = 'https://pic1.forkway.cn/cdn/20231115155336.png'
        logo = 'https://pic1.forkway.cn/cdn/20231115155336.png?imageMogr2/thumbnail/720x'
        sorted = 216
        support_resource = [dict(
            resource=['紫东太初']
        )]
        support_bots = ['feishu', 'dingding']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcn4BrhFJQRwPwxcM5CrHPisc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/R0Hew3f0ri34NkkMHHGchrpOnTt'

    class KimiChatAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'KimiChat'
        title = 'KimiChat'
        title_en = 'KimiChat'
        description = '💥 突破20万上下文窗口的对话大模型，来自国产大模型明星团队-月之暗面，帮你看更大的世界'
        description_en = '💥 A large dialogue model with over 200,000 context windows, from the domestic large model star team - Dark Side of the Moon, helping you see a bigger world'
        problem = '超长上下文解决90%的大模型定制化问题'
        problem_en = 'Ultra-long context solves 90% of large model customization problems'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/UR37dNfoIoyhI1xlR9LcRfI5ngc'
        manual_en = 'https://connect-ai.feishu.cn/docx/GdPQdGVEGo13dKxAreEcI5l9nXg'
        icon = 'https://pic1.forkway.cn/cdn/img_v2_7c9dd6d9-6188-4252-8ffa-a667f9ef926g.png'
        logo = 'https://pic1.forkway.cn/cdn/img_v2_7c9dd6d9-6188-4252-8ffa-a667f9ef926g.png?imageMogr2/thumbnail/720x'
        sorted = 10
        support_resource = [dict(
            resource=['Moonshot']
        )]
        support_bots: List[str] = ['feishu', 'dingding', 'wework']
        feedback_url: str = ''
        deploy: str = 'https://connect-ai.feishu.cn/wiki/SwNiwPI3FiD8Mrkfy0lc4JAMn3e'

    class HunyuanAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'Hunyuan'
        title = '混元聊天机器人'
        title_en = 'Hunyuan ChatBot'
        description = '由腾讯研发的大语言模型，具备强大的中文创作能力，复杂语境下的逻辑推理能力，以及可靠的任务执行能力。'
        description_en = 'The large language model developed by Tencent has powerful Chinese creation capabilities, logical reasoning capabilities in complex contexts, and reliable task execution capabilities.'
        problem = '由腾讯研发的大语言模型，具备强大的中文创作能力，复杂语境下的逻辑推理能力，以及可靠的任务执行能力。'
        problem_en = 'The large language model developed by Tencent has powerful Chinese creation capabilities, logical reasoning capabilities in complex contexts, and reliable task execution capabilities.'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/wiki/Bd3uwZinGihjZ7kMOTHcw8jbnxg'
        manual_en = 'https://connect-ai.feishu.cn/wiki/SQ8QwwQC2iVbFNkzftXcYgtcnFf'
        icon = 'https://pic.forkway.cn/cdn/20231215144331.png'
        logo = 'https://pic.forkway.cn/cdn/20231215144331.png?imageMogr2/thumbnail/720x'
        sorted = 218
        support_resource = [dict(
            resource=['Hunyuan']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = ''
        deploy: str = 'https://connect-ai.feishu.cn/wiki/Z10WwnMajivqRJkABHtc9GSnneg'

    class GeminiAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'Gemini'
        title = 'Gemini聊天机器人'
        title_en = 'Gemini Chatbot'
        description = 'Google Gemini是一个跨模态的、真正通用的AI模型，可以无缝地处理文本、视觉、音频、图像和视频等多种输入和输出'
        description_en = 'Google Gemini is a cross-modal, truly universal AI model that can seamlessly handle multiple inputs and outputs such as text, visuals, audio, images, and videos.'
        problem = 'Google Gemini是一个跨模态的、真正通用的AI模型，可以无缝地处理文本、视觉、音频、图像和视频等多种输入和输出'
        problem_en = 'Google Gemini is a cross-modal, truly universal AI model that can seamlessly handle multiple inputs and outputs such as text, visuals, audio, images, and videos.'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/wiki/LUdMwh5ByiDaZgkt71jcbf8SnJb'
        manual_en = 'https://connect-ai.feishu.cn/wiki/LUdMwh5ByiDaZgkt71jcbf8SnJb'
        icon = 'https://pic1.forkway.cn/cdn/20231227172458.png'
        logo = 'https://pic1.forkway.cn/cdn/20231227172458.png?imageMogr2/thumbnail/720x'
        sorted = 9
        support_resource = [dict(
            resource=['Gemini']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = ''
        deploy: str = 'https://connect-ai.feishu.cn/wiki/E2HzwwIjgijWEkkt5VvcMk3fnEc'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        if platform == 'feishu':
            return AgentAction(tool='openai_chat_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='openai_chat_dingding_command', tool_input=kwargs, log="")
        elif platform in ['wework', 'wxwork']:
            return AgentAction(tool='openai_chat_wework_command', tool_input=kwargs, log="")
        return AgentAction(tool='openai_chat', tool_input=kwargs, log="")
