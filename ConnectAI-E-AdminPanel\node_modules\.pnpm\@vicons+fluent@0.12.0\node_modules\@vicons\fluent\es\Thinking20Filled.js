import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.695 2.642C8.28 2.242 9.038 2 10 2c.962 0 1.72.242 2.305.642c.583.397.963.929 1.21 1.46c.33.706.436 1.437.47 1.915c.647.048 1.31.203 1.855.567C16.558 7.063 17 7.854 17 9c0 1.17-.438 1.966-1.164 2.44c-.685.448-1.555.56-2.336.56h-7c-.79 0-1.657-.129-2.34-.584C3.442 10.937 3 10.146 3 9s.442-1.937 1.16-2.416c.545-.364 1.208-.519 1.856-.567c.033-.478.138-1.209.468-1.916a3.555 3.555 0 0 1 1.21-1.46zM10 15a2 2 0 1 1-4 0a2 2 0 0 1 4 0zm-5 1.5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Thinking20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
