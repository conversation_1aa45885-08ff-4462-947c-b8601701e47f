resolver 127.0.0.11;

upstream upstream_redis {
    nchan_redis_server redis;
}

client_max_body_size 10m;

server {
    listen 80 default_server;
    error_log /dev/stderr debug;

    location ~ ^/chat/pubsub/(\w+)$ {
        nchan_pubsub;
        nchan_websocket_client_heartbeat PING PONG;
        nchan_websocket_ping_interval 3;
        nchan_channel_id $1_$arg_visitor_id;
        nchan_subscriber_first_message 0;
        nchan_publisher_upstream_request /ws/pub/$1;
        nchan_subscribe_request /ws/sub/$1;
        nchan_unsubscribe_request /ws/unsub/$1;
        nchan_authorize_request /ws/auth/$1;
        # nchan_redis_pass upstream_redis;
        nchan_message_buffer_length 0;
    }

    location /chat {
        proxy_pass http://proxy;
        proxy_set_header Host manager.connect.ai;
    }

    location ~ ^/chat/reply/(\w+)$ {
        content_by_lua_block {
            local uri, n, err = ngx.re.gsub(ngx.var.uri, "reply", "pubsub")
            uri = uri .. "?" .. ngx.var.query_string
            ngx.req.read_body()
            -- send message to server
            ngx.location.capture(uri, { method = ngx.HTTP_POST, body = ngx.req.get_body_data() })
            ngx.sleep(0.2)
            -- wait reply message
            ngx.req.set_method(ngx.HTTP_GET)
            ngx.req.discard_body()
            ngx.exec(uri)
        }
    }

    location /ws {
        proxy_pass http://proxy;
        proxy_ignore_client_abort on;
        proxy_set_header X-Publisher-Type $nchan_publisher_type;
        proxy_set_header X-Prev-Message-Id $nchan_prev_message_id;
        proxy_set_header X-Message-Id $nchan_message_id;
        proxy_set_header X-Channel-Id $nchan_channel_id;
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header Host manager.connect.ai;
    }
}
