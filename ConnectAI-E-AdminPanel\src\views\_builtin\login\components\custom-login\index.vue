<template>
  <div class="relative min-h-screen flex">
    <div
      class="flex justify-center flex-col items-center sm:flex-row items-center md:items-start sm:justify-center md:justify-start flex-auto min-w-0 bg-white"
    >
      <div
        class="sm:w-1/2 xl:w-2/5 h-full hidden md:flex flex-auto items-center justify-start p-10 overflow-hidden bg-purple-900 text-white bg-no-repeat bg-cover relative"
        style="
          background-image: url(https://images.unsplash.com/photo-1579451861283-a2239070aaa9?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=1950&amp;q=80);
        "
      >
        <div class="absolute bg-gradient-to-b from-blue-900 to-gray-900 opacity-75 inset-0 z-0"></div>
        <div class="absolute triangle min-h-screen right-0 w-16" style=""></div>
        <!-- <a href="https://www.connectai-e.com/" class="z-50"> -->
        <div class="flex absolute top-5 text-center text-gray-100 focus:outline-none">
          <system-logo class="object-cover mx-auto w-8 h-8 rounded-full w-10 h-10 text-white" />
          <p class="text-2xl ml-3">
            <strong>{{ $t('message.system.title') }}</strong>
          </p>
        </div>
        <!-- </a> -->
        <icon-local-login-home class="h-130 w-130 absolute right-1 mr-1" />
        <!-- <a href="https://www.connectai-e.com/" class="z-20"> -->
        <div class="w-full max-w-lg z-20">
          <div class="sm:text-3xl xl:text-5xl font-bold leading-tight mb-6">{{ $t('message.system.fxai') }}</div>
          <div class="sm:text-sm xl:text-md text-gray-200 leading-loose">
            {{ $t('message.system.jdgx') }}<br />{{ $t('message.system.rbsbjd') }}
          </div>
        </div>
        <!-- </a> -->
        <div v-if="!lark" class="flex absolute bottom-5 text-center text-gray-100 focus:outline-none">
          <a class="text-md ml-3 z-99" href="https://beian.miit.gov.cn/" target="_blank"> 鄂ICP备2023011946号 </a>
        </div>
        <!---remove custom style-->
        <ul class="circles z-0">
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
        </ul>
      </div>

      <div class="absolute right-3 top-3 p-1">
        <n-dropdown :options="options" trigger="hover" :value="language" @select="handleSelect">
          <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24">
            <path
              fill="#000"
              d="M12 22q-2.05 0-3.875-.788t-3.188-2.15q-1.362-1.362-2.15-3.187T2 12q0-2.075.788-3.888t2.15-3.174Q6.3 3.575 8.124 2.788T12 2q2.075 0 3.888.788t3.174 2.15q1.363 1.362 2.15 3.175T22 12q0 2.05-.788 3.875t-2.15 3.188q-1.362 1.362-3.175 2.15T12 22Zm0-2.05q.65-.9 1.125-1.875T13.9 16h-3.8q.3 1.1.775 2.075T12 19.95Zm-2.6-.4q-.45-.825-.788-1.713T8.05 16H5.1q.725 1.25 1.813 2.175T9.4 19.55Zm5.2 0q1.4-.45 2.488-1.375T18.9 16h-2.95q-.225.95-.562 1.838T14.6 19.55ZM4.25 14h3.4q-.075-.5-.113-.988T7.5 12q0-.525.038-1.012T7.65 10h-3.4q-.125.5-.188.988T4 12q0 .525.063 1.012T4.25 14Zm5.4 0h4.7q.075-.5.113-.988T14.5 12q0-.525-.038-1.012T14.35 10h-4.7q-.075.5-.113.988T9.5 12q0 .525.038 1.012T9.65 14Zm6.7 0h3.4q.125-.5.188-.988T20 12q0-.525-.063-1.012T19.75 10h-3.4q.075.5.113.988T16.5 12q0 .525-.038 1.012T16.35 14Zm-.4-6h2.95q-.725-1.25-1.812-2.175T14.6 4.45q.45.825.788 1.713T15.95 8ZM10.1 8h3.8q-.3-1.1-.775-2.075T12 4.05q-.65.9-1.125 1.875T10.1 8Zm-5 0h2.95q.225-.95.563-1.838T9.4 4.45Q8 4.9 6.912 5.825T5.1 8Z"
            />
          </svg>
        </n-dropdown>
      </div>

      <div
        v-if="typ == 'login'"
        class="md:flex md:items-center md:justify-center w-full sm:w-auto md:h-full xl:w-2/5 sm:rounded-lg md:rounded-none bg-white"
      >
        <section class="max-w-md w-full space-y-8 mra mla">
          <div class="flex flex-col items-center justify-center px-6 mx-auto md:h-screen lg:py-0">
            <div class="w-full bg-white rounded-lg md:mt-0 sm:max-w-md xl:p-0">
              <div class="space-y-4 md:space-y-6 p-8 commonShadow">
                <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">
                  {{ $t('message.system.dl') }}
                </h1>
                <n-form class="space-y-4 md:space-y-6" :rules="rules" size="large" :show-label="false">
                  <div>
                    <label for="email" class="block mb-2 text-sm font-medium text-gray-900">{{
                      $t('message.system.uid')
                    }}</label>
                    <input
                      id="email"
                      v-model="email"
                      name="email"
                      class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5"
                      :placeholder="$t('message.system.phoneoremail')"
                      required=""
                    />
                  </div>
                  <div>
                    <label for="passwd" class="block mb-2 text-sm font-medium text-gray-900">{{
                      $t('message.system.pwd')
                    }}</label>
                    <input
                      id="passwd"
                      v-model="passwd"
                      type="password"
                      name="passwd"
                      placeholder="••••••••"
                      class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5"
                      required=""
                    />
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          id="terms"
                          v-model="agreeTerms"
                          aria-describedby="remember"
                          type="checkbox"
                          class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300"
                          required=""
                        />
                      </div>
                      <div class="ml-3 text-sm">
                        <label for="terms" class="font-light text-gray-500"
                          >{{ $t('message.system.yyd') }}
                          <a
                            class="font-medium text-primary-600 hover:underline"
                            target="_blank"
                            href="https://www.connectai-e.com/terms"
                            >{{ $t('message.system.fwxy') }}</a
                          >
                          {{ $t('message.system.and') }}
                          <a
                            class="font-medium text-primary-600 hover:underline"
                            target="_blank"
                            href="https://www.connectai-e.com/privacy"
                            >{{ $t('message.system.ystk') }}</a
                          >.</label
                        >
                      </div>
                    </div>
                    <a href="#" class="text-sm font-medium text-primary-600 hover:underline"></a>
                  </div>
                  <button
                    type="submit"
                    class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                    @click="handleSubmit"
                  >
                    <svg
                      v-if="auth.loginLoading"
                      aria-hidden="true"
                      role="status"
                      class="inline w-4 h-4 mr-3 text-white animate-spin"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                      />
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                      />
                    </svg>
                    {{ $t('message.system.login') }}
                  </button>
                  <!-- <p class="text-sm font-light text-gray-500">
                    <a
                      v-if="!lark"
                      href="javascript:;"
                      class="font-medium text-primary-600 hover:underline"
                      @click="switchType('telephone')"
                      >{{ $t('message.system.kjdl') }}</a
                    >
                    {{ $t('message.system.myzh') }}
                    <a
                      href="javascript:;"
                      class="font-medium text-primary-600 hover:underline"
                      @click="switchType('register')"
                      >{{ $t('message.system.email') }}</a
                    >
                  </p> -->
                </n-form>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { FormRules } from 'naive-ui';
import { useStorage } from '@vueuse/core';
import { REGEXP_PHONE, REGEXP_EMAIL } from '@/config';
import { useAuthStore } from '@/store';
import { useI18n } from 'vue-i18n';
import { localStg } from '@/utils';
import { useSmsCode } from '@/hooks';
import { formRules, isLark as lark } from '@/utils';
import { t } from '@/locales';

const { locale } = useI18n();

const language = ref<I18nType.langType>(localStg.get('lang') || 'zh-CN');
const options = [
  {
    label: '中文',
    key: 'zh_CN'
  },
  {
    label: 'English',
    key: 'en_US'
  },
  {
    label: 'Tiếng Việt',
    key: 'vi_VN'
  }
];
const handleSelect = (key: string) => {
  language.value = key as I18nType.langType;
  locale.value = key;
  localStg.set('lang', key as I18nType.langType);
  document.cookie = `__lang__=${language.value};path=/;`;
  location.reload();
};

const { label, isCounting, loading: smsLoading, getSmsCode } = useSmsCode();
const { label: label1, isCounting1, loading: emailLoading, getSmsCode: getEmailCode } = useSmsCode(true);

const typ =  ref('login'); // login/register/telephone
const email = ref('');
const passwd = ref('');
const phone = ref('');
const code = ref('');
const agreeTerms = useStorage('agreeTerms', false);

const auth = useAuthStore();
const { login, loginCode, register } = useAuthStore();

function switchType(t) {
  // console.log('switchType', t);
  typ.value = t;
}

function handleSmsCode() {
  getSmsCode(phone.value);
}

function handleEmailCode() {
  getEmailCode(email.value);
}

async function handleSubmit() {
  if (!agreeTerms.value) {
    return;
  }
  login(email.value, passwd.value);
}

async function handleSubmitPhone() {
  // 验证码注册或者登录
  if (!agreeTerms.value) {
    return;
  }
  loginCode(phone.value, code.value);
}

async function handleSubmitEmail() {
  // 邮箱注册
  if (!agreeTerms.value) {
    return;
  }
  register(email.value, code.value, passwd.value);
}

const rules: FormRules = {
  email: [
    {
      validator(rule: any, value: string) {
        if (typ.value === 'register') {
          if (value && REGEXP_EMAIL.test(value)) {
            return true;
          }
          return new Error(t('message.global.qtxemail'));
        }
        if (typ.value === 'login') {
          if (value && (REGEXP_EMAIL.test(value) || REGEXP_PHONE.test(value))) {
            return true;
          }
          return new Error(t('message.global.qtxuid'));
        }
        return new Error(t('message.global.qtxuid'));
      }
    }
  ],
  passwd: formRules.pwd
};
</script>

<style scoped lang="scss">
.commonShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;

  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}

/*remove custom style*/
.circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.circles li {
  position: absolute;
  display: block;
  list-style: none;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  animation: animate 25s linear infinite;
  bottom: -150px;
}

.circles li:nth-child(1) {
  left: 25%;
  width: 80px;
  height: 80px;
  animation-delay: 0s;
}

.circles li:nth-child(2) {
  left: 10%;
  width: 20px;
  height: 20px;
  animation-delay: 2s;
  animation-duration: 12s;
}

.circles li:nth-child(3) {
  left: 70%;
  width: 20px;
  height: 20px;
  animation-delay: 4s;
}

.circles li:nth-child(4) {
  left: 40%;
  width: 60px;
  height: 60px;
  animation-delay: 0s;
  animation-duration: 18s;
}

.circles li:nth-child(5) {
  left: 65%;
  width: 20px;
  height: 20px;
  animation-delay: 0s;
}

.circles li:nth-child(6) {
  left: 75%;
  width: 110px;
  height: 110px;
  animation-delay: 3s;
}

.circles li:nth-child(7) {
  left: 35%;
  width: 150px;
  height: 150px;
  animation-delay: 7s;
}

.circles li:nth-child(8) {
  left: 50%;
  width: 25px;
  height: 25px;
  animation-delay: 15s;
  animation-duration: 45s;
}

.circles li:nth-child(9) {
  left: 20%;
  width: 15px;
  height: 15px;
  animation-delay: 2s;
  animation-duration: 35s;
}

.circles li:nth-child(10) {
  left: 85%;
  width: 150px;
  height: 150px;
  animation-delay: 0s;
  animation-duration: 11s;
}

@keyframes animate {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
    border-radius: 0;
  }

  100% {
    transform: translateY(-1000px) rotate(720deg);
    opacity: 0;
    border-radius: 50%;
  }
}

.triangle {
  border-top: 100vh solid #fff;
  border-left: 25rem solid transparent;
}
</style>
