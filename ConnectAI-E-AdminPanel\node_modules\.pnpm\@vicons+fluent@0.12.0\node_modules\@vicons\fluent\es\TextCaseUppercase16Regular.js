import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 2.5a.5.5 0 0 1 .47.327l3.5 9.5a.5.5 0 0 1-.939.346L6.546 10H2.454l-.985 2.673a.5.5 0 0 1-.938-.346l3.5-9.5a.5.5 0 0 1 .47-.327zm0 1.946L2.822 9h3.356L4.5 4.446zm5-1.946A.5.5 0 0 0 9 3v9.5a.5.5 0 0 0 .5.5H12a3 3 0 0 0 1.157-5.769A2.75 2.75 0 0 0 11.25 2.5H9.5zM13 5.25A1.75 1.75 0 0 1 11.25 7H10V3.5h1.25c.966 0 1.75.784 1.75 1.75zM12 12h-2V8h2a2 2 0 1 1 0 4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseUppercase16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
