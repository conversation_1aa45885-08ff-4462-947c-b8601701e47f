# 代码文档 - proxy.conf

## 文件作用
Nginx反向代理配置文件，定义多端口服务的路由规则和代理设置。

## 逐行代码解释

### 全局代理配置 (1-21行)
```nginx
# proxy 配置

# HTTP 1.1 support
proxy_http_version 1.1;                    # 使用HTTP/1.1协议
proxy_buffering off;                       # 关闭代理缓冲，实现实时传输
proxy_set_header Host $http_host;          # 传递原始Host头
proxy_set_header Upgrade $http_upgrade;    # 支持WebSocket升级
proxy_set_header Connection $proxy_connection;  # 传递连接类型
proxy_set_header X-Real-IP $remote_addr;   # 传递真实客户端IP
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # 传递代理链IP
proxy_set_header X-Forwarded-Proto $proxy_x_forwarded_proto;  # 传递协议类型
proxy_set_header X-Forwarded-Ssl $proxy_x_forwarded_ssl;      # 传递SSL状态
proxy_set_header X-Forwarded-Port $proxy_x_forwarded_port;    # 传递端口信息
proxy_set_header X-Original-URI $request_uri;  # 传递原始URI

# Mitigate httpoxy attack (see README for details)
proxy_set_header Proxy "";                 # 防止httpoxy攻击，清空Proxy头

proxy_read_timeout 600;                    # 代理读取超时：10分钟
client_max_body_size 100m;                 # 客户端最大请求体：100MB

root /var/www/html;                         # 静态文件根目录
```

### 主服务器配置 - 端口81 (24-62行)
```nginx
server {
    listen 81 default_server;              # 监听81端口，默认服务器
    server_name _;                          # 匹配所有域名

    # 静态文件服务
    location / {
        try_files $uri $uri/ /index.html;  # 尝试文件 -> 目录 -> index.html
        index  index.html index.htm;       # 默认索引文件
    }
    
    # 上传文件目录
    location /upload { 
        index index.html; 
    }
    
    # 飞书回调接口代理
    location /feishu {
        proxy_set_header Host manager.connect.ai;  # 设置目标Host
        proxy_pass http://manager.connect.ai;      # 代理到管理服务
    }
    
    # 钉钉回调接口代理
    location /dingding {
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    
    # 企业微信回调接口代理
    location /wework {
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    
    # API接口代理
    location /api {
        proxy_buffering off;                       # 关闭缓冲，实时响应
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    
    # 文件下载接口代理
    location /api/file {
        proxy_buffering off;
        proxy_set_header Host know.connect.ai;    # 代理到知识库服务
        proxy_pass http://know.connect.ai;
    }
    
    # 聊天WebSocket代理
    location /chat {
        proxy_set_header Host nchan.connect.ai;   # 代理到实时通信服务
        proxy_pass http://nchan.connect.ai;
        proxy_http_version 1.1;                   # WebSocket需要HTTP/1.1
        proxy_set_header Upgrade $http_upgrade;   # 支持协议升级
        proxy_set_header Connection "upgrade";    # 设置连接升级
    }
}
```

### 知识库服务器配置 - 端口82 (65-79行)
```nginx
server {
    listen 82 default_server;              # 监听82端口，默认服务器
    server_name _;                          # 匹配所有域名

    # 文件服务
    location /files {
        root /home/<USER>/DataChat-API;     # 文件根目录
        try_files $uri $uri/ /index.html;  # 文件查找规则
        index  index.html index.htm;       # 默认索引文件
    }

    # 其他请求代理到知识库服务
    location / {
        proxy_set_header Host know.connect.ai;
        proxy_pass http://know.connect.ai;
    }
}
```

## 服务架构说明

### 端口分配
- **端口81**: 主要Web服务和API代理
- **端口82**: 知识库服务和文件服务

### 服务映射
- **manager.connect.ai**: 管理服务（用户、应用、回调）
- **know.connect.ai**: 知识库服务（文档、聊天）
- **nchan.connect.ai**: 实时通信服务（WebSocket）

### 路由规则
```
端口81:
├── / → 静态文件服务
├── /upload → 上传文件目录
├── /feishu → 飞书回调 → manager服务
├── /dingding → 钉钉回调 → manager服务
├── /wework → 企微回调 → manager服务
├── /api → API接口 → manager服务
├── /api/file → 文件下载 → know服务
└── /chat → WebSocket聊天 → nchan服务

端口82:
├── /files → 本地文件服务
└── / → 知识库API → know服务
```

## 技术特点

### 反向代理
- **负载均衡**: 支持多个后端服务的请求分发
- **协议支持**: 同时支持HTTP和WebSocket协议
- **头部传递**: 完整传递客户端信息到后端服务

### 性能优化
- **缓冲控制**: 关闭代理缓冲实现实时响应
- **超时设置**: 10分钟读取超时适应长时间处理
- **文件上传**: 支持100MB大文件上传

### 安全防护
- **httpoxy防护**: 清空Proxy头防止攻击
- **IP传递**: 正确传递客户端真实IP
- **协议识别**: 支持HTTPS和SSL状态传递

### 微服务支持
- **服务分离**: 不同功能模块部署在不同服务
- **域名路由**: 基于Host头的服务路由
- **接口隔离**: API和静态资源分离部署
