'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M2 4.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2zM2 9a1 1 0 1 0 0-2a1 1 0 0 0 0 2zm1 3.5a1 1 0 1 1-2 0a1 1 0 0 1 2 0zM5.5 3a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1h-9zM5 8a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9A.5.5 0 0 1 5 8zm.5 4a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1h-9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'TextBulletListLtr16Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
