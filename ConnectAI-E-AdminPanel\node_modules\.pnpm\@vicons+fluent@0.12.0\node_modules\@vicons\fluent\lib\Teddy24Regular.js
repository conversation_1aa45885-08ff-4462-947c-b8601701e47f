'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M17.5 3.875a4.125 4.125 0 0 1 2.697 7.246c.278.749.428 1.548.428 2.379c0 2.39-1.243 4.519-3.18 5.914c-.283.204-.58.391-.89.562l-.037.02A9.439 9.439 0 0 1 12 21.125a9.44 9.44 0 0 1-4.518-1.129a8.746 8.746 0 0 1-.882-.55l-.045-.032c-1.937-1.395-3.18-3.524-3.18-5.914c0-.83.15-1.63.428-2.379a4.125 4.125 0 1 1 6.335-5.068A9.75 9.75 0 0 1 12 5.875c.64 0 1.262.061 1.862.178A4.124 4.124 0 0 1 17.5 3.875zm-3.977 12.522c-.11.204-.3.374-.533.497c-.276.146-.62.231-.99.231s-.714-.085-.99-.23a1.261 1.261 0 0 1-.532-.498a4.39 4.39 0 0 0-2.523 2.433A8.17 8.17 0 0 0 12 19.875c1.5 0 2.887-.386 4.045-1.045a4.377 4.377 0 0 0-2.523-2.433zM12 7.125c-4.116 0-7.375 2.9-7.375 6.375c0 1.799.87 3.44 2.282 4.608a5.626 5.626 0 0 1 10.186 0c1.412-1.168 2.282-2.81 2.282-4.608c0-3.475-3.26-6.375-7.375-6.375zm-5.5-2a2.875 2.875 0 0 0-2.127 4.81c.968-1.614 2.57-2.882 4.509-3.547A2.876 2.876 0 0 0 6.5 5.125zm11 0c-.975 0-1.858.49-2.382 1.263c1.94.665 3.541 1.933 4.509 3.547a2.875 2.875 0 0 0-2.127-4.81z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Teddy24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
