import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)   

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from tornado.options import options
from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage
from langchain.schema import AIMessage


logger = logging.getLogger(__name__)

MODELS = {
    'spark1.1-chat': '/v1.1',
    'spark1.5-chat': '/v1.1',  # 兼容旧的默认值
    'spark2.1-chat': '/v2.1',
    'spark3.1-chat': '/v3.1'
}


class XingHuoClient(object):

    def build_query(
        self,
        model='spark1.1-chat',
        api_base='',
        api_key='',
        app_id='',
        secret_key='',
        **kwargs
    ):
        if model not in MODELS.keys():
            raise Exception('not supported model {}'.format(model))
        api = MODELS[model]
        headers = {
            'Authorization': 'Bearer {}'.format(api_key),
            'Content-Type': 'application/json',
        }
        if app_id:
            headers['api-base'] = api_base
            headers['api-key'] = api_key
            headers['app-id'] = app_id
            headers['secret-key'] = secret_key
            from core.api_base import NewApiBase
            api_base = NewApiBase('星火认知大模型').url
        body = json.dumps(kwargs)
        return '{}{}/chat'.format(api_base, api), body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    def create(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class XingHuoChat(SimpleChatModel):
    """
    xinghuo https://www.xfyun.cn/doc/spark/Web.html#_1-%E6%8E%A5%E5%8F%A3%E8%AF%B4%E6%98%8E
    """
    client: Any  #: :meta private:
    model_name: str = "spark3.1-chat"
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    app_id: Optional[str] = None
    secret_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 3
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 0.9       # 默认0.9，范围 (0, 1.0]，不能为0
    top_k: float = 4               # 从k个候选中随机选择⼀个（⾮等概率）
    max_tokens: int = 2048         # V1.5取值为[1,4096]，V2.0取值为[1,8192]。默认为2048	模型回答的tokens的最大长度

    def _llm_type(self) -> str:
        return "xinghuo_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'app_id': self.app_id,
            'secret_key': self.secret_key,
            'api_base': self.api_base,
            'model': self.model_name,
            'temperature': self.temperature,
            'top_k': self.top_k,
            'max_tokens': self.max_tokens,
        }

        message_dicts = []
        for message in messages:
            if isinstance(message, AIMessage):
                message_dicts.append({'role': 'assistant', 'content': message.content})
            else:
                message_dicts.append({'role': 'user', 'content': message.content})

        self.client = XingHuoClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, messages=message_dicts, **params):
                # minimax stream模式和openai类似
                token = stream_resp['payload']['choices']['text'][0].get('content', '')
                response += token
                if run_manager:
                    run_manager.on_llm_new_token(token)
            return response
        else:
            full_response = completion_with_retry(self, messages=message_dicts, **params)
            return full_response['payload']['choices']['text'][0].get('content', '')


if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    async def main():
        from langchain.schema import HumanMessage
        from core.api_base import NewApiBase

        api_key = ''
        api_base = NewApiBase('星火认知大模型').url

        chat = XingHuoChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        # messages = [HumanMessage(content='你是谁')]
        messages = [HumanMessage(content='推荐成都旅游线路')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


