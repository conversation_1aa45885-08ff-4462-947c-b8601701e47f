import logging
from time import time
from typing import Any, List, Optional

import httpx
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import AIMessage, BaseMessage, ChatGeneration, ChatResult

logger = logging.getLogger(__name__)

class RMClient(object):
    def __init__(self,api_base,api_key=''):
        self.api_key=api_key
        self.api_base=api_base


    def stream(self, content, timeout=600):
        started = time()
        ended = started + timeout

        url = f"{self.api_base}/api/v2/remove-background"

        try:
            with httpx.stream(
                    method='POST',
                    url=url,
                    files={'image': content},
                    headers={'Authorization': f"Basic {self.api_key}"},
                    timeout=timeout,
                ) as create_result:

                # content_length = int(create_result.headers.get('Content-Length', None))
                # 受限于 Proxy，此处拿不到真实的 Content-Length
                content_length = 0
                if create_result.status_code != httpx.codes.OK:
                    logger.error('Error: {}'.format(create_result.text))
                    yield {'result': 'created:failed', 'length': content_length}
                    return

                for chunk in create_result.iter_bytes(chunk_size=1024):
                    # 判断超时：
                    if time() >= ended:
                        logger.error('Error: Timeout')
                        yield {'result': 'created:failed', 'length': content_length}
                        break
                    
                    # print(f"Get {len(chunk)} bytes data from server.")
                    yield {'result': 'created:success', 'data': chunk, 'length': content_length}

        except Exception as e:
            logger.error('Error: {}'.format(e))
            yield {'result': 'created:failed', 'length': 0}
            return


    def create(self, content, streaming=False, timeout=600, **kwargs) -> Any:
        # TODO: 添加检查result
        if streaming:
            return self.stream(content=content, timeout=timeout)
        else:
            data = b''
            for result in self.stream(content=content, timeout=timeout):

                if result['result'] == 'created:success':
                    data += result['data']
                else:
                    return None

            return data


class RMChat(SimpleChatModel):
    """
    Removebg
    """

    client: Any
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    streaming: bool = False

    def _llm_type(self) -> str:
        return "removebg_chat"
    
    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:

        params = {
            'api_key': self.api_key,
            'api_base': self.api_base,
            'streaming': self.streaming,
        }

        # 只获取一条 message
        message = messages.pop()
        # 这意味着图片的二进制数据要通过 message 传输
        params.update(**message.additional_kwargs)


        self.client = RMClient(
            api_base=self.api_base,
            api_key=self.api_key
        )
        response = {}
        
        if params['streaming']:

            # 往 response 里都需要写入哪些内容？
            
            response['data'] = b''
            current_length = 0

            for result in self.client.create(**params):
                # 这里既要计算token（进度）又要处理报错
                
                if result['result'] == 'created:success':
                    response['data'] += result['data']

                    # if run_manager:

                    #     current_length += len(result['data'])

                    #     # print(f"Current length: {current_length}, Total length: {result['length']}") 
                    #     # token = f"{int((current_length / result['length'])*100)}%"
                    #     token = f"{current_length // 1024}Kb"
                    #     # print(f"Current token: {token}")
                    #     # TODO: 需要对接这个函数的接口！！！
                    #     run_manager.on_llm_new_token(token)
                else:
                    # 直接抛出错误给on_llm_error
                    raise Exception("Error: {}".format(result['result']))
                    # response['result'] = 'created:failed'
                    # response['data'] = b''
                    # response['length'] = 0
                    # break

            # 向 response 写入数据
            response['result'] = 'created:success'
            response['length'] = len(response['data'])
        else:
            
            response['data'] = self.client.create(**params)
            response['length'] = len(response['data'])
            
            if response['data'] is None:
                raise Exception("Error: {}".format(response['result']))
                # response['result'] = 'created:failed'
                # response['length'] = 0

        # log 的格式是什么？
        logger.info(f"chat res: recive {response['length']} bits from pixian")
        
        message = AIMessage(content="", additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])

def early_test():

    import asyncio
    import os
    
    async def main():

        api_base = os.getenv("REMOVEBG_API_BASE")
        api_key = os.getenv("REMOVEBG_API_KEY")
        
        pwd = os.getcwd()
        pic_path = os.path.join(pwd, "server/core/test.jpg")
        result_path = pwd

        client = RMClient(
            api_base=api_base,
            api_key=api_key
        )

        def test_normal():
            
            streaming = False
            data = client.create(content=open(pic_path, 'rb'), api_base=api_base, api_key=api_key, streaming=streaming)
            
            with open(os.path.join(pwd, "server/core/normal.jpg"), 'wb') as f:
                f.write(data)

            print("Normal Done, content length: ", len(data))

        def test_stream():
            streaming = True
            length = 0
            with open(pic_path, 'rb') as f:
                with open(os.path.join(result_path, "server/core/stream.jpg"), 'wb') as f_2:    
                    for result in client.create(content=f, streaming=streaming):
                        f_2.write(result['data'])

                    # 由于无法从 Content-Length 获取真实的长度，所以这里拿不到总长度
                    length = result['length']

            print("Stream Done, content length: ", length)

        test_normal()
        test_stream()

    asyncio.run(main())

def message_test():

    import asyncio
    import os

    async def main():
        
        pwd = os.getcwd()
        pic_path = os.path.join(pwd, "server/core/IMG_20231007_003759.jpg")
        result_2_path = os.path.join(pwd, "server/core/message.jpg")

        from langchain.schema import HumanMessage


        def test_chat(streaming: bool) -> None:
            params = {
                'content': open(pic_path, 'rb'),
            }

            chat = RMChat(
                    api_base=os.getenv("REMOVEBG_API_BASE"),
                    api_key=os.getenv("REMOVEBG_API_KEY"),
                    streaming=streaming,
            )

            messages = [HumanMessage(content="", additional_kwargs=params)]
            response = chat(messages=messages)

            # 注意这里调用结果的方式和外部调用不同
            # 外部调用会再包一层
            additional_kwargs = response.additional_kwargs

            data = additional_kwargs['data']
            with open(result_2_path, 'wb') as f:
                f.write(data)

            print(f"Streaming: {streaming}; Test complete")

        test_chat(streaming=False)
        test_chat(streaming=True)

    asyncio.run(main())


if __name__ == "__main__":
    import dotenv
    dotenv.load_dotenv("server/core/.env")

    early_test()
    message_test()
