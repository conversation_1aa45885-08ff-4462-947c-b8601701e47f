#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证ConnectAI系统主流程
包括数据库验证、API测试、登录验证等
"""

import os
import sys
import json
import sqlite3
import requests
import time
import threading
import subprocess
from pathlib import Path

def load_admin_info():
    """加载管理员信息"""
    try:
        with open("./data/admin_info.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 无法加载管理员信息: {e}")
        return None

def verify_database():
    """验证数据库"""
    print("🔍 验证数据库...")
    
    db_path = "./data/connectai.db"
    if not Path(db_path).exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        tables = ['tenant', 'account', 'tenant_admin', 'application_category', 'resource_category']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  ✅ {table} 表: {count} 条记录")
        
        # 检查管理员账号
        cursor.execute("SELECT email, name FROM account WHERE email = ?", ('<EMAIL>',))
        admin = cursor.fetchone()
        if admin:
            print(f"  ✅ 管理员账号: {admin[0]} ({admin[1]})")
        else:
            print("  ❌ 管理员账号不存在")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

def start_datachat_api():
    """启动DataChat API"""
    print("🚀 启动DataChat API...")
    
    try:
        # 切换到DataChat-API目录并启动服务
        if os.name == 'nt':
            python_cmd = "DataChat-API/venv/Scripts/python.exe"
        else:
            python_cmd = "DataChat-API/venv/bin/python"
        
        # 启动DataChat API
        process = subprocess.Popen([
            python_cmd, "DataChat-API/simple_app.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务启动
        time.sleep(3)
        
        # 检查服务是否启动
        try:
            response = requests.get("http://localhost:5000/health", timeout=5)
            if response.status_code == 200:
                print("✅ DataChat API 启动成功")
                return process
            else:
                print("❌ DataChat API 启动失败")
                return None
        except:
            print("❌ DataChat API 无法访问")
            return None
            
    except Exception as e:
        print(f"❌ DataChat API 启动失败: {e}")
        return None

def test_datachat_api():
    """测试DataChat API"""
    print("🔍 测试DataChat API...")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("  ✅ 健康检查通过")
        else:
            print("  ❌ 健康检查失败")
            return False
        
        # 测试主页
        response = requests.get("http://localhost:5000/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 主页访问成功: {data.get('message', 'N/A')}")
        else:
            print("  ❌ 主页访问失败")
            return False
        
        # 测试搜索API
        search_data = {"query": "test"}
        response = requests.post("http://localhost:5000/api/search", 
                               json=search_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 搜索API测试通过: {data.get('total', 0)} 个结果")
        else:
            print("  ❌ 搜索API测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ DataChat API 测试失败: {e}")
        return False

def create_simple_manager_server():
    """创建简化版manager-server用于测试"""
    print("🔧 创建简化版manager-server...")
    
    server_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Manager Server用于测试
"""

import json
import sqlite3
import hashlib
from flask import Flask, request, jsonify, session
from flask_cors import CORS

app = Flask(__name__)
CORS(app)
app.secret_key = 'connectai-test-secret'

def hash_password(password):
    return hashlib.md5(password.encode('utf-8')).hexdigest()

def get_db():
    return sqlite3.connect('./data/connectai.db')

@app.route('/')
def index():
    return jsonify({
        "message": "ConnectAI Manager Server",
        "version": "1.0.0",
        "status": "running"
    })

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})

@app.route('/api/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        if not email or not password:
            return jsonify({"error": "邮箱和密码不能为空"}), 400
        
        # 验证用户
        conn = get_db()
        cursor = conn.cursor()
        
        hashed_password = hash_password(password)
        cursor.execute("""
            SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.email = ? AND a.passwd = ? AND a.status = 0
        """, (email, hashed_password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            session['user_id'] = user[0]
            session['user_email'] = user[2]
            return jsonify({
                "success": True,
                "message": "登录成功",
                "user": {
                    "id": user[0],
                    "name": user[1],
                    "email": user[2],
                    "tenant_id": user[3],
                    "tenant_name": user[4]
                }
            })
        else:
            return jsonify({"error": "邮箱或密码错误"}), 401
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/user/info', methods=['GET'])
def user_info():
    """获取用户信息"""
    if 'user_id' not in session:
        return jsonify({"error": "未登录"}), 401
    
    try:
        conn = get_db()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name, t.apikey
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.id = ?
        """, (session['user_id'],))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return jsonify({
                "user": {
                    "id": user[0],
                    "name": user[1],
                    "email": user[2],
                    "tenant_id": user[3],
                    "tenant_name": user[4],
                    "tenant_apikey": user[5]
                }
            })
        else:
            return jsonify({"error": "用户不存在"}), 404
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/applications', methods=['GET'])
def get_applications():
    """获取应用列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, a.name, a.title, a.description, c.title as category
            FROM application a
            LEFT JOIN application_category c ON a.category_id = c.id
            WHERE a.status = 0
            ORDER BY a.sorted, a.created_at
        """)
        
        apps = cursor.fetchall()
        conn.close()
        
        applications = []
        for app in apps:
            applications.append({
                "id": app[0],
                "name": app[1],
                "title": app[2],
                "description": app[3],
                "category": app[4] or "未分类"
            })
        
        return jsonify({"applications": applications})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/logout', methods=['POST'])
def logout():
    """用户登出"""
    session.clear()
    return jsonify({"message": "登出成功"})

if __name__ == '__main__':
    print("🚀 启动Manager Server测试版...")
    print("服务将在 http://localhost:3000 运行")
    app.run(host='0.0.0.0', port=3000, debug=True)
'''
    
    with open("simple_manager_server.py", "w", encoding="utf-8") as f:
        f.write(server_content)
    
    print("✅ 简化版manager-server创建完成")

def test_manager_server():
    """测试Manager Server"""
    print("🔍 测试Manager Server...")
    
    try:
        # 测试主页
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 主页访问成功: {data.get('message', 'N/A')}")
        else:
            print("  ❌ 主页访问失败")
            return False
        
        # 测试登录API
        admin_info = load_admin_info()
        if not admin_info:
            print("  ❌ 无法加载管理员信息")
            return False
        
        login_data = {
            "email": admin_info['admin_email'],
            "password": admin_info['admin_password']
        }
        
        response = requests.post("http://localhost:3000/api/login", 
                               json=login_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"  ✅ 管理员登录成功: {data['user']['name']}")
                
                # 保存session用于后续测试
                session_cookies = response.cookies
                
                # 测试获取用户信息
                response = requests.get("http://localhost:3000/api/user/info", 
                                      cookies=session_cookies, timeout=5)
                if response.status_code == 200:
                    user_data = response.json()
                    print(f"  ✅ 用户信息获取成功: {user_data['user']['tenant_name']}")
                else:
                    print("  ❌ 用户信息获取失败")
                    return False
                
                # 测试应用列表
                response = requests.get("http://localhost:3000/api/applications", 
                                      cookies=session_cookies, timeout=5)
                if response.status_code == 200:
                    apps_data = response.json()
                    print(f"  ✅ 应用列表获取成功: {len(apps_data['applications'])} 个应用")
                else:
                    print("  ❌ 应用列表获取失败")
                    return False
                
            else:
                print(f"  ❌ 管理员登录失败: {data.get('message', 'Unknown error')}")
                return False
        else:
            print("  ❌ 登录API请求失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Manager Server 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 ConnectAI 系统验证")
    print("=" * 50)
    
    # 1. 验证数据库
    if not verify_database():
        print("❌ 数据库验证失败，请先运行 init_database_with_admin.py")
        return False
    
    # 2. 启动DataChat API
    datachat_process = start_datachat_api()
    if not datachat_process:
        print("❌ DataChat API 启动失败")
        return False
    
    # 3. 测试DataChat API
    if not test_datachat_api():
        print("❌ DataChat API 测试失败")
        return False
    
    # 4. 创建简化版Manager Server
    create_simple_manager_server()
    
    print("\\n🚀 启动Manager Server进行测试...")
    print("请在另一个终端运行: python simple_manager_server.py")
    print("然后按回车继续测试...")
    input()
    
    # 5. 测试Manager Server
    if not test_manager_server():
        print("❌ Manager Server 测试失败")
        return False
    
    print("\\n🎉 所有测试通过！")
    print("\\n📊 验证结果:")
    print("✅ 数据库初始化正常")
    print("✅ DataChat API 运行正常")
    print("✅ Manager Server 运行正常")
    print("✅ 管理员登录验证通过")
    print("✅ 主要API接口正常")
    
    print("\\n🔗 访问地址:")
    print("Manager Server: http://localhost:3000")
    print("DataChat API: http://localhost:5000")
    
    admin_info = load_admin_info()
    if admin_info:
        print("\\n👤 管理员账号:")
        print(f"邮箱: {admin_info['admin_email']}")
        print(f"密码: {admin_info['admin_password']}")
    
    return True

if __name__ == "__main__":
    main()
