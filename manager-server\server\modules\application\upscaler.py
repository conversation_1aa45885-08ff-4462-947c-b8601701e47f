class UpScalerTool(CTool):
    name: str = 'UpScaler'
    description: str = 'image upscaling tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class UpScalerCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(
                                FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(
                                _('🤖️：已传输，%(progress)s%% ...', progress=token)))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                # logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                img_bin = additional_kwargs['image_bin']
                width, height = additional_kwargs['target_width'], additional_kwargs['target_height']
                del additional_kwargs['image_bin']  # 删除data，防止存入数据库的内容过大
                if platform == 'feishu':
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    time.sleep(1)
                    img_key = syncify(client.upload_image_binary)(img_bin)
                    additional_kwargs.update({'img_key': img_key})  # 用于存入数据库
                    contents = [
                        FeishuMessageImage(img_key=img_key, alt='图片'),
                    ]
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            FeishuMessageDiv('size {} x {}'.format(width, height), tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                            header=FeishuMessageCardHeader(content='UpScaler Bot 🎉', template='blue'),
                        )
                    )

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(
                                '🤖️：' + str(error), tag='lark_md'),
                        )
                    )

        model.callbacks = [UpScalerCallbackHandler()]
        chat = ClipdropChat(**model)
        if platform == 'feishu':
            rate = session.extra.get("rate", 2)
            data_extra = data.get('extra', {})
            img_key = data_extra.get('input_kwargs', {}).get('img_key', '')
            message_id = data_extra.get('message_id')
            # logging.info("data extra %r %r %r", data_extra, img_key, message_id)
            img_bin = syncify(client.get_message_resource)(message_id, img_key)
            messages = [HumanMessage(content='', additional_kwargs=dict(content=img_bin, rate=rate))]
            return chat.invoke(messages)


class UpScalerShortcutTool(CTool):
    name: str = 'UpScaler_shortcut'
    description: str = 'upscaler shortcut tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class UpScalerCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ShortcutCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(
                                FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(
                                _('🤖️：已传输，%(progress)s%% ...', progress=token)))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                # logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                img_bin = additional_kwargs['image_bin']
                width, height = additional_kwargs['target_width'], additional_kwargs['target_height']
                del additional_kwargs['image_bin']  # 删除data，防止存入数据库的内容过大

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    img_key = syncify(client.upload_image_binary)(img_bin)
                    additional_kwargs.update({'img_key': img_key})  # 用于存入数据库
                    contents = [
                        FeishuMessageImage(img_key=img_key, alt='图片'),
                    ]
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            FeishuMessageDiv('size {} x {}'.format(width, height), tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                            header=FeishuMessageCardHeader(content='UpScaler Bot 🎉', template='blue'),
                        )
                    )
                else:
                    pass

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(
                                '🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [UpScalerCallbackHandler()]
        chat = ClipdropChat(**model)
        if platform == 'feishu':
            rate = int(data.extra.extra.get('rate', 0)) or session.extra.get("rate", 2)
            data_extra = data.get('extra', {})
            img_key = data_extra.get('input_kwargs', {}).get('img_key', '')
            if data_extra.get('message_type', '') == 'interactive':
                message_id = data_extra.get('message_id')
                img_bin = syncify(httpx.AsyncClient().get)(
                    f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?image_key={img_key}&reply_message_id={message_id}').content
            else:
                img_bin = syncify(client.get_message_resource)('', img_key)
            messages = [HumanMessage(content='', additional_kwargs=dict(
                content=img_bin, rate=rate))]
            return chat.invoke(messages)


class FeishuCommand(CommandTool):
    next_tool_name: str = 'UpScaler'
    name: str = 'UpScaler_feishu_command'
    description: str = 'UpScaler feishu command'
    rate_options = [2, 4, 8]
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '上传图片，BOT回复高清放大图片',
        '若您配置了该应用的快捷消息，可在图片消息的快捷消息中快速去除背景'
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def rate_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(str(value), f'{value}x') for value in self.rate_options],
            placeholder=_('选择放大倍数'),
            initial_option=str(session.extra.get('rate', self.rate_options[0])),
            value={'command': 'rate'},
            confirm=FeishuMessageConfirm(
                title=_('您确定选择此放大倍数吗？'),
                text=_('一键获取高清放大图'),
            )
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是AI图片高清放大助手，支持对图片进行2，4，8倍按宽高比例放大**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('**🧹UpScaler 使用**\n选择放大倍数，聊天框中输入图片，BOT将回复高清放大图片'),
                    tag='lark_md',
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **放大倍数选择**\n'),
                    tag='lark_md',
                    extra=self.rate_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('**👋 需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if 'shortcut_action' in data.extra.extra:
            self.next_tool_name = data.extra.extra.get('shortcut_action', self.next_tool_name)
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif not input and action:
            if action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        if data.extra.message_type in ['image', 'post', 'interactive']:
            return data.extra.message_type,
        return 'note',

    def on_post(self):
        # 富文本消息
        platform_content = data.extra.extra.platform_content
        input_img_keys = []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg.get('tag', '') == 'img':
                    input_img_keys.append(msg['image_key'])
        input_kwargs = {}
        if len(input_img_keys) == 1:
            input_kwargs = {'img_key': input_img_keys[0]}
        if not input_kwargs:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        data['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_image(self):
        # 图片消息
        platform_content = data.extra.extra.platform_content
        input_kwargs = {'img_key': platform_content['image_key']}
        data['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_interactive(self):
        # 卡片消息
        if 'imageKey' in data.extra.extra:
            input_kwargs = {'img_key': data.extra.extra.imageKey}
            data['extra']['input_kwargs'] = input_kwargs
            return self.next_tool_name
        return

    def on_rate(self, rate=None):
        session.set_extra('rate', int(rate))
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已选择放大倍数') + f'：{rate}x', tag="lark_md"),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送图片，不支持自然语言（除帮助外）、文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送图片！')
        return send_note(text, title)


class UpScalerAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'AI绘画'
        name: str = 'UpScaler'
        title: str = '放大高清图'
        title_en: str = 'image upscaling helper'
        description: str = '🚀 仅需几秒，即可实现图像的高级提升、噪点清除和质量增强！'
        description_en: str = '🚀 Upscale, denoise and enhance your images in seconds'
        problem: str = 'AIGC 的图片质量不符合生产要求'
        problem_en: str = 'Picture quality from AIGC does not meet the production requirements'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/B6and04afo7c76xhwRicBja7nfb'
        manual_en: str = 'https://connect-ai.feishu.cn/docx/ZMtHdyVrro4tymxN5GNclemSnKc'
        icon: str = 'https://pic1.forkway.cn/cdn/202309190942470.png'
        logo: str = 'https://pic1.forkway.cn/cdn/202309190942470.png?imageMogr2/thumbnail/720x'
        sorted: int = 109
        support_resource: List[object] = [dict(
            category=ModelCategory.Image.value,
            scene=ModelCategory.Image.value,
            title='图片处理',
            tip='',
            required=True,
            resource=['Clipdrop']
        )]
        support_bots: List[str] = ['feishu']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnBb6w74sJOwI9zI04grgQsh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/P0njwnJTri815ekcZ9gcoZJpnog'
        action_template = """
${detail.content.messages.length > 0
  ? detail.content.messages.map((item, index) => html`
    ${parseImageFromMessage(item).map(image => html`
      <div style="padding:20px">
      <div style="font-size: 13px;color:black;background:white;border-radius:10px">
        <div style="padding:10px;line-height:2em">处理图片</div>
        <div style="display: flex;">
          <quark-image style="width: 100%;" src="${image.url}" radius="10px" alt="loading"></quark-image>
        </div>
      </div>
      <form method="post">
        <input type="hidden" name="messageId" value="${item.openMessageId}">
        <input type="hidden" name="action" value="UpScaler_shortcut">
        <input type="hidden" name="user" value="${JSON.stringify(user)}">
        <input type="hidden" name="message" value="${JSON.stringify(item)}">
        <input type="hidden" name="imageKey" value="${image.image_key}">
        <div style="font-size: 13px;display:flex;align-items:center;justify-content: space-between;gap:10px;margin-bottom:20px;margin-top:20px;background-color:#fff;padding:30px;border-radius:10px">
          <div>
            <input type="radio" id="rate2" name="rate" value="2" checked style="margin-top: 5px" />
            <label for="2">2x</label>
          </div>
          <div>
            <input type="radio" id="rate4" name="rate" value="4" style="margin-top: 5px" />
            <label for="4">4x</label>
          </div>
          <div>
            <input type="radio" id="rate8" name="rate" value="8" style="margin-top: 5px" />
            <label for="8">8x</label>
          </div>
        </div>
        <quark-button type="primary" size="big" style="position: relative;width: 100%;display: block;margin: 10px auto;">
          {{_('高清放大', lang=lang)}}
          <button type="submit" style="position: absolute;width: 100%;height:100;opacity:0;cursor: pointer;">透明按钮触发表单提交事件</button>
        </quark-button>
      </form>
    </div>
    `)}
    `)
  : detail.errMsg || '无图片'
}
        """

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(
                    last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='UpScaler_feishu_command', tool_input=kwargs, log="")
        return AgentAction(tool='UpScaler', tool_input=kwargs, log="")
