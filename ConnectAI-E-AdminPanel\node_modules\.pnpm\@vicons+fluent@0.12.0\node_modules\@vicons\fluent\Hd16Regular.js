'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4.5 5a.5.5 0 0 1 .5.5V8h1.5V5.5a.5.5 0 0 1 1 0v5a.5.5 0 0 1-1 0V9H5v1.5a.5.5 0 0 1-1 0v-5a.5.5 0 0 1 .5-.5zM9 5a.5.5 0 0 0-.5.5v5a.5.5 0 0 0 .5.5h.417A2.583 2.583 0 0 0 12 8.417v-.834A2.583 2.583 0 0 0 9.417 5H9zm2 3.417c0 .846-.664 1.538-1.5 1.58V6.003c.836.043 1.5.735 1.5 1.581v.834zM2 4.5A2.5 2.5 0 0 1 4.5 2h7A2.5 2.5 0 0 1 14 4.5v7a2.5 2.5 0 0 1-2.5 2.5h-7A2.5 2.5 0 0 1 2 11.5v-7zM4.5 3A1.5 1.5 0 0 0 3 4.5v7A1.5 1.5 0 0 0 4.5 13h7a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 11.5 3h-7z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Hd16Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
