<template>
  <div
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <ResourcesForm
      ref="formRef"
      v-model:data="data.resource_ids"
      :resources="resources"
      :loading="loading"
      @update:value="handleResourcesChange"
    />
    <div class="mb-6">
      <label for="settings-timezone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
        t('message.my.fxcgl')
      }}</label>
      <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
      <n-select
        v-else
        v-model:value="data.sensitive_id"
        multiple
        size="large"
        filterable
        class="block w-full"
        :placeholder="t('message.my.qnxzfxc')"
        clearable
        :options="sens.map((item) => ({ value: item.id, label: item.category }))"
      />
    </div>
    <div v-if="!isDataset" class="mb-4">
      <label for="settings-timezone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
        t('message.my.cjcgl')
      }}</label>
      <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
      <n-select
        v-else
        v-model:value="data.prompt_id"
        filterable
        multiple
        size="large"
        class="block w-full"
        :placeholder="t('message.my.cjcgl')"
        clearable
        :options="prompt.map((item) => ({ value: item.id, label: item.title }))"
      />
    </div>
    <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
    <button
      v-else
      type="button"
      class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      @click="handleSave"
    >
      <icon-akar-icons-save class="mr-2" />
      {{ t('message.my.bczy') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { toPairs } from 'lodash-es';
import { fetchSensList } from '@/service/api/log';
import { fetchPromptList } from '@/service/api/prompt';
import { updateAppSetting } from '@/service/api/messenger';
import { t } from '@/locales';
import { getModelPermission } from '@/views/bot/info/utils';
import ResourcesForm from '@/views/bot/info/component/resourcesForm.vue';

const message = useMessage();

const route = useRoute();

const emit = defineEmits(['change', 'update:data']);

const sens = ref<ApiSensitive.SensitiveTypes[]>([]);

const prompt = ref<ApiPrompt.PromptTypes[]>([]);

const formRef = ref();

const props = defineProps<{
  data: ApiApp.APPSetting;
  loading: boolean;
  isDataset: boolean;
  resources: ApiApp.AppResources[];
}>();

const data = useVModel(props, 'data', emit);

async function handleSave() {
  await formRef.value?.validate();
  await updateAppSetting({ id: route.query.id as string, data: data.value });
  message.success(t('message.msg.bccg'));
}

function handleResourcesChange(cur_resource_ids: Record<string, string>) {
  const resource_ids = {
    ...data.value.resource_ids,
    ...cur_resource_ids
  };
  const user_permission = toPairs(resource_ids)
    .map(([scene, resource_id]) => {
      const { models, name, description } =
        props.resources.find((r) => r.scene === scene)?.resource?.find((r) => r.id === resource_id) || {};
      return models ? getModelPermission(models, resource_id, 'allow_users', 'deny_users', name, description) : [];
    })
    .flat();

  const group_permission = toPairs(resource_ids)
    .map(([scene, resource_id]) => {
      const { models, name, description } =
        props.resources.find((r) => r.scene === scene)?.resource?.find((r) => r.id === resource_id) || {};
      return models ? getModelPermission(models, resource_id, 'allow_groups', 'deny_groups', name, description) : [];
    })
    .flat();
  data.value.user_permission = user_permission as ApiApp.APPSetting['user_permission'];
  data.value.group_permission = group_permission as ApiApp.APPSetting['group_permission'];
}

onMounted(async () => {
  const {
    data: { data: sensData }
  } = await fetchSensList({ page: 1, size: 99999 });
  sens.value = sensData;
  const {
    data: { data: promptData }
  } = await fetchPromptList({ page: 1, size: 99999 });
  prompt.value = promptData;
});
</script>
