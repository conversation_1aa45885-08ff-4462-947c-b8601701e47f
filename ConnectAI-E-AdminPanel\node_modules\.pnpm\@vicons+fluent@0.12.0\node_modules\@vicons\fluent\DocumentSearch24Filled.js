'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M13 8V2H7a2 2 0 0 0-2 2v5.303a6 6 0 0 1 8.207 8.18l3.53 3.53c.278.277.443.626.495.987H19a2 2 0 0 0 2-2V10h-6a2 2 0 0 1-2-2zm1.97 14.78a.75.75 0 1 0 1.06-1.06l-4.112-4.113A4.978 4.978 0 0 0 13 14.5a4.984 4.984 0 0 0-1.43-3.5A4.985 4.985 0 0 0 8 9.5a4.978 4.978 0 0 0-3 1a5 5 0 0 0 5.82 8.13l4.15 4.15zM8 11a3.5 3.5 0 1 1 0 7a3.5 3.5 0 0 1 0-7zm6.5-3V2.5l6 6H15a.5.5 0 0 1-.5-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DocumentSearch24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
