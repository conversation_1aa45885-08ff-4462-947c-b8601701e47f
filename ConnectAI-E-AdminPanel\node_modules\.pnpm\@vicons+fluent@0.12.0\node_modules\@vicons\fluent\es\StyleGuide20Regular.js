import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.068 5.993A2.005 2.005 0 0 1 6.484 3.54l5.46-1.47a1.995 1.995 0 0 1 2.447 1.414l2.54 9.522a2.005 2.005 0 0 1-1.415 2.452l-5.46 1.47a1.995 1.995 0 0 1-2.448-1.413l-2.54-9.522zM6.74 4.507c-.533.143-.85.692-.707 1.226l2.54 9.522c.142.534.69.85 1.223.707l5.461-1.47c.533-.143.85-.692.708-1.226l-2.54-9.523a.997.997 0 0 0-1.224-.706l-5.46 1.47zM4.001 8.53l-1.33 4.966a2 2 0 0 0 1.413 2.45l.076.02A2.997 2.997 0 0 1 4 15v-.187a1 1 0 0 1-.364-1.058l.365-1.362V8.53zM5 9.617V15a2 2 0 0 0 2 2h.106a2.495 2.495 0 0 1-.334-.742l-.082-.307a1 1 0 0 1-.69-.95v-1.635L5 9.617zM8.25 7a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'StyleGuide20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
