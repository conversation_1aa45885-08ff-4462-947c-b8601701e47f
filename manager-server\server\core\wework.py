# https://developer.work.weixin.qq.com/document/path/90372
from typing import Dict, Any
from uuid import uuid4

class Base(Dict):

    def __getattr__(self, name: str) -> Any:
        try:
            return self[name]
        except KeyError:
            raise AttributeError(name)

    def __setattr__(self, name: str, value: Any) -> None:
        self[name] = value


class WXCardSource(Dict):
    def __init__(self, desc='', icon_url='https://connect-ai-e.com/logo.svg', desc_color=1):
        super().__init__(desc=desc, icon_url=icon_url, desc_color=desc_color)


class WXCardButton(Dict):
    def __init__(self, text='', key='', type=0, style=None, url=None):
        data = dict(text=text)
        if type is not None:
            data['type'] = type
        if style:
            data['style'] = style
        if key:
            data['key'] = key
        if url:
            data['url'] = url
        super().__init__(**data)


class WXCardSubmitButton(Dict):
    def __init__(self, text='', key=''):
        super().__init__(text=text, key=key)


class WXCardCheckboxOption(Dict):
    def __init__(self, id='', text='', is_checked=False):
        super().__init__(id=id, text=text, is_checked=is_checked)


class WXCardSelectOption(Dict):
    def __init__(self, id='', text=''):
        super().__init__(id=id, text=text)


class WXCardButtonSelection(Dict):
    def __init__(self, *options, question_key='', option_list=list(), title='', selected_id=None):
        data = dict(
            option_list=option_list + list(options),
            question_key=question_key,
        )
        if title:
            data['title'] = title
        if selected_id:
            data['selected_id'] = selected_id
        super().__init__(**data)


class ButtonInteractionCardMessage(Dict):
    def __init__(self, *btns, button_list=list(), source=None, task_id=None, button_selection=None, main_title=None, **kwargs):
        # 多按钮卡片（最多2排6个）
        # disable: action_menu, quote_area, horizontal_content_list, sub_title_text
        # card_action 整体跳转
        template_card = dict(
            card_type='button_interaction',
            task_id=task_id or str(uuid4()),  # 必须
            button_list=button_list + list(btns),
        )
        if source:
            if isinstance(source, str):
                source = WXCardSource(source)
            if isinstance(source, dict):
                template_card['source'] = source
        if button_selection:
            template_card['button_selection'] = button_selection

        if main_title:
            if isinstance(main_title, str):
                main_title = WXCardMainTitle(main_title)
            if isinstance(main_title, dict):
                template_card['main_title'] = main_title

        super().__init__(
            msgtype='template_card',
            template_card=template_card,
        )

class WXCardMainTitle(Dict):
    def __init__(self, title='', desc=None):
        data = dict(title=title)
        if desc:
            data['desc'] = desc
        super().__init__(**data)


class WXCardEmphasisContent(WXCardMainTitle): pass


class MultipleInteractionCardMessage(Dict):
    def __init__(self, *select, select_list=list(), submit_button=None, main_title='', task_id='', source=None, **kwargs):
        # 多下拉选择卡片（最多3个下拉选择）
        # disable: action_menu, quote_area, horizontal_content_list, sub_title_text
        # card_action 整体跳转
        template_card = dict(
            card_type='multiple_interaction',
            task_id=task_id or str(uuid4()),  # 必须
            select_list=select_list + list(select),
        )
        if source:
            if isinstance(source, str):
                source = WXCardSource(source)
            if isinstance(source, dict):
                template_card['source'] = source
        if submit_button:
            if isinstance(submit_button, str):
                submit_button = WXCardSubmitButton(submit_button, submit_button)
            if isinstance(submit_button, dict):
                template_card['submit_button'] = submit_button

        if main_title:
            if isinstance(main_title, str):
                main_title = WXCardMainTitle(main_title)
            if isinstance(main_title, dict):
                template_card['main_title'] = main_title

        super().__init__(
            msgtype='template_card',
            template_card=template_card,
        )


class WXTextCard(Dict):
    def __init__(self, title='', description='', url='https://www.connectai-e.com', btntxt=''):
        data = dict(title=title, description=description, url=url)
        if btntxt:
            data['btntxt'] = btntxt
        super().__init__(dict(
            msgtype="textcard",
            textcard=data,
        ))


class TextNoticeCardMessage(Dict):
    def __init__(self, *jump, jump_list=list(), source=None, emphasis_content=None):
        # 文本通知卡片
        # 主要通过main_title和emphasis_content（关键数据）描述信息
        template_card = dict(
            card_type='text_notice',
            task_id=task_id or str(uuid4()),  # 必须
            jump_list=jump_list + list(jump),
        )
        if source:
            if isinstance(source, str):
                source = WXCardSource(source)
            if isinstance(source, dict):
                template_card['source'] = source

        if main_title:
            if isinstance(main_title, str):
                main_title = WXCardMainTitle(main_title)
            if isinstance(main_title, dict):
                template_card['main_title'] = main_title

        if emphasis_content:
            if isinstance(emphasis_content, str):
                main_title = WXCardEmphasisContent(emphasis_content)
            if isinstance(emphasis_content, dict):
                template_card['emphasis_content'] = emphasis_content

        super().__init__(
            msgtype='template_card',
            template_card=template_card,
        )


class VoteInteractionCardMessage(Dict):

    def __init__(self, *options, option_list=list(), question_key='', mode=0, source=None, emphasis_content=None):
        # 投票选择卡片，mode=0/1决定是单选还是多选
        template_card = dict(
            card_type='vote_interaction',
            task_id=task_id or str(uuid4()),  # 必须
            checkbox=dict(
                question_key=question_key,
                option_list=option_list + list(options),
                mode=mode,
            )
        )
        if source:
            if isinstance(source, str):
                source = WXCardSource(source)
            if isinstance(source, dict):
                template_card['source'] = source

        if main_title:
            if isinstance(main_title, str):
                main_title = WXCardMainTitle(main_title)
            if isinstance(main_title, dict):
                template_card['main_title'] = main_title

        if submit_button:
            if isinstance(submit_button, str):
                submit_button = WXCardSubmitButton(submit_button, submit_button)
            if isinstance(submit_button, dict):
                template_card['submit_button'] = submit_button

        super().__init__(
            msgtype='template_card',
            template_card=template_card,
        )


class WXImage(Dict):

    def __init__(self, media_id):
        super().__init__(
            msgtype="image",
            image=dict(media_id=media_id)
        )


class WEWorkCallbackError(Exception):
    """机器人回调异常"""
    pass


if __name__ == "__main__":
    button_interaction = ButtonInteractionCardMessage(
        WXCardButton('按钮1', 'btn1'),
        WXCardButton('按钮2', 'btn2'),
        button_selection=WXCardButtonSelection(
            WXCardSelectOption('btn_selection_id1', text='100分'),
            WXCardSelectOption('btn_selection_id2', text='101分'),
            question_key='question_key1',
            title='企业微信评分'
        )
    )
    print(button_interaction)

    multiple_interaction = MultipleInteractionCardMessage(
        WXCardButtonSelection(
            WXCardSelectOption('btn_selection_id1', text='选项1'),
            WXCardSelectOption('btn_selection_id2', text='选项2'),
            question_key='question_key1',
            title='选择器标签1'
        ),
        WXCardButtonSelection(
            WXCardSelectOption('btn_selection_id1', text='选项1'),
            WXCardSelectOption('btn_selection_id2', text='选项2'),
            question_key='question_key2',
            title='选择器标签2'
        ),
        main_title=WXCardMainTitle(
            "欢迎使用企业微信",
            desc="您的好友正在邀请您加入企业微信"
        ),
        submit_button=WXCardSubmitButton('提交', 'key')
    )
    print(multiple_interaction)

