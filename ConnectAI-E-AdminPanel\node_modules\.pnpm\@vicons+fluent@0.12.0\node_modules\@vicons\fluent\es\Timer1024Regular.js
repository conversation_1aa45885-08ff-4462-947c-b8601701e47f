import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M19.5 15a2.5 2.5 0 0 1 2.493 2.335l.006.164v2.004a2.5 2.5 0 0 1-4.994.165L17 19.503V17.5a2.5 2.5 0 0 1 2.5-2.5zM12 4.998a8.5 8.5 0 0 1 8.476 9.142a3.442 3.442 0 0 0-1.497-.102a7 7 0 1 0-5.476 6.298v.91l.011.185v.432A8.5 8.5 0 1 1 12 4.998zm3.996 10.651l.007.1v5.497a.75.75 0 0 1-1.494.102l-.007-.102v-4.454l-.512.173a.75.75 0 0 1-.911-.378l-.039-.095a.75.75 0 0 1 .378-.91l.094-.04l1.502-.503a.75.75 0 0 1 .982.61zm3.503.85a1 1 0 0 0-.992.883l-.007.117v2.004a1 1 0 0 0 1.992.117l.006-.117V17.5a1 1 0 0 0-.999-1zM12 8a.75.75 0 0 1 .743.649l.007.102v4.5a.75.75 0 0 1-1.493.101l-.007-.101v-4.5A.75.75 0 0 1 12 8zm7.162-2.878l.08.061l1.132.986a.75.75 0 0 1-.905 1.193l-.081-.062l-1.13-.986a.75.75 0 0 1 .904-1.192zM14.25 2.486a.75.75 0 0 1 .102 1.493l-.102.007h-4.5a.75.75 0 0 1-.102-1.493l.102-.007h4.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer1024Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
