'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M11.25 13.75l-.085-.073a.75.75 0 0 0-.976.073l-.69.69V7.75l-.007-.102A.75.75 0 0 0 8.75 7l-.102.006A.75.75 0 0 0 8 7.75v6.691l-.69-.69l-.084-.074a.75.75 0 0 0-.976 1.133l1.969 1.971l.084.073a.75.75 0 0 0 .977-.073l1.97-1.97l.073-.085a.75.75 0 0 0-.072-.976zM1.999 12c0 5.523 4.477 10 10 10s10-4.477 10-10s-4.477-10-10-10s-10 4.477-10 10zm18.5 0a8.5 8.5 0 1 1-17 0a8.5 8.5 0 0 1 17 0zm-2.75 1.75l-.084-.073a.75.75 0 0 0-.976.073l-.69.69V7.75l-.007-.102a.75.75 0 0 0-.743-.649l-.102.007a.75.75 0 0 0-.648.743v6.692l-.69-.69l-.083-.074a.75.75 0 0 0-.977 1.133l1.969 1.971l.084.073a.75.75 0 0 0 .977-.073l1.97-1.97l.073-.085a.75.75 0 0 0-.072-.976z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ArrowCircleDownDouble24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
