# 文档命名规则变更说明

## 变更内容

### 旧命名规则
- 格式：`CODE_DOC_<源文件名>.md`
- 示例：`CODE_DOC_server.py.md`、`CODE_DOC_index.ts.md`

### 新命名规则
- 格式：`<源文件名>_CODE_DOC.md`
- 示例：`server.py_CODE_DOC.md`、`index.ts_CODE_DOC.md`

## 变更原因

1. **更好的文件排序**：新规则使得文档文件在目录中紧邻对应的源文件
2. **更直观的关联**：源文件名在前，更容易识别对应关系
3. **符合常见约定**：类似于 `.bak`、`.tmp` 等后缀的命名习惯

## 已完成的变更

### 文件重命名
- 已将所有现有的 `CODE_DOC_*.md` 文件重命名为 `*_CODE_DOC.md` 格式
- 总计重命名了 42 个文档文件

### .gitignore 更新
已更新所有项目的 `.gitignore` 文件：
- 根目录 `.gitignore`
- `manager-server/.gitignore`
- `ConnectAI-E-AdminPanel/.gitignore`
- `DataChat-API/.gitignore`
- `ConnectAI-Helper/.gitignore`

将规则从 `CODE_DOC_*.md` 改为 `*_CODE_DOC.md`

## 新增文档

### manager-server 项目新增
- `server/core/downloader.py_CODE_DOC.md` - 高性能异步文件下载器
- `server/core/know.py_CODE_DOC.md` - 知识库服务客户端
- `server/core/feishu.py_CODE_DOC.md` - 飞书API客户端
- `server/modules/application/model.py_CODE_DOC.md` - 应用管理模型

### DataChat-API 项目新增
- `server/server.py_CODE_DOC.md` - Flask应用启动文件
- `server/tasks.py_CODE_DOC.md` - Celery任务配置
- `server/sse.py_CODE_DOC.md` - Server-Sent Events实现

## 最新统计

### 文档总数
- **总计**: 49个详细文档文件
- **Python文件**: 25个文档
- **TypeScript文件**: 12个文档
- **Vue文件**: 1个文档
- **配置文件**: 6个文档
- **构建文件**: 2个文档
- **说明文档**: 3个文档

### 项目覆盖情况
- **manager-server**: 21个文档（核心后端服务）
- **ConnectAI-E-AdminPanel**: 6个文档（前端管理界面）
- **DataChat-API**: 7个文档（知识库API服务）
- **ConnectAI-Helper**: 5个文档（浏览器扩展）
- **deploy**: 2个文档（部署配置）
- **根目录**: 8个文档（项目总览和配置）

## 文档特色

### 新增的重要文档特点

#### 1. 知识库集成文档
- **know.py_CODE_DOC.md**: 详细解释了知识库服务的客户端实现
- **tasks.py_CODE_DOC.md**: 深入分析了Celery异步任务的配置和执行
- **sse.py_CODE_DOC.md**: 完整说明了Server-Sent Events的实现原理

#### 2. 飞书集成文档
- **feishu.py_CODE_DOC.md**: 全面覆盖了飞书API的集成方案
- 包含事件处理、消息发送、文件上传等功能
- 详细的安全机制和认证流程说明

#### 3. 应用管理文档
- **model.py_CODE_DOC.md**: 深度解析了应用管理的数据模型
- 涵盖了20+AI模型的集成架构
- 详细的资源管理和租户隔离机制

#### 4. 下载器文档
- **downloader.py_CODE_DOC.md**: 高性能文件下载的实现细节
- 分块下载、断点续传、CDN集成等技术特点
- 性能优化和错误处理机制

## 使用建议

### 查找文档
1. 在源文件所在目录查找对应的 `*_CODE_DOC.md` 文件
2. 文档文件会紧邻源文件显示，便于快速定位
3. 使用IDE的文件搜索功能搜索 `_CODE_DOC.md` 后缀

### 维护文档
1. 新增源文件时，按照新规则创建对应文档
2. 修改源文件时，同步更新对应的文档文件
3. 确保文档内容与代码实现保持同步

### Git 管理
- 文档文件已被 `.gitignore` 忽略，不会被提交到版本库
- 这样避免了文档与代码的版本冲突
- 团队成员可以独立维护自己的文档副本

## 后续计划

### 待补充的文档
1. **manager-server**: 还有一些核心模块的文件需要补充文档
2. **前端组件**: 可以为重要的Vue组件创建详细文档
3. **API文档**: 可以基于现有代码生成API接口文档
4. **部署文档**: 补充更详细的部署和运维文档

### 文档质量提升
1. **代码示例**: 为每个模块添加更多实用的代码示例
2. **最佳实践**: 总结各个模块的最佳实践和注意事项
3. **故障排除**: 添加常见问题的故障排除指南
4. **性能优化**: 补充性能优化的建议和技巧

这次文档命名规则的变更和内容补充，进一步完善了项目的文档体系，为团队的开发和维护工作提供了更好的支持。
