import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M22.5 12a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zm-3.816-9a4 4 0 0 0-2.732 1.079L3.77 15.474a4.25 4.25 0 0 0-.101 6.11l6.326 6.325a4.25 4.25 0 0 0 5.78.213a8.956 8.956 0 0 1-.87-1.942l-.314.315a2.25 2.25 0 0 1-3.182 0l-6.326-6.326a2.25 2.25 0 0 1 .054-3.234l12.18-11.396A2 2 0 0 1 18.685 5H25.5A1.5 1.5 0 0 1 27 6.5v6.757a2 2 0 0 1-.586 1.415l-.234.234c.682.212 1.33.504 1.932.864A4 4 0 0 0 29 13.257V6.5A3.5 3.5 0 0 0 25.5 3h-6.816zM16 23.5a7.5 7.5 0 1 0 15 0a7.5 7.5 0 0 0-15 0zm6.477 4.432a1.023 1.023 0 1 1 2.046 0a1.023 1.023 0 0 1-2.046 0zm-1.704-6.477a2.727 2.727 0 0 1 5.454 0c0 .996-.288 1.554-1.028 2.329l-.36.367l-.158.17c-.387.434-.5.72-.5 1.224a.682.682 0 1 1-1.363 0c0-.996.289-1.554 1.028-2.329l.36-.367l.159-.17c.386-.434.499-.72.499-1.224a1.364 1.364 0 1 0-2.728 0a.682.682 0 0 1-1.363 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagQuestionMark32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
