<template>
  <div
    v-if="loading"
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space vertical>
      <n-skeleton height="40px" width="33%" :sharp="false" />
      <n-skeleton height="60px" :sharp="false" />
      <n-skeleton height="60px" />
      <n-skeleton height="60px" />
      <n-skeleton height="40px" width="100px" :sharp="false" />
    </n-space>
  </div>
  <div
    v-else
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flow-root mb-4">
      <h3 class="text-xl font-semibold dark:text-white">{{ $t('message.my.jqrqxgl') }}</h3>
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <!-- Item 1 -->
        <div class="py-4">
          <div class="flex items-center justify-between mb-4">
            <div class="flex flex-col flex-grow">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $t('message.my.xzkyq') }}</div>
              <div class="text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('message.my.gdltjqr') }}</div>
            </div>
            <label for="restrict-group" class="relative flex items-center cursor-pointer">
              <input
                id="restrict-group"
                :checked="isAllowGroups"
                type="checkbox"
                class="sr-only"
                @change="handleGroupSwitch"
              />
              <span
                class="h-6 bg-gray-200 border border-gray-200 rounded-full w-11 toggle-bg dark:bg-gray-700 dark:border-gray-600"
              ></span>
            </label>
          </div>
          <div v-show="isAllowGroups">
            <n-collapse accordion :default-expanded-names="[0]">
              <n-collapse-item
                v-for="(group, index) in group_group_permission"
                :key="index"
                :name="index"
                :title="group?.[0]?.llm"
              >
                <div class="grid grid-cols-8 gap-2 items-center">
                  <template v-for="(item, i) in group" :key="i">
                    <div class="text-sm font-medium text-gray-900 dark:text-white col-span-3">{{ item.name }}</div>
                    <n-select
                      class="col-span-1"
                      v-model:value="item.flag"
                      size="large"
                      :placeholder="t('message.my.xzsfky')"
                      :options="allowOrDenyOptions"
                      @update:value="(value) => handleAllowGroups(value, item)"
                    />
                    <n-select
                      filterable
                      multiple
                      tag
                      :placeholder="t('message.my.qsrqmc')"
                      class="col-span-4"
                      size="large"
                      :show-arrow="false"
                      :show="false"
                      :value="item.flag ? item.allow_groups : item.deny_groups"
                      @update:value="(value) => handleGroups(value, item)"
                    />
                  </template>
                </div>
              </n-collapse-item>
            </n-collapse>
          </div>
        </div>

        <!-- Item 2 -->
        <div>
          <div class="flex items-center justify-between pt-4 mb-4">
            <div class="flex flex-col flex-grow">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $t('message.my.xzslsy') }}</div>
              <div class="text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('message.my.gdltjqrsl') }}</div>
            </div>
            <label for="account-activity" class="relative flex items-center cursor-pointer">
              <input
                id="account-activity"
                :checked="isAllowUsers"
                type="checkbox"
                class="sr-only"
                @change="handlePrivateSwitch"
              />
              <span
                class="h-6 bg-gray-200 border border-gray-200 rounded-full w-11 toggle-bg dark:bg-gray-700 dark:border-gray-600"
              ></span>
            </label>
          </div>
          <div v-show="isAllowUsers">
            <n-collapse accordion :default-expanded-names="[0]">
              <n-collapse-item
                v-for="(user, index) in group_user_permission"
                :key="index"
                :name="index"
                :title="user?.[0]?.llm"
              >
                <div class="grid grid-cols-8 gap-2 items-center">
                  <template v-for="(item, i) in user" :key="i">
                    <div class="text-sm font-medium text-gray-900 dark:text-white col-span-3">{{ item.name }}</div>
                    <n-select
                      class="col-span-1"
                      v-model:value="item.flag"
                      size="large"
                      :placeholder="t('message.my.xzsfky')"
                      :options="allowOrDenyOptions"
                      @update:value="(value) => handleAllowUsers(value, item)"
                    />
                    <n-tree-select
                      multiple
                      cascade
                      checkable
                      filterable
                      check-strategy="child"
                      :options="treeData"
                      :value="item.flag ? item.allow_users : item.deny_users"
                      @update:value="(value) => handleUsers(value, item)"
                      :placeholder="t('message.my.qsryhmc')"
                      class="col-span-4"
                      size="large"
                    />
                  </template></div
              ></n-collapse-item>
            </n-collapse>
          </div>
        </div>
      </div>
    </div>
    <button
      type="button"
      class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      @click="handleSave"
    >
      <icon-akar-icons-save class="mr-2" />
      {{ t('message.my.bc') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { isEmpty } from 'lodash-es';
import { updateAppSetting } from '@/service/api/messenger';
import { fetchSeatList } from '@/service/api/management';
import { t } from '@/locales';

const props = defineProps<{
  data: ApiApp.APPSetting;
  models: ApiApp.AppResource['models'];
  loading: boolean;
  resources: ApiApp.AppResources[];
}>();
const message = useMessage();
const route = useRoute();
const emit = defineEmits(['update:data']);
const data = useVModel(props, 'data', emit);
const isAllowGroups = ref(false);
const isAllowUsers = ref(false);

const group_user_permission = ref<any>([]);
const group_group_permission = ref<any>([]);

const filterAndMapPermissions = (
  permissions: ApiApp.UserPermission[] | ApiApp.GroupPermission[],
  resources: ApiApp.AppResources[] | ApiApp.AppResource[]
) => {
  return resources
    .map(({ resource, scene }) => {
      const find = permissions.filter((item) =>
        resource.find((r) => r.id === item.resource_id && r.models.some((m) => m.id === item.model_id))
      );
      const updatedFind = find.map((item) => {
        const matchingResource = resource.find((r) => r.id === item.resource_id);
        return { ...item, llm: scene, description: matchingResource?.description };
      });
      return updatedFind;
    })
    .filter((item) => !isEmpty(item));
};

watch(
  () => [data.value.user_permission, data.value.group_permission],
  ([new_user_permission = [], new_group_permission = []]) => {
    group_user_permission.value = filterAndMapPermissions(new_user_permission, props.resources);
    group_group_permission.value = filterAndMapPermissions(new_group_permission, props.resources);
  },
  { immediate: true, deep: true }
);

watch(data, (v) => {
  isAllowGroups.value = v.group_permission.some((item) => !isEmpty(item.allow_groups) || !isEmpty(item.deny_groups));
  isAllowUsers.value = v.user_permission.some((item) => !isEmpty(item.allow_users) || !isEmpty(item.deny_users));
});

const allowOrDenyOptions: { label: string; value: any }[] = [
  {
    label: t('message.my.ky'),
    value: true
  },
  {
    label: t('message.my.bky'),
    value: false
  }
];
// TypeScript
function handleGroupSwitch(e: any) {
  const { checked } = e.target;
  // 更新 isAllowGroups 的值
  isAllowGroups.value = checked;
  // 如果开关未选中
  if (!checked) {
    const val = group_group_permission.value.flat().map((item) => {
      // 返回新的对象，将 allow_groups 和 deny_groups 设置为空数组
      return { ...item, allow_groups: [], deny_groups: [] };
    });
    // 更新 data.value.group_permission 的值
    data.value.group_permission = val;
  }
}

function handlePrivateSwitch(e: any) {
  const { checked } = e.target;
  // 更新 isAllowUsers 的值
  isAllowUsers.value = checked;
  // 如果开关未选中
  if (!checked) {
    const val = group_user_permission.value.flat().map((item) => {
      // 返回新的对象，将 allow_users 和 deny_users 设置为空数组
      return { ...item, allow_users: [], deny_users: [] };
    });
    // 更新 data.value.user_permission 的值
    data.value.user_permission = val;
  }
}
function handleAllowGroups(flag: boolean, item: ApiApp.GroupPermission) {
  const allow = [...item.allow_groups];
  const deny = [...item.deny_groups];
  item.allow_groups = flag ? deny : [];
  item.deny_groups = flag ? [] : allow;
  data.value.group_permission = group_group_permission.value.flat();
}

function handleGroups(value: string[], item: ApiApp.GroupPermission) {
  if (item.flag) {
    item.allow_groups = value;
  } else {
    item.deny_groups = value;
  }
  data.value.group_permission = group_group_permission.value.flat();
}

function handleAllowUsers(flag: boolean, item: ApiApp.UserPermission) {
  const allow = [...item.allow_users];
  const deny = [...item.deny_users];
  item.allow_users = flag ? deny : [];
  item.deny_users = flag ? [] : allow;
  data.value.user_permission = group_user_permission.value.flat();
}

function handleUsers(value: string[], item: ApiApp.UserPermission) {
  if (item.flag) {
    item.allow_users = value;
  } else {
    item.deny_users = value;
  }
  data.value.user_permission = group_user_permission.value.flat();
}

async function handleSave() {
  await updateAppSetting({ id: route.query.id as string, data: data.value });
  message.success(t('message.msg.bccg'));
}

const seats = ref([]);
const optionsRef = ref([]);
function handleSearch(keyword?: string) {
  fetchSeatList({ keyword, page: 1, size: 99999 })
    .then((res) => {
      if (res.data?.data?.length > 0) {
        return res.data.data;
      }
      return [];
    })
    .then((data) => {
      seats.value = data;
      return data.map((i) => ({ label: i.name, value: i.name }));
    })
    .then((options) => {
      optionsRef.value = options;
    });
}
const treeData = computed(() => {
  const temp = seats.value.reduce((s, i) => {
    (s[i.department] = s[i.department] || []).push({ key: i.name, label: i.name });
    return s;
  }, {});
  // console.log('temp', temp)
  return Object.entries(temp).map(([k, v]) => {
    const value = k === 'undefined' || !k ? t('message.dashboard.unkown') : k;
    return { children: v, label: value, key: value };
  });
});

onMounted(() => {
  // 初始化
  handleSearch();
});
</script>
