# https://hackernoon.com/how-to-speed-up-file-downloads-with-python
import logging
import async<PERSON>
import httpx
from tornado.options import options
from tornado.httpclient import AsyncHTTP<PERSON>lient, HTTPRequest
AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient")


class AsyncClient(object):
    async def __aenter__(self):
        return self
    async def __aexit__(self, *args):
        pass
    async def request(self, url, method='GET', headers=dict(), timeout=5):
        response = await AsyncHTTPClient().fetch(HTTPRequest(
            url,
            method=method,
            proxy_host=options.PROXY_HOST if 'mpic.forkway.cn' not in url else None,
            proxy_port=options.PROXY_PORT,
            proxy_username=options.PROXY_USERNAME,
            proxy_password=options.PROXY_PASSWORD,
            headers=headers,
            connect_timeout=3,
            request_timeout=timeout,
            validate_cert=False,
        ))
        # 模拟一下httpx.Response
        setattr(response, 'status_code', response.code)
        setattr(response, 'content', response.body)
        return response
    async def head(self, url):
        return await self.request(url, 'HEAD')

    async def get(self, url, headers=dict(), timeout=5):
        return await self.request(url, 'GET', headers=headers, timeout=timeout)


class ChunkedDownloader(object):

    def __init__(self, chunk_size=1 * 1024 ** 2, max_chunk=10, timeout=10, proxy=None, proxies=None):
        self.chunk_size = chunk_size
        self.max_chunk = max_chunk
        self.timeout = timeout
        self.proxy = proxy  # 废弃的参数
        self.proxies = proxies

    @property
    def client(self):
        # 尝试使用tornado的httpclient，底层是curlasynchttpclient
        return AsyncClient()
        # return httpx.AsyncClient(proxies=self.proxies)

    async def get_content_length(self, url):
        async with self.client as client:
            response = await client.head(url)
            if response.status_code >= 300:
                raise Exception()
            return int(response.headers.get('content-length')), response.headers.get('accept-ranges', '') == 'bytes'

    def parts_generator(self, size, start=0, part_size=1 * 1024 ** 2):
        while size - start > part_size:
            yield start, start + part_size - 1
            start += part_size
        yield start, size

    async def download_chunk(self, url, headers):
        logging.debug("download %r %r", url, headers)
        try:
            async with self.client as client:
                response = await client.get(url, headers=headers, timeout=self.timeout)
                return response.content
        except Exception as e:
            logging.error(e)
            async with self.client as client:
                response = await client.get(url, headers=headers, timeout=self.timeout)
                return response.content

    async def download(self, url):
        try:
            size, support_range = await self.get_content_length(url)
        except Exception as e:
            size, support_range = await self.get_content_length(url)
        chunk_size = self.chunk_size
        if size / self.chunk_size > self.max_chunk:
            chunk_size = int((size + self.max_chunk + 1) / self.max_chunk)
        tasks = []
        for number, sizes in enumerate(self.parts_generator(size, part_size=chunk_size if support_range else size)):
            tasks.append(self.download_chunk(url, {'Range': f'bytes={sizes[0]}-{sizes[1]}'}))
        logging.info("download file %r in %r chunks", url, len(tasks))
        result = await asyncio.gather(*tasks)
        return b''.join(result)


async def download_bytes(url, sync_oss=False):
    proxies = {
        "http://": options.PROXY_WEB,
        "https://": options.PROXY_WEB,
    } if not options.OVERSEAS else None
    # 1. 尝试使用代理
    # 2. 重试一次
    # 3. 如果需要同步oss，尝试调用一次head触发（这个放在finally里面不占用函数正常调用时间）
    try:
        return await ChunkedDownloader(proxies=proxies).download(url)
    except Exception as e:
        logging.exception(e)
        return await ChunkedDownloader(proxies=proxies).download(url)

    finally:
        async def sync_to_oss():
            try:
                oss_url = url.replace(
                    'cdn.discordapp.com', 'mpic.forkway.cn',
                ).replace(
                    'oaidalleapiprodscus.blob.core.windows.net', 'mpic.forkway.cn',
                ).replace(
                    'storage.googleapis.com', 'mpic.forkway.cn',
                )
                response = await httpx.AsyncClient().head(oss_url)
                logging.debug('response %r', response)
            except Exception as e:
                logging.error(e)
                try:
                    response = await httpx.AsyncClient().head(oss_url)
                    logging.debug('response %r', response)
                except Exception as e:
                    logging.error(e)
        asyncio.gather(sync_to_oss())


if __name__ == '__main__':
    import time
    import sys
    import os.path
    from urllib.parse import urlparse
    from tornado.options import options, define
    define('PROXY_HOST', default='')
    define('PROXY_PORT', default=7777)
    define('PROXY_USERNAME', default='')
    define('PROXY_PASSWORD', default='')
    define('DOMAIN', default='connectai-e.com')
    define('OVERSEAS', default=True)
    define('PROXY_WEB', default='')

    async def main():
        if len(sys.argv) <= 1:
            print('Add URLS')
            exit(1)
        urls = sys.argv[1:]
        async def download_to_file(url):
            filename = os.path.basename(urlparse(url).path)
            print('filename', filename)
            # content = await ChunkedDownloader().download(url)
            content = await download_bytes(url, True)
            with open(f'/tmp/{filename}', 'wb+') as f:
                f.write(content)
        await asyncio.gather(*[download_to_file(url) for url in urls])

    start_code = time.monotonic()
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())
    print(f'{time.monotonic() - start_code} seconds!')


