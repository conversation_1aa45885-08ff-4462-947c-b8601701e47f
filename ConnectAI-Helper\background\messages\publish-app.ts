import type { PlasmoMessaging } from "@plasmohq/messaging";
import { getCookieAndToken } from "~utils/browser";
import { Configuration } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";

//用户和机器人的会话首次被创建 是v0版本，要抓包看具体的代码
const defaultEvents = ["im.message.message_read_v1", "im.message.receive_v1", "20"];
const defaultScopeIds = [
  "21001", // 获取与更新群组信息
  "7", // 读取群消息
  "21003", // 更新应用所创建群的群信息
  "21002", // 获取群组信息
  "20001", // 获取与发送单聊、群组消息
  "20011", // 获取用户在群组中@机器人的消息
  "3001", // 接收群聊中@机器人消息事件
  "20012", // 获取群组中所有消息
  "6005", // 新应用创建群聊的信息
  "6081", // 以应用身份读取通讯录
  "20010", // 获取用户发给机器人的单聊消息
  "3000", // 读取用户发给机器人的单聊消息
  "20013", // 发送应用内佳绩消息
  "20014", // 发送电话加急消息
  "20015", // 发送短信加急消息
  "20008", // 获取单聊、群组消息
  "1000", // 以应用的身份发消息
  "1006", // 给一个或多个部门的成员批量发消息
  "1005", // 给多个用户批量发送消息
  "20009", // 获取上传图片或文件资源
  "41003", // 查看新版文档
  "26010", // 查看知识库
  "101221", // 获取客户端用户代理信息
  "100032" // 获取通讯录基本信息
];

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  // TODO
  const {
    appId, eventCallback, cardCallback,
    events = defaultEvents,
    scopeIds = defaultScopeIds,
    encryptKey,
    verificationToken,
    domain
  } = req.body;
  const params = await getCookieAndToken(domain);
  console.log("publish app info", params, req);
  // console.log("scopeIds", scopeIds);
  const configCookie = new Configuration({ domain });
  // 这里手动设置csrftoken
  configCookie.csrfToken = params.csrf_token;
  const app = new OpenApp(configCookie);

  await app.versionManager.clearUnPublishedVersion(appId);
  // add event
  await app.eventManager.addEvent(appId, events);
  // const eventInfo = await app.eventManager.getEventInfo(appId)
  // const { encryptKey, verificationUrl, verificationToken } = eventInfo
  await app.eventManager.addEventCallBack(appId, {
    encryptKey,
    verificationToken,
    verificationUrl: eventCallback
  });

  // add bot
  await app.enableBot(appId);
  // 先用默认的权限
  await app.addScope(appId, defaultScopeIds);
  await app.botManager.addBotCallBack(appId, cardCallback);

  // publishbot
  const response = await app.versionManager.createAndPublishNextVersion(appId);

  res.send({ response });
};

export default handler;

