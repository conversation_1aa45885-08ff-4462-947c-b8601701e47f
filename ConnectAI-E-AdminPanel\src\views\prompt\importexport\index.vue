<template>
  <div class="h-full">
    <n-card :title="$t('message.prompt.pldrtsc')" class="h-full shadow-sm rounded-16px" content-style="overflow:hidden">
      <n-tabs type="line" class="flex-col-stretch h-full" pane-class="flex-1-hidden">
        <n-tab-pane v-for="item in maps" :key="item.id" :name="item.id" :tab="$t(item.label)">
          <component :is="item.component" />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue';
import { importRemote, importLocal } from './components';

interface Map {
  id: string;
  label: string;
  component: Component;
}

const maps: Map[] = [
  { id: '1', label: 'message.prompt.bdbg', component: importLocal },
  { id: '2', label: 'message.prompt.zxlj', component: importRemote }
];
</script>

<style scoped></style>
