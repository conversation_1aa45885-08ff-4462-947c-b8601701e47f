import base
import logging
import time
import json
from tornado.ioloop import IOLoop
from tornado.options import options
from sqlalchemy import alias, and_, or_, select, func, distinct, text
from core.base_model import MysqlModel
from core.schema import ChatLog, TenantSeat, ObjID, TenantProduct


class SyncSeats(MysqlModel):

    async def get_tenant(self):
        # test
        return ['64d6368e927ed10001aa2d1e']
        return [t for t, in self.session.query(distinct(ChatLog.tenant_id)).all()]

    async def get_seats_list_by_tenant_id(self, tenant_id):
        return [t for t, in self.session.query(distinct(ChatLog.username)).filter(
            ChatLog.tenant_id == tenant_id,
        )]

    async def save_seats(self, tenant_id, seats):
        for seat in seats:
            await self.try_insert_seats(tenant_id, seat)

    async def try_insert_seats(self, tenant_id, name):
        if self.session.query(TenantSeat.id).filter(
            TenantSeat.tenant_id == tenant_id,
            TenantSeat.name == name,
            TenantSeat.status > -1,
        ).limit(1).scalar():
            logging.warn("Duplicate %r %r", tenant_id, name)
            return
        logging.warn("sync seat %r %r", tenant_id, name)
        self.session.begin_nested()
        self.session.add(TenantSeat(
            id=ObjID.new_id(),
            tenant_id=tenant_id,
            name=name,
            status=1,
        ))
        self.session.commit()

    async def try_update_seats_for_product(self, tenant_id):
        seats_count = int(self.session.query(func.count(TenantSeat.id)).filter(
            TenantSeat.tenant_id == tenant_id,
            TenantSeat.status > -1,
        ).scalar())
        current_product = self.session.query(TenantProduct).filter(
            TenantProduct.tenant_id == tenant_id,
            TenantProduct.status == 1,
        ).order_by(
            TenantProduct.expired.desc(),
        ).first()
        if current_product and seats_count > current_product.seats:
            logging.info("update seats for Product %r %r", tenant_id, seats_count)
            self.session.begin_nested()
            # 当前付费的，并且是生效的套餐，只要人数
            self.session.query(TenantProduct).filter(
                TenantProduct.id == current_product.id,
            ).update(dict(seats=seats_count), synchronize_session=False)
            self.session.commit()

    async def run(self):
        for tenant_id in await self.get_tenant():
            logging.info("tenant_id %r", tenant_id)
            seats = await self.get_seats_list_by_tenant_id(tenant_id)
            logging.info("get seats %r", seats)
            await self.save_seats(tenant_id, seats)
            await self.try_update_seats_for_product(tenant_id)


if __name__ == '__main__':
    with SyncSeats() as task:
        IOLoop.current().run_sync(task.run)

