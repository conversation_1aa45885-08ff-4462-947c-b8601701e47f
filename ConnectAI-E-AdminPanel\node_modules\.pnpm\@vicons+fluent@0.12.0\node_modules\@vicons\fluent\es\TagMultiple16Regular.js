import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.357 4.66a.774.774 0 0 1-1.09 0a.761.761 0 0 1 0-1.081a.774.774 0 0 1 1.09 0c.3.299.3.783 0 1.081zm-8.821 4.5a1.992 1.992 0 0 1 0-2.83l4.76-4.73a2.02 2.02 0 0 1 1.415-.586L11.975 1a2.007 2.007 0 0 1 2.021 2.015l-.024 3.373a1.995 1.995 0 0 1-.59 1.4l-4.69 4.66a2.022 2.022 0 0 1-2.846 0L2.536 9.16zm.712-2.123a.996.996 0 0 0 0 1.415l3.31 3.29c.393.39 1.03.39 1.423 0l4.69-4.661a.997.997 0 0 0 .294-.7l.025-3.373A1.003 1.003 0 0 0 11.979 2l-3.264.014a1.01 1.01 0 0 0-.708.293l-4.76 4.73zM1.998 9.75a2 2 0 0 0 .46 2.115l1.964 1.964a4 4 0 0 0 5.657 0l3.482-3.482a1.5 1.5 0 0 0 .44-1.06v-.78l-3.922 3.922c-.069.069-.14.135-.212.197l-.495.496a3 3 0 0 1-4.243 0l-.499-.499a4.08 4.08 0 0 1-.208-.194l-1.964-1.964a1.993 1.993 0 0 1-.46-.715z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagMultiple16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
