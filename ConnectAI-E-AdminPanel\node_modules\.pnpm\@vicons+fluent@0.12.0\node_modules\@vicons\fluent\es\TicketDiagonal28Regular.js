import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.47 3.919a1.062 1.062 0 0 0-1.54-.04L3.863 15.949a1.063 1.063 0 0 0 .03 1.531l1.375 1.272c.037.035.104.071.233.076c.134.006.303-.025.474-.095a3.167 3.167 0 0 1 4.21 3.894a1.183 1.183 0 0 0-.058.481c.016.128.057.192.095.226l1.02.944a1.062 1.062 0 0 0 1.465-.022l11.528-11.322a1.063 1.063 0 0 0 .045-1.47l-1.075-1.192c-.034-.036-.094-.077-.218-.096a1.159 1.159 0 0 0-.466.042a3.17 3.17 0 0 1-4.021-3.05c0-.41.078-.802.22-1.163c.066-.167.095-.33.09-.459c-.006-.124-.04-.188-.074-.225l-1.264-1.401zm-2.6-1.1a2.562 2.562 0 0 1 3.714.095l1.265 1.401c.634.703.498 1.651.266 2.24a1.667 1.667 0 0 0 2.001 2.217c.609-.17 1.566-.21 2.201.494l1.075 1.192a2.563 2.563 0 0 1-.107 3.545L13.757 25.325a2.562 2.562 0 0 1-3.535.053l-1.02-.944c-.714-.66-.643-1.647-.447-2.262a1.667 1.667 0 0 0-2.216-2.05c-.599.243-1.577.39-2.29-.27l-1.376-1.271a2.563 2.563 0 0 1-.072-3.694L14.871 2.82z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
