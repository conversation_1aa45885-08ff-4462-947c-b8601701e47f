<template>
  <div class="h-full jc-center flex items-center cursor-pointer" @click="handleClickLink">
    <span v-if="isSubscribe()">{{ needUpgrade() ? t('message.header.per') : t('message.header.team') }}</span>
    <span v-if="isSubscribe()">：{{ expiredDate() }} {{ t('message.header.end') }}</span>
    <a
      href="#"
      class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-semibold mr-2 px-2.5 py-1 rounded dark:bg-gray-700 dark:text-blue-400 inline-flex items-center justify-center ml-2"
      >{{ isSubscribe() && !needUpgrade() ? t('message.header.xf') : t('message.header.up') }}</a
    >
  </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import { useThemeStore } from '@/store';
import { useTenantPrivilege } from '@/hooks';
import { t } from '@/locales';

const { isSubscribe, needUpgrade, expiredDate } = useTenantPrivilege();
const route = useRoute();
const router = useRouter();
defineOptions({ name: 'GithubSite' });

const theme = useThemeStore();
function handleClickLink() {
  router.push({ name: 'dashboard_pricing' });
  // 线上bug，点一次不进入页面
  // 动态路由对应的js加载了，但是没有路由过去，加载完成之后，后面每次点击都能成功路由过去...
  setTimeout(() => {
    router.replace({ name: 'dashboard_pricing' });
  }, 200)
}
</script>

<style scoped></style>
