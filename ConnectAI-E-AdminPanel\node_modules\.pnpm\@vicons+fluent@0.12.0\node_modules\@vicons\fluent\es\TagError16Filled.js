import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M1.535 6.327L6.295 1.6a2.02 2.02 0 0 1 1.415-.585L10.974 1a2.007 2.007 0 0 1 2.022 2.014l-.019 2.574a5.5 5.5 0 0 0-7.428 7.31a2.008 2.008 0 0 1-.704-.454l-3.31-3.288a1.99 1.99 0 0 1 0-2.829zm7.729-1.669c.3.299.788.299 1.089 0c.3-.298.3-.783 0-1.082a.774.774 0 0 0-1.09 0c-.3.3-.3.784 0 1.082zM15 10.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zM10.5 8a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 1 0v-2a.5.5 0 0 0-.5-.5zm0 5.125a.625.625 0 1 0 0-1.25a.625.625 0 0 0 0 1.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagError16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
