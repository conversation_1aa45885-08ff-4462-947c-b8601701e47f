#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动DataChat-API的脚本
适用于本地开发环境，无需Docker和Elasticsearch
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def setup_environment():
    """设置环境变量"""
    # 设置Flask环境变量
    os.environ["FLASK_ENV"] = "development"
    os.environ["FLASK_DEBUG"] = "1"
    os.environ["FLASK_APP"] = "server/app.py"
    
    # 设置API配置
    os.environ["FLASK_OPENAI_API_KEY"] = ""
    os.environ["FLASK_OPENAI_API_BASE"] = "https://api.openai.com/v1"
    os.environ["FLASK_UPLOAD_PATH"] = "./data/files"
    os.environ["FLASK_DOMAIN"] = "http://localhost:5000"
    
    # 使用文件存储替代Elasticsearch
    os.environ["USE_FILE_STORE"] = "True"
    os.environ["FILE_STORE_PATH"] = "./data/search_index"
    
    print("✅ 环境设置完成")

def init_file_store():
    """初始化文件存储系统（替代Elasticsearch）"""
    try:
        # 创建搜索索引目录
        index_dir = Path("./data/search_index")
        index_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建基础索引文件
        index_file = index_dir / "documents.json"
        if not index_file.exists():
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump({"documents": [], "metadata": {"total": 0}}, f, ensure_ascii=False, indent=2)
        
        # 创建文件上传目录
        files_dir = Path("./data/files")
        files_dir.mkdir(parents=True, exist_ok=True)
        
        print("✅ 文件存储系统初始化完成")
        
    except Exception as e:
        print(f"❌ 文件存储系统初始化失败: {e}")
        return False
    
    return True

def create_simple_app():
    """创建简化版的Flask应用"""
    app_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版DataChat API服务器
用于本地开发环境
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
from pathlib import Path

app = Flask(__name__)
CORS(app)

# 配置
app.config['UPLOAD_FOLDER'] = os.environ.get('FLASK_UPLOAD_PATH', './data/files')
app.config['INDEX_FOLDER'] = os.environ.get('FILE_STORE_PATH', './data/search_index')

@app.route('/')
def index():
    return jsonify({
        "message": "ConnectAI DataChat API",
        "version": "1.0.0",
        "status": "running"
    })

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})

@app.route('/api/search', methods=['POST'])
def search():
    """简单的文档搜索功能"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        
        # 从文件存储中搜索
        index_file = Path(app.config['INDEX_FOLDER']) / 'documents.json'
        if index_file.exists():
            with open(index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
            
            # 简单的文本匹配搜索
            results = []
            for doc in index_data.get('documents', []):
                if query.lower() in doc.get('content', '').lower():
                    results.append(doc)
            
            return jsonify({
                "results": results[:10],  # 返回前10个结果
                "total": len(results)
            })
        
        return jsonify({"results": [], "total": 0})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload():
    """文件上传功能"""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        # 保存文件
        upload_dir = Path(app.config['UPLOAD_FOLDER'])
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / file.filename
        file.save(str(file_path))
        
        return jsonify({
            "message": "File uploaded successfully",
            "filename": file.filename,
            "path": str(file_path)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 启动DataChat API服务器...")
    print("服务将在 http://localhost:5000 运行")
    app.run(host='0.0.0.0', port=5000, debug=True)
'''
    
    # 写入简化的应用文件
    app_file = Path("DataChat-API/simple_app.py")
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(app_content)
    
    print("✅ 简化版应用创建完成")

def start_datachat_api():
    """启动DataChat API服务器"""
    try:
        # 切换到DataChat-API目录
        os.chdir("DataChat-API")
        
        # 确定Python可执行文件路径
        if os.name == 'nt':  # Windows
            python_cmd = "venv\\Scripts\\python.exe"
        else:  # Unix/Linux/Mac
            python_cmd = "venv/bin/python"
        
        # 启动服务器
        print("🚀 启动DataChat API服务器...")
        print("服务将在 http://localhost:5000 运行")
        print("按 Ctrl+C 停止服务")
        
        # 使用subprocess启动服务器
        subprocess.run([python_cmd, "simple_app.py"], check=True)
        
    except KeyboardInterrupt:
        print("\\n⏹️  服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 正在启动ConnectAI DataChat API...")
    
    # 检查虚拟环境是否存在
    if not os.path.exists("DataChat-API/venv"):
        print("❌ 虚拟环境不存在，请先运行环境设置")
        return False
    
    # 设置环境
    setup_environment()
    
    # 初始化文件存储
    if not init_file_store():
        return False
    
    # 创建简化版应用
    create_simple_app()
    
    # 启动服务器
    start_datachat_api()
    
    return True

if __name__ == "__main__":
    main()
