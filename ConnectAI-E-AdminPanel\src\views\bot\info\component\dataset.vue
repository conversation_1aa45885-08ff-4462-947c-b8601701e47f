<template>
  <div
    v-if="loading"
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space vertical>
      <n-skeleton height="40px" width="33%" :sharp="false" />
      <n-skeleton height="60px" :sharp="false" />
      <n-skeleton height="60px" />
      <n-skeleton height="60px" />
      <n-skeleton height="40px" width="100px" :sharp="false" />
    </n-space>
  </div>
  <div
    v-else
    class="p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <h3 class="text-xl font-semibold dark:text-white mb-4">{{ t('message.my.knowledge') }}</h3>
    <div class="mb-4">
      <label for="settings-timezone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
        >{{ t('message.my.knowledge_select_label') }}</label
      >
      <n-select
        v-model:value="data.collection_id"
        filterable
        size="large"
        class="block w-full"
        :placeholder="t('message.my.knowledge_placeholder')"
        clearable
        :options="options"
      />
    </div>
    <button
      type="button"
      class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      @click="handleSave"
    >
      <icon-akar-icons-save class="mr-2" />
      {{ t('message.my.bc') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { fetchDatasetList } from '@/service/api/knowledge';
import { updateAppSetting } from '@/service/api/app';
import { t } from '@/locales';

const route = useRoute();
const message = useMessage();

const options = ref<{ label: string; value: string }[]>([]);

const props = defineProps<{
  data: ApiApp.APPSetting;
  loading: boolean;
}>();
const emit = defineEmits(['update:data']);

const data = useVModel(props, 'data', emit);

async function getDatasetList() {
  try {
    const res = await fetchDatasetList({
      page: 1,
      size: 99999
    });
    options.value = res.data?.data?.map(item => ({ value: item.id, label: item.name }));
  } catch (err) {
    console.error(err);
  }
}

async function handleSave() {
  await updateAppSetting({ id: route.query.id as string, data: data.value });
  message.success(t('message.msg.bccg'));
}

onMounted(() => {
  getDatasetList();
});
</script>

<style scoped></style>
