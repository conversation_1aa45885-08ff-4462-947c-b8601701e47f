@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\vue-demi@0.14.5_vue@3.3.0\node_modules\vue-demi\bin\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\vue-demi@0.14.5_vue@3.3.0\node_modules\vue-demi\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\vue-demi@0.14.5_vue@3.3.0\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\vue-demi@0.14.5_vue@3.3.0\node_modules\vue-demi\bin\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\vue-demi@0.14.5_vue@3.3.0\node_modules\vue-demi\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\vue-demi@0.14.5_vue@3.3.0\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\vue-demi@0.14.5_vue@3.3.0\node_modules\vue-demi\bin\vue-demi-switch.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\vue-demi@0.14.5_vue@3.3.0\node_modules\vue-demi\bin\vue-demi-switch.js" %*
)
