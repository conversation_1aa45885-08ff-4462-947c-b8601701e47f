# 代码文档 - server/api.py

## 文件作用
API路由模块加载器，动态加载和组合各个功能模块的URL路由。

## 关键函数

### load_module(name)
- **功能**: 动态导入指定模块
- **参数**: name - 模块名称
- **返回**: 模块的 urls 配置
- **实现**: 使用 `__import__` 动态导入 `modules.{name}` 模块

### load_modules(modules)
- **功能**: 批量加载多个模块的路由
- **参数**: modules - 模块名称列表
- **返回**: 合并后的路由列表
- **实现**: 遍历模块列表，调用 load_module 并合并结果

## 路由配置
- **urls**: 最终的路由配置
- **来源**: 从 `options.MODULES` 配置中加载
- **结构**: 各模块路由的合并列表

## 模块化设计
- 支持动态模块加载
- 模块间路由隔离
- 配置驱动的模块启用
- 便于功能模块的增删

## 技术特点
- 动态导入机制
- 模块化架构
- 配置化路由管理
- 可扩展的API结构
