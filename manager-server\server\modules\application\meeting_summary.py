class MeetingSummaryTool(CTool):
    name: str = 'MeetingSummary'
    description: str = '会议纪要整理小助手'
    metting_prompt: str ="""我们来玩游戏。 你将扮演 MeetingGPT，一个帮助人们整理会议纪要的中文人工智能。
该 AI 旨在将用户输入的录音文字稿整理成逻辑清晰、结构清楚的会议纪要，它知道如何将每条信息放入笔记中对应的位置，尽管同一主题的信息可能散落在文字稿中不同的位置。
最重要的游戏规则：
（1）永远不要解释你自己，只要给我所要求的输出即可。 如果我要求你在“xx”之间显示一些东西，你会完全按照我的要求显示它。
（2）输出格式：以bulletpoint的形式输出每一段话，一个bulletpoint下可以有sub bulletpoint，表示进一步解释的逻辑关系。
（3）每一个bulletpoint句末不要有标点
（4）以中文输出
我将把会议的录音文字稿发给你，你会按照要求输出整理完成的中文会议纪要。"""

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class MeetingSummaryCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在读取，请稍等...'))),
                            header=FeishuMessageCardHeader(_('会议纪要'), template='blue'))
                    )
                else:
                    send_message(AppResult.ReplyText, _('正在读取，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成会议摘要，请稍等...'))),
                            header=FeishuMessageCardHeader(_('会议纪要'), template='blue'),
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # 同时添加当前用户输入的问题，以及ai回复问题
                content = response.generations[0][0].text
                # 翻译助手不需要历史记录
                # session.add_message({'role': 'human', 'content': input.strip()})
                # session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader(_('会议纪要'), template='blue'),
                        ),
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))

        model.callbacks = [MeetingSummaryCallbackHandler()]
        m = {value: content for value, content in ai_model}
        # temperature = session.temperature
        temperature = 0.8
        model_name = m.get(session.model_id, ai_model[0][1])

        # 支持文心一言+openai+azure
        if model.openai_api_type == '文心一言':
            chat = WenXinChat(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        elif model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )

        if 'openai_api_type' in model and model.openai_api_type == '文心一言':
            # 文心一言必须奇数条消息，不支持SystemMessage，消息不能为空
            system_message = [HumanMessage(content=self.metting_prompt), AIMessage(content='好的')]
        else:
            system_message = [SystemMessage(content=self.metting_prompt)]

        # 不需要上下文
        messages = system_message + [HumanMessage(content=input)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class DingdingCommand(CommandTool):
    next_tool_name: str = 'MeetingSummary'
    name: str = 'meeting_summary_dingding_command'
    description: str = 'meeting_summary dingding command'

    def send_usage(self):
        # TODO 可使用ActionCardMessage替代，dtmdLink放到actionURL内即可
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/model',
                    _('🚀 AI模型切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("你好呀， 我是会议小秘书。 您可以将会议的文字稿发送给我，让我帮助整理会议纪要。")
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/model' or input[:2] == '模型':
            # 如果"/model {model_name}"，就发送成功消息，否则发送选项
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_model(self, model_name=None):
        # 如果"/model {model_name}"，就发送成功消息，否则发送选项
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            action_value = m[model_name]
            session['model_id'] = action_value
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择模型：%(model)s", model=model_name),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🚀 机器人提醒'),
                        text=_('无可用模型'),
                    )
                )
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/model ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下模型：'),
                    title=_('🚀 AI模型切换'),
                ),
            )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)

class FeishuCommand(CommandTool):
    next_tool_name: str = 'MeetingSummary'
    name: str = 'meeting_summary_feishu_command'
    description: str = 'meeting_summary feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<示例> 或 /example 即可查看示例'
    ]

    def example_question(self):
        return """
会议日期：2023年7月15日

张总经理：大家好，欢迎参加今天的会议。首先，我要对大家在上半年的辛勤工作表示感谢。我们今天的主题是对2023年上半年的销售业绩进行复盘分析。让我们先来看一下整体销售情况。李总，请您介绍一下今年上半年的销售数据。

李销售总监：感谢张总。上半年我们的销售额达到1.5亿，较去年同期增长了15%。这其中，产品A销售额增长了10%，产品B销售额增长了20%。虽然总体表现不错，但也有一些值得关注的问题。

王市场总监：对，我注意到我们的市场份额在上半年有所下降，特别是在北区。经过市场调研，我们发现竞争对手推出了一款类似产品，并在广告宣传上下了不少功夫，导致我们的竞争优势受到了一定冲击。

赵财务总监：另外，上半年的销售成本也有所上升。我们做了成本结构分析，发现运输和物流方面的费用增加了8%，主要是由于原材料价格上涨和油价上扬所致。这对我们的利润率产生了一定的影响。

张总经理：好的，谢谢大家的汇报。现在，让我们深入分析一下销售表现的原因。陈经理、刘经理，请你们分别谈谈你们所在区域的销售情况以及遇到的挑战。

陈区域销售经理：上半年我们区域的销售情况还算不错，主要得益于产品A的市场需求增加。我们在客户关系维护方面做得还可以，但市场竞争确实越来越激烈。一些竞争对手采取了价格战策略，导致我们在一些大客户中的销售份额下降。

刘区域销售经理：我们区域的销售也有增长，但是增速稍慢于其他区域。我们发现有些客户对产品B的反馈意见，主要是在性能方面的问题。这导致我们需要更多的售后服务，增加了一定的成本。另外，由于竞争对手在我们区域加大了市场推广，我们需要采取更有针对性的广告宣传，以提升产品知名度。

张总经理：谢谢陈经理和刘经理的分享。看来产品质量和售后服务对销售业绩有一定的影响，同时市场推广也很关键。我们需要加强对产品质量的监控，同时提高售后服务的水平。王总，请你研究一下市场调研报告，提出针对北区市场的具体推广方案。

王市场总监：好的，我会尽快进行市场调研，与销售团队合作，制定北区市场的推广计划，并加强与渠道商的合作，提升产品在该区域的竞争力。

赵财务总监：对于成本方面，我们可以考虑优化物流渠道，寻找更经济高效的运输方式，以降低运输成本。另外，可以与供应商进行谈判，争取一定的优惠，降低原材料成本。

张总经理：很好，大家提出的建议都很中肯。让我们制定一个具体的行动计划，分别解决产品质量、售后服务、市场推广和成本控制等问题。每个部门负责人请提出相应的目标和措施，并设定落实时间节点。

（会议进行了约三个小时，详细讨论了各项问题的解决方案，并分工明确。最后总结了会议要点。）

张总经理：非常感谢大家的积极参与和精彩讨论。通过这次会议，我们明确了下半年的工作方向和目标。希望大家共同努力，全力以赴，实现更优异的销售业绩！会议到此结束。
        """

    def example_answer(self):
        return """
会议时间:2023年7月15日
张总经理感谢大家的辛勤工作，并介绍了会议主题：2023年上半年销售业绩复盘分析。
李销售总监汇报了上半年销售数据，总销售额1.5亿，增长15%。产品A销售额增长10%，产品B销售额增长20%。
王市场总监指出市场份额下降，特别是北区，竞争对手推出类似产品并大量宣传。
赵财务总监提到销售成本上升，主要是运输和物流费用增加，影响利润率。
张总经理要求陈经理和刘经理分别谈区域销售情况和挑战。
陈区域销售经理表示销售不错，但竞争对手价格战导致销售份额下降。
刘区域销售经理提到销售增速稍慢，客户对产品B性能有反馈，需要更多售后服务。
张总经理总结原因，需要加强产品质量、提高售后服务水平，并制定北区市场推广方案。
王市场总监将进行市场调研，制定北区市场推广计划，加强与渠道商合作。
赵财务总监提出优化物流渠道、降低运输成本，与供应商谈判降低原材料成本。
张总经理要求各部门负责人制定行动计划，解决产品质量、售后服务、市场推广和成本控制等问题。
会议讨论了约三个小时，详细讨论问题解决方案，并分工明确。
张总经理感谢大家的参与和讨论，明确下半年工作方向和目标，希望实现更优异的销售业绩。会议结束。    
        """

    @property
    def ai_example_btn(self):
        return FeishuMessageButton(
            _('查看示例'),
            type='default',
            value={'example': 1},

        )

    @property
    def ai_answer_btn(self):
        return FeishuMessageButton(
            _('精炼后的会议纪要'),
            type='primary',
            value={'answer': 1},
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**嗨! 我是会议小秘书~ 告诉我会议的文字稿，我会帮你整理出精彩的会议纪要**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('📖 **查看示例**\n文本回复 *示例* 或 */example*'),
                    tag='lark_md',
                    extra=self.ai_example_btn
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def send_example_question(self):
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**示例：会议文字稿**') + '\n' + self.example_question(),
                    tag='lark_md'
                ),
                FeishuMessageHr(),
                FeishuMessageAction(self.ai_answer_btn),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('提醒：示例中的会议内容，仅供参考。'))
                ),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/example' or input[:2] == '示例':
            return 'example',
        elif not input and action:
            if action['tag'] == 'button':
                if 'example' in action['value']:
                    return 'example',
                elif 'answer' in action['value']:
                    return 'answer',
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            real_model_name = m.get(model_name, model_name)
            if real_model_name in ['tts-1', 'tts-1-hd']:
                return send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageAction(self.voice_select),
                        FeishuMessageNote(
                            FeishuMessagePlainText(_('提醒：选择不同的声音，找到一种与你想要的音调和听众相匹配的声音。'))
                        ),
                        header=FeishuMessageCardHeader(_('🚀 声音切换'), template='blue'),
                    )
                )
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择模型：**%(model)s**', model=real_model_name),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_example(self):
        return self.send_example_question()

    def on_answer(self):
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**精炼后的会议纪要**') + '\n' + self.example_answer(),
                    tag='lark_md'
                ),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('提醒：示例中的会议内容，仅供参考。'))
                ),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class MeetingSummaryAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = '会议总结'
        title: str = '会议摘要助手'
        title_en: str = 'Meeting Summary'
        description: str = '📄 结合业务精炼会议内容'
        description_en: str = '📄 Refining meeting content in combination with business'
        problem: str = '会议时间过长，无法抓住要点？'
        problem_en: str = 'Meetings are too long to get to the point'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/HBDydIY85oRcnTxhPQ1c7LFVnad'
        manual_en: str = 'https://q5o2cctqdb7.sg.larksuite.com/docx/ScfbdImBjoFIO6xsqhRlsaB6gbu'
        icon: str = 'https://pic1.forkway.cn/cdn/202308211256647.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/202308211256647.png?imageMogr2/thumbnail/720x'
        sorted: int = 102
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['OpenAI', 'Azure', '文心一言']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnJ3Un9pRrEsmwGQ1DMLCXdc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/MkyswwkRRiJgKRkoY0lc73S9npc'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='meeting_summary_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='meeting_summary_dingding_command', tool_input=kwargs, log="")
        return AgentAction(tool='MeetingSummary', tool_input=kwargs, log="")
