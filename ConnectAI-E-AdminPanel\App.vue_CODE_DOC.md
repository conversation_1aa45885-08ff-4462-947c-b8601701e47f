# 代码文档 - src/App.vue

## 文件作用
Vue应用的根组件，配置全局主题、国际化和路由系统。

## 模板结构
- **n-config-provider**: Naive UI全局配置提供者
  - 主题配置: `theme.naiveTheme`
  - 主题覆盖: `theme.naiveThemeOverrides`
  - 语言配置: `langValue`
  - 日期语言: `dateValue`
- **naive-provider**: 自定义Naive UI提供者
- **router-view**: Vue Router视图容器

## 核心功能

### 国际化支持
- **支持语言**:
  - zh_CN: 中文简体 (zhCN, dateZhCN)
  - vi_VN: 越南语 (viVN, dateViVN)
  - default: 英语 (enUS, dateEnUS)

### 语言检测和设置
- **browserLanguage()**: 检测浏览器语言
- **hasCookie()**: 检查语言Cookie是否存在
- **自动设置**: 首次访问时自动设置语言Cookie
- **getCookieValue()**: 获取当前语言设置

### 主题管理
- **useThemeStore()**: 主题状态管理
- **动态主题**: 支持主题切换
- **主题覆盖**: 自定义主题样式

### 全局初始化
- **subscribeStore()**: 订阅状态变化
- **useGlobalEvents()**: 注册全局事件监听

## 技术特点
- 基于Composition API
- 响应式主题系统
- 自动语言检测
- Cookie持久化语言设置
- 全局状态管理集成

## 依赖模块
- **naive-ui**: UI组件库
- **@/store**: 状态管理
- **@/composables**: 组合式函数
- **@/utils**: 工具函数
