from core.base_handler import (
    arguments,
    authenticated
)

from core.utils import ObjIDStr
from modules.account.model import NewAdminModel

from .base_handler import BaseHandler

# 1. 账号密码创建新用户
class NewAccountHandler(BaseHandler):

    #post /admin/account
    @authenticated
    @arguments
    async def post(
        self,
        user_id: ObjIDStr = "", #占位
        name: str = '',
        password: str = '',
        model: NewAdminModel = None,
    ):
        await model.register_admin(name,password)

        self.finish({
            'code': 0,
            'msg': '用户创建成功',
        })


    #get /admin/account
    @authenticated
    @arguments
    async def get(
        self,
        user_id: ObjIDStr = "", #占位
        page: int = 1, 
        page_size: int = 10,
        model: NewAdminModel = None
    ):
        admin_list, total = await model.get_admin_list(page, page_size)

        # 当提供的page和page_size不合法时，返回空列表（get_tenant_list处理）
        self.finish({
            "code": 0,
            "msg": "success",
            "page": page,
            "page_size": page_size,
            "total": total,
            "data": [
                {
                    "user_id": admin["id"],
                    "name": admin["name"],
                    "is_on": admin["is_on"]

                } for admin in admin_list
            ]
        })


    #put /admin/account/{admin_id}
    @authenticated
    @arguments
    async def put(
        self, 
        user_id: ObjIDStr = "", 
        name: str = "",
        password: str = "",
        is_on: str = "",
        model: NewAdminModel = None
    ):
        # for arg in [name, password, is_on]:
        #     if arg == "" or arg is None:
        #         self.finish({
        #             "code": 1,
        #             "msg": "参数不能为空",
        #         })

        await model.update_admin(user_id, name, password,is_on)

        self.finish({
            "code": 0,
            "msg": "success",
        })

    
class AdminLoginHandler(BaseHandler):

    #post /admin/account/login
    @arguments
    async def post(
        self,
        name: str = '',
        password: str = '',
        model: NewAdminModel = None,
    ):
        admin_id = model.login(name,password)
        self.current_user = admin_id
        self.gen_session_id(user_id=admin_id)
        self.finish({
            'code': 0,
            'msg': '登录成功',
        })

class AdminLogoutHandler(BaseHandler):

    #post /admin/account/logout
    @authenticated
    @arguments
    async def post(self, model: NewAdminModel = None):
        # 直接清除cookie即可,没有cookie拿不到session,session自己过期
        self.clear_all_cookies(path='/')
        self.finish({
            'code': 0,
            'msg': 'success',
        })


