from .handler import (
    ResourceCategoryHandler,
    ResourceHandler,
    TenantResourceHandler,
    TenantResourceV1Handler,
    ResourcePriceHandler,
    ApplicationCategoryHandler,
    ApplicationHander,
    ApplicationShareHandler,
    TenantApplicationHander,
    ApplicationResourceHandler,
    ApplicationResourcesHandler,
    ApplicationClientHandler,
    ApplicationClientSettingHandler,
    ApplicationSettingHandler,
    ApplicationInfoHandler,
    ApplicationShopHandler,
    KnowledgeCode2SessionHandler,
    KnowledgeHandler,
    AppInstanceClientSettingHandler,
    AppInstanceSettingHandler,
    AppInstanceInfoHandler,
    KnowledgeListHandler,
    KnowledgeAppHandler,
)

urls = [
    # 资源相关接口
    (r"/api/resource/category", ResourceCategoryHandler),
    (r"/api/resource", ResourceHandler),
    (r"/api/resource/([0-9a-z]{24})/(active|start|stop)", ResourceHandler),
    (r"/api/resource/([0-9a-z]{24})/price", ResourcePriceHandler),
    (r"/api/resource/([0-9a-z]{24})", TenantResourceHandler),
    (r"/api/v1/resource/([0-9a-z]{24})", TenantResourceV1Handler),
    # 应用相关接口
    # 应用市场接口(无需登录)
    (r"/api/app/shop", ApplicationShopHandler),
    (r"/api/app/category", ApplicationCategoryHandler),
    (r"/api/app", ApplicationHander),
    (r"/api/app/([0-9a-z]{24})", ApplicationHander),
    (r"/api/app/([0-9a-z]{24})/(buy|deploy)", ApplicationHander),
    # 应用信息接口(无需登录)
    (r"/api/app/share/([0-9a-z]{24})", ApplicationShareHandler),
    # 租户应用广场接口
    (r"/api/tenant/app", TenantApplicationHander),

    # 应用配置相关接口
    # 当前应用支持的资源列表，参数为：<instance_id>
    (r"/api/app/([0-9a-z]{24})/resource", ApplicationResourceHandler),
    # 当前应用支持的多资源列表
    (r"/api/app/([0-9a-z]{24})/resources", ApplicationResourcesHandler),
    # 当前应用支持的客户端列表，当前主要是飞书机器人，参数为<instance_id>，启用机器人
    (r"/api/app/([0-9a-z]{24})/client", ApplicationClientHandler),
    # 配置机器人配置信息，获取以及保存
    (r"/api/app/([0-9a-z]{24})/client/([0-9a-z]{24})", ApplicationClientSettingHandler),
    # 配置应用，分为查询配置以及更新配置
    (r"/api/app/([0-9a-z]{24})/setting", ApplicationSettingHandler),
    (r"/api/app/([0-9a-z]{24})/info", ApplicationInfoHandler),
    # 知识库接口：本来知识库可以独立，并做跳转登录，但是这里在后台的单页面应用做跳转感觉不合适
    # 所以做一次转发，后端默认做好知识库那边的权限认证
    (r"/api/code2session", KnowledgeCode2SessionHandler),
    (r"/api/collection(.*)", KnowledgeHandler),
    (r"/api/file", KnowledgeHandler),
    (r"/api/upload", KnowledgeHandler),
    # 新的按instance_id的一套接口
    (r"/api/instance/([0-9a-z]{24})/client/([0-9a-z]{24})", AppInstanceClientSettingHandler),
    (r"/api/instance/([0-9a-z]{24})/setting", AppInstanceSettingHandler),
    (r"/api/instance/([0-9a-z]{24})/info", AppInstanceInfoHandler),
    # 新的知识库接口
    (r"/api/app/knowledge", KnowledgeAppHandler),
    (r"/api/knowledge", KnowledgeListHandler),
    # 这个支持delete动作
    (r"/api/instance/([0-9a-z]{24})", AppInstanceInfoHandler),
]
