'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M10.142 10.5H6.86L8.5 5.815l1.641 4.685zm.526 1.5l.33.944l1.178-1.177l-2.873-8.198c-.266-.759-1.339-.759-1.605 0L4.042 14.003a.75.75 0 0 0 1.416.496L6.333 12h4.335zm5.14-2.452l-4.829 4.83a2.197 2.197 0 0 0-.578 1.02l-.374 1.498a.916.916 0 0 0-.024.14a4.601 4.601 0 0 1-1.111-.088c-.073-.017-.1-.11-.066-.178c.18-.348.233-1.073-.404-1.33c-.86-.345-1.978.125-2.862.498c-.366.154-.692.29-.944.346c-.387.086-.848-.065-1.216-.249c-.212-.106-.482.082-.36.286c.219.366.614.737 1.326.825c.82.102 1.391-.152 1.975-.41c.4-.178.805-.358 1.3-.428c.086-.012.145.09.112.17c-.152.357-.133.894.316 1.244c.518.405 2.191.511 3.313.183l1.221-.305c.387-.097.74-.296 1.021-.578l4.83-4.83a1.87 1.87 0 0 0-2.645-2.644z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DrawText20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
