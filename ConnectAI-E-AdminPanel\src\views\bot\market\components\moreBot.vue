<template>
  <div>
    <section class="">
      <div class="max-w-screen-xl px-4 py-8 mx-auto text-center lg:py-12 lg:px-8">
        <figure class="max-w-screen-md mx-auto">
          <svg
            class="h-12 mx-auto mb-3 text-gray-400 dark:text-gray-600"
            viewBox="0 0 24 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.038 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z"
              fill="currentColor"
            />
          </svg>
          <blockquote>
            <p class="text-2xl font-medium text-gray-900 dark:text-white">
              "{{ $t('message.market.River') }}"
            </p>
          </blockquote>
          <figcaption class="flex items-center justify-center my-6 space-x-3">
            <img class="w-6 h-6 rounded-full" src="/images/author.png" alt="profile picture" />
            <div class="flex items-center divide-x-2 divide-gray-500 dark:divide-gray-700">
              <div class="pr-3 font-medium text-gray-900 dark:text-white">River</div>
              <div class="pl-3 text-sm font-light text-gray-500 dark:text-gray-400">FeiShu-OpenAI {{ $t('message.market.author') }}</div>
            </div>
          </figcaption>
          <a
            v-if="isLark"
            href="https://wenjuan.feishu.cn/m?t=siacrdk5JcPi-39rp"
            target="_blank"
            class="py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
          >
          {{ $t('message.market.sqsjgd') }}
          </a>
          <a
            v-else
            href="https://wenjuan.feishu.cn/m?t=sXDhkm8JS8Li-th5e"
            target="_blank"
            class="py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
          >
          {{ $t('message.market.sqsjgd') }}
          </a>
        </figure>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { isLark } from '@/utils';

</script>

<style scoped></style>
