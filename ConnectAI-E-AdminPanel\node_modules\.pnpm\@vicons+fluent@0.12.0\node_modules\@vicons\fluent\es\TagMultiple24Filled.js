import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.75 2a3.25 3.25 0 0 0-2.242.898L3.696 9.395a2.25 2.25 0 0 0-.034 3.223l6.256 6.226a2.25 2.25 0 0 0 3.166.01l6.945-6.835c.621-.61.97-1.445.97-2.316V4.75A2.75 2.75 0 0 0 18.25 2h-5.498zm3.5 5.75a1.25 1.25 0 1 1 0-2.5a1.25 1.25 0 0 1 0 2.5zm3.006 6.433l1.475-1.451c.045-.044.089-.09.132-.136a2.75 2.75 0 0 1-.691 2.813l-5.334 5.229a4.75 4.75 0 0 1-6.666-.016l-4.356-4.31a2.75 2.75 0 0 1-.681-2.808l1.629 1.62c.***************.107.121l2.411 2.386l1.931 1.922a3.251 3.251 0 0 0 4.575.014l5.334-5.229c.05-.049.094-.1.134-.155z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagMultiple24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
