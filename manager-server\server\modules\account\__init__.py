
from .handler import (
    Account<PERSON>andler,
    AccountCodeHandler,
    PromptCategoryHandler,
    PromptHandler,
    PromptImportHandler,
    SensitiveHandler,
    ChatLogHandler,
    TenantHandlerHandler,
    ProductHandler,
    OrderHandler,
    WXPayHandler,
    WXPayCallbackHandler,
    TenantSeatHandler,
    TenantSeatParseHandler,
    TenantSeatImportHandler,
    TenantSeatDepartmentHandler,
    TenantSeatConfigHandler,
)

urls = [
    # 登录相关接口
    # 1. 手机号/邮箱+密码-->登录        默认
    # 2. 手机号+验证码（注册或者登录）  快捷登录
    # 3. 邮箱+验证码+密码-->注册        邮箱注册
    (r"/api/account/login", AccountHandler),
    (r"/api/account/logout", AccountHandler),
    (r"/api/account/code", AccountCodeHandler),
    (r"/api/account/update", AccountHandler), # 修改租户信息
    (r"/api/account/info", AccountHandler), # 获取租户信息
    # 提示词接口
    (r"/api/prompt/category", PromptCategoryHandler),
    (r"/api/prompt", PromptHandler),
    (r"/api/prompt/([0-9a-z]{24})", PromptHandler),  # 添加＋删除
    (r"/api/prompt/export", PromptHandler, dict(export=True)),
    (r"/api/prompt/import", PromptImportHandler),
    # 敏感词接口
    (r"/api/sensitive", SensitiveHandler),
    (r"/api/sensitive/([0-9a-z]{24})", SensitiveHandler),  # 上线下线
    (r"/api/sensitive/([0-9a-z]{24})/(start|stop)", SensitiveHandler),  # 上线下线
    (r"/api/sensitive/export", SensitiveHandler, dict(export=True)),
    # 对话日志
    (r"/api/chatlog", ChatLogHandler),
    (r"/api/chatlog/export", ChatLogHandler, dict(export=True)),
    # 帐号权限控制（这里是针对租户的）
    (r"/api/tenant/handler", TenantHandlerHandler),
    # 套餐接口
    (r"/api/product", ProductHandler),
    (r"/api/order", OrderHandler),
    (r"/api/wepay", WXPayHandler),
    (r"/api/wepay/callback", WXPayCallbackHandler),
    # 座席相关接口，搜索创建编辑删除
    (r"/api/seats", TenantSeatHandler),
    # 解析导入文件，返回解析出用户列表
    (r"/api/seats/parse", TenantSeatParseHandler),
    # 导入用户（使用列表导入）
    (r"/api/seats/import", TenantSeatImportHandler),
    (r"/api/seats/department", TenantSeatDepartmentHandler),
    (r"/api/seats/config", TenantSeatConfigHandler),
]
