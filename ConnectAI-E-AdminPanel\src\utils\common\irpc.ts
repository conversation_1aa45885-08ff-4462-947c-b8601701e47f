interface IRpcOptions {
  container: HTMLIFrameElement;
  action: string;
  onMessage?: (message: string) => void;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  onComplete?: (results: any[]) => void;
  timeout?: number;
}

export const IRpc = ({
  container,
  action,
  onMessage,
  onSuccess,
  onError,
  onComplete,
  timeout
}: IRpcOptions): Promise<any> => {
  return new Promise((resolve, reject) => {
    const callback_name = `irpc_${Math.random().toString().substr(2)}`;
    const results: any[] = [];
    // 设置超时时间
    const t = timeout
      ? setTimeout(() => {
          (window as any)[callback_name]({ error: '超时，请重试' });
        }, timeout)
      : 0;
    (window as any)[callback_name] = (response: any) => {
      results.push(response);
      const { message, result, error } = response;
      if (message) {
        onMessage?.(message);
      }
      if (result) {
        resolve(result);
        onSuccess?.(result);
        onComplete?.(results);
        delete (window as any)[callback_name];
        clearTimeout(t);
      }
      if (error) {
        reject(error);
        onError?.(error);
        onComplete?.(results);
        delete (window as any)[callback_name];
        clearTimeout(t);
      }
    };
    // console.log(callback_name, window[callback_name])
    const formatAction = `${action + (action.indexOf('?') > -1 ? '&' : '?')}callback=${callback_name}`;
    container.src = formatAction;
    // TODO 使用form提交
  });
};
