import type { PlasmoMessaging } from "@plasmohq/messaging"
import { getCookieAndToken } from '~utils/browser'
import { Configuration, FeishuLoginCookies } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";


const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  // get people
  const { domain } = req.body
  const params = await getCookieAndToken(domain)
  console.log('get people', params, req)
  const configCookie = new Configuration({ domain });
  // 这里手动设置csrftoken
  configCookie.csrfToken = params.csrf_token
  const app = new OpenApp(configCookie);
  const response = await app.peopleManager.getAllPeople();

  res.send({ response })
}

export default handler

