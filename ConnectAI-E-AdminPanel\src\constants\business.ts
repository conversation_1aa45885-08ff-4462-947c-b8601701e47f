import { t } from '@/locales';

export const loginModuleLabels: Record<UnionKey.LoginModule, string> = {
  'pwd-login': t('message.global.pwdlogin'),
  'code-login': t('message.global.codelogin'),
  register: t('message.global.register'),
  'reset-pwd': t('message.global.repwd'),
  'bind-wechat': t('message.global.wechat')
};

export const userRoleLabels: Record<Auth.RoleType, string> = {
  super: t('message.global.superadmin'),
  admin: t('message.global.admin'),
  user: t('message.global.user')
};

export const userRoleOptions: Common.OptionWithKey<Auth.RoleType>[] = [
  { value: 'super', label: userRoleLabels.super },
  { value: 'admin', label: userRoleLabels.admin },
  { value: 'user', label: userRoleLabels.user }
];

/** 用户性别 */
export const genderLabels: Record<UserManagement.GenderKey, string> = {
  0: t('message.global.woman'),
  1: t('message.global.man')
};

export const genderOptions: Common.OptionWithKey<UserManagement.GenderKey>[] = [
  { value: '0', label: genderLabels['0'] },
  { value: '1', label: genderLabels['1'] }
];

/** 用户状态 */
export const userStatusLabels: Record<UserManagement.UserStatusKey, string> = {
  1: t('message.global.use'),
  2: t('message.global.ban'),
  3: t('message.global.ice'),
  4: t('message.global.delete')
};

export const userStatusOptions: Common.OptionWithKey<UserManagement.UserStatusKey>[] = [
  { value: '1', label: userStatusLabels['1'] },
  { value: '2', label: userStatusLabels['2'] },
  { value: '3', label: userStatusLabels['3'] },
  { value: '4', label: userStatusLabels['4'] }
];

/* 增值版本 */

export const UPGRADE_LABEL = t('message.global.zzfw');

// 企联AI助手插件下载页面
export const PLUGIN_DOWNLOAD_URL = 'https://connect-ai.feishu.cn/docx/N4uCdhqPyoir9rxuAk2cudxMnBb';
