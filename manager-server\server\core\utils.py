import re
import urllib
import warnings
import asyncio
import os, stat
import json
import base64
import logging
import bcrypt
import random
import string
import subprocess
import httpx
import tornado.locale
from functools import partial
from copy import deepcopy
from os.path import join, dirname
from tempfile import NamedTemporaryFile
from tornado.options import options
from datetime import datetime, time
from decimal import Decimal
from sqlalchemy.orm.collections import InstrumentedList
from sqlalchemy.orm.attributes import QueryableAttribute, InstrumentedAttribute
from sqlalchemy.orm.base import instance_state

from .downloader import ChunkedDownloader
from .schema import Base, bson
from .exception import ParametersError
from PIL import Image, ImageFont, ImageDraw
from Crypto.Cipher import AES
from typing import Dict, Any
from io import BytesIO

warnings.filterwarnings("ignore", category=DeprecationWarning)

# 加载i18n
i18n_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'locales')
tornado.locale.load_translations(i18n_path)
tornado.locale.set_default_locale('zh_CN')


def _(text, lang=options.DEFAULT_LOCALE, **kwargs):
    user_locale = tornado.locale.get(lang)
    if '：' in text:
        text = user_locale.translate(text) % kwargs
        return '：'.join([user_locale.translate(t) % kwargs for t in text.split('：')])
    if '\n' in text:
        return '\n'.join([user_locale.translate(t) % kwargs for t in text.split('\n')])
    return user_locale.translate(text) % kwargs


def row2dict(row, lang=''):
    """将对象(一般为orm row)转换为dict"""
    record = {}
    # 清除掉过期状态，强制的跳过state._load_expired(state, passive)
    # 如果有字段确实需要而没有的，要么设置default值，要么使用refresh从数据库拿到server_default值
    state = instance_state(row)
    state.expired_attributes.clear()
    attributes, cls = deepcopy(row.__dict__), row.__class__
    for c in dir(row):
        if hasattr(cls, c):
            a = getattr(cls, c)
            # hybrid_property
            if isinstance(a, QueryableAttribute) and not isinstance(a, InstrumentedAttribute):
                attributes[c] = 1  # 这里只需要有attribute name就可以了

    for c in attributes:
        if not c.startswith('_') and 'metadata' != c:
            try:
                v = row.__getattribute__(c)
            except KeyError as e:  # https://github.com/zzzeek/sqlalchemy/blob/master/lib/sqlalchemy/orm/attributes.py#L579 这个函数可能会raise KeyError出来
                logging.exception(e)
                v = datetime.now() if c in ['created', 'modified'] else None
            if isinstance(v, Base):
                v = row2dict(v, lang=lang)
            elif isinstance(v, Decimal):
                v = int(v)
            # 特殊处理一下生日，以及开始时间结束时间
            elif c in ['start', 'end'] and row.__tablename__ in ['work', 'education']:
                v = v.strftime('%Y-%m')
            elif c in ['birthday'] and row.__tablename__ in ['user']:
                v = v.strftime('%Y-%m-%d')
            elif c in ['account_type'] and row.__tablename__ in ["admin_user"]:
                v = v.split(",")
            elif isinstance(v, datetime):
                v = v.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(v, time):
                v = v.isoformat()
            elif isinstance(v, InstrumentedList):
                v = list(map(lambda i: row2dict(i, lang=lang), v))
            elif isinstance(v, str) and '%' not in v:
                # 翻译字段
                v = _(v, lang=lang)
            record[c] = v

    return record


def chunks(l, n):
    """Yield successive n-sized chunks from l."""
    for i in range(0, len(l), n):
        yield l[i:i + n]


def format_time(t):
    return t.strftime('%Y-%m-%d %H:%M:%S')


class DateTimeStr(str):

    def __new__(cls, value, **kwargs):
        try:
            return datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logging.exception(e)
            raise ParametersError(value)
        return str(value, **kwargs)


class TimeStr(str):

    def __new__(cls, value, **kwargs):
        # 尝试两种格式
        try:
            return datetime.strptime(value, '%H:%M')
        except Exception as e:
            try:
                return datetime.strptime(value, '%H:%M:%S')
            except Exception as e:
                logging.exception(e)
                raise ParametersError(value)
        return str(value, **kwargs)


class DateStr(str):

    def __new__(cls, value, **kwargs):
        try:
            datetime.strptime(value, '%Y-%m-%d')
        except Exception as e:
            logging.exception(e)
            raise ParametersError(value)
        return str(value, **kwargs)


class ObjIDStr(str):

    def __new__(cls, value, **kwargs):
        if not bson.ObjectId.is_valid(value):
            raise ParametersError(value)
        return str(value, **kwargs)


class ExtendJSONEncoder(json.JSONEncoder):

    def default(self, obj):
        if isinstance(obj, Decimal):
            return int(obj) if obj == obj.to_integral_value() else float(obj)
        elif isinstance(obj, datetime):
            return format_time(obj)
        elif isinstance(obj, Base):
            return row2dict(obj)
        elif isinstance(obj, InstrumentedList):
            return [row2dict(i) for i in obj]

        return super(ExtendJSONEncoder, self).default(obj)


def json_encode(value) -> str:
    """JSON-encodes the given Python object."""
    # JSON permits but does not require forward slashes to be escaped.
    # This is useful when json data is emitted in a <script> tag
    # in HTML, as it prevents </script> tags from prematurely terminating
    # the javascript.  Some json libraries do this escaping by default,
    # although python's standard library does not, so we do it here.
    # http://stackoverflow.com/questions/1580647/json-why-are-forward-slashes-escaped
    return json.dumps(value, cls=ExtendJSONEncoder, ensure_ascii=False).replace("</", "<\\/")


def hash_password(password):
    return bcrypt.hashpw(password.encode("utf8"), bcrypt.gensalt())


def check_password(origin_password, hashed_password):
    return bcrypt.hashpw(origin_password.encode("utf8"), hashed_password.encode("utf8")).decode() == hashed_password


def gen_text():
    return ''.join(random.sample(string.ascii_letters + string.digits, 4))


def draw_lines(draw, num, width, height):
    for num in range(num):
        x1 = random.randint(0, width / 2)
        y1 = random.randint(0, height / 2)
        x2 = random.randint(0, width)
        y2 = random.randint(height / 2, height)
        draw.line(((x1, y1), (x2, y2)), fill='black', width=1)


def random_color():
    return random.randint(32, 127), random.randint(32, 127), random.randint(32, 127)


def gen_verification_code():
    file_path = os.path.abspath(os.path.dirname(__file__))
    code = gen_text()
    width, height = 120, 46
    im = Image.new('RGB', (width, height), 'white')
    font = ImageFont.truetype(os.path.join(file_path, "arial.ttf"), 40)
    draw = ImageDraw.Draw(im)
    for item in range(4):
        draw.text(
            (5 + random.randint(-3, 3) + 23 * item, 5 + random.randint(-3, 3)), text=code[item], fill=random_color(),
            font=font)
    draw_lines(draw, 4, width, height)
    return im, code


def aes_encrypt(data, key):
    BS = len(key)
    pad1 = lambda s: s + (BS - len(s) % BS) * chr(BS - len(s) % BS)
    Cryptor = AES.new(str.encode(key), AES.MODE_ECB)
    base64_data = str(base64.b64encode(data), encoding="utf8")
    ciphertext = Cryptor.encrypt((pad1(base64_data).encode("utf8")))
    return ciphertext


def aes_decrypt(data, key):
    data = base64.b64decode(data)
    unpad1 = lambda s: s[0:-ord(s[-1:])]
    aes = AES.new(str.encode(key), AES.MODE_ECB)
    decrypt_text = aes.decrypt(data)
    bin_decrypt_result = unpad1(decrypt_text)
    bin_decrypt_result = bin_decrypt_result.decode('utf8')
    plain_text = base64.b64decode(bin_decrypt_result)
    return json.loads(plain_text)


class ObjectDict(Dict[str, Any]):
    """Makes a dictionary behave like an object, with attribute-style access."""

    def __getattr__(self, name: str) -> Any:
        try:
            value = self[name]
            if isinstance(value, (list, tuple)):
                return [ObjectDict(i) if isinstance(i, dict) else i for i in value]
            return ObjectDict(value) if isinstance(value, dict) else value
        except KeyError:
            raise AttributeError(name)

    def __setattr__(self, name: str, value: Any) -> None:
        self[name] = value


def get_event_loop():
    try:
        loop = asyncio.get_event_loop()
    except Exception as e:
        logging.error(e)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop


def syncify(func):
    def call(*args, **kwargs):
        return get_event_loop().run_until_complete(
            partial(func, *args, **kwargs)()
        )
    return call


def asyncify(func):
    def wrapper(*args, **kwargs):
        return get_event_loop().run_in_executor(
            None,
            partial(func, *args, **kwargs)
        )
    return wrapper


def parse_urls(input):
    # 移除不可见字符(零宽字符等)
    input = re.sub(r'[\u200B\u200D]', '', input)
    input_urls = []
    for x in input.split():
        parsed_url = urllib.parse.urlparse(x)
        if parsed_url.scheme and parsed_url.netloc:
            input_urls.append(x)
    if not (input_urls and 'http' in input_urls[0]):
        return []
    return input_urls


def compress_image(image: bytes or str, quality=80, save_format='', **kwargs):
    if isinstance(image, str):
        img = Image.open(image)
    else:
        img = Image.open(BytesIO(image))
    max_width = int(kwargs.get('max_width', kwargs.get('max_size', 0)))
    max_height = int(kwargs.get('max_height', kwargs.get('max_size', 0)))
    if max_width and img.width > max_width:
        img.thumbnail((max_width, int(img.height * (max_width / img.width))))
    if max_height and img.height > max_height:
        img.thumbnail((int(img.width * (max_height / img.height)), max_height))
    save_format = save_format or img.format
    if img.format in ['PNG', 'WEBP'] and save_format == 'JPEG':
        img.convert('RGB')
    elif img.format == 'JPEG' and save_format == 'PNG':
        img.convert('RGBA')
    bytes_data = BytesIO()
    try:
        img.save(bytes_data, format=save_format, quality=quality)
    except Exception as e:
        img.save(bytes_data, format=img.format, quality=quality)
    logging.info('image compressed: %r', (img.size, img.format, img.mode, save_format))
    image_bytes = bytes_data.getvalue()
    img.close()
    return image_bytes


async def download_bytes(url, proxy=False):
    try:
        content = await ChunkedDownloader(proxy=proxy).download(url)
    except Exception as e:
        logging.exception(e)
        try:
            content = await ChunkedDownloader(proxy=proxy).download(url.replace('mpic.forkway.cn', 'proxy.aionekey.shop'))
        except Exception as e:
            logging.error(e)
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=600)
                content = response.content
    return content


async def upload_file(file_obj):
    # 如果是线上服务器，使用CDN，其他的（私有化部署）使用本地文件夹存储即可
    ext = file_obj['filename'].split('.').pop()
    upload_path = join(dirname(dirname(dirname(dirname(__file__)))), 'dist', options.OSS_CDN_PATH)
    with NamedTemporaryFile(prefix=f'{upload_path}/', suffix=f'.{ext}', delete=False) as f:
        os.chmod(f.name, stat.S_IRWXU | stat.S_IRWXO)
        f.write(file_obj['body'])
        f.close()
        # using oss storage image
        if options.DOMAIN in ['connect-ai-e.com', 'connectai-e.com']:
            url = f"{options.OSS_CDN_HOST}/{options.OSS_CDN_PATH}/{f.name.split('/').pop()}"
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.head(url, timeout=30)
                    if response.status_code != 200:
                        response = await client.head(url, timeout=30)
                    if response.status_code != 200:
                        raise NotFound('上传失败')
                    logging.info("debug head response %r", response)
            finally:
                os.unlink(f.name)
        else:
            url = f"{options.SCHEMA}://{options.DOMAIN}/{options.OSS_CDN_PATH}/{f.name.split('/').pop()}"
    return url


def run_command(cmd, input=""):
    rst = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, input=input.encode("utf-8"))
    assert rst.returncode == 0, rst.stderr.decode("utf-8")
    return rst.stdout.decode("utf-8")


def mp3_to_opus(content, ac=1, ar=16000):
    with NamedTemporaryFile(suffix='.mp3', delete=False) as f:
        target = f"{f.name}.opus"
        duration = 0
        try:
            os.chmod(f.name, stat.S_IRWXU | stat.S_IRWXO)
            f.write(content)
            f.close()
            result = run_command(f"ffmpeg -i {f.name} -acodec libopus -ac {ac} -ar {ar} {target} 2>&1")
            logging.info("debug %r", result)
            try:
                t = result.split("time=").pop().split(".")[0]
                d = datetime.strptime(t, '%H:%M:%S')
                dd = d - datetime(1900, 1, 1, 0, 0)
                duration = int(dd.total_seconds() * 1000)
            except Exception as e:
                logging.error(e)
            content = open(target, 'rb').read()
            return content, duration
        finally:
            os.unlink(f.name)
            os.unlink(target)

async def translate_to(text: str, api_key: str, dest: str = 'en', source: str = 'zh'):
    # https://open.feishu.cn/document/server-docs/ai/translation-v1/translate
    # "zh": 汉语；"zh-Hant": 繁体汉语；"en": 英语；"ja": 日语；"ru": 俄语；"de": 德语；"fr": 法语；"it": 意大利语；"pl": 波兰语；"th": 泰语；"hi": 印地语；"id": 印尼语；"es": 西班牙语；"pt": 葡萄牙语；"ko": 朝鲜语；"vi": 越南语；
    url = f'{options.SCHEMA}://translate.{options.PROXY_BASE_SERVER}/open-apis/translation/v1/text/translate'
    params = {
        'text': text,
        'source_language': source,
        'target_language': dest
    }
    headers = {
        'api-key': api_key
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(url, data=json.dumps(params), headers=headers, timeout=100)
        result = response.json()
        return result['data']['text']


def get_image_info(image: bytes or str):
    if isinstance(image, str):
        img = Image.open(image)
    else:
        img = Image.open(BytesIO(image))
    info = {'format': img.format.lower(), 'size': img.size}
    img.close()
    return info
