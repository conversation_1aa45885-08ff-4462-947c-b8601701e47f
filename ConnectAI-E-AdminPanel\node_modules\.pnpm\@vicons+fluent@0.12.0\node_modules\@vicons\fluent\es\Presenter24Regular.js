import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M20.245 12.997c.709 0 1.022.892.469 1.335l-4.718 3.778v1.644a2.25 2.25 0 0 1-2.25 2.25H10.25A2.25 2.25 0 0 1 8 19.754V18.11l-4.72-3.778c-.553-.443-.24-1.335.47-1.335h16.495zm-2.137 1.5H5.887l3.332 2.667a.75.75 0 0 1 .281.585v2.005c0 .414.336.75.75.75h3.496a.75.75 0 0 0 .75-.75V17.75a.75.75 0 0 1 .281-.585l3.331-2.667zM8.75 9h6.495a1.75 1.75 0 0 1 1.744 1.606l.006.143V12h-1.5v-1.25a.25.25 0 0 0-.192-.243l-.058-.007H8.75a.25.25 0 0 0-.243.193l-.007.057V12H7v-1.25a1.75 1.75 0 0 1 1.607-1.744L8.75 9h6.495H8.75zM12 2a3 3 0 1 1 0 6a3 3 0 0 1 0-6zm0 1.5a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Presenter24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
