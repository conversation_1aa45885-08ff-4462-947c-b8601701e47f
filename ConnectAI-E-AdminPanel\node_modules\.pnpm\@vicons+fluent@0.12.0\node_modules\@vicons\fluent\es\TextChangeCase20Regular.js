import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.491 3.5a.5.5 0 0 1 .465.336l3.652 10.5a.5.5 0 0 1-.944.328L15.737 12h-4.754l-1.016 2.677a.5.5 0 0 1-.928.017l.244-1.03l.99-2.61V11h.02l2.724-7.177a.5.5 0 0 1 .474-.323zm-.027 1.963L11.363 11h4.027l-1.926-5.537zm-8.57 3.224c-.486.019-.913.132-1.17.26a.5.5 0 0 1-.448-.894c.41-.205.983-.342 1.58-.365c.6-.023 1.272.067 1.868.365C8.004 8.693 8 9.96 8 10.463V14.5a.5.5 0 0 1-1 0v-.412c-.913.666-2.01 1.094-3.129.796c-1.884-.504-2.534-3.043-.649-4.3c.715-.476 1.584-.609 2.375-.569c.49.025.971.117 1.4.25c-.015-.479-.112-1.013-.72-1.318c-.405-.202-.9-.278-1.383-.26zM7 11.325a4.716 4.716 0 0 0-1.454-.31c-.66-.034-1.292.084-1.77.401c-1.115.744-.763 2.204.353 2.502c.902.24 1.938-.248 2.871-1.13v-1.463z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextChangeCase20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
