class SnapStoryApiTool(CTool):
    name: str = 'snap_story'
    description: str = 'snap story ai'
    platform_roles = {
        'moments': """你是一个社交媒体专家的角色，专注于为微信朋友圈生成吸引人的文案，你需要根据我发给你的图片，深入理解图片的内容，然后综合图片所展现出来的情感元素及故事性，或文艺或朴实，为我提供一条朋友圈文案，文案应该散发出吸引力，引发朋友们的互动，同时不会过于炫耀或者引人厌烦，可以适当加一些Emoji表情
以下是一个样例：
掠过香料市集，感受异域风情的馥郁。每一步都踏在飘散着历史的芳香上，这里，每一缕气息都能讲述一个故事。""",
        'xiaohongshu': """你是一个社交媒体专家的角色，专注于为小红书吸引人的文案，你需要根据我发给你的图片，深入理解图片的内容，然后综合图片所展现出来的情感元素及故事性，或文艺或朴实，为我提供一条小红书文案，文案应该散发出吸引力，引发朋友们的互动，同时不会过于炫耀或者引人厌烦。小红书的风格是：很吸引眼球的标题，每个段落都加 emoji，最后加一些 tag。"""
    }

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class SnapStoryCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs):
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                content = response.generations[0][0].text
                input_kwargs = data.extra.extra.get('input_kwargs', {})

                action_options = [
                    [_('生成朋友圈文案'), {'platform': 'moments', 'input_kwargs': input_kwargs}],
                    [_('生成小红书文案'), {'platform': 'xiaohongshu', 'input_kwargs': input_kwargs}],
                ]
                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageAction(
                                # FeishuMessageButton(_('复制文案'), type='default', value={'action': 'copy'}),
                                *[FeishuMessageButton(option[0], value=option[1]) for option in action_options]
                            ),
                            header=FeishuMessageCardHeader(_('🎉 专属配图文案已生成!'), template='blue')
                        ),
                    )
                elif platform == 'dingding':
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            # *[DingDingActionCardButton('dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/button ' + json.dumps(option[1]))), option[0]) for option in action_options],
                            title=_('🎉 专属配图文案已生成!'),
                            text=content
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any):
                logging.error('llm error: %r', error)
                if platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md')
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [SnapStoryCallbackHandler()]
        temperature = 1
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        del model['openai_api_type']
        input_kwargs = data.extra.extra.get('input_kwargs', {})
        if 'action' in data.extra.extra and data.extra.extra.action['tag'] == 'button':
            action_value = dict(data.extra.extra.get('action', {}).get('value', {}))
            if 'action' in action_value:
                return
            elif 'platform' in action_value:
                session.set_extra('platform', action_value['platform'])
                input_kwargs = action_value['input_kwargs']
                data['extra']['extra']['input_kwargs'] = input_kwargs
        _platform = session.extra.get('platform', 'moments')
        content = []
        if _platform in self.platform_roles:
            content.append({
                'type': 'text',
                'text': self.platform_roles[_platform]
            })
        if 'image_urls' in input_kwargs:
            for image_url in input_kwargs['image_urls']:
                content.append({
                    'type': 'image_url',
                    'image_url': {'url': image_url}  # {'url': '', 'detail': 'high|low'}
                })
        logging.info('chat contents: %r', content)
        messages = [HumanMessage(content=content)]
        chat = ChatOpenAI(
            temperature=temperature,
            model_name=model_name,
            max_tokens=800,
            **model,
        )
        return chat.invoke(messages)


class DingdingCommand(CommandTool):
    next_tool_name: str = 'snap_story'
    name: str = 'snap_story_dingding_command'
    description: str = 'snap story dingding command'
    platform_options = {'moments': '朋友圈', 'xiaohongshu': '小红书'}

    @property
    def platform_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value=k, content=v) for k, v in self.platform_options.items()],
            placeholder=_('选择朋友圈/小红书'),
            value={'command': 'platform'},
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/platform',
                    _('🌍 选择分享平台')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("👋 **你好呀，我是AI识图文案助手**")
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:9] == '/platform' or input[:2] == '平台':
            platform_name = input.replace('/platform', '').replace('平台', '').strip()
            return 'platform', platform_name
        elif input[:9] == '/button ':
            try:
                btn_value = json.loads(input.replace('/button '))
                data['extra']['extra']['input_kwargs'] = btn_value
            except Exception as e:
                logging.error(e)
            return None,
        if data.extra.message_type in ['picture', 'richText']:
            return data.extra.message_type, data.extra.extra.platform_content
        return 'note',

    def on_platform(self, platform_name=''):
        _platform = ''
        for k, v in self.platform_options.items():
            if platform_name.lower() in [k, v]:
                _platform = k
                break
        if not _platform:
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton('dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/platform ' + v)), v) for k, v in self.platform_options.items()],
                    text=_('🤖 选择分享平台'),
                    title=_('🤖 机器人提醒'),
                ),
            )
        session.set_extra('platform', _platform)
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_('🤖 机器人提醒'),
                text=_('已选择分享平台：%(platform)s', platform=self.platform_options[_platform]),
            )
        )

    def on_picture(self, platform_content):
        robot_code = bot.app_id
        download_code = platform_content.downloadCode
        image_url = syncify(client.get_message_resource)(robot_code, download_code)
        if not self.check_image_format(image_url):
            return self.on_note()
        data['extra']['extra']['input_kwargs'] = {'image_urls': [image_url]}
        return self.next_tool_name

    def on_richText(self, platform_content):
        robot_code = bot.app_id
        input_text, input_image_keys = '', []
        for msg in platform_content['richText']:
            if 'text' in msg and '@' not in msg and not input_text:
                input_text = msg['text'].strip()
            elif msg.get('type', '') == 'picture':
                input_image_keys.append(msg['downloadCode'])
        input_text = input_text.strip()
        if not input_image_keys:
            return self.on_note()
        image_urls = get_event_loop().run_until_complete(asyncio.gather(*[functools.partial(client.get_message_resource, robot_code, download_code)() for download_code in input_image_keys]))
        for image_url in image_urls:
            if not self.check_image_format(image_url):
                return self.on_note()
        data['extra']['extra']['input_kwargs'] = {'image_urls': image_urls}
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送图片，不支持自然语言（除帮助外）、文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送图片！')
        return send_note(text, title)

    def check_image_format(self, image_url):
        image_type = image_url.split('?')[0].split('.')[-1]
        if not image_type:
            return True
        return image_type.lower() in ['png', 'jpeg', 'jpg', 'webp', 'gif']



class FeishuCommand(CommandTool):
    next_tool_name: str = 'snap_story'
    name: str = 'snap_story_feishu_command'
    description: str = 'snap story feishu command'
    platform_options = {'moments': '朋友圈', 'xiaohongshu': '小红书'}
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入“平台”或者“/platform”可选择切换默认的平台',
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def platform_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value=k, content=v) for k, v in self.platform_options.items()],
            placeholder=_('选择朋友圈/小红书'),
            initial_option=session.extra.get('platform', list(self.platform_options.keys())[0]),
            value={'command': 'platform'},
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **你好呀，我是AI识图文案助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🌍 **选择分享平台**\n文本回复 *平台* 或 */platform*'),
                    tag='lark_md',
                    extra=self.platform_select
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒 需要帮助吗？'), template='blue')
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:9] == '/platform' or input[:2] == '平台':
            return 'platform',
        elif not input and action:
            if action['tag'] == 'button':
                return None,
            elif action['tag'] == 'select_static':
                return action['value']['command'], action['option']
        if data.extra.message_type in ['image', 'post']:
            return data.extra.message_type, data.extra.extra.platform_content
        return 'note',

    def on_platform(self, platform_name=''):
        _platform = ''
        for k, v in self.platform_options.items():
            if platform_name.lower() in [k, v]:
                _platform = k
                break
        if not _platform:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.platform_select),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🤖 选择分享平台'), template='blue'),
                )
            )
        session.set_extra('platform', _platform)
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageNote(
                    FeishuMessagePlainText(_('已选择分享平台：%(platform)s', platform=self.platform_options[_platform]))
                ),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_image(self, platform_content):
        message_id = data.extra.message_id
        image_key = platform_content.image_key
        if not self.check_image_format(message_id, image_key):
            return self.on_note()
        image_url = f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?message_id={message_id}&image_key={image_key}&auto_download='
        data['extra']['extra']['input_kwargs'] = {'image_urls': [image_url]}
        return self.next_tool_name

    def on_post(self, platform_content):
        message_id = data.extra.message_id
        input_text, input_image_keys = '', []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg.get('tag', '') == 'text':
                    input_text = msg['text']
                elif msg.get('tag', '') == 'img':
                    if not self.check_image_format(message_id, msg['image_key']):
                        return self.on_note()
                    input_image_keys.append(msg['image_key'])
        input_text = input_text.strip()
        if not input_image_keys:
            return self.on_note()
        image_urls = [f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?message_id={message_id}&image_key={image_key}&auto_download=' for image_key in input_image_keys]
        data['extra']['extra']['input_kwargs'] = {'image_urls': image_urls}
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送图片，不支持自然语言（除帮助外）、文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送图片！')
        return send_note(text, title)

    def check_image_format(self, message_id, image_key):
        image_type = ''
        try:
            image_bytes = syncify(client.get_message_resource)(message_id, image_key)
            image_info = get_image_info(image_bytes)
            image_type = image_info.get('format')
        except Exception as e:
            logging.error(e)
        if not image_type:
            return True
        return image_type.lower() in ['png', 'jpeg', 'jpg', 'webp', 'gif']


class SnapStoryApiAgent(CAgent):
    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'SnapStory'
        title: str = '图语AI'
        title_en: str = 'SnapStoryAI'
        description: str = '📷 让照片说故事，AI一键生成朋友圈文案📝，轻松分享，吸引眼球👀！'
        description_en: str = '📷 Let photos tell stories, AI generates copy for your circle of friends with one click📝, easy to share and attract attention👀!'
        problem: str = '解放你的发文困扰，让每张照片都变得有趣有故事，AI 图文匹配，秒变文案达人，朋友圈小红书随心畅享！'
        problem_en: str = 'Free you from the trouble of posting and make every photo interesting and story-telling. With AI image and text matching, you can become a copywriting expert in seconds. You can enjoy Xiaohongshu in your circle of friends as you like!'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/wiki/Ck1DwUg2iiDfREkMJndcZXx7nIc'
        manual_en: str = 'https://connect-ai.feishu.cn/wiki/VpGkwn39ti27E9kt493cCypVn4f?create_from=create_doc_to_wiki'
        icon: str = 'https://pic1.forkway.cn/cdn/20231129110444.png'
        logo: str = 'https://pic1.forkway.cn/cdn/20231129110444.png?imageMogr2/thumbnail/720x'
        sorted: int = 106
        support_resource: List[object] = [dict(
            category=ModelCategory.Vision.value,
            scene=ModelCategory.Vision.value,
            title='视觉',
            tip='',
            required=True,
            resource=['OpenAI']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcn8oPlGmsCQlhTAAVul5Eg7o'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/YeuiwfA6HiKA7dkUfXYc2pmhnug'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log='')
            return AgentFinish(
                {'output': last_result},
                'result for action {}: {}'.format(last_action.tool, str(last_result)))

        if platform == 'feishu':
            return AgentAction(tool='snap_story_feishu_command', tool_input=kwargs, log='')
        elif platform == 'dingding':
            return AgentAction(tool='snap_story_dingding_command', tool_input=kwargs, log='')
        return AgentAction(tool='snap_story', tool_input=kwargs, log='')
