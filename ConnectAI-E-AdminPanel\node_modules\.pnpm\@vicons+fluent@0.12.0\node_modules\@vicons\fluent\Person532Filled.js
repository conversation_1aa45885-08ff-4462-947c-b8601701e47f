'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M16 16a7 7 0 1 0 0-14a7 7 0 0 0 0 14zm-8.5 2A3.5 3.5 0 0 0 4 21.5v.5c0 2.394 1.523 4.418 3.685 5.794C9.859 29.177 12.802 30 16 30c1.142 0 2.251-.105 3.305-.303A2.5 2.5 0 0 1 21 26.05v-.415a2.499 2.499 0 0 1-1.739-2.622L19.74 18H7.5zm13.755-.095A1 1 0 0 1 22.25 17h6.25a1 1 0 1 1 0 2h-5.34l-.295 3.09l1.91-.26a4.606 4.606 0 1 1 .62 9.17h-.617a5.222 5.222 0 0 1-3.532-1.375l-.422-.388a1 1 0 0 1 1.352-1.474l.423.388a3.222 3.222 0 0 0 2.179.849h.616a2.606 2.606 0 1 0-.35-5.188l-3.16.429a1 1 0 0 1-1.13-1.086l.5-5.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Person532Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
