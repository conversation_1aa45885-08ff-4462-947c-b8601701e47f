import bson
import json
import logging
from sqlalchemy import (
    Column, TIMESTAMP, Integer, String, Text,
    text, BINARY, Time,
    Float, DECIMAL, ForeignKey,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.hybrid import hybrid_property
from datetime import datetime


B = declarative_base()


class ObjID(BINARY):
    """基于bson.ObjectId用于mysql主键的自定义类型"""
    def bind_processor(self, dialect):
        def processor(value):
            return bson.ObjectId(value).binary if bson.ObjectId.is_valid(value) else value

        return processor

    def result_processor(self, dialect, coltype):
        def processor(value):
            return str(bson.ObjectId(value)) if bson.ObjectId.is_valid(value) else value

        return processor

    @staticmethod
    def new_id():
        return str(bson.ObjectId())

    @staticmethod
    def is_valid(value):
        return bson.ObjectId.is_valid(value)


class JSONStr(String):
    """自动转换 str 和 dict 的自定义类型"""
    def bind_processor(self, dialect):
        def processor(value):
            try:
                if isinstance(value, str) and (value[0] == '%' or value[-1] == '%'):
                    # 使用like筛选的情况
                    return value
                return json.dumps(value, ensure_ascii=False)
            except Exception as e:
                logging.error(e)
                return value
        return processor

    def result_processor(self, dialect, coltype):
        def processor(value):
            try:
                return json.loads(value)
            except Exception as e:
                logging.error(e)
                return value
        return processor

    @staticmethod
    def is_valid(value):
        try:
            json.loads(value)
            return True
        except Exception as e:
            logging.error(e)
            return False


class JSONText(Text):
    """自动转换 str 和 dict 的自定义类型"""
    def bind_processor(self, dialect):
        def processor(value):
            try:
                if isinstance(value, str) and (value[0] == '%' or value[-1] == '%'):
                    # 使用like筛选的情况
                    return value
                return json.dumps(value, ensure_ascii=False)
            except Exception as e:
                logging.error(e)
                return value
        return processor

    def result_processor(self, dialect, coltype):
        def processor(value):
            try:
                return json.loads(value)
            except Exception as e:
                logging.error(e)
                return value
        return processor

    @staticmethod
    def is_valid(value):
        try:
            json.loads(value)
            return True
        except Exception as e:
            logging.error(e)
            return False


class Base(B):
    """公共字段"""
    __abstract__ = True
    id = Column(ObjID(12), primary_key=True)

    status = Column(Integer, nullable=True, server_default=text("'0'"), comment="状态")
    created = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="创建时间")
    modified = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment="修改时间")


class Tenant(Base):
    """租户信息表"""
    __tablename__ = 'tenant'
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    display_name = Column(String(128), nullable=True, server_default=text("''"), comment="展示名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="备注")
    # 账户状态（0为已启用，1为已停用，-1为已删除）
    # 默认帐号0, 付费帐号1
    level = Column(Integer, nullable=True, server_default=text("'0'"), comment="账户等级")
    auto_add_seat = Column(Integer, nullable=True, server_default=text("'1'"), comment="自动添加座席")


class TenantAdmin(Base):
    """租户管理员表"""
    __tablename__ = 'tenant_admin'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="管理员ID")


class TenantWithKey(Tenant):
    apikey = Column(String(128), nullable=True, server_default=text("''"), comment="apikey")


class User(Base):
    """用户信息表"""
    __tablename__ = 'user'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    email = Column(String(128), nullable=True, server_default=text("''"), comment="邮箱")
    telephone = Column(String(128), nullable=True, server_default=text("''"), comment="手机号")
    department = Column(String(128), nullable=True, server_default=text("''"), comment="部门")
    remark = Column(String(512), nullable=True, server_default=text("''"), comment="备注")
    # 账户状态（0为已启用，1为已停用，-1为已删除）


class Account(User):
    passwd = Column(String(128), nullable=True, server_default=text("''"), comment="登录密码")


class AdminAccount(Base):
    """后台管理用户信息表"""
    __tablename__ = 'admin_account'
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    password = Column(String(128), nullable=True, server_default=text("''"), comment="登录密码")
    is_on = Column(String(128), nullable=True, server_default=text("''"), comment="管理员状态") # yes or no


class TenantSeat(Base):
    """座席"""
    __tablename__ = 'tenant_seat'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    unionid = Column(String(128), nullable=True, server_default=text("''"), comment="unionid")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    telephone = Column(String(128), nullable=True, server_default=text("''"), comment="手机号")
    department = Column(String(128), nullable=True, server_default=text("''"), comment="部门")
    info = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="用户其他信息")
    # status=1 为启用


class Sensitive(Base):
    """敏感词"""
    __tablename__ = 'sensitive'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    # 是否需要拆分到表
    category = Column(String(128), nullable=True, server_default=text("''"), comment="分类")
    name = Column(JSONStr(1024), nullable=True, server_default=text("'[]'"), comment="敏感词多个分隔")


class PromptCategory(Base):
    """提示词分类"""
    __tablename__ = 'prompt_category'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="敏感词")


class Prompt(Base):
    """提示词"""
    __tablename__ = 'prompt'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    category_id = Column(ObjID(12), ForeignKey('prompt_category.id'), nullable=True, comment="分类ID")
    title = Column(String(128), nullable=True, server_default=text("''"), comment="提示词标题")
    description = Column(String(256), nullable=True, server_default=text("''"), comment="提示词描述")
    content = Column(String(1024), nullable=True, server_default=text("''"), comment="提示词内容")
    example = Column(String(1024), nullable=True, server_default=text("''"), comment="提示词示例")


class ResourceCategory(Base):
    """资源类型"""
    __tablename__ = 'resource_category'
    name = Column(String(128), nullable=True, server_default=text("''"), comment="资源类型名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="资源类型描述")


class Resource(Base):
    """资源"""
    __tablename__ = 'resource'
    category_id = Column(ObjID(12), ForeignKey('resource_category.id'), nullable=True, comment="资源类型ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="模型名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="模型描述")
    icon = Column(String(256), nullable=True, server_default=text("''"), comment="模型图标")
    provider = Column(String(256), nullable=True, server_default=text("''"), comment="模型提供商")
    sorted = Column(Integer, nullable=True, server_default=text("'0'"), comment="排序")

    # 用于存储当前这个资源怎么进行配置（类似怎么定义前端表单逻辑）
    config = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="其他配置项")


class Model(Base):
    """模型"""
    __tablename__ = 'model'
    resource_id = Column(ObjID(12), ForeignKey('resource.id'), nullable=True, comment="资源ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="模型版本名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="模型版本描述")
    price = Column(Integer, nullable=True, default=0, server_default=text("0"), comment="版本价格")
    category = Column(String(128), nullable=True, server_default=text("''"), comment="模型类别")


class TenantResource(Base):
    """租户激活模型(资源)"""
    __tablename__ = 'tenant_resource'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    resource_id = Column(ObjID(12), ForeignKey('resource.id'), nullable=True, comment="资源ID")
    # 20230711 重新设计之后apikey放到这边保存
    api_key = Column(String(1024), nullable=True, server_default=text("''"), comment="apikey")
    api_base = Column(String(128), nullable=True, server_default=text("''"), comment="api_base")
    extra = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="其他配置项")


class ApplicationCategory(Base):
    """应用类型"""
    __tablename__ = 'application_category'
    name = Column(String(128), nullable=True, server_default=text("''"), comment="应用类型名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="应用类型描述")


class Application(Base):
    """应用"""
    __tablename__ = 'application'
    category_id = Column(ObjID(12), ForeignKey('application_category.id'), nullable=True, comment="应用类型ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="应用名称")
    icon = Column(String(256), nullable=True, server_default=text("''"), comment="应用图标")
    logo = Column(String(256), nullable=True, server_default=text("''"), comment="应用logo")
    title = Column(String(128), nullable=True, server_default=text("''"), comment="应用标题")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="应用描述")
    price = Column(Integer, nullable=True, default=0, server_default=text("0"), comment="应用价格")
    sorted = Column(Integer, nullable=True, server_default=text("'0'"), comment="排序")
    problem = Column(String(256), nullable=True, server_default=text("''"), comment="解决问题")
    video = Column(String(256), nullable=True, server_default=text("''"), comment="demo视频链接")
    manual = Column(String(256), nullable=True, server_default=text("''"), comment="应用云文档链接")
    title_en = Column(String(128), nullable=True, server_default=text("''"), comment="应用标题en")
    description_en = Column(String(512), nullable=True, server_default=text("''"), comment="应用描述en")
    problem_en = Column(String(256), nullable=True, server_default=text("''"), comment="解决问题en")
    video_en = Column(String(256), nullable=True, server_default=text("''"), comment="demo视频链接en")
    manual_en = Column(String(256), nullable=True, server_default=text("''"), comment="应用云文档链接en")
    feedback_url = Column(String(256), nullable=True, server_default=text("''"), comment="用户反馈收集url")
    show = Column(Integer, nullable=True, default=1, server_default=text("1"), comment="是否展示在应用市场")
    deploy = Column(String(256), nullable=True, server_default=text("''"), comment="应用配置文档")
    # 现在使用ApplicationSupportResource以及ApplicationSupportBot存储
    # settings = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="应用支持配置项")
    # 考虑到应用的数量后面是动态增长的。所以，将应用队列进行合并。按instance_id去初始化Agent处理用户输入


class ApplicationWithActionTemplate(Application):
    action_template = Column(Text, nullable=True, comment="快捷消息前端template")


class ApplicationWithWXSuite(Application):
    # id和secret是配置的时候就写好的
    wx_suite_id = Column(String(256), nullable=True, server_default=text("''"), comment="企微第三方应用id")
    wx_suite_secret = Column(String(256), nullable=True, server_default=text("''"), comment="企微第三方应用secret")
    # ticket会定时更新
    wx_suite_ticket = Column(String(256), nullable=True, server_default=text("''"), comment="企微第三方应用ticket")


class ApplicationImplementation(Base):
    """应用实现"""
    __tablename__ = 'application_implementation'
    application_id = Column(ObjID(12), ForeignKey('application.id'), nullable=True, comment="应用ID")
    version = Column(Integer, nullable=True, default=0, server_default=text("0"), comment="应用版本")
    hash = Column(String(128), nullable=True, server_default=text("''"), comment="代码hash")
    code = Column(Text, nullable=True, comment="自定义应用代码")


class ApplicationSupportResource(Base):
    """应用支持资源类型"""
    __tablename__ = 'application_support_resource'
    application_id = Column(ObjID(12), ForeignKey('application.id'), nullable=True, comment="应用ID")
    resource_id = Column(ObjID(12), ForeignKey('resource.id'), nullable=True, comment="资源ID")
    category = Column(String(128), ForeignKey('model.category'), nullable=True, comment="模型类别")
    scene = Column(String(128), nullable=True, server_default=text("''"), comment="多资源的场景")
    title = Column(String(128), nullable=True, server_default=text("''"), comment="scene的标题")
    tip = Column(String(256), nullable=True, server_default=text("''"), comment=" 资源tip")
    required = Column(Integer, nullable=True, server_default=text("'1'"), comment="是否必须")
    sorted = Column(Integer, nullable=True, server_default=text("'0'"), comment="排序")


class AppInstance(Base):
    """应用实例"""
    """一个租户下面，一个应用只能部署一个，不支持多个"""
    __tablename__ = 'app_instance'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    application_id = Column(ObjID(12), ForeignKey('application.id'), nullable=True, comment="应用ID")
    resource_id = Column(ObjID(12), ForeignKey('resource.id'), nullable=True, comment="资源ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="应用名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="应用描述")
    icon = Column(String(1024), nullable=True, server_default=text("''"), comment="应用图标")
    # 包含 resource_ids: {<application_support_resource.scene>: <resource.id>}
    extra = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="这个一些额外的配置")


class AppInstancePrompt(Base):
    """应用实例提示词关联表"""
    __tablename__ = 'app_instance_prompt'
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用实例ID")
    prompt_id = Column(ObjID(12), ForeignKey('prompt.id'), nullable=True, comment="提示词ID")


class AppInstanceSensitive(Base):
    """应用实例敏感词关联表"""
    __tablename__ = 'app_instance_sensitive'
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用实例ID")
    sensitive_id = Column(ObjID(12), ForeignKey('sensitive.id'), nullable=True, comment="敏感词ID")


class UserAccessAppInstance(Base):
    """用户访问应用实例关联表"""
    __tablename__ = 'user_access_app_instance'
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用实例ID")
    model_id = Column(ObjID(12), ForeignKey('model.id'), nullable=True, comment="模型版本ID")


class Bot(Base):
    """机器人表"""
    __tablename__ = 'bot'
    platform = Column(String(128), nullable=True, server_default=text("'feishu'"), comment="机器人平台")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="机器人名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="机器人描述")
    component = Column(String(128), nullable=True, server_default=text("''"), comment="机器人组件")


class BotInstance(Base):
    """机器人实例表"""
    __tablename__ = 'bot_instance'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    bot_id = Column(ObjID(12), ForeignKey('bot.id'), nullable=True, comment="平台ID")
    # instance_id = Column(ObjID(12), nullable=True, comment="应用实例ID")  # 这个需要
    name = Column(String(128), nullable=True, server_default=text("''"), comment="机器人名称")
    description = Column(String(128), nullable=True, server_default=text("''"), comment="机器人描述")
    info = Column(JSONStr(1024), nullable=True, server_default=text("'{}'"), comment="机器人信息（飞书可以接口获取）")

    # TODO 内置应用，加一个字段
    app_id = Column(String(128), nullable=True, comment="飞书app_id")
    app_secret = Column(String(128), nullable=True, comment="飞书app_secret")
    encript_key = Column(String(128), nullable=True, comment="飞书encript_key")
    validation_token = Column(String(128), nullable=True, comment="飞书validation_token")
    agent_id = Column(String(128), nullable=True, server_default=text("''"), comment="钉钉agent_id")
    crop_id = Column(String(128), nullable=True, server_default=text("''"), comment="企微crop_id")


class ApplicationSupportBot(Base):
    """应用支持的机器人列表"""
    __tablename__ = 'application_support_bot'
    application_id = Column(ObjID(12), ForeignKey('application.id'), nullable=True, comment="应用ID")
    bot_id = Column(ObjID(12), ForeignKey('bot.id'), nullable=True, comment="机器人ID")


class AppInstanceBotInstance(Base):
    """应用实例机器人实例关联表"""
    __tablename__ = 'app_instance_bot_instance'
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用实例ID")
    bot_instance_id = Column(ObjID(12), ForeignKey('bot_instance.id'), nullable=True, comment="机器人实例ID")


class ChatLog(Base):
    """聊天日志"""
    __tablename__ = 'chat_log'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    bot_instance_id = Column(ObjID(12), ForeignKey('bot_instance.id'), nullable=True, comment="机器人ID")
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用实例ID")
    model_id = Column(ObjID(12), ForeignKey('model.id'), nullable=True, comment="模型ID")
    sensitive_id = Column(ObjID(12), ForeignKey('sensitive.id'), nullable=True, comment="风险词ID")
    chat_id = Column(String(128), nullable=True, server_default=text("''"), comment="聊天会话ID")
    message_id = Column(String(128), nullable=True, server_default=text("''"), comment="聊天消息ID")
    message_type = Column(String(128), nullable=True, server_default=text("''"), comment="聊天消息类型")
    sender_id = Column(String(128), nullable=True, server_default=text("''"), comment="发送消息人的ID")
    username = Column(String(128), nullable=True, server_default=text("''"), comment="发送消息的人")
    # 输出给前端的时候，先判断message_type，然后尝试从extra取出对应的数据格式化给前端
    content = Column(Text, nullable=True, comment="用户输入内容")
    extra = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="剩余的信息")
    reply_message_id = Column(String(128), nullable=True, server_default=text("''"), comment="回复消息ID")
    # 执行成功后返回
    result = Column(JSONText, nullable=True, comment="对应instance执行结果")
    # 这个内容是否有必要存储？？？
    # reply_message_content = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="回复内容")


class Policy(Base):
    """应用权限控制策略（中间过渡版）"""
    __tablename__ = 'policy'
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用ID")
    model_id = Column(ObjID(12), ForeignKey('model.id'), nullable=True, comment="模型版本ID")
    allow_groups = Column(JSONStr(512), nullable=True, server_default=text("'[]'"), comment="允许群名")
    allow_users = Column(JSONStr(512), nullable=True, server_default=text("'[]'"), comment="允许用户")
    deny_groups = Column(JSONStr(512), nullable=True, server_default=text("'[]'"), comment="屏蔽群名")
    deny_users = Column(JSONStr(512), nullable=True, server_default=text("'[]'"), comment="屏蔽用户")
    vip_users = Column(JSONStr(512), nullable=True, server_default=text("'[]'"), comment="VIP用户")
    admin_users = Column(JSONStr(512), nullable=True, server_default=text("'[]'"), comment="ADMIN用户")


class Wallet(Base):
    """账户余额"""
    __tablename__ = 'wallet'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    category = Column(String(128), nullable=True, server_default=text("''"), comment="类别")
    # 这个字段和category结合使用
    target_id = Column(ObjID(12), nullable=True, comment="目标ID")
    description = Column(String(128), nullable=True, server_default=text("''"), comment="描述")
    amount = Column(Integer, nullable=True, server_default=text("'0'"), comment="金额")


class Product(Base):
    """商品（套餐）"""
    __tablename__ = 'product'
    name = Column(String(128), nullable=True, server_default=text("''"), comment="套餐名称")
    description = Column(String(128), nullable=True, server_default=text("''"), comment="描述")
    # 套餐类型（preview/person/enterprise/privatization）
    category = Column(String(128), nullable=True, server_default=text("''"), comment="套餐类型")
    # 套餐时长  7/90/365，对应7天/3月/一年
    # duration = Column(Integer, nullable=True, server_default=text("'0'"), comment="时长")
    # 给一个最小的购买次数（当前就是按照这个数字下单的）
    seats = Column(Integer, nullable=True, server_default=text("'0'"), comment="座席数")
    min = Column(Integer, nullable=True, server_default=text("'0'"), comment="最小购买数量")
    # day/month/year
    type = Column(String(32), nullable=True, server_default=text("'0'"), comment="按月/按年")
    price = Column(Integer, nullable=True, server_default=text("'0'"), comment="价格")
    real_price = Column(Integer, nullable=True, server_default=text("'0'"), comment="优惠价格")
    items = Column(JSONStr(1024), nullable=True, server_default=text("'[]'"), comment="包含项目")
    # 企业套餐版本（standard/advanced/privatization）
    edition = Column(String(128), nullable=True, server_default=text("''"), comment="企业套餐版本")


class TenantProduct(Base):
    """购买套餐记录"""
    __tablename__ = 'tenant_product'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    product_id = Column(ObjID(12), ForeignKey('product.id'), nullable=True, comment="套餐ID")
    upgrade_product_id = Column(ObjID(12), nullable=True, comment="从个人套餐升级，升级前ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    seats = Column(Integer, nullable=True, server_default=text("'0'"), comment="座席数")
    number = Column(Integer, nullable=True, server_default=text("'0'"), comment="购买数量")
    type = Column(String(32), nullable=True, server_default=text("'0'"), comment="按月/按年")
    price = Column(Integer, nullable=True, server_default=text("'0'"), comment="支付金额")
    expired = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment="到期时间")


class Messenger(Base):
    """客服"""
    __tablename__ = 'messenger'
    tenant_id = Column(ObjID(12), ForeignKey('tenant.id'), nullable=True, comment="租户ID")
    user_id = Column(ObjID(12), ForeignKey('user.id'), nullable=True, comment="用户ID")
    bot_instance_id = Column(ObjID(12), ForeignKey('bot_instance.id'), nullable=True, comment="机器人ID")
    instance_id = Column(ObjID(12), ForeignKey('app_instance.id'), nullable=True, comment="应用实例ID")
    chat_id = Column(String(64), nullable=True, server_default=text("''"), comment="话题群ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="客服名称")
    description = Column(String(512), nullable=True, server_default=text("''"), comment="客服描述")
    platform = Column(String(32), nullable=True, server_default=text("''"), comment="渠道：官网/抖音...")
    ai = Column(Integer, nullable=True, server_default=text("'0'"), comment="是否开启AI能力")


class MessengerWithConfig(Messenger):
    # 客户端的配置，web页面配置
    config = Column(JSONStr(2048), nullable=True, server_default=text("'{}'"), comment="其他配置项")


class MessengerSeat(Base):
    """客服人工座席"""
    __tablename__ = 'messenger_seat'
    messenger_id = Column(ObjID(12), ForeignKey('messenger.id'), nullable=True, comment="客服ID")
    name = Column(String(128), nullable=True, server_default=text("''"), comment="姓名")
    open_id = Column(String(128), nullable=True, server_default=text("''"), comment="飞书open_id")
    start_time = Column(Time, nullable=True, server_default=text("'00:00'"), comment="开始时间")
    end_time = Column(Time, nullable=True, server_default=text("'00:00'"), comment="结束时间")


