#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动manager-server的脚本
适用于本地开发环境，无需Docker
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def setup_environment():
    """设置环境变量和路径"""
    # 确保数据目录存在
    data_dir = Path("./data")
    data_dir.mkdir(exist_ok=True)
    
    # 设置环境变量
    os.environ["DEBUG"] = "True"
    os.environ["ENV"] = "development"
    os.environ["SERVER_PORT"] = "3000"
    
    print("✅ 环境设置完成")

def init_database():
    """初始化SQLite数据库"""
    db_path = "./data/connectai.db"
    
    try:
        # 创建数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建基础表（简化版）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS applications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库初始化完成")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    
    return True

def start_manager_server():
    """启动manager-server"""
    try:
        # 切换到manager-server目录
        os.chdir("manager-server")
        
        # 激活虚拟环境并启动服务器
        if os.name == 'nt':  # Windows
            activate_cmd = "venv\\Scripts\\activate"
            python_cmd = "venv\\Scripts\\python.exe"
        else:  # Unix/Linux/Mac
            activate_cmd = "source venv/bin/activate"
            python_cmd = "venv/bin/python"
        
        # 启动服务器
        print("🚀 启动manager-server...")
        print("服务将在 http://localhost:3000 运行")
        print("按 Ctrl+C 停止服务")
        
        # 使用subprocess启动服务器
        subprocess.run([python_cmd, "server/server.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 正在启动ConnectAI Manager Server...")
    
    # 检查虚拟环境是否存在
    if not os.path.exists("manager-server/venv"):
        print("❌ 虚拟环境不存在，请先运行环境设置")
        return False
    
    # 设置环境
    setup_environment()
    
    # 初始化数据库
    if not init_database():
        return False
    
    # 启动服务器
    start_manager_server()
    
    return True

if __name__ == "__main__":
    main()
