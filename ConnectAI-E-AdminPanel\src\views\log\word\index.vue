<template>
  <div class="h-full">
    <NoPermission v-if="deny('page.log.word')" />
    <n-card v-else class="h-full shadow-sm rounded-16px pt-2" content-style="overflow:hidden flex flex-col">
      <template #header>
        <div class="w-full flex justify-between items-center">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
              Search
            </label>
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="$t('message.log.srfxc')"
              />
              <button
                class="text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ $t('message.log.ss') }}
              </button>
            </div>
          </form>
          <div class="flex justify-start gap-4 items-start">
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAddSens"
            >
              <icon-akar-icons-circle-plus class="mr-2" />
              {{ $t('message.log.xzfxzt') }}
            </button>
            <button
              type="button"
              class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
              @click="handleExport"
            >
              <icon-akar-icons-cloud-download class="mr-2" />
              {{ $t('message.log.pldc') }}
            </button>
          </div>
        </div>
        <n-divider class="pt-2" />
      </template>
      <loading-empty-wrapper class="min-h-[350px]" :loading="loading" :empty="empty">
        <WordTable
          v-if="!loading && !empty"
          :data="data"
          :pagination-options="paginationOptions"
          @handle-edit="handleEdit"
          @handle-delete="handleDelete"
          @handle-active="handleActive"
        />
      </loading-empty-wrapper>
    </n-card>
    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        :title="t('message.log.bj')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form ref="formRef" :label-width="80" :model="formValue" :rules="rules">
          <n-form-item :label="t('message.log.fxzt')" path="category">
            <n-input v-model:value="formValue.category" :placeholder="t('message.log.qsr')" />
          </n-form-item>
          <n-form-item :label="t('message.log.fx')" path="name">
            <n-select
              v-model:value="formValue.name"
              filterable
              multiple
              tag
              :placeholder="t('message.log.qsrenter')"
              :show-arrow="false"
              :show="false"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.log.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              {{ t('message.log.qd') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { FormInst, FormRules, FormItemRule } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { useLoadingEmpty, usePagination, useTenantPrivilege } from '@/hooks';
import {
  delSensitive,
  fetchSensList,
  addSensitive,
  editSensitive,
  exportSensList,
  updateSensitiveStatus
} from '~/src/service/api/log';
import WordTable from './word-table.vue';
import { t } from '@/locales';
const formRef = ref<FormInst | null>(null);
const keyword = ref('');
const data = ref<ApiSensitive.SensitiveTypes[]>([]);
const showModal = ref(false);
const itemCount = ref<number>(0);
const { pagination, paginationOptions } = usePagination({ itemCount });
const { deny } = useTenantPrivilege();
const formValue = ref<{
  id?: string;
  category: string;
  name: string[];
}>({
  category: '',
  name: []
});
const rules: FormRules = {
  category: {
    required: true,
    message: t('message.log.qsrfxzt'),
    trigger: ['input']
  },
  name: {
    required: true,
    validator(rule: FormItemRule, value: []) {
      if (!value || value.length === 0) {
        return new Error(t('message.log.qsrfxc'));
      }
      return true;
    },
    trigger: ['input', 'blur']
  }
};

const message = useMessage();
const { loading, startLoading, endLoading, empty, setEmpty } = useLoadingEmpty();

watch(pagination, () => {
  getSenstList();
});

async function getSenstList(keyword?: string) {
  startLoading();
  try {
    const res = await fetchSensList({ ...pagination, keyword });
    data.value = res.data?.data || [];
    itemCount.value = res.data!.total;
    endLoading();
    setEmpty(res.data?.total === 0);
  } catch (err) {}
}

function handleSearch(e: Event) {
  e.preventDefault();
  getSenstList(keyword.value);
}

function handleExport() {
  exportSensList(keyword.value);
}

async function handleAddSens() {
  showModal.value = true;
}

function handleClose() {
  showModal.value = false;
  formValue.value = {
    category: '',
    name: [],
    id: ''
  };
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (formValue.value.id) {
        const { id, ...data } = formValue.value;
        await editSensitive({ id, data });
      } else {
        await addSensitive(formValue.value);
      }
      message.success(t('message.msg.bccg'));
      handleClose();
      getSenstList();
    }
  });
}

function handleEdit({ name, category, id }: ApiSensitive.SensitiveTypes) {
  formValue.value = {
    name,
    category,
    id
  };
  showModal.value = true;
}

async function handleDelete({ id }: ApiSensitive.SensitiveTypes) {
  await delSensitive({ id });
  message.success(t('message.msg.sccg'));
  getSenstList();
}

async function handleActive(status: boolean, { id }: ApiSensitive.SensitiveTypes) {
  const action = status ? 'start' : 'stop';
  await updateSensitiveStatus({ id, action });
  message.success(t('message.msg.gxztcg'));
  getSenstList();
}

onMounted(() => {
  getSenstList();
});
</script>
