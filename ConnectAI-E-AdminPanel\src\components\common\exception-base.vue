<template>
  <div class="flex-col-center gap-24px min-h-520px wh-full overflow-hidden">
    <div class="mx-auto max-w-screen-sm text-center">
      <img
        class="mx-auto mb-4"
        src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/404/404-computer.svg"
        alt="404 Not Found"
      />
      <h1 class="mb-4 text-2xl font-extrabold text-primary-600 dark:text-primary-500">Not Found</h1>
      <p class="mb-10 text-3xl tracking-tight font-bold text-gray-900 md:text-4xl dark:text-white">
        Whoops! That page doesn’t exist.
      </p>
    </div>
    <router-link :to="{ name: routeHomePath }">
      <button
        type="button"
        class="text-white bg-[#050708] hover:bg-[#050708]/80 focus:ring-4 focus:outline-none focus:ring-[#050708]/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:hover:bg-[#050708]/40 dark:focus:ring-gray-600 mr-2 mb-2"
      >
        返回首页
        <svg
          class="w-4 h-4 ml-2 -mr-1"
          aria-hidden="true"
          focusable="false"
          data-prefix="fab"
          data-icon="apple"
          role="img"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 434.4700012207031 415.82000732421875"
        >
          <defs>
            <clipPath id="master_svg0_33_4729">
              <rect x="0" y="0" width="434.4700012207031" height="415.82000732421875" rx="0" />
            </clipPath>
          </defs>
          <g clip-path="url(#master_svg0_33_4729)">
            <g>
              <g>
                <g>
                  <path
                    d="M417.61996989822387,63.05999992370605L398.61996989822387,63.05999992370605C390.26496989822385,63.054499923706054,383.49096989822385,56.28539992370605,383.4799698982239,47.929999923706056C383.4769698982239,46.403399923706054,383.70896989822387,44.88549992370606,384.1699698982239,43.429999923706056L391.1699698982239,22.079999923706055C394.66596989822386,11.220099923706055,386.5789698982239,0.10030242370605469,375.1699698982239,0.07999992370605469L183.79996989822388,0.07999992370605469C176.50796989822388,0.08423481370605469,170.04896989822387,4.783609923706055,167.79996989822388,11.719999923706055Q154.29996989822388,52.999999923706056,154.29996989822388,53.06999992370606C152.10096989822387,59.06709992370605,146.39796989822386,63.05829992370605,140.0099698982239,63.06999992370606L100.22996989822387,63.06999992370606C92.95396989822387,63.08419992370605,86.50586989822388,67.75909992370606,84.22996989822387,74.66999992370606L0.839999898223877,330.75999992370606C-2.666050101776123,341.621999923706,5.426649898223877,352.74899992370604,16.839969898223877,352.75999992370606L36.11996989822388,352.75999992370606C46.19726989822388,352.960999923706,53.27256989822388,362.76199992370607,50.28996989822388,372.38999992370606L43.349969898223875,393.78999992370603C39.82326989822388,404.65499992370604,47.92716989822388,415.7979999237061,59.349969898223875,415.78999992370603L313.69996989822386,415.78999992370603C320.9869698982239,415.8099999237061,327.4529698982239,411.121999923706,329.69996989822386,404.18999992370607L343.06996989822386,363.18999992370607C345.1289698982239,356.98099992370607,350.9359698982239,352.78999992370603,357.4799698982239,352.78999992370603L397.25996989822386,352.78999992370603C404.54096989822386,352.79099992370607,410.9969698982239,348.11099992370606,413.25996989822386,341.18999992370607L422.80996989822387,311.76999992370605C426.33696989822386,300.90499992370604,418.23296989822387,289.76199992370607,406.80996989822387,289.76999992370605L387.7999698982239,289.76999992370605C379.44496989822386,289.7639999237061,372.67096989822386,282.9949999237061,372.6599698982239,274.63999992370606C372.6669698982239,273.40699992370605,372.81796989822385,272.17799992370607,373.1099698982239,270.97999992370603Q373.26996989822385,270.2999999237061,380.32996989822385,248.78999992370606C383.8359698982239,237.92799992370607,375.74296989822386,226.80099992370606,364.32996989822385,226.78999992370606L250.99996989822387,226.78999992370606C239.59096989822388,226.76999992370605,231.5039698982239,215.64999992370605,234.99996989822387,204.78999992370606L236.27996989822387,200.85999992370606C238.52896989822386,193.92999992370605,244.99396989822387,189.24299992370607,252.27996989822387,189.25999992370606L324.49996989822387,189.25999992370606C331.79396989822385,189.26199992370604,338.2569698982239,184.55999992370604,340.49996989822387,177.61999992370605L353.7899698982239,136.90999992370607C355.75996989822386,130.53599992370604,361.6509698982239,126.18999992370605,368.31996989822386,126.18999992370605L408.0999698982239,126.18999992370605C415.3809698982239,126.19099992370606,421.83696989822386,121.51099992370605,424.0999698982239,114.58999992370606L433.68996989822386,85.20999992370605C437.3239698982239,74.27289992370605,429.14496989822385,62.99899992370605,417.61996989822387,63.05999992370605ZM371.3399698982239,82.64999992370605Q360.61996989822387,115.80999992370606,360.4199698982239,116.20999992370605C358.1979698982239,122.15299992370605,352.51496989822385,126.08899992370606,346.1699698982239,126.07999992370605L209.72996989822389,126.07999992370605C202.4389698982239,126.08699992370606,195.9809698982239,130.78599992370604,193.72996989822389,137.71999992370604L151.42996989822387,267.71999992370604C147.8619698982239,278.5909999237061,155.98796989822387,289.76499992370606,167.42996989822387,289.71999992370604L345.7299698982239,289.71999992370604C354.3999698982239,289.71999992370604,361.62996989822386,296.82999992370605,361.25996989822386,305.49999992370607C361.1989698982239,306.81999992370606,360.9699698982239,308.1269999237061,360.57996989822385,309.38999992370606L349.93996989822386,341.99999992370607C348.0659698982239,348.44499992370606,342.1209698982239,352.84699992370605,335.4099698982239,352.75999992370606L77.54996989822388,352.75999992370606C69.19456989822388,352.75399992370603,62.42106989822388,345.984999923706,62.40996989822388,337.62999992370607C62.41486989822388,336.15999992370604,62.630369898223876,334.6989999237061,63.04996989822388,333.28999992370603C63.13006989822388,333.0499999237061,147.58996989822387,73.69999992370606,147.58996989822387,73.65999992370605C149.58996989822387,67.51999992370605,155.29996989822388,63.05999992370605,160.27996989822387,63.05999992370605Q337.5399698982239,63.05999992370605,356.12996989822386,63.05999992370605L357.68996989822386,63.05999992370605C367.53596989822387,63.58209992370605,374.25996989822386,73.23219992370605,371.3399698982239,82.64999992370605Z"
                    fill="currentColor"
                    fill-opacity="1"
                  />
                </g>
              </g>
            </g>
          </g>
        </svg>
      </button>
    </router-link>
  </div>
</template>

<script lang="ts" setup>
import { routeName } from '@/router';

defineOptions({ name: 'ExceptionBase' });

type ExceptionType = '403' | '404' | '500';

interface Props {
  /** 异常类型 403 404 500 */
  type: ExceptionType;
}

defineProps<Props>();

const routeHomePath = routeName('root');
</script>

<style scoped></style>
