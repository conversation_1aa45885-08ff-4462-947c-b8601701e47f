import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.996 19.01a.75.75 0 0 1 .743.648l.007.102v1.5a.75.75 0 0 1-1.493.102l-.007-.102v-1.5a.75.75 0 0 1 .75-.75zm6.022-2.073l1.06 1.061a.75.75 0 0 1-1.06 1.06l-1.061-1.06a.75.75 0 1 1 1.06-1.06zm-10.984 0a.75.75 0 0 1 0 1.061l-1.06 1.06a.75.75 0 1 1-1.06-1.06l1.06-1.06a.75.75 0 0 1 1.06 0zM12 6.476a5.525 5.525 0 1 1 0 11.05a5.525 5.525 0 0 1 0-11.05zm0 1.5a4.025 4.025 0 1 0 0 8.05a4.025 4.025 0 0 0 0-8.05zM11.25 9a.75.75 0 0 1 .743.648L12 9.75v2.253h1.25a.75.75 0 0 1 .743.649l.006.102a.75.75 0 0 1-.648.743l-.102.007h-2a.75.75 0 0 1-.742-.649l-.007-.101V9.75a.75.75 0 0 1 .75-.75zm10 2.268a.75.75 0 0 1 .101 1.493l-.101.007h-1.5a.75.75 0 0 1-.102-1.493l.102-.007h1.5zm-17-.029a.75.75 0 0 1 .102 1.493l-.102.007h-1.5a.75.75 0 0 1-.102-1.493l.102-.007h1.5zm1.64-6.37l.084.072L7.034 6a.75.75 0 0 1-.976 1.134l-.084-.073l-1.06-1.06a.75.75 0 0 1 .976-1.134zm13.188.072a.75.75 0 0 1 .073.976l-.073.084l-1.06 1.061a.75.75 0 0 1-1.134-.976L16.957 6l1.06-1.06a.75.75 0 0 1 1.061 0zM12 1.989a.75.75 0 0 1 .743.648l.007.102v1.5a.75.75 0 0 1-1.493.102l-.007-.102v-1.5a.75.75 0 0 1 .75-.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TimeAndWeather24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
