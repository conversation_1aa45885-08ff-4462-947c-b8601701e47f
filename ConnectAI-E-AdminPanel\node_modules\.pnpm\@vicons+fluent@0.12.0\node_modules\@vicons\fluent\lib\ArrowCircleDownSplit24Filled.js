'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M11.999 21.998c-5.523 0-10-4.477-10-10s4.477-10 10-10s10 4.477 10 10s-4.477 10-10 10zm-1-8l-.083-.072a.75.75 0 0 0-.977.072l-.439.44v-3.94h5v3.941l-.512-.44l-.084-.073a.75.75 0 0 0-.977 1.133l1.792 1.72l.084.073a.75.75 0 0 0 .976-.073l1.72-1.72l.073-.084a.75.75 0 0 0-.073-.976l-.084-.073a.75.75 0 0 0-.976.073l-.439.438v-4.69l-.007-.101a.75.75 0 0 0-.743-.649L15.23 9h-2.48V5.747l-.007-.101a.75.75 0 0 0-.744-.649l-.101.007a.75.75 0 0 0-.649.744v3.25H8.794a.763.763 0 0 0-.043 0l-.102.006A.75.75 0 0 0 8 9.748v4.691L7.56 14l-.083-.073a.75.75 0 0 0-.977 1.133l1.719 1.72l.084.073a.75.75 0 0 0 .977-.073L11 15.06l.072-.084a.75.75 0 0 0-.072-.977z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ArrowCircleDownSplit24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
