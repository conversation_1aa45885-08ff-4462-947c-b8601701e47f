<template>
  <div class="flex justify-between items-center text-gray-500">
    <span
      class="bg-green-100 text-primary-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded dark:bg-primary-200 dark:text-primary-800"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4" viewBox="0 0 24 24">
        <g fill="none" fill-rule="evenodd">
          <path
            d="M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .**************.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"
          />
          <path
            fill="currentColor"
            d="M3 5a2 2 0 0 1 2-2h3v5H3V5Zm5 5H3v9a2 2 0 0 0 2 2h3V10Zm2 11h9a2 2 0 0 0 2-2v-9H10v11Zm0-13V3h9a2 2 0 0 1 2 2v3H10Z"
          />
        </g>
      </svg>
      Sheet
    </span>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
