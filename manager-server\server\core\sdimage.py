import json
import logging
import random
from time import time, sleep
from typing import Optional, List, Any
import uuid
import httpx
from langchain.chat_models.base import SimpleChatModel
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.schema import BaseMessage, AIMessage, HumanMessage, ChatResult, ChatGeneration


class SDImageClientV2(object):
    def __init__(self, api_base, api_key='', api_id='', api_secret=''):
        self.api_base = api_base
        self.api_key = api_key
        self.api_id = api_id
        self.api_secret = api_secret
        # {'name': 'ckpt'} query_all_models获取
        self.models = {'CuteYukiMix(特化可爱风格adorable style）': 'cuteyukimixAdorable_neochapter2_28169.ckpt', 'IdolTrainee_JuicyBoy': 'IdolTrainee_JuicyBoy_Year_1.0_21193.safetensors', 'ouka_star': 'oukaStar_10_104453.safetensors', 'ouka_gufeng': 'oukaGufeng_s1_95718.safetensors', 'ouka_niji5': 'oukaNiji5_v10_85504.safetensors', 'FuwaFuwaMix': 'fuwafuwamix_v15BakedVae_61766.safetensors', 'BeenYou Lite': 'beenyouLite_l15_34440.safetensors', 'Inkpunk Diffusion': 'inkpunkDiffusion_v2_1087.ckpt', 'Samaritan 3d Cartoon': 'samaritan3dCartoon_samaritan3dCartoonV3_81270.safetensors', 'CamelliaMIx_2.5D': 'camelliamix25D_v2_44219.safetensors', 'BeenYou': 'beenyou_r13_27688.safetensors', 'Dreamlike Diffusion 1.0': 'dreamlikeDiffusion10_10_1274.ckpt', 'Based64': 'based64_v3_31472.safetensors', 'Comic Babes': 'comicBabes_v1_20294.safetensors', 'Disney Pixar Cartoon Type A': 'disneyPixarCartoon_v10_65203.safetensors', 'ToonYou': 'toonyou_beta5Unstable_30240.safetensors', 'Dark Sushi 2.5D 大颗寿司2.5D': 'darkSushi25D25D_v30_48671.safetensors', '万象熔炉 | Anything V5/Ink': 'AnythingV5Ink_ink_9409.safetensors', 'Pastel-Mix [Stylized Anime Model]': 'pastelMixStylizedAnime_pastelMixPrunedFP16_5414.safetensors', '【Checkpoint】YesMix': 'CheckpointYesmix_v20_9139.safetensors', 'majicMIX sombre 麦橘唯美': 'majicmixSombre_v20_62778.safetensors', 'Realistic Vision V5.0': 'realisticVisionV50_v50VAE_4201.safetensors', 'AbyssOrangeMix3 (AOM3)': 'abyssorangemix3AOM3_aom3a1b_9942.safetensors', 'majicMIX lux 麦橘奇幻': 'majicmixLux_v2_56967.safetensors', ' Counterfeit-V3.0': 'CounterfeitV30_v30_4468.safetensors', 'MeinaMix': 'meinamix_meinaV11_7240.safetensors', 'Dark Sushi Mix 大颗寿司Mix': 'darkSushiMixMix_225D_24779.safetensors', 'Deliberate': 'deliberate_v2_4823.safetensors', '国风3 GuoFeng3': '3Guofeng3_v34_10415.safetensors', 'majicMIX realistic 麦橘写实': 'majicmixRealistic_v6_43331.safetensors', 'Cetus-Mix': 'cetusMix_Whalefall2_6755.safetensors', 'ReV Animated': 'revAnimated_v122_7371.safetensors', 'GhostMix': 'ghostmix_v20Bakedvae_36520.safetensors', 'DreamShaper': 'dreamshaper_8_4384.safetensors'}

    def get_headers(self):
        headers = {
            'api-key': self.api_key,
            'X-Request-req-accessKeyId': self.api_id,
            'X-Request-req-accessKeySecret': self.api_secret,
            'Content-Type': 'application/json'
        }
        return headers

    def query_text2image(self, prompt, task_id, **kwargs):
        url = '{}/v2/text2image'.format(self.api_base)
        headers = self.get_headers()
        # 默认参数
        params = {
            'steps': 40,
            'denoising_strength': 0.6,
            'width': 512,
            'height': 512,
            'n_iter': 1,
            'hr_scale': 1,
            'enable_hr': False,
        }
        model_name = kwargs.get('model_name', 'DreamShaper')
        params.update({
            'prompt': prompt,
            'alwayson_scripts': {
                'override_settings': {
                    'CLIP_stop_at_last_layers': 2
                },
                'sd_model_checkpoint': self.models[model_name],
                'id_task': task_id
            },
            # 可以从外部传入更新参数
            **kwargs
        })
        result = httpx.post(url, headers=headers, data=json.dumps(params), timeout=60).json()
        # print(result, url, headers, params)
        return result

    def query_result_info(self, task_id):
        url = '{}/queryTaskInfo'.format(self.api_base)
        headers = self.get_headers()
        params = {'businessTaskId': task_id}
        result = httpx.post(url, headers=headers, data=json.dumps(params), timeout=60).json()
        return result

    def query_model_list(self, **kwargs):
        url = f'{self.api_base}/v2/queryModelList'
        result = httpx.post(url, headers=self.get_headers(), data=json.dumps(kwargs), timeout=60).json()
        return result

    def query_all_models(self):
        models = {}
        nums = 0
        page = 1
        while 1:
            result = self.query_model_list(pageNum=page)
            if not result or not result.get('data'): break
            data = result['data']
            total = data.get('total', 0)
            rows = data.get('rows', [])
            nums += len(rows)
            page += 1
            for row in rows:
                if row['modelStatus'] == 1:
                    models[row['sdModelName']] = row['sdModelCheckpoint']
            if nums >= total: break
        # print(len(models), nums, page)
        return models

    def stream(self, prompt, timeout=600, **kwargs):
        started = time()
        ended = started + timeout
        try:
            # 每次任务重新生成一个id 使用uuid
            task_id = str(uuid.uuid4())
            create_result = self.query_text2image(prompt=prompt, task_id=task_id, **kwargs)
            if create_result.get('data'):
                data = create_result['data']
                task_id = data.get('task_id', task_id)
                yield create_result
                create_result['result'] = 'submit:success'
                while time() < ended:
                    result = self.query_result_info(task_id=task_id)
                    yield result
                    status = result['data']['status']
                    if status == 1:
                        # 成功
                        break
                    elif status == 2:
                        # 失败
                        create_result['result'] = 'created:error'
                        yield create_result
                        break
                    sleep(4)
                if time() >= ended:
                    create_result['result'] = 'result:timeout'
                    yield create_result
            else:
                create_result['result'] = 'created:error'
                yield create_result
        except Exception as e:
            logging.error(e)
            yield {'result': 'created:error'}

    def create(self, stream=False, timeout=600, prompt='', **kwargs):
        result = self.stream(timeout=timeout, prompt=prompt, **kwargs)
        if stream:
            return result
        else:
            return list(result).pop()


class SDImageChat(SimpleChatModel):
    client: Any  #: :meta private:
    api_base: Optional[str] = ''
    api_key: Optional[str] = ''
    api_id: Optional[str] = ''
    api_secret: Optional[str] = ''
    max_retries: int = 6
    streaming: bool = False

    def _llm_type(self) -> str:
        return "sdimage_chat"

    def _call(self, messages, stop, run_manager, **kwargs):
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ):
        params = {'stream': self.streaming, **kwargs}
        message = messages.pop()
        params.update(**message.additional_kwargs)
        self.client = SDImageClientV2(
            api_base=self.api_base,
            api_key=self.api_key,
            api_id=self.api_id,
            api_secret=self.api_secret
        )
        if self.streaming:
            response = {}
            # 每4秒查询一次，1024尺寸平均需要40秒 2048尺寸平均需要200秒
            token_cnt = 0
            for stream_resp in self.client.create(**params):
                response = stream_resp
                # token = stream_resp.get('data', {})
                if run_manager:
                    if params.get('width') <= 1024 and params.get('height') <= 1024:
                        token_cnt += random.randint(2, 8)
                    else:
                        token_cnt += 1
                    if token_cnt >= 99:
                        token_cnt = 99
                    # 传入进度条
                    run_manager.on_llm_new_token(f'{token_cnt}%')
            data = response.get('data', {})
        else:
            response = self.client.create(**params)
            data = response.get('data', {})
        try:
            image_url = data.get('differentialImageRspList')[0].get('image_url')
            message = AIMessage(content=image_url, additional_kwargs=data)
            return ChatResult(generations=[ChatGeneration(message=message)])
        except Exception as e:
            logging.info('response error:', response)
            logging.error(e)
            return None


if __name__ == '__main__':
    import asyncio
    async def main():
        prompt = 'a cute catoon girl'
        api_base = 'https://sdimage.forkway.cn/sd'
        api_key = ''
        from langchain.schema import HumanMessage

        # client = SDImageClient(api_base, api_id, api_secret)
        # result = client.query_text2image(prompt=prompt, task_id=str(uuid.uuid4()), model_name='anythingv5')
        # print(result)

        # models = ['anythingv5', 'rev122', 'dream', 'real', 'guofeng', 'dreamshaper_7']
        models = ['CuteYukiMix(特化可爱风格adorable style）', 'ouka_gufeng', 'Samaritan 3d Cartoon', 'Deliberate', 'majicMIX realistic 麦橘写实', 'DreamShaper']
        for model in models:
            chat = SDImageChat(
                api_base=api_base,
                api_key=api_key,
                # api_id=api_id,
                # api_secret=api_secret,
                streaming=True
            )
            messages = [HumanMessage(content=prompt, additional_kwargs=dict(prompt=prompt,))]
            result = chat(
                messages,
                model_name=model,
                # enable_hr=True,
                # hr_scale=2
            )
            print(model, result)

    asyncio.run(main())
