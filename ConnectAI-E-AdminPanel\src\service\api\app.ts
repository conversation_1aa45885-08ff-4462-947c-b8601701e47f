import { request } from '../request';

export function getAppShareDetail(id: string) {
  return request.get<ApiApp.Resp.APPDetail>(`/api/app/share/${id}`);
}
export function getAppDetail(id: string) {
  return request.get<ApiApp.Resp.APPDetail>(`/api/app/${id}`);
}
export function getAppList(params: ApiApp.Req.APPList) {
  return request.get<ApiApp.Resp.AppList>('/api/app', { params });
}

export function getAppCategory() {
  return request.get<ApiApp.Resp.AppCategoryList>('/api/app/category', { params: { page: 1, size: 999 } });
}

export function updateAppStatus({ id, action }: ApiApp.Req.UpdateStatus) {
  return request.post(`/api/app/${id}/${action}`);
}

export function getAppResource({ id }: ApiApp.Req.GetAppResource) {
  return request.get<ApiApp.Resp.AppResourceList>(`/api/app/${id}/resource`);
}

export function getAppResources({ id }: ApiApp.Req.GetAppResource) {
  return request.get<ApiApp.Resp.AppResourcesList>(`/api/app/${id}/resources`);
}

export function getAppClient({ id }: ApiApp.Req.GetAppClient) {
  return request.get<ApiApp.Resp.AppClientList>(`/api/app/${id}/client`);
}

export function getAppClientBot({ id, botId }: ApiApp.Req.GetAppClientBot) {
  return request.get<ApiApp.Resp.AppClientBot>(`/api/app/${id}/client/${botId}`);
}

export function updateAppClientBot({ id, botId, data }: ApiApp.Req.UpdateAppClientBot) {
  return request.post(`/api/app/${id}/client/${botId}`, data);
}

export function updateAppSetting({ id, data }: ApiApp.Req.UpdateAppSetting) {
  return request.post(`/api/app/${id}/setting`, data);
}

export function getAppSetting({ id }: ApiApp.Req.AppSetting) {
  return request.get<ApiApp.Resp.AppSetting>(`/api/app/${id}/setting`);
}

export function getAppInfo({ id }: ApiApp.Req.AppInfo) {
  return request.get<ApiApp.Resp.AppInfo>(`/api/app/${id}/info`);
}

export function getTenantAppList(params: ApiApp.Req.APPList) {
  return request.get<ApiApp.Resp.AppList>('/api/tenant/app', { params });
}
