#!/bin/env bash
# add to crontab

cd -- "$( dirname -- "${BASH_SOURCE[0]}" )"

MYSQL_ROOT_PASSWORD=passwd
MYSQL_DATABASE=database
BACKUPS=/home/<USER>

[ -f .env ] && source .env

echo backup $MYSQL_DATABASE to $BACKUPS/$MYSQL_DATABASE-$(date "+%Y%m%d-%H%M%S").sql.gz

sudo docker-compose exec -T mysql mysqldump -uroot -p$MYSQL_ROOT_PASSWORD $MYSQL_DATABASE | gzip > $BACKUPS/$MYSQL_DATABASE-$(date "+%Y%m%d-%H%M%S").sql.gz

