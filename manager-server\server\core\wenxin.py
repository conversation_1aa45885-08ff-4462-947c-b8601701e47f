import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)   

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)
from tornado.options import options, define

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.adapters.openai import convert_dict_to_message, convert_message_to_dict
from langchain.schema import ChatGeneration, ChatResult, BaseMessage

logger = logging.getLogger(__name__)


MODELS = {
    'ERNIE-Bot': '/chat/completions',
    'ERNIE-Bot-turbo': '/chat/eb-instant',
    'BLOOMZ-7B': '/chat/bloomz_7b1',
    'ERNIE-Bot 4.0': '/chat/completions_pro'
}

class WenXinClient(object):

    def build_query(
        self,
        messages=list(),
        model='ERNIE-Bot-turbo',
        api_base='https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
        api_key='',
        app_id='',
        secret_key='',
        **kwargs
    ):
        if model not in MODELS.keys():
            raise Exception('not supported model {}'.format(model))
        api = MODELS[model]
        headers = dict()
        if app_id:
            headers = {
                'api-base': api_base,
                'app-id': app_id,
                'api-key': api_key,
                'secret-key': secret_key,
            }
            from core.api_base import NewApiBase
            api_base = NewApiBase('文心一言').url
        return '{}{}?access_token={}'.format(api_base, api, api_key), json.dumps(dict(messages=messages, **kwargs)), headers

    def stream(self, url, data, headers, timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    def create(self, messages=list(), model='ERNIE-Bot-turbo', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages, model=model, stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers, timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    async def acreate(self, messages=list(), model='ERNIE-Bot-turbo', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages, model=model, stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


async def acompletion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the async completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    async def _completion_with_retry(**kwargs: Any) -> Any:
        # Use OpenAI's async api https://github.com/openai/openai-python#async-api
        return await llm.client.acreate(**kwargs)

    return await _completion_with_retry(**kwargs)


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class WenXinChat(SimpleChatModel):
    """
    文心一言
    """
    client: Any  #: :meta private:
    # 当前支持3个模型
    model_name: str = "ERNIE-Bot-turbo"  # ERNIE-Bot/BLOOMZ-7B
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    # 支持官方的key
    app_id: Optional[str] = None
    secret_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 0.95   # 默认0.95，范围 (0, 1.0]，不能为0
    top_p: float = 0.8          # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    penalty_score: float = 1.0  # 通过对已生成的token增加惩罚，减少重复生成的现象。
    user_id: str = ''           # 表示最终用户的唯一标识符，可以监视和检测滥用行为，防止接口恶意调用

    def _llm_type(self) -> str:
        return "weixin_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'app_id': self.app_id,
            'secret_key': self.secret_key,
            'model': self.model_name,
        }
        if self.model_name in ['ERNIE-Bot', 'ERNIE-Bot-turbo', 'ERNIE-Bot 4.0']:
            params.update({
                'temperature': self.temperature or 0.01,
                'top_p': self.top_p,
                'penalty_score': self.penalty_score,
            })

        message_dicts = [convert_message_to_dict(m) for m in messages]
        self.client = WenXinClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, messages=message_dicts, **params):
                # 文心每一个stream的结果都有　usage
                token = stream_resp.get("result", "")
                response += token
                if run_manager:
                    run_manager.on_llm_new_token(token)
            return response
        else:
            full_response = completion_with_retry(self, messages=message_dicts, **params)
            return full_response["result"]


if __name__ == "__main__":
    import asyncio
    from core.api_base import NewApiBase
    async def main():
        client = WenXinClient()
        messages = [{"role":"user","content":"帮我推荐一条旅游路线"}]
        api_key = ''
        api_base = NewApiBase('文心一言').url

        from langchain.schema import HumanMessage

        chat = WenXinChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        messages = [HumanMessage(content='你是谁')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


