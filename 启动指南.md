# 🚀 ConnectAI 项目启动指南

## ✅ 环境搭建完成状态

### 已完成的工作
- ✅ **manager-server** Python虚拟环境创建完成，依赖安装成功
- ✅ **DataChat-API** Python虚拟环境创建完成，依赖安装成功  
- ✅ **数据目录** 创建完成 (`./data/`)
- ✅ **配置文件** 已适配本地环境（SQLite替代MySQL）
- ✅ **启动脚本** 已创建完成

### 依赖验证结果
```
✅ manager-server dependencies OK
✅ DataChat-API dependencies OK
```

## 🎯 快速启动步骤

### 1. 启动后端服务

#### 方式一：使用自动化脚本
```bash
# 在项目根目录 (c:\Users\<USER>\code\connect-ai\project-manager)
python setup_and_start.py
```

#### 方式二：手动启动各服务

**启动DataChat-API:**
```bash
cd DataChat-API
venv\Scripts\activate
python simple_app.py
# 服务运行在 http://localhost:5000
```

**启动Manager Server:**
```bash
cd manager-server  
venv\Scripts\activate
python server/server.py
# 服务运行在 http://localhost:3000
```

### 2. 验证服务状态

**检查DataChat-API:**
```bash
curl http://localhost:5000/health
# 或在浏览器访问 http://localhost:5000
```

**检查Manager Server:**
```bash
curl http://localhost:3000
# 或在浏览器访问 http://localhost:3000
```

## 📦 前端环境设置

### 需要安装Node.js

**下载安装Node.js:**
1. 访问 https://nodejs.org/
2. 下载LTS版本 (推荐18.x或20.x)
3. 运行安装程序，按默认设置安装

**验证安装:**
```bash
node --version
npm --version
```

**安装前端依赖:**
```bash
# 管理面板
cd ConnectAI-E-AdminPanel
npm install
npm run dev
# 前端将运行在 http://localhost:3000 或其他端口

# 浏览器扩展
cd ConnectAI-Helper  
npm install
npm run dev
```

## 🗂️ 项目架构

### 服务端口分配
- **Manager Server**: http://localhost:3000
- **DataChat API**: http://localhost:5000  
- **前端管理面板**: http://localhost:8080 (需要Node.js)

### 数据存储
- **数据库**: SQLite (`./data/connectai.db`)
- **文件存储**: `./data/files/`
- **搜索索引**: `./data/search_index/`

### 本地环境替代方案
| 生产环境 | 本地环境 | 状态 |
|---------|---------|------|
| MySQL | SQLite | ✅ 已配置 |
| Redis | 内存存储 | ✅ 已配置 |
| Elasticsearch | 文件存储 | ✅ 已配置 |
| RabbitMQ | 内存队列 | ✅ 已配置 |
| Docker | 本地进程 | ✅ 已配置 |

## 🔧 开发工具推荐

### 必需工具
- **Python 3.8+** ✅ 已安装
- **Node.js 18+** ❌ 需要安装

### 推荐工具
- **VS Code** - 代码编辑器
- **Postman** - API测试
- **DB Browser for SQLite** - 数据库查看

## 🐛 常见问题解决

### 1. 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# 杀死进程 (Windows)
taskkill /PID <PID> /F
```

### 2. 虚拟环境问题
```bash
# 重新激活虚拟环境
cd manager-server
venv\Scripts\activate

cd DataChat-API  
venv\Scripts\activate
```

### 3. 依赖问题
```bash
# 重新安装依赖
pip install -r requirements.txt
```

## 📝 下一步操作

### 立即可以做的
1. ✅ 启动后端服务 (manager-server + DataChat-API)
2. ✅ 测试API接口
3. ✅ 查看数据库文件

### 需要Node.js后可以做的  
1. ❌ 启动前端管理面板
2. ❌ 启动浏览器扩展开发环境
3. ❌ 完整的端到端测试

## 🎉 总结

**恭喜！您的ConnectAI项目后端环境已经完全搭建完成！**

✅ **已完成:**
- Python后端服务环境
- 数据库和存储配置  
- 本地开发环境适配
- 启动脚本和文档

⏳ **待完成:**
- 安装Node.js
- 前端服务启动
- 完整功能测试

**现在您可以:**
1. 运行 `python setup_and_start.py` 启动后端服务
2. 访问 http://localhost:3000 和 http://localhost:5000 测试服务
3. 安装Node.js后启动前端服务

---

**🔥 项目已经可以运行了！开始您的ConnectAI开发之旅吧！**
