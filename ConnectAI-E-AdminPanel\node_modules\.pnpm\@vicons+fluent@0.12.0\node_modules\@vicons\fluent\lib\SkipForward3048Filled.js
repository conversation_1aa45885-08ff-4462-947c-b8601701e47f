'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M44 5.5a1.5 1.5 0 0 0-3 0v7.027C36.776 7.582 30.709 4.9 23.979 5c-8.4.126-16.269 4.411-20.072 12.084a1.5 1.5 0 0 0 2.688 1.332C9.832 11.883 16.59 8.111 24.023 8c6.51-.097 12.229 2.768 15.874 8H31.5a1.5 1.5 0 0 0 0 3h11a1.5 1.5 0 0 0 1.5-1.5v-12zM10.486 26.73a1.5 1.5 0 0 0 2.11-.081l.002-.001l.034-.034c.037-.036.1-.095.19-.17c.181-.15.464-.363.841-.578A6.68 6.68 0 0 1 17 25c1.685 0 2.75.457 3.361.973c.603.51.847 1.14.831 1.739C21.164 28.816 20.097 30.5 17 30.5a1.5 1.5 0 1 0 0 3c1.72 0 2.806.46 3.427.979c.61.51.852 1.138.835 1.728C21.23 37.301 20.146 39 17 39a6.678 6.678 0 0 1-3.337-.865a5.794 5.794 0 0 1-.84-.579a3.584 3.584 0 0 1-.225-.204h-.001a1.5 1.5 0 0 0-2.201 2.038c-.557-.607 0 0 0 0l.002.002l.002.002l.004.005l.012.013a2.903 2.903 0 0 0 .134.134c.084.081.2.188.349.312c.296.248.724.567 1.278.883A9.68 9.68 0 0 0 17 42c4.292 0 7.17-2.551 7.26-5.707c.044-1.535-.614-3.032-1.908-4.115a6.014 6.014 0 0 0-.317-.247c1.335-1.043 2.115-2.515 2.156-4.143c.04-1.527-.61-3.021-1.892-4.105C21.026 22.606 19.22 22 17 22a9.678 9.678 0 0 0-4.823 1.26a8.803 8.803 0 0 0-1.278.882a6.697 6.697 0 0 0-.45.413l-.018.017l-.015.017l-.012.012l-.004.005l-.003.003l-.001.001l-.002.002a1.5 1.5 0 0 0 .092 2.118zM34.315 22c-2.7 0-4.626 1.244-5.787 3.233c-1.095 1.874-1.463 4.313-1.463 6.767c0 2.454.368 4.893 1.462 6.767C29.69 40.757 31.616 42 34.316 42s4.626-1.244 5.787-3.233c1.095-1.874 1.463-4.313 1.463-6.767c0-2.454-.368-4.893-1.463-6.767C38.941 23.244 37.015 22 34.315 22zm-4.25 10c0-2.24.35-4.052 1.053-5.254C31.753 25.66 32.703 25 34.315 25s2.562.659 3.197 1.746c.702 1.202 1.053 3.014 1.053 5.254c0 2.24-.35 4.052-1.053 5.254C36.877 38.34 35.927 39 34.315 39s-2.562-.659-3.197-1.746c-.702-1.202-1.053-3.014-1.053-5.254z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'SkipForward3048Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
