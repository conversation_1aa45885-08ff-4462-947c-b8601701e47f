import logging
from flask import jsonify
from app import app
from models import init
from routes import *


class PermissionDenied(Exception):
    pass


class NeedAuth(Exception):
    pass


@app.errorhandler(Exception)
def api_exception(e):
    logging.exception(e)
    if isinstance(e, PermissionDenied):
        return jsonify({'code': -1, 'msg': str(e)}), 403
    if isinstance(e, NeedAuth):
        return jsonify({'code': -1, 'msg': str(e)}), 401
    return jsonify({'code': -1, 'msg': str(e)}), 500


if __name__ == "__main__":
    try:
        init()
        print("DataChat API服务器启动中...")
        print("服务地址: http://localhost:5000")
        app.run(port=5000, host="0.0.0.0", debug=True)
    except Exception as e:
        logging.exception(e)
        print(f"启动失败: {e}")
