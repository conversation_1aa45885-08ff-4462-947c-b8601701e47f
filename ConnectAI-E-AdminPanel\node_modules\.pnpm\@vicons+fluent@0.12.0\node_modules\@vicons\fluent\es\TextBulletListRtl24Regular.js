import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M20.75 17.5a1.25 1.25 0 1 0 0 2.499a1.25 1.25 0 0 0 0-2.499zm-3.5.5H2.75a.75.75 0 0 0-.102 1.493l.102.007h14.5a.75.75 0 0 0 .102-1.493L17.25 18zm3.5-7a1.25 1.25 0 1 0 0 2.499a1.25 1.25 0 0 0 0-2.499zm-3.5.5H2.75a.75.75 0 0 0-.102 1.493L2.75 13h14.5a.75.75 0 0 0 .102-1.493l-.102-.007zm3.5-7a1.25 1.25 0 1 0 0 2.499a1.25 1.25 0 0 0 0-2.499zm-3.5.5H2.75a.75.75 0 0 0-.102 1.493l.102.007h14.5a.75.75 0 0 0 .102-1.493L17.25 5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListRtl24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
