<template>
  <n-space v-if="loading" vertical>
    <n-skeleton height="100px" circle class="mla mra" />
    <n-skeleton height="28px" width="80%" :sharp="false" />
    <n-skeleton height="60px" />
    <n-space>
      <n-skeleton height="40px" width="100px" :sharp="false" />
      <n-skeleton height="40px" width="54px" :sharp="false" />
    </n-space>
  </n-space>
  <div
    v-else
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl flex-1 font-semibold dark:text-white">{{ $t('message.messenger.lang') }}</h3>
      <n-select class="flex-1" v-model:value="data.lang" :options="langOptions" />
    </div>
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl flex-1 font-semibold dark:text-white">{{ $t('message.messenger.zts') }}</h3>
      <n-color-picker class="flex-1" v-model:value="data.theme.primaryColor" />
    </div>

    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl flex-1 font-semibold dark:text-white">{{ $t('message.messenger.hyy') }}</h3>
      <n-input class="flex-1" v-model:value="data.welcome" />
    </div>
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl flex-1 font-semibold dark:text-white">{{ $t('Logo') }}</h3>
      <n-upload
        class="w-auto"
        :action="action"
        list-type="image-card"
        :max="1"
        :default-file-list="previewFileList"
        @preview="handlePreview"
        @finish="handleFinish"
      />
      <n-modal v-model:show="showModal" preset="card" style="width: 1000px" title="Logo">
        <img :src="previewImageUrl" style="width: 100%" />
      </n-modal>
    </div>

    <button
      type="button"
      class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      @click="handleSave"
    >
      <icon-akar-icons-save class="mr-2" />
      {{ $t('message.bot.save') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage, type UploadFileInfo } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { updateMessengerWebConfig } from '@/service/api/messenger';
import { t } from '@/locales';
const message = useMessage();

const route = useRoute();

const emit = defineEmits(['change', 'update:data']);

const action = import.meta.env.PROD === false ? '/api/api/oss/upload' : '/api/oss/upload';

const showModal = ref(false);
const previewImageUrl = ref('');

const previewFileList = ref<UploadFileInfo[]>([]);

const props = defineProps<{
  data: ApiMessenger.Resp.MessengerWebConfig;
  loading: boolean;
}>();

const data = useVModel(props, 'data', emit);

watch(
  () => data.value.logo,
  (val) => {
    previewFileList.value = [{ url: val, status: 'finished', name: 'logo', id: 'logo' }];
  }
);

const langOptions = [
  { label: '中文', value: 'zh_CN' },
  { label: 'English', value: 'en_US' }
];

async function handleSave() {
  await updateMessengerWebConfig({ id: route.query.id as string, data: data.value });
  message.success(t('message.msg.bccg'));
}

function handlePreview(file: UploadFileInfo) {
  const { url } = file;
  previewImageUrl.value = url as string;
  showModal.value = true;
}

function handleFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  const res = JSON.parse((options.event?.target as any).response ?? '{}');
  data.value.logo = res?.data;
  return { ...options.file, url: res?.data };
}
</script>
