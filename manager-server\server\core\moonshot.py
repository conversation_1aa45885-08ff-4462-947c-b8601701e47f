import json
import httpx
import logging
import sys
import warnings
from typing import (
    Any,
    Dict,
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)
from tornado.options import options
from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.adapters.openai import convert_dict_to_message, convert_message_to_dict
from langchain.schema import ChatGeneration, ChatResult, BaseMessage

logger = logging.getLogger(__name__)


class MoonshotClient(object):

    def build_query(
        self,
        messages=list(),
        api_base='',
        api_key='',
        **kwargs
    ):
        body = json.dumps(dict(messages=messages, **kwargs))
        headers = {'api-key': api_key, 'Authorization': 'Bearer {}'.format(api_key)}
        if 'https://kimichat' != api_base[:16]:
            headers = {
                'api-key': api_key,
                'api-base': api_base,
            }
            from core.api_base import NewApiBase
            api_base = NewApiBase('Moonshot').url
        return '{}/v1/chat/completions'.format(api_base), body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                if 'data:' == line[:5] and '[DONE]' not in line:
                    yield json.loads(line[5:])

    def create(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            return response.json()

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5] and '[DONE]' not in line:
                    yield json.loads(line[5:])

    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class MoonshotChat(SimpleChatModel):
    client: Any  #: :meta private:
    # 当前支持3个模型
    model_name: str = "moonshot-v1-8k"  # moonshot-v1-8k
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 3
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 0.95   # 默认0.95，范围 (0, 1.0]，不能为0
    top_p: float = 0.8          # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    user_id: str = ''           # 表示最终用户的唯一标识符，可以监视和检测滥用行为，防止接口恶意调用

    def _llm_type(self) -> str:
        return "moonshot_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'model': self.model_name,
            'temperature': self.temperature,
            'top_p': self.top_p,
        }

        message_dicts = [convert_message_to_dict(m) for m in messages]
        self.client = MoonshotClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, messages=message_dicts, **params):
                choices = stream_resp.get("choices", [])
                token = ''
                if len(choices) > 0:
                    token = choices[0]['delta'].get('content', '')
                    if run_manager:
                        run_manager.on_llm_new_token(token)
                    # print(token)
                response += token
            return response
        else:
            full_response = completion_with_retry(self, messages=message_dicts, **params)
            # print(full_response)
            return full_response['choices'][0]['message']['content']


if __name__ == "__main__":
    import asyncio
    async def main():
        from langchain.schema import HumanMessage

        api_key = ''
        api_base = 'https://api.moonshot.cn'

        chat = MoonshotChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        messages = [HumanMessage(content='你是谁')]
        # messages = [HumanMessage(content='帮我推荐一条西藏旅游线路')]
        result = chat(messages)
        print(result)
        # {'id': 'cmpl-dc996ab4b2544b6cae1c0c5e170d2f7d', 'object': 'chat.completion', 'created': 1699236974, 'model': 'moonshot-v1-8k', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': ' 当然可以！西藏是一个美丽而神秘的地方，拥有许多令人叹为观止的自然景观和丰富的文化遗产。以下是一条为期10天的西藏旅游线路推荐，涵盖了西藏的主要景点和文化体验：\n\n第1天：抵达拉萨\n- 抵达拉萨贡嘎国际机场或火车站，前往酒店办理入住手续。\n- 休息适应高原气候，晚上可以漫步在布达拉宫广场，欣赏夜景。\n\n第2天：拉萨市区游\n- 参观布达拉宫，了解西藏的历史和文化。\n- 游览大昭寺，感受藏传佛教的虔诚氛围。\n- 漫步八廓街，品尝当地特色小吃，购买手工艺品。\n\n第3天：拉萨 - 纳木错\n- 早上乘车前往纳木错，途中欣赏念青唐古拉山的壮丽景色。\n- 抵达纳木错后，游览湖边风光，观赏日落。\n\n第4天：纳木错 - 羊卓雍错 - 拉萨\n- 早晨在纳木错观赏日出，然后前往羊卓雍错。\n- 抵达羊卓雍错后，游览湖边风光，欣赏雪山倒影。\n- 下午返回拉萨。\n\n第5天：拉萨 - 日喀则\n- 乘车前往日喀则，途中欣赏雅鲁藏布江风光。\n- 抵达日喀则后，参观扎什伦布寺，了解藏传佛教文化。\n\n第6天：日喀则 - 定日 - 珠峰大本营\n- 乘车前往珠峰大本营，途中欣赏喜马拉雅山脉的壮丽景色。\n- 抵达珠峰大本营后，观赏珠穆朗玛峰的日落。\n\n第7天：珠峰大本营 - 日喀则\n- 早晨在珠峰大本营观赏日出，然后返回日喀则。\n\n第8天：日喀则 - 拉萨\n- 乘车返回拉萨，途中欣赏西藏高原风光。\n\n第9天：拉萨 - 林芝\n- 乘车前往林芝，途中欣赏尼洋河风光。\n- 抵达林芝后，游览巴松措，欣赏湖光山色。\n\n第10天：林芝 - 拉萨\n- 乘车返回拉萨，结束愉快的西藏之旅。\n\n这条线路涵盖了西藏的主要景点，包括布达拉宫、大昭寺、纳木错、羊卓雍错、珠峰大本营、扎什伦布寺、巴松措等。在旅行过程中，您将欣赏到西藏高原的壮丽风光，体验藏传佛教文化，品尝当地特色美食。希望这条线路能为您的西藏之行带来美好的回忆！'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 9, 'completion_tokens': 550, 'total_tokens': 559}}
        # data: {"id":"cmpl-8498a13c71294f68b277bfbf07a724cc","object":"chat.completion.chunk","created":1699241429,"model":"moonshot-v1-8k","choices":[{"index":0,"delta":{},"finish_reason":"stop","usage":{"prompt_tokens":5,"completion_tokens":34,"total_tokens":39}}]}

    asyncio.run(main())

