import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M19.5 15a2.5 2.5 0 0 1 2.493 2.335l.005.164v2.004a2.5 2.5 0 0 1-4.993.165L17 19.503V17.5a2.5 2.5 0 0 1 2.5-2.5zM11.997 5a8.5 8.5 0 0 1 8.476 9.138a3.487 3.487 0 0 0-3.575 1.018a1.752 1.752 0 0 0-2.057-1.108l-.147.042l-1.501.504l-.157.062l-.144.078c-.696.415-1.022 1.187-.844 1.928l.043.148l.062.157l.078.144l.098.148c.242.332.571.562.935.671l.158.04l.079.012v3.264l.011.18v.44a8.5 8.5 0 1 1-1.516-16.865zm3.998 10.648l.006.1v5.497a.75.75 0 0 1-1.493.102l-.007-.102v-4.454l-.513.173a.75.75 0 0 1-.91-.378l-.04-.095a.75.75 0 0 1 .379-.91l.094-.04l1.502-.503a.75.75 0 0 1 .982.61zm3.503.85a1 1 0 0 0-.992.884l-.007.116v2.004a1 1 0 0 0 1.992.117l.006-.117V17.5a1 1 0 0 0-.999-1zm-7.501-8.498a.75.75 0 0 0-.743.648l-.007.102v4.5l.007.102a.75.75 0 0 0 1.486 0l.007-.102v-4.5l-.007-.102a.75.75 0 0 0-.743-.648zm7.17-2.878l.081.062l1.15.999a.75.75 0 0 1-.903 1.193l-.082-.061l-1.149-.999a.75.75 0 0 1 .903-1.194zm-4.92-2.622a.75.75 0 0 1 .102 1.493l-.102.007h-4.5a.75.75 0 0 1-.102-1.493l.102-.007h4.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer1024Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
