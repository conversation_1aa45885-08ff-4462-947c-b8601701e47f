import time
import uuid
import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)   

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)
from tornado.options import options, define

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage
from langchain.schema import HumanMessage, AIMessage, SystemMessage

logger = logging.getLogger(__name__)


class HunyuanClient(object):

    def build_query(
        self,
        messages=list(),
        api_base='https://hunyuan.cloud.tencent.com',
        api_key='',
        app_id='',
        secret_id='',
        secret_key='',
        **kwargs
    ):
        headers = {'Content-Type': 'application/json', 'api-key': api_key}
        if app_id:
            headers = {
                'api-base': api_base,
                'app-id': app_id,
                'secret-id': secret_id,
                'secret-key': secret_key,
            }
            from core.api_base import NewApiBase
            api_base = NewApiBase('Hunyuan').url
        return '{}/hyllm/v1/chat/completions'.format(api_base), dict(messages=messages, **kwargs), headers

    def stream(self, url, data, headers, timeout=120):
        with httpx.stream("POST", url, json=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                if 'data: ' == line[:6]:
                    yield json.loads(line[6:])

    def create(self, messages=list(), model='', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages, stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers, timeout=timeout)
        else:
            response = httpx.post(url, json=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers, timeout=120):
        async with httpx.AsyncClient().stream("POST", url, json=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    async def acreate(self, messages=list(), model='', stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(messages, stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, json=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


async def acompletion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the async completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    async def _completion_with_retry(**kwargs: Any) -> Any:
        # Use OpenAI's async api https://github.com/openai/openai-python#async-api
        return await llm.client.acreate(**kwargs)

    return await _completion_with_retry(**kwargs)


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class HunyuanChat(SimpleChatModel):
    # https://cloud.tencent.com/document/product/1729/97732
    client: Any  #: :meta private:
    model_name: str = ""
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    # 支持官方的key
    app_id: Optional[str] = None
    secret_id: Optional[str] = None
    secret_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 1.0    # 默认1.0，取值区间为[0.0, 2.0]
    top_p: float = 1.0          # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认1.0，取值区间为[0.0, 1.0]
    user_id: str = ''           # 表示最终用户的唯一标识符，可以监视和检测滥用行为，防止接口恶意调用

    def _llm_type(self) -> str:
        return "hunyuan_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        timestamp = int(time.time())
        params = {
            'stream': int(self.streaming),
            'api_key': self.api_key,
            'api_base': self.api_base,
            'app_id': self.app_id,
            'secret_id': self.secret_id,
            'secret_key': self.secret_key,
            # 'model': self.model_name,
            'temperature': self.temperature,
            'top_p': self.top_p,
            'timestamp': timestamp,
            'expired': timestamp + 3600,
            'query_id': 'connectai_' + str(uuid.uuid4())
        }

        message_dicts = []
        for i in range(0, len(messages), 2):
            m = messages[i]
            if isinstance(m, HumanMessage) or isinstance(m, SystemMessage):
                if i + 1 == len(messages):
                    message_dicts.append({'role': 'user', 'content': m.content})
                    continue
                m2 = messages[i + 1]
                if isinstance(m2, AIMessage) and m2.content:
                    message_dicts.append({'role': 'user', 'content': m.content})
                    message_dicts.append({'role': 'assistant', 'content': m2.content})
        message_dicts = message_dicts[-40:]  # 长度最多为40
        self.client = HunyuanClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, messages=message_dicts, **params):
                if 'error' in stream_resp:
                    logging.error(stream_resp['error'])
                    raise Exception(stream_resp['error']['message'])
                token = stream_resp['choices'][0]['delta']['content']
                response += token
                if run_manager:
                    run_manager.on_llm_new_token(token)
            return response
        else:
            full_response = completion_with_retry(self, messages=message_dicts, **params)
            if 'error' in full_response:
                logging.error(full_response['error'])
                raise Exception(full_response['error']['message'])
            return full_response['choices'][0]['messages']['content']


if __name__ == "__main__":
    import asyncio
    async def main():
        api_key = '123'
        api_base = 'http://0.0.0.0:10086'

        chat = HunyuanChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        messages = [HumanMessage(content='你是谁')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


