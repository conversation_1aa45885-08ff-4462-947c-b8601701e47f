import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 3.75a.75.75 0 0 0-1.5 0v20.5a.75.75 0 0 0 1.5 0V3.75zM17 25h-5a1 1 0 0 1-1-1v-5.5h6V25zm4.25 0H18.5v-6.5H25v2.75A3.75 3.75 0 0 1 21.25 25zM25 11v6h-6.5v-6H25zm0-4.25V9.5h-6.5V3h2.75A3.75 3.75 0 0 1 25 6.75zM11 4a1 1 0 0 1 1-1h5v6.5h-6V4zm6 13v-6h-6v6h6z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TableStackLeft28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
