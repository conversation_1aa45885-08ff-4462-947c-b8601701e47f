# -*- coding: utf-8 -*-
import json
import logging
from base import *
from tornado.options import options, parse_command_line
from core.consumer import BaseConsumer, LOGGER, LOG_FORMAT
from core.rabbitmq import MqSession
from modules.callback.model import FeishuModel
from settings.constant import AppResult


class FeishuConsumer(BaseConsumer):

    async def process_message(self, tag, app_id, data, **kwargs):
        try:
            logging.info("DEBUG %r", data)
            # TODO 
            logging.info("DEBUG result_type %r", data.result_type)
            with FeishuModel() as model:
                model.init_by_bot_id(data.extra.bot_instance_id)
                if data.result_type in [AppResult.ReplyCard.value, AppResult.ReplyNewCard.value]:
                    reply_message_id = data.get('additional_kwargs', {}).get('reply_message_id')
                    response = await model.client.reply(
                        reply_message_id or data.extra.message_id,
                        data.result_content,
                        msg_type='interactive'
                    )
                    if data.result_type == AppResult.ReplyCard.value and not reply_message_id:
                        await model.update_reply_message_id(data.log_id, response.data.message_id)
                elif data.result_type == AppResult.SendCard.value:
                    # 这里使用chat_id确保群聊回复到群聊，单聊回复到单聊
                    response = await model.client.send(
                        data.extra.chat_id,
                        data.result_content,
                        receive_id_type='chat_id',
                        msg_type='interactive'
                    )
                    await model.update_reply_message_id(data.log_id, response.data.message_id)
                elif data.result_type == AppResult.UpdateCard.value:
                    if 'additional_kwargs' in data and data.additional_kwargs.get('reply_message_id'):
                        reply_message_id = data.additional_kwargs.reply_message_id
                    elif 'additional_kwargs' in data and data.additional_kwargs.get('reply_log_id'):
                        reply_message_id = await model.get_reply_message_id(data.additional_kwargs.reply_log_id)
                    else:
                        reply_message_id = await model.get_reply_message_id(data.log_id)
                    if reply_message_id:
                        await model.client.update(
                            reply_message_id,
                            data.result_content,
                        )
                    else:
                        logging.error('can not find reply_message_id')
                elif data.result_type == AppResult.ReplyAudio.value:
                    response = await model.client.reply(
                        data.extra.message_id,
                        data.result_content,
                        msg_type='audio'
                    )
                    if data.result_type == AppResult.ReplyCard.value:
                        await model.update_reply_message_id(data.log_id, response.data.message_id)
                elif data.result_type == AppResult.CardWithButton.value:
                    # TODO 测试发送卡片消息
                    await model.client.send_card(
                        data.extra.extra.open_id,
                        'card title',
                        {
                            'tag': 'img', 'img_key': 'img_v2_9b2a46dd-7f05-48b5-a988-377fa11de8dg',
                            'alt': {'tag': 'plain_text', 'content': '图片'}
                        },
                        actions=[
                            {
                                'tag': 'button',
                                'text': {'tag': 'plain_text', 'content': 'button1'},
                                'value': {'value1': 1}
                            }
                        ]
                    )
                elif data.result_type == AppResult.ShortcutCard.value:
                    # 仅单聊
                    response = await model.client.send(
                        data.extra.sender_id,
                        data.result_content,
                        msg_type='interactive'
                    )
                    await model.update_reply_message_id(data.log_id, response.data.message_id)
                elif data.result_type == AppResult.Audio.value:
                    response = await model.client.send(
                        data.extra.chat_id,
                        data.result_content,
                        receive_id_type='chat_id',
                        msg_type='audio'
                    )
                    await model.update_reply_message_id(data.log_id, response.data.message_id)
                elif data.result_type == AppResult.File.value:

                    response = await model.client.send(
                        data.extra.chat_id,
                        data.result_content,
                        receive_id_type='chat_id',
                        msg_type='file'
                    )
                    await model.update_reply_message_id(data.log_id, response.data.message_id)

            self.acknowledge_message(tag)
        except Exception as e:
            LOGGER.exception(e)
            self.reject_message(tag, requeue=False)
            return False


def main():
    logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)

    logging.info("DEBUG %r", options.RABBIT_MQ_URI)
    consumer = FeishuConsumer(
        options.RABBIT_MQ_URI,
        queue=options.QUEUE_FEISHU,
        exchange=options.RABBIT_MQ_EXCHANGE,
        prefetch_count=10,
    )
    try:
        consumer.run()
    except KeyboardInterrupt:
        consumer.stop()


if __name__ == '__main__':
    main()

