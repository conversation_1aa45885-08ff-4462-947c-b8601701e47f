import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12 4.998a8.5 8.5 0 0 1 8.432 9.578a3.483 3.483 0 0 0-1.453-.547a7 7 0 1 0-3.724 5.667a3.429 3.429 0 0 0-.253 1.302v.25l.009.168l.005.031A8.5 8.5 0 1 1 12 4.997zm6.503 10a2.5 2.5 0 0 1 .165 4.994l-.165.005a1 1 0 0 0-.846.465l-.02.035h2.616a.75.75 0 0 1 .744.648l.006.102a.75.75 0 0 1-.648.743l-.102.007h-3.501a.75.75 0 0 1-.744-.648l-.006-.102v-.249a2.501 2.501 0 0 1 2.336-2.495l.165-.006a1 1 0 0 0 .117-1.993l-.117-.007h-.251a.75.75 0 0 0-.744.649l-.006.101a.75.75 0 0 1-1.5 0a2.25 2.25 0 0 1 2.096-2.245l.154-.005h.251zM12 8a.75.75 0 0 1 .743.648l.007.102v4.5a.75.75 0 0 1-1.493.101l-.007-.101v-4.5A.75.75 0 0 1 12 8zm7.162-2.88l.08.062l1.132.986a.75.75 0 0 1-.905 1.193l-.081-.062l-1.13-.986a.75.75 0 0 1 .904-1.192zM14.25 2.487a.75.75 0 0 1 .102 1.493l-.102.007h-4.5a.75.75 0 0 1-.102-1.493l.102-.007h4.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer224Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
