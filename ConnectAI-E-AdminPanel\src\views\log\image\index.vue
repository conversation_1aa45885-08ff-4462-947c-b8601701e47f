<template>
  <div class="h-full">
    <n-card class="shadow-sm rounded-6px">
      <div class="flex justify-between w-full items-center">
        <n-space class="flex justify-start items-center gap-8">
          <n-input
            v-model:value="searchKeywords.content"
            placeholder="按内容过滤"
            clearable
            size="large"
            :on-keydown="handleKeydown"
            :on-clear="handleClear"
          ></n-input>
          <n-input
            v-model:value="searchKeywords.instanceName"
            placeholder="按应用名称过滤"
            clearable
            size="large"
            :on-keydown="handleKeydown"
            :on-clear="handleClear"
          ></n-input>
          <n-date-picker
            v-model:value="range"
            size="large"
            type="daterange"
            clearable
            :actions="null"
            :close-on-select="true"
          />
          <button
            type="button"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            @click="handleSearch(searchKeywords)"
          >
            <icon-akar-icons-search class="mr-2" />
            查询日志
          </button>
        </n-space>

        <button
          type="button"
          class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 min-w-130px dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
          @click="handleExport(exportCfg)"
        >
          <icon-akar-icons-cloud-download class="mr-2" />
          批量导出
        </button>
      </div>
    </n-card>
    <n-card class="shadow-sm min-h-[80%] rounded-6px mt-4">
      <loading-empty-wrapper class="min-h-600px" :loading="loading" :empty="empty">
        <!-- <PicTable :data="data" :pagination-options="paginationOptions" @handleEdit="handleEdit"
          @handleDelete="handleDelete" @handleActive="handleActive" /> -->
      </loading-empty-wrapper>
    </n-card>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref, h } from 'vue';
import { useRoute } from 'vue-router';
import type { DataTableColumn } from 'naive-ui';
import { NSpace, NPopover, NButton } from 'naive-ui';
import { useLoadingEmpty, usePagination } from '@/hooks';
import type { DataSource } from '~/src/service/api/log';
import { exportMJLogList as exportLogList, fetchMJLogList as fetchLogList } from '~/src/service/api/log';
import { exportCSV } from '~/src/utils/common/export';
const PAGE_SIZE = 10;
const data = ref<DataSource<ApiLog.Resp.LogList>>({});

const { loading, startLoading, endLoading, empty, setEmpty } = useLoadingEmpty();
const route = useRoute();
const searchKeywords = reactive({ content: '', instanceName: '' });
const exportCfg = ref<typeof searchKeywords>();
const date = new Date();
const now = date.getTime();
const range = ref<[number, number]>([date.setDate(date.getDate() - 7), now]);
const currentPage = ref(1);

const { pagination, paginationOptions } = usePagination();

function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    handleSearch(searchKeywords);
  }
}
function handleClear() {
  getLogList();
}

async function getLogList(keyword?: string) {
  startLoading();
  try {
    const res = await fetchLogList({ ...pagination, keyword });
    data.value = res.data?.data || [];
    endLoading();
    setEmpty(res.data?.total === 0);
  } catch (err) {}
}

async function handleSearch(keywords: typeof searchKeywords) {
  // try {
  //   await getLogList(1, keywords.content, keywords.instanceName);
  //   currentPage.value = 1;
  // } catch (err) {}
  // exportCfg.value = { ...keywords };
}
function handleUpdatePage(page: number) {
  // currentPage.value = page;
  // getLogList(page);
}
async function handleExport(exportConfig: any) {
  // const formatted = range.value.map(ts => dateFormat(ts));
  // try {
  //   const res = await exportLogList({
  //     content: exportConfig?.content,
  //     instanceName: exportConfig?.instanceName,
  //     since: formatted[0],
  //     until: formatted[1]
  //   });
  //   exportCSV(res.data!);
  // } catch (err) {
  //   console.error(err);
  // }
}
onMounted(() => {
  getLogList();
});
</script>

<style scoped></style>
