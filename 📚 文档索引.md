# 📚 ConnectAI 文档索引

## 🚀 快速开始

### 主要文档
- **[README_本地启动.md](README_本地启动.md)** - 本地环境启动指南 ⭐
- **[README.md](README.md)** - 项目总体介绍
- **[CODE_DOC_项目文档说明.md](CODE_DOC_项目文档说明.md)** - 项目文档说明

### 启动脚本
- **[start_connectai.py](start_connectai.py)** - 一键启动所有服务 ⭐
- **[check_environment.py](check_environment.py)** - 环境检查工具 ⭐
- **[quick_start.bat](quick_start.bat)** - Windows快速启动脚本
- **[init_database_with_admin.py](init_database_with_admin.py)** - 数据库初始化脚本

## 📋 使用流程

### 1. 首次使用
```bash
# 1. 检查环境
python check_environment.py

# 2. 如果环境未就绪，初始化数据库
python init_database_with_admin.py

# 3. 启动服务
python start_connectai.py
```

### 2. 日常使用
```bash
# 直接启动（推荐）
python start_connectai.py

# 或使用Windows批处理
quick_start.bat
```

## 🔧 工具脚本

| 脚本 | 功能 | 状态 |
|------|------|------|
| `start_connectai.py` | 一键启动所有服务 | ✅ 推荐使用 |
| `check_environment.py` | 环境检查和诊断 | ✅ 推荐使用 |
| `test_services_running.py` | 测试服务运行状态 | ✅ 可用 |
| `init_database_with_admin.py` | 初始化数据库和管理员 | ✅ 首次使用 |
| `simple_verify.py` | 简单验证脚本 | ⚠️ 已被替代 |

## 📁 项目结构

### 核心服务
```
manager-server/
├── simple_flask_server.py     # Manager Server (Flask版本) ✅
├── venv/                       # Python虚拟环境 ✅
└── requirements.txt            # 依赖列表 ✅

DataChat-API/
├── simple_app.py               # DataChat API (Flask版本) ✅
├── venv/                       # Python虚拟环境 ✅
└── requirements.txt            # 依赖列表 ✅
```

### 数据存储
```
data/
├── connectai.db               # SQLite数据库 ✅
├── admin_info.json            # 管理员信息 ✅
├── files/                     # 文件存储 ✅
└── search_index/              # 搜索索引 ✅
```

### 前端项目（需要Node.js）
```
ConnectAI-E-AdminPanel/        # Vue3管理面板
ConnectAI-Helper/              # React浏览器扩展
```

## 🌐 服务地址

- **Manager Server**: http://localhost:3000
- **DataChat API**: http://localhost:5000
- **健康检查**: http://localhost:5000/health

## 👤 预置账号

- **邮箱**: <EMAIL>
- **密码**: admin123
- **租户**: default-tenant

## 🧪 API测试

### 基础测试
```bash
# 健康检查
curl http://localhost:5000/health

# 登录测试
curl -X POST http://localhost:3000/api/login \
  -H 'Content-Type: application/json' \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

### 功能测试
```bash
# 获取分类
curl http://localhost:3000/api/categories

# 搜索测试
curl -X POST http://localhost:5000/api/search \
  -H 'Content-Type: application/json' \
  -d '{"query": "test"}'
```

## 🛠️ 故障排除

### 常见问题
1. **环境检查失败** → 运行 `python init_database_with_admin.py`
2. **端口被占用** → 检查3000和5000端口
3. **依赖问题** → 重新安装requirements.txt
4. **数据库问题** → 重新初始化数据库

### 诊断工具
- `check_environment.py` - 全面环境检查
- `test_services_running.py` - 服务状态检查

## 📊 系统架构

### 本地环境适配
| 生产组件 | 本地替代 | 状态 |
|---------|---------|------|
| MySQL | SQLite | ✅ |
| Redis | 内存存储 | ✅ |
| Elasticsearch | 文件存储 | ✅ |
| RabbitMQ | 内存队列 | ✅ |
| Docker | 本地进程 | ✅ |

### 技术栈
- **后端**: Python + Flask + SQLite
- **前端**: Vue3 + React (需要Node.js)
- **存储**: 本地文件系统
- **搜索**: 文件索引

## 🎯 开发指南

### 后端开发
1. 修改 `manager-server/simple_flask_server.py`
2. 修改 `DataChat-API/simple_app.py`
3. 重启服务测试

### 前端开发
1. 安装Node.js
2. 启动前端开发服务器
3. 配置API代理

### 数据库管理
1. 使用SQLite工具查看数据
2. 修改 `init_database_with_admin.py` 调整表结构
3. 重新初始化数据库

## 📞 技术支持

### 文档资源
- 本文档索引 - 快速查找相关文档
- README_本地启动.md - 详细启动指南
- 各模块的CODE_DOC文档 - 代码详细说明

### 脚本工具
- 环境检查和诊断工具
- 一键启动和测试脚本
- 数据库管理工具

---

## 🎉 总结

**ConnectAI本地环境已完全配置完成！**

### ✅ 可用功能
- 完整的后端API服务
- 预置管理员账号
- 数据库和文件存储
- 搜索和上传功能

### 🚀 立即开始
1. 运行 `python start_connectai.py`
2. 访问 http://localhost:3000
3. 使用 <EMAIL> / admin123 登录

**开始您的ConnectAI开发之旅吧！** 🚀
