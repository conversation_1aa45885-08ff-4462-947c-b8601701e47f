'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M12 2a2.5 2.5 0 0 0-2.5 2.5v3H7.112c-1.174 0-2.077.993-2.04 2.131c.043 1.292.045 3.356-.217 5.483c-.265 2.148-.787 4.264-1.735 5.729A.75.75 0 0 0 3.75 22h11.775l-1.012-1.013a1.746 1.746 0 0 1-.345-.487H11.13l.353-1.587a.75.75 0 0 0-1.464-.326L9.593 20.5H8.28l.079-.21c.23-.623.497-1.49.632-2.434a.75.75 0 0 0-1.485-.212a10.674 10.674 0 0 1-.555 2.128c-.103.28-.198.507-.267.663l-.029.065H5.01c.722-1.574 1.117-3.445 1.334-5.202c.096-.787.159-1.564.197-2.298H17.5v1.193a1.744 1.744 0 0 1 1.5-.047V9.75a2.25 2.25 0 0 0-2.25-2.25H14.5v-3A2.5 2.5 0 0 0 12 2zm5.5 9.5H6.59c.01-.736-.001-1.39-.019-1.918A.557.557 0 0 1 7.111 9h3.139a.75.75 0 0 0 .75-.75V4.5a1 1 0 1 1 2 0v3.75c0 .414.336.75.75.75h3a.75.75 0 0 1 .75.75v1.75zm-2.28 7.72a.75.75 0 0 1 1.06 0l1.27 1.27v-4.74a.75.75 0 0 1 1.5 0v4.64l1.17-1.17a.75.75 0 1 1 1.06 1.06l-2.5 2.5a.75.75 0 0 1-1.06 0l-2.5-2.5a.75.75 0 0 1 0-1.06z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'PaintBrushArrowDown24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
