<template>
  <div class="cursor-pointer">
    <div class="relative bg-white dark:bg-gray-800 py-4 px-6 rounded-xl w-64 my-2 shadow-xl cardShadow min-w-340px">
      <div class="flex justify-start items-center">
        <div class="text-white flex items-center rounded-md p1 shrink-0">
          <component
            :is="
              iconRender({
                cdnIcon: icon
              })
            "
            style="width: 64px; height: 64px"
          />
        </div>
        <div>
          <p class="text-xl font-semibold mb-1 truncate max-w-240px">{{ title }}</p>
          <div class="flex space-x-2 text-gray-400 text-xs items-center">
            <p class="">
              {{ typeNameFormat }}
            </p>
          </div>
        </div>
      </div>

      <div class="flex space-x-0 absolute right-6 top-3">
        <div class="hover:bg-gray-200 hover:op-90 rounded-md p-1 op-60">
          <!--          <svg xmlns="http://www.w3.org/2000/svg" width="20" viewBox="0 0 24 24">-->
          <!--            <g fill="none" fill-rule="evenodd">-->
          <!--              <path-->
          <!--                d="M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"-->
          <!--              />-->
          <!--              <path-->
          <!--                fill="currentColor"-->
          <!--                d="M4 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H4Zm4.625 5.63a1.235 1.235 0 0 1 1.715-.992c.504.216 1.568.702 2.916 1.48a28.331 28.331 0 0 1 2.74 1.786a1.234 1.234 0 0 1 0 1.98a28.3 28.3 0 0 1-2.74 1.784a28.322 28.322 0 0 1-2.916 1.482a1.234 1.234 0 0 1-1.715-.992a28.566 28.566 0 0 1-.176-3.264c0-1.551.112-2.719.176-3.264Z"-->
          <!--              />-->
          <!--            </g>-->
          <!--          </svg>-->
        </div>
      </div>
      <div class="mt-2">
        <div class="text-sm line-clamp-2 h-40px">{{ descriptionFormat }}</div>
        <div class="border-t-1 op-30 my-2"></div>
        <div class="flex justify-between items-end mt-2">
          <div class="my-2">
            <div class="flex space-x-2 hover:op-80">
              <component v-if="showIcon('dingding')" :is="iconRender({ localIcon: 'dingding' })" class="w-6 h-6 rounded-full border-white" />
              <component v-if="showIcon('feishu')" :is="iconRender({ localIcon: 'feishu' })" class="w-6 h-6 rounded-full border-white" />
              <component v-if="showIcon('wxwork')" :is="iconRender({ localIcon: 'wxwork' })" class="w-6 h-6 rounded-full border-white" />
              <component v-if="showIcon('wework')" :is="iconRender({ localIcon: 'wework' })" class="w-6 h-6 rounded-full border-white" />
            </div>
          </div>
          <div class="">
            <div
              v-if="!tenantStatus"
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleBuy"
            >
              {{ $t('message.market.ljhq') }}
            </div>
            <div
              v-else-if="tenantStatus === 1"
              class="btn-install bg-gradient-to-r! from-cyan-500 to-blue-500 hover:bg-gradient-to-bl! focus:ring-4 focus:outline-none focus:ring-cyan-300 dark:focus:ring-cyan-800"
              @click.stop="handleInstall"
            >
              {{ $t('message.market.az') }}
            </div>
            <div
              v-else-if="tenantStatus === 2"
              class="btn-install bg-gradient-to-br! from-purple-600 to-blue-500 hover:bg-gradient-to-bl! focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800"
              @click.stop="handleDetail(id)"
            >
              {{ $t('message.market.gl') }}
            </div>

            <button
              v-else-if="tenantStatus === -1"
              class="relative inline-flex items-center justify-center p-0.5 mr-2 overflow-hidden text-sm font-medium text-gray-900 rounded-lg group bg-gradient-to-br from-cyan-500 to-blue-500 group-hover:from-cyan-500 group-hover:to-blue-500 hover:text-white dark:text-white focus:ring-4 focus:outline-none focus:ring-cyan-200 dark:focus:ring-cyan-800"
              @click.stop="handleWait(id)"
            >
              <span
                class="relative px-5 py-1.5 transition-all ease-in duration-75 bg-white dark:bg-gray-900 rounded-md group-hover:bg-opacity-0"
              >
                {{ $t('message.market.jjfb') }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹框 -->
    <purchase-tip v-model:value="showTip" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { routeName } from '@/router';
import { useIconRender, useRouterPush } from '@/composables';
import { useTenantPrivilege } from '@/hooks';
import { t } from '@/locales';
const { allow, deny } = useTenantPrivilege();
const showTip = ref(false);
const { iconRender } = useIconRender();

// 颜色 区分LLM类型 红色 文本类、蓝色 图片类、绿色 视频类

interface Props {
  title: string;
  price: number;
  tenantStatus: number;
  description: string;
  name: string;
  id: string;
  icon: string;
  typeName?: string[]; // 类型 小程序、机器人、多维表格等
  support_bot: Array<string>;
}
defineOptions({ name: 'BotCard' });
const props = defineProps<Props>();
const emit = defineEmits(['handle-buy', 'handle-install']);
const { routerPush } = useRouterPush();

// typeName backend api is not ready, now moc
const typeNameFormat = computed(() => {
  // return string
  const { typeName } = props;
  return props.typeName?.join('、') || 'IM Bot';
});

const descriptionFormat = computed(() => {
  const { description } = props;
  return description || t('message.my.zwms');
});
function handleBuy() {
  if (allow(`app.buy.${props.id}`)) {
    emit('handle-buy');
  } else {
    showTip.value = true;
  }
}

function handleWait() {
  return '';
}
function handleInstall() {
  emit('handle-install');
}

function showIcon(x:string){
  return props.support_bot.includes(x)
}

function handleDetail(id: string) {
  routerPush({ name: routeName('bot_info'), query: { id } });
}
</script>

<style scoped lang="scss">
.cardShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;

  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}
</style>
