<template>
  <div
    class="w-[340px] h-[380px] bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"
  >
    <div class="flex justify-between items-center px-4 py-4">
      <button
        id="dropdownButton"
        :data-dropdown-toggle="dropDownId"
        class="inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1.5"
        type="button"
      >
        <span class="sr-only">Open dropdown</span>
        <svg
          aria-hidden="true"
          class="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"
          ></path>
        </svg>
      </button>
      <RebotStatus :status="robot.tenant_status" :if-loading="false" />
      <!-- Dropdown menu -->
      <div
        :id="dropDownId"
        class="z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow w-32 dark:bg-gray-700"
      >
        <ul aria-labelledby="dropdownButton" class="py-2">
          <li>
            <a
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
              href="#"
              @click="(e) => handleCheckLog(e, robot.title)"
              >{{ $t('message.my.dhrz') }}</a
            >
          </li>
        </ul>
      </div>
    </div>

    <div class="p-8 rounded-t-lg flex-center h-[180px]" alt="product image">
      <component :is="iconRender({ cdnIcon: robot.icon, fontSize: 160 })" style="width: 220px" />
    </div>

    <div class="px-5 pb-5">
      <div class="i-flex-y-center gap-2">
        <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">{{ robot.title }}</h5>
      </div>
      <div class="flex justify-start mt-2.5 mb-5">
        <span
          v-if="robot.name"
          class="bg-indigo-100 text-indigo-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-indigo-200 dark:text-indigo-800"
          >{{ robot.name }}</span
        >
        <span
          class="bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-blue-200 dark:text-blue-800 ml-2"
          >{{ $t('message.my.feishu') }}</span
        >
        <span
          class="bg-gray-100 text-gray-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-gray-200 dark:text-gray-800 ml-2"
          >{{ $t('message.my.dingding') }}</span
        >
      </div>
      <div class="flex items-center justify-end">
        <div
          v-if="robot.tenant_status === 1"
          class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          @click.stop="handleInstall(robot)"
        >
          {{ $t('message.market.ljaz') }}
        </div>
        <div
          v-if="robot.tenant_status === 2"
          class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          @click.stop="handleDetails(robot.id)"
        >
          {{ $t('message.market.ckxq') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import { initDropdowns, initModals } from 'flowbite';
import { routeName } from '@/router';
import { useBotStore } from '@/store';
import { useIconRender, useRouterPush } from '@/composables';
import RebotStatus from '@/views/bot/my/components/rebotStatus.vue';
import { getAppClient, getAppResource, getAppResources } from '@/service/api/app';

const botStore = useBotStore();
const { setApp, setAppClient, setAppResource, setInstallBotShow, setAppResources } = botStore;
const { routerPush } = useRouterPush();

const props = defineProps<{ robot: ApiApp.Application }>();

const { iconRender } = useIconRender();

const dropDownId = computed(() => {
  return `dropdown-${props.robot.id}`;
});

onMounted(() => {
  initDropdowns();
  initModals();
});

async function handleInstall(item: ApiApp.Application) {
  const {
    data: { data: clientData }
  } = await getAppClient({ id: item.id });
  const {
    data: { data: resourceData }
  } = await getAppResource({ id: item.id });
  const {
    data: { data: resourcesData }
  } = await getAppResources({ id: item.id });
  setApp(item);
  setAppClient(clientData);
  setAppResource(resourceData);
  setAppResources(resourcesData);
  setInstallBotShow(true);
}

function handleDetails(id: string) {
  routerPush({ name: routeName('bot_info'), query: { id } });
}

function handleCheckLog(e: Event, name: string) {
  e.preventDefault();
  routerPush({ name: routeName('log_chat'), query: { app: name } });
}
</script>

<style scoped lang="scss">
.cardShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}
</style>
