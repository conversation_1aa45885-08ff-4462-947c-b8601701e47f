import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.952 4.079A4 4 0 0 1 18.684 3H25.5A3.5 3.5 0 0 1 29 6.5v6.757a4 4 0 0 1-1.172 2.829l-.467.467a5.002 5.002 0 0 0-9.282 1.553A4.002 4.002 0 0 0 15 22v6c0 .214.018.424.05.628a4.252 4.252 0 0 1-5.054-.719l-6.326-6.326a4.25 4.25 0 0 1 .101-6.109L15.952 4.08zM22.5 12a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zm-3 7.5H19a2.5 2.5 0 0 0-2.5 2.5v6a2.5 2.5 0 0 0 2.5 2.5h8a2.5 2.5 0 0 0 2.5-2.5v-6a2.5 2.5 0 0 0-2.5-2.5h-.5V19a3.5 3.5 0 1 0-7 0v.5zm2-.5a1.5 1.5 0 0 1 3 0v.5h-3V19zm3.5 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagLock32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
