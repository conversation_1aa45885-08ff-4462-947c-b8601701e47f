<template>
  <div>
    <NoPermission v-if="deny('page.log.chat')" />
    <n-card v-else class="shadow-sm rounded-16px h-full flex flex-col">
      <template #header>
        <div class="flex flex-wrap justify-between w-full items-center gap-4">
          <n-space class="flex justify-start items-center gap-8">
            <n-input
              v-model:value="searchKeywords.keyword"
              :placeholder="t('message.log.anrgl')"
              clearable
              size="large"
              :on-keydown="handleKeydown"
              :on-clear="handleClear"
            ></n-input>
            <n-input
              v-model:value="searchKeywords.app"
              :placeholder="t('message.log.ayygl')"
              clearable
              size="large"
              :on-keydown="handleKeydown"
              :on-clear="handleClear"
            ></n-input>
            <n-input
              v-model:value="searchKeywords.user"
              :placeholder="t('message.log.ayhm')"
              clearable
              size="large"
              :on-keydown="handleKeydown"
              :on-clear="handleClear"
            ></n-input>
            <n-date-picker
              v-model:value="range"
              size="large"
              type="daterange"
              clearable
              :actions="null"
              :close-on-select="true"
            />
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleSearch(searchKeywords)"
            >
              <icon-akar-icons-search class="mr-2" />
              {{ t('message.log.cxrz') }}
            </button>
          </n-space>
          <button
            type="button"
            class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 min-w-130px dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
            @click="handleExport"
          >
            <icon-akar-icons-cloud-download class="mr-2" />
            {{ t('message.log.pldc') }}
          </button>
        </div>
      </template>
      <loading-empty-wrapper class="min-h-350px" :loading="loading" :empty="empty">
        <ChatTable v-if="!loading && !empty" :data="data" :pagination-options="paginationOptions" />
      </loading-empty-wrapper>
    </n-card>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { NSpace } from 'naive-ui';
import dayjs from 'dayjs';
import { useLoadingEmpty, usePagination, useTenantPrivilege } from '@/hooks';
import { fetchLogList, exportLogList } from '@/service/api/log';
import ChatTable from './chat-table.vue';
import { t } from '@/locales';
const data = ref<ApiLog.ChatLog[]>([]);
const route = useRoute();

const { deny } = useTenantPrivilege();
const { loading, startLoading, endLoading, empty, setEmpty } = useLoadingEmpty();
const date = new Date();
const now = date.getTime();
const range = ref<[number, number]>([date.setDate(date.getDate() - 7), now]);
const searchKeywords = reactive({
  keyword: '',
  app: route.query?.app as string,
  user: route.query?.user as string,
  get start_date() {
    return dayjs(range.value[0]).format('YYYY-MM-DD');
  },
  get end_date() {
    return dayjs(range.value[1]).format('YYYY-MM-DD');
  }
});
const itemCount = ref<number>(0);

const { pagination, paginationOptions } = usePagination({ itemCount });

watch(pagination, () => {
  getLogList(searchKeywords);
});

function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    handleSearch(searchKeywords);
  }
}
function handleClear() {
  getLogList();
}

async function getLogList(keywords?: typeof searchKeywords) {
  startLoading();
  try {
    const res = await fetchLogList({ ...pagination, ...keywords });
    console.log(res.data?.data);
    data.value = res.data?.data || [];
    itemCount.value = res.data!.total;
    endLoading();
    setEmpty(res.data?.total === 0);
  } catch (err) {}
}

async function handleSearch(keywords: typeof searchKeywords) {
  await getLogList(keywords);
}

async function handleExport() {
  exportLogList(searchKeywords);
}
onMounted(() => {
  getLogList(searchKeywords);
});
</script>

<style scoped></style>
