#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个服务启动
用于调试启动问题
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_datachat_api():
    """测试DataChat API启动"""
    print("🧪 测试DataChat API启动...")
    
    # 检查文件是否存在
    script_path = Path("DataChat-API/simple_app.py")
    python_path = Path("DataChat-API/venv/Scripts/python.exe")
    
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    if not python_path.exists():
        print(f"❌ Python解释器不存在: {python_path}")
        return False
    
    print(f"✅ 脚本路径: {script_path}")
    print(f"✅ Python路径: {python_path}")
    
    try:
        # 使用绝对路径启动
        command = [str(python_path.absolute()), "simple_app.py"]
        cwd = "DataChat-API"
        
        print(f"🚀 启动命令: {command}")
        print(f"🚀 工作目录: {cwd}")
        
        process = subprocess.Popen(
            command,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=False
        )
        
        print(f"✅ 进程已启动，PID: {process.pid}")
        
        # 等待几秒钟
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 进程仍在运行")
            
            # 测试端口
            import socket
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', 5000))
                sock.close()
                
                if result == 0:
                    print("✅ 端口5000已开放")
                    
                    # 测试HTTP请求
                    try:
                        import urllib.request
                        response = urllib.request.urlopen("http://localhost:5000/", timeout=3)
                        print("✅ HTTP请求成功")
                        print(f"   响应状态: {response.status}")
                        
                        # 停止进程
                        process.terminate()
                        process.wait(timeout=5)
                        print("✅ 进程已停止")
                        return True
                        
                    except Exception as e:
                        print(f"❌ HTTP请求失败: {e}")
                else:
                    print("❌ 端口5000未开放")
            except Exception as e:
                print(f"❌ 端口检查失败: {e}")
        else:
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            
            # 读取错误输出
            try:
                stdout, stderr = process.communicate(timeout=1)
                if stdout:
                    print(f"标准输出: {stdout.decode('utf-8', errors='ignore')}")
                if stderr:
                    print(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
            except:
                pass
        
        # 清理进程
        try:
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)
        except:
            pass
        
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def test_manager_server():
    """测试Manager Server启动"""
    print("\n🧪 测试Manager Server启动...")
    
    # 检查文件是否存在
    script_path = Path("manager-server/simple_flask_server.py")
    python_path = Path("manager-server/venv/Scripts/python.exe")
    
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    if not python_path.exists():
        print(f"❌ Python解释器不存在: {python_path}")
        return False
    
    print(f"✅ 脚本路径: {script_path}")
    print(f"✅ Python路径: {python_path}")
    
    try:
        # 使用绝对路径启动
        command = [str(python_path.absolute()), "simple_flask_server.py"]
        cwd = "manager-server"
        
        print(f"🚀 启动命令: {command}")
        print(f"🚀 工作目录: {cwd}")
        
        process = subprocess.Popen(
            command,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=False
        )
        
        print(f"✅ 进程已启动，PID: {process.pid}")
        
        # 等待几秒钟
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 进程仍在运行")
            
            # 测试端口
            import socket
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', 3000))
                sock.close()
                
                if result == 0:
                    print("✅ 端口3000已开放")
                    
                    # 测试HTTP请求
                    try:
                        import urllib.request
                        response = urllib.request.urlopen("http://localhost:3000/", timeout=3)
                        print("✅ HTTP请求成功")
                        print(f"   响应状态: {response.status}")
                        
                        # 停止进程
                        process.terminate()
                        process.wait(timeout=5)
                        print("✅ 进程已停止")
                        return True
                        
                    except Exception as e:
                        print(f"❌ HTTP请求失败: {e}")
                else:
                    print("❌ 端口3000未开放")
            except Exception as e:
                print(f"❌ 端口检查失败: {e}")
        else:
            print(f"❌ 进程已退出，返回码: {process.returncode}")
            
            # 读取错误输出
            try:
                stdout, stderr = process.communicate(timeout=1)
                if stdout:
                    print(f"标准输出: {stdout.decode('utf-8', errors='ignore')}")
                if stderr:
                    print(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
            except:
                pass
        
        # 清理进程
        try:
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)
        except:
            pass
        
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 ConnectAI 单服务启动测试")
    print("=" * 50)
    
    # 测试DataChat API
    datachat_ok = test_datachat_api()
    
    # 测试Manager Server
    manager_ok = test_manager_server()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"DataChat API: {'✅ 成功' if datachat_ok else '❌ 失败'}")
    print(f"Manager Server: {'✅ 成功' if manager_ok else '❌ 失败'}")
    
    if datachat_ok and manager_ok:
        print("\n🎉 所有服务测试通过！")
        print("💡 现在可以使用 start_connectai.py 启动完整服务")
    else:
        print("\n❌ 部分服务测试失败")
        print("💡 请检查错误信息并修复问题")
    
    return 0 if (datachat_ok and manager_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
