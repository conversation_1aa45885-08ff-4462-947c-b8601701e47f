'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M6.948 2.075A4.153 4.153 0 0 0 3.216 3.21a4.13 4.13 0 0 0-1.078 3.986a1.45 1.45 0 0 1 2.349.726a1.447 1.447 0 0 1 1.65 1.324a1.443 1.443 0 0 1 1.337 1.334a1.443 1.443 0 0 1 .919 2.46l-.235.234l.691.653a.487.487 0 0 0 .68-.01a.491.491 0 0 0 .046-.639l-.038-.038a.507.507 0 0 1-.047-.054l-.101-.102a.502.502 0 0 1 0-.709a.498.498 0 0 1 .705 0l.14.14c.03.03.058.06.084.092l.075.076a.472.472 0 0 0 .67 0a.477.477 0 0 0 0-.673l-.15-.15a.5.5 0 0 1-.146-.367a.5.5 0 0 1 .852-.356l.113.114a.524.524 0 0 0 .743 0a.529.529 0 0 0 0-.745l-.025-.026a1.808 1.808 0 0 1-.091-.085l-.775-.778a.502.502 0 0 1 0-.708a.498.498 0 0 1 .706 0l.728.73a.506.506 0 0 1 .049.044l.06.06a.806.806 0 0 0 1.08-.056a.813.813 0 0 0 0-1.147l-.738-.741a.509.509 0 0 1-.029-.027L10.448 4.73h-.766L7.97 6.032c-.828.63-2.01.47-2.641-.358a1.882 1.882 0 0 1 .357-2.64l1.262-.96zm3.71 1.655a.5.5 0 0 1 .356.15l2.696 2.74a3.153 3.153 0 0 0-1.515-4.296a2.185 2.185 0 0 0-1.097-.29h-2.45a.502.502 0 0 1-.049.041L6.292 3.83a.882.882 0 0 0-.168 1.238a.886.886 0 0 0 1.241.168l1.846-1.404a.5.5 0 0 1 .302-.102h1.145zm-4.311 7.269l-.657.655a.446.446 0 0 1-.63 0a.443.443 0 0 1-.012-.616l.013-.013l.656-.655l.012-.011a.447.447 0 0 1 .618.011a.443.443 0 0 1 0 .629zm.051 1.989a.446.446 0 0 0 .63 0l.657-.655a.443.443 0 0 0-.07-.685a.446.446 0 0 0-.56.057l-.657.655a.443.443 0 0 0 0 .628zM3.413 8.591l-.656.655a.446.446 0 0 1-.63 0a.443.443 0 0 1-.012-.616l.013-.013l.656-.655l.012-.011a.446.446 0 0 1 .617.012a.443.443 0 0 1 0 .628zM4.89 9.704l-.656.654a.446.446 0 0 1-.63 0a.443.443 0 0 1-.012-.615l.013-.013l.656-.655l.012-.012a.446.446 0 0 1 .617.012a.443.443 0 0 1 0 .629z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Handshake16Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
