<template>
  <n-data-table scroll-x="1500" :columns="columns" :data="data" :bordered="false" />
  <div class="flex justify-end mt-4"><n-pagination v-bind="paginationOptions" /></div>
</template>

<script setup lang="tsx">
import { ref, toRefs } from 'vue';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { NButton, NDivider, NPopconfirm } from 'naive-ui';
import {t} from '@/locales';

const props = defineProps<{
  data: ApiPrompt.PromptTypes[];
  paginationOptions: PaginationProps;
  catesOptions: { label: string; value: string }[];
}>();
const { data, paginationOptions, catesOptions = [] } = toRefs(props);
const emit = defineEmits(['handle-edit', 'handle-delete', 'handle-active']);
const popconfirm = ref();

const columns: DataTableColumns<ApiPrompt.PromptTypes> = [
  {
    title: t('message.prompt.id'),
    key: 'id',
    width: 100,
    align: 'center',
    render(row, index) {
      return <div>{index + 1}</div>;
    }
  },
  {
    title: t('message.prompt.lb'),
    key: 'category_id',
    width: 180,
    sorter: 'default',
    align: 'center',
    render({ category_id }) {
      return catesOptions.value?.find(item => item.value === category_id)?.label;
    }
  },
  {
    title:  t('message.prompt.bt'),
    key: 'title',
    resizable: true,
    align: 'center'
  },
  {
    title:  t('message.prompt.js'),
    key: 'description',
    resizable: true,
    align: 'center'
  },
  {
    title:  t('message.prompt.nr'),
    key: 'content',
    resizable: true,
    align: 'center'
  },
  {
    title:  t('message.prompt.jl'),
    key: 'example',
    resizable: true,
    align: 'center'
  },
  {
    title:  t('message.prompt.cz'),
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render({ category_id, ...rest }) {
      return (
        <div class="flex gap-2">
          <NButton tertiary size={'small'} onClick={() => emit('handle-edit', { category_id, ...rest })}>
            {t('message.prompt.bj')}
          </NButton>
          <NPopconfirm showIcon={false} negativeText={null} positiveText={null} ref={popconfirm}>
            {{
              action: () => (
                <div class="flex justify-start gap-1 items-center">
                  {t('message.prompt.jjsc')}『{catesOptions.value?.find(item => item.value === category_id)?.label}』
                  <NDivider vertical />
                  <NButton
                    type={'error'}
                    tertiary
                    size={'small'}
                    onClick={() => [emit('handle-delete', { category_id, ...rest }), popconfirm.value.setShow(false)]}
                  >
                    {t('message.prompt.qrsc')}
                  </NButton>
                </div>
              ),
              trigger: () => (
                <NButton tertiary size={'small'}>
                  {t('message.prompt.sc')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
];
</script>
