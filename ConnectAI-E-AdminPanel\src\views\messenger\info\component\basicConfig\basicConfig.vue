<template>
  <div
    v-if="loading"
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space vertical>
      <n-skeleton height="40px" width="33%" :sharp="false" />
      <n-skeleton height="60px" :sharp="false" />
      <n-skeleton height="60px" />
      <n-skeleton height="60px" />
      <n-skeleton height="40px" width="100px" :sharp="false" />
    </n-space>
  </div>
  <div
    v-else
    class="p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <h3 class="text-xl font-semibold dark:text-white mb-4">{{ t('message.messenger.jcpz') }}</h3>
    <div class="flex flex-wrap content-start items-start gap-[30px]">
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <component :is="iconRender({ localIcon: 'feishu' })" class="text-36px" />
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ t('message.messenger.fsxxzf') }}
              </h5>
            </div>
            <div>
              <n-tag v-if="messengerInfo?.bot_instance_id === null" round :bordered="false" class="cursor-pointer">
                {{ t('message.my.dpz') }}
                <template #icon>
                  <n-icon :component="StopCircleSharp" />
                </template>
              </n-tag>
              <n-tag v-else round :bordered="false" type="success" class="cursor-pointer">
                {{ t('message.my.ypz') }}
                <template #icon>
                  <n-icon :component="CheckmarkCircle" />
                </template>
              </n-tag>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            {{ $t('message.messenger.fsinfo') }}
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleDetail()"
            >
              {{ t('message.messenger.pz') }}
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ t('message.messenger.glfshtq') }}
              </h5>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            <n-select v-model:value="chat_id" :options="chatListOptions"></n-select>
          </div>
          <div class="flex justify-between items-center gap-2">
            <div class="text-blue-700 text-center cursor-pointer" @click="refreshChatList">
              {{ $t('message.messenger.dwsx') }}
            </div>
            <button
              type="button"
              class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleSaveChat"
            >
              <icon-akar-icons-save class="mr-2" />
              {{ t('message.messenger.save') }}
            </button>
          </div>
        </div>
      </div>
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ t('message.messenger.webpz') }}
              </h5>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            {{ $t('message.messenger.webinfo') }}
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleWebSDk()"
            >
              {{ t('message.messenger.ckpz') }}
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ t('message.messenger.apipz') }}
              </h5>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            {{ $t('message.messenger.apiinfo') }}
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleWebApi()"
            >
              {{ t('message.messenger.pz') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <n-modal v-model:show="show" :mask-closable="true" :close-on-esc="true" :auto-focus="false">
    <Config v-model:data="messengerBot" :ai-info="aiInfo" @close="show = false" />
  </n-modal>
  <n-modal v-model:show="sdkShow" :mask-closable="true" :close-on-esc="true" :auto-focus="false">
    <div>
      <section class="dark:bg-gray-900 bg-white rounded-8px p-8">
        <div class="mb-4">
          <n-text>{{ t('message.messenger.tit') }}</n-text>
          <n-text code>{{ `&lt;head&gt;` }}</n-text>
          <n-text>{{ t('message.messenger.and') }}</n-text>
          <n-text code>{{ `&lt;/head&gt;` }}</n-text>
          <n-text>{{ t('message.messenger.bql') }}：</n-text>
        </div>
        <h2 class="mb-4">{{  t('message.messenger.fs1') }}：</h2>
        <div v-html="sdkHtml"></div>
        <div v-html="messengerHtml" class="mb-4"></div>
        <h2 class="mb-4">{{  t('message.messenger.fs2') }}：</h2>
        <div v-html="messengerHtml2" class="mb-4"></div>
        <h2 class="mb-4">{{  t('message.messenger.fs3') }}：{{ t('message.messenger.iframe') }}</h2>
        <div v-html="messengerHtml3"></div>
        <div class="flex justify-end items-center gap-2 mt-4">
          <div
            class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            @click="sdkShow = false"
          >
            {{ t('message.messenger.qd') }}
          </div>
        </div>
      </section>
    </div>
  </n-modal>
  <n-modal v-model:show="apiShow" :mask-closable="true" :close-on-esc="true" :auto-focus="false">
    <n-card :title="t('message.messenger.apipz')" style="width: 800px" :bordered="false" role="dialog" aria-modal="true">
      <n-form ref="formRef" :label-width="80" :model="data">
        <n-form-item class="flex-1" :label="t('message.messenger.kqapi')" path="api">
          <n-switch
            v-model:value="data.api"
            @update-value="handleOpen"
            :checked-value="1"
            :unchecked-value="0"
          ></n-switch>
        </n-form-item>
        <n-form-item v-if="data.api" class="flex-1" label="AccessToken" path="access_token">
          <n-input v-model:value="data.access_token" readonly>
            <template #suffix>
              <icon-mdi-refresh @click="handleGetAccessToken" class="mr-4px text-16px cursor-pointer" /></template
          ></n-input>
        </n-form-item>
      </n-form>
      <div v-if="data.api">
        <n-divider></n-divider>
        <n-text type="warning" :depth="1">{{ t('message.messenger.tjtxx') }}：</n-text>
        <n-text tag="div" type="warning" :depth="1">Authorization:{{ data.access_token }}</n-text>
        <n-divider></n-divider>
        <n-tabs type="line" animated>
          <n-tab-pane name="config" :tab="t('message.messenger.hqxx')">
            <n-text class="mb-2" tag="div" type="info" :depth="1">Url: {{ configUrl }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1">Method: {{ 'GET' }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >Headers: { 'Authorization': '{{ data.access_token }}' }</n-text
            >
          </n-tab-pane>
          <n-tab-pane name="get" :tab="t('message.messenger.hqxx')">
            <n-text class="mb-2" tag="div" type="info" :depth="1">Url: {{ url }}?{{ query }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1">Method: {{ 'GET' }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >Headers: { 'Authorization': '{{ data.access_token }}' }</n-text
            >
            <n-text class="mb-2" tag="div" type="info" :depth="1">Query: {{ query }}</n-text>
          </n-tab-pane>
          <n-tab-pane name="text" :tab="t('message.messenger.fsxx')">
            <n-text class="mb-2" tag="div" type="info" :depth="1">Url: {{ url }}?{{ query }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1">Method: {{ 'POST' }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >Headers: {' Content-Type': 'text/plain', 'Authorization': '{{ data.access_token }}' }</n-text
            >
            <n-text class="mb-2" tag="div" type="info" :depth="1">Data: 'inputValue' </n-text>
          </n-tab-pane>
          <n-tab-pane name="file" :tab="t('message.messenger.fsxx')">
            <n-text class="mb-2" tag="div" type="info" :depth="1">Url: {{ url }}?{{ query }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1">Method: {{ 'POST' }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >Headers: { 'Content-Type': 'multipart/form-data', 'Authorization': '{{ data.access_token }}' }</n-text
            >
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >Accept: .doc,.docx,.opus,.mp4,.pdf,.xls,.ppt,.xlsx,.pptx,.txt</n-text
            >
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >FormData: {
              <n-text class="pl-4" tag="div" type="info" :depth="1"> file: (binary) ; </n-text>
              }
            </n-text>
          </n-tab-pane>
          <n-tab-pane name="image" :tab="t('message.messenger.fstp')">
            <n-text class="mb-2" tag="div" type="info" :depth="1">Url: {{ url }}?{{ query }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1">Method: {{ 'POST' }}</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >Headers: {'Content-Type': 'multipart/form-data', 'Authorization': '{{ data.access_token }}' }</n-text
            >
            <n-text class="mb-2" tag="div" type="info" :depth="1">Accept: image/*</n-text>
            <n-text class="mb-2" tag="div" type="info" :depth="1"
              >FormData: {
              <n-text class="pl-4" tag="div" type="info" :depth="1"> image: (binary) ; </n-text>
              }
            </n-text>
          </n-tab-pane>
        </n-tabs>
      </div>
      <template #footer>
        <div class="flex justify-end items-center gap-2 mt-4">
          <div
            class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            @click="apiShow = false"
          >
          {{ t('message.messenger.qd') }}
          </div>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { onMounted, ref, provide, computed, toRefs, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage, useDialog } from 'naive-ui';
import { CheckmarkCircle, StopCircleSharp } from '@vicons/ionicons5';
import { useIconRender } from '@/composables';
import {
  getMessengerChatList,
  getMessengerBot,
  updateMessenger,
  updateAccessToken,
  getMessengerWebConfig,
  updateMessengerWebConfig
} from '@/service/api/messenger';
import Config from './config.vue';
import { t } from '@/locales';
import { useVModel } from '@vueuse/core';
import Prism from 'prismjs';
import 'prismjs/themes/prism.css';
import qs from 'query-string';

provide('close', close);

const { iconRender } = useIconRender();
const route = useRoute();
const messengerBot = ref();
const show = ref(false);
const id = route.query.id as string;

const chatList = ref<ApiMessenger.MessengerChat[]>([]);
const sdkShow = ref(false);
const apiShow = ref(false);

const props = defineProps<{
  data: ApiMessenger.Resp.MessengerWebConfig;
  messengerInfo: ApiMessenger.MessengerChatInfoDetails;
  loading: boolean;
  aiInfo: ApiApp.AppInfo;
}>();

const { messengerInfo } = toRefs(props);
const emit = defineEmits(['change', 'update:data']);
const data = useVModel(props, 'data', emit);

const chat_id = ref(''); // 单独用一个变量来保存chat_id，因为messengerInfo.value.chat_id会在其他地方被更新使用，导致实际还会落到数据库就会报错

const formRef = ref();
const dialog = useDialog();

watch(
  () => messengerInfo.value.chat_id,
  (val: string) => {
    chat_id.value = val;
  }
);

// eslint-disable-next-line no-useless-escape
const sdk = `<script src="${location.origin}/messenger/sdk.js"><\/script>`;
const messengerHtml = computed(() => {
  let messenger;

  if (data.value.api) {
    messenger = `<lark-messenger botid="${messengerInfo.value.bot_instance_id}" accesstoken="${data.value.access_token}"></lark-messenger>`;
  } else {
    messenger = `<lark-messenger botid="${messengerInfo.value.bot_instance_id}"></lark-messenger>`;
  }
  return Prism.highlight(messenger, Prism.languages.markup, 'markup');
});

const messengerHtml2 = computed(() => {
  let messenger;

  if (data.value.api) {
    messenger = `<script type="text/javascript">
    (function(e,b,f,g,a){var d=b.createElement("script");d.src="${location.origin}/messenger/sdk.js";
    var c=b.getElementsByTagName("script")[0];a=b.querySelector(a);a||(a=b.createElement("div"),b.body.appendChild(a));
    c.onload=function(){return new e.larkmessenger.LarkMessenger({target:a,props:{botid:f,accesstoken:g}})};
    c.parentNode.insertBefore(d,c)})(window,document,"${messengerInfo.value.bot_instance_id}","${data.value.access_token}","#messenger");
<\/script>`;
  } else {
    messenger = `<script type="text/javascript">
  (function(e,b,f,a){var d=b.createElement("script");d.src="${location.origin}/messenger/sdk.js";
  var c=b.getElementsByTagName("script")[0];a=b.querySelector(a);a||(a=b.createElement("div"),b.body.appendChild(a));
  c.onload=function(){return new e.larkmessenger.LarkMessenger({target:a,props:{botid:f}})};
  c.parentNode.insertBefore(d,c)})(window,document,"${messengerInfo.value.bot_instance_id}","#messenger");
<\/script>`;
  }

  return Prism.highlight(messenger, Prism.languages.markup, 'markup').replace(/\n/g, '<br>');
});

const messengerHtml3 = computed(() => {
  const query = data.value.api
    ? {
        botid: messengerInfo.value.bot_instance_id,
        accesstoken: data.value.access_token
      }
    : {
        botid: messengerInfo.value.bot_instance_id
      };

  const messenger = `${location.origin}/messenger/?${qs.stringify(query)}`;

  return Prism.highlight(messenger, Prism.languages.javascript, 'javascript');
});

const sdkHtml = Prism.highlight(sdk, Prism.languages.markup, 'markup');

const url = computed(() => {
  return `${location.origin}/chat/pubsub/${messengerInfo.value.bot_instance_id}`;
});

const configUrl = computed(() => {
  return `${location.origin}/chat/${messengerInfo.value.bot_instance_id}/client`;
});

const query = computed(() => {
  const params = data.value.form
    ? {
        visitor_id: '',
        email: '',
        nickname: '',
        telephone: ''
      }
    : {
        visitor_id: ''
      };
  return qs.stringify(params);
});

const chatListOptions = computed(() => {
  return chatList.value.map((item) => {
    return {
      label: item.name,
      value: item.chat_id
    };
  });
});

const message = useMessage();

async function handleDetail() {
  const {
    data: { data }
  } = await getMessengerBot({ id });
  messengerBot.value = { ...data };
  show.value = true;
}

function close() {
  show.value = false;
}

async function refreshChatList() {
  await getChatList();
  message.success(t('message.msg.sxcg'));
}

async function getChatList() {
  try {
    const {
      data: { data }
    } = await getMessengerChatList({ id });
    chatList.value = data;
  } catch (err) {}
}

async function handleSaveChat() {
  await updateMessenger({
    messenger_id: id,
    chat_id: chat_id.value
  });
  emit('change');
  message.success(t('message.msg.bccg'));
}

function handleWebSDk() {
  sdkShow.value = true;
}

function handleWebApi() {
  apiShow.value = true;
}

async function handleOpen(opened: boolean) {
  await handleSaveWebConfig();
}

async function handleGetAccessToken() {
  await handleRefreshAccessToken();
}

async function handleRefreshAccessToken() {
  dialog.warning({
    title: t('message.messenger.warn'),
    content: t('essage.messenger.sxtoken'),
    positiveText: t('message.messenger.qd'),
    negativeText: t('message.messenger.qx'),
    onPositiveClick: async () => {
      await updateAccessToken({
        id,
        action: 'refresh_access_token'
      });
      const res = await getMessengerWebConfig({ id });
      data.value = res?.data?.data;
      message.success(t('message.msg.sxcg'));
    }
  });
}

async function handleSaveWebConfig() {
  await updateMessengerWebConfig({ id: route.query.id as string, data: data.value });
}
onMounted(() => {
  getChatList();
});
</script>

<style scoped></style>
