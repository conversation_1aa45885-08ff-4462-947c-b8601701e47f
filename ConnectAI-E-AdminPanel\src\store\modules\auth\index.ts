import { nextTick, unref } from 'vue';
import { defineStore } from 'pinia';
import { router } from '@/router';
import { fetchLogin, fetchLoginCode, fetchRegister, changeCompanyname, fetchInfo } from '@/service';
import { useRouterPush } from '@/composables';
import { refresh<PERSON>and<PERSON> } from '@/hooks';
import { localStg } from '@/utils';
import useShowNotify from '@/hooks/common/use-show-notify';
import { useTabStore } from '../tab';
import { useRouteStore } from '../route';
import { clearAuthStorage, getToken, getUserInfo } from './helpers';

interface AuthState {
  /** 用户信息 */
  userInfo: Auth.UserInfo;
  /** 用户token */
  token: string;
  /** 登录的加载状态 */
  loginLoading: boolean;
  type: string;
}

const isLark = Boolean(JSON.parse(import.meta.env.VITE_IS_LARK)); // 判断是否为海外版
const loginType = isLark ? 'login' : 'telephone';

export const useAuthStore = defineStore('auth-store', {
  state: (): AuthState => ({
    userInfo: getUserInfo(),
    token: getToken(),
    loginLoading: false,
    type: loginType,
  }),
  getters: {
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state.token);
    }
  },
  actions: {
    /** 重置auth状态 */
    resetAuthStore() {
      const { toLogin } = useRouterPush(false);
      const { resetTabStore } = useTabStore();
      const { resetRouteStore } = useRouteStore();
      const route = unref(router.currentRoute);

      clearAuthStorage();
      this.$reset();

      if (route.meta.requiresAuth) {
        toLogin();
      }

      nextTick(() => {
        this.type = 'telephone';
        resetTabStore();
        resetRouteStore();
      });
    },
    /**
     * 处理登录后成功或失败的逻辑
     * @param backendToken - 返回的token
     */
    async handleActionAfterLogin(backendToken: ApiAuth.Token, loginForm: Auth.UserInfo, type?: string) {
      const route = useRouteStore();
      const { toLoginRedirect } = useRouterPush(false);
      const { setShow } = useShowNotify();

      const loginSuccess = await this.loginByToken(backendToken, loginForm);
      console.log('我是登录成功');
      console.log(loginSuccess);
      console.log(type);

      if (loginSuccess && type === 'login') {
        await route.initAuthRoute();

        // 跳转登录后的地址
        toLoginRedirect();

        refreshHandler();

        // // 登录成功弹出欢迎提示
        if (route.isInitAuthRoute) {
          setShow();
        }

        return;
      }else if(loginSuccess && type === 'register'){
        console.log('切换card');
        this.type = 'companyname'; // 更新 type 状态
        return;
      } else {
        return;
      }

      // 不成功则重置状态
      this.resetAuthStore();
    },
    // 修改企业名称
    async Companyname(teamname: string) {
      const route = useRouteStore();
      const { toLoginRedirect } = useRouterPush(false);
      const { setShow } = useShowNotify();

      this.loginLoading = true;
      const res = await changeCompanyname(teamname);
      console.log(res);
      if(res.data.code === 0){
        this.loginLoading = false;
        // 跳转登录后的地址
        toLoginRedirect();

        refreshHandler();

        // 登录成功弹出欢迎提示
        if (route.isInitAuthRoute) {
          setShow();
        }
      }else{
        this.loginLoading = false;
      }
    },
    /**
     * 根据token进行登录
     * @param backendToken - 返回的token
     */
    async loginByToken(backendToken: ApiAuth.Token, loginForm: Auth.UserInfo) {
      let successFlag = false;
      const { email, passwd } = loginForm;
      // 先把token存储到缓存中(后面接口的请求头需要token)
      const { token } = backendToken;
      const userInfo: Auth.UserInfo = {
        email,
        passwd
      };
      localStg.set('token', token);
      localStg.set('userInfo', userInfo);
      this.token = token;
      this.userInfo = userInfo;
      successFlag = true;

      return successFlag;
    },
    /**
     * 登录
     * @param email - 邮箱
     * @param passwd - 密码
     */
    async login(email: string, passwd: string) {
      this.loginLoading = true;
      const {error,data:{type}} = await fetchLogin(email, passwd);
      // HACK: 临时登录,新接口没返回token
      let data;
      if (error?.code !== -1) {
        data = {
          token: 'mock-token'
        };
      }

      if (data) {
        await this.handleActionAfterLogin(data, { email, passwd },type);
      }
      this.loginLoading = false;
    },
    /**
     * 登录
     * @param phone - 手机号
     * @param code - 验证码
     */
    async loginCode(phone: string, code: string) {
      this.loginLoading = true;
      const { error,data:{type} } = await fetchLoginCode(phone, code);
      console.log(error,type);
      // HACK: 临时登录,新接口没返回token
      let data;
      if (error?.code !== -1) {
        data = {
          token: 'mock-token'
        };
      }

      if (data) {
        await this.handleActionAfterLogin(data, { email: phone, passwd: code }, type);
      }
      this.loginLoading = false;
    },
    /**
     * 注册
     * @param email - 邮箱
     * @param code - 验证码
     * @param passwd - 密码
     */
    async register(email: string, code: string, passwd) {
      this.loginLoading = true;
      const { error,data:{type} } = await fetchRegister(email, code, passwd);
      // HACK: 临时登录,新接口没返回token
      let data;
      if (error?.code !== -1) {
        data = {
          token: 'mock-token'
        };
      }

      if (data) {
        await this.handleActionAfterLogin(data, { email, passwd }, type);
      }
      this.loginLoading = false;
    }
  }
});
