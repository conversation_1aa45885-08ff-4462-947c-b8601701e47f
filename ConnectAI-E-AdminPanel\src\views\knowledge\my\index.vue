<template>
  <div class="w-full">
    <NoPermission v-if="deny('page.knowledge.my')" />
    <n-card v-else class="h-full shadow-sm rounded-16px pt-2" content-style="overflow:hidden">
      <template #header>
        <div class="w-full flex justify-between items-center">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
              >Search</label
            >
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="t('message.knowledge.qsrzsk')"
              />
              <button
                type="submit"
                class="text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ t('message.knowledge.search') }}
              </button>
            </div>
          </form>
          <div class="flex justify-end gap-4 items-center min-w-280px">
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAddFeishuWiki"
            >
              <component :is="iconRender({ localIcon: 'feishu' })" class="w-4 h-4 mr-2" />
              {{ t('添加飞书知识库') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAddPrompt"
            >
              <icon-akar-icons-circle-plus class="mr-2" />
              {{ t('message.knowledge.newzsk') }}
            </button>
          </div>
        </div>
      </template>
      <div
        class="flex content-start flex-grow-0 flex-wrap justify-start items-start flex-row mx-auto w-full h-full overflow-auto"
      >
        <div v-for="(dataset, index) in data" :key="index" class="ml-[30px] mt-[30px]">
          <DatasetCard :dataset="dataset" @handle-edit="handleEdit" @handle-delete="handleDelete"></DatasetCard>
        </div>
      </div>
    </n-card>
    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        :title="formValue.id ? t('message.knowledge.xgzsk') : t('message.knowledge.newzsk')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form ref="formRef" :label-width="80" :model="formValue" :rules="rules">
          <n-form-item :label="t('message.knowledge.zskmc')" path="name">
            <n-input v-model:value="formValue.name" :placeholder="t('message.knowledge.qsrzskmc')" />
          </n-form-item>
          <n-form-item :label="t('message.knowledge.zskinfo')" path="description">
            <n-input v-model:value="formValue.description" :placeholder="t('message.knowledge.qsrzskinfo')" />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('message.knowledge.qx') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              {{ t('message.knowledge.qd') }}
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
    <FeishuDocModal
      v-model:show-lark-wiki="showLarkWikiModal"
      v-model:show-tips="showTipsModal"
      v-model:data="botSetting"
      v-model:spaces="spaces"
      @after-upload="handleAfterUpload"
    />
  </div>
</template>

<script lang="ts" setup>
import { isEmpty } from 'lodash-es';
import { onMounted, ref, provide } from 'vue';
import { useIconRender } from '@/composables';
import { useMessage, useDialog } from 'naive-ui';
import type { FormInst, FormRules } from 'naive-ui';
import { useTenantPrivilege } from '@/hooks';
import { fetchDatasetList, createDataset, updateDataset, deleteDataset, getFeishuWiki, fetchDatasetBotConfig } from '@/service/api/knowledge';
import DatasetCard from './components/datasetCard.vue';
import FeishuDocModal from '../info/components/feishu-doc-modal.vue';
import { t } from '@/locales';
const { iconRender } = useIconRender();

const { deny } = useTenantPrivilege();
const data = ref<ApiDataset.Dataset[]>([]);

const keyword = ref('');

const message = useMessage();
const dialog = useDialog();
const formValue = ref<{
  name: string;
  description: string;
  id?: string;
}>({
  name: '',
  description: ''
});
const rules: FormRules = {
  name: {
    required: true,
    message: t('message.knowledge.qsrzskmc'),
    trigger: ['input']
  }
};
const formRef = ref<FormInst | null>(null);
const showModal = ref(false);

const botSetting = ref({
  platform: 'feishu'
});
const uploadCloudDocLoading = ref(false);
const showTipsModal = ref(false);
const showLarkWikiModal = ref(false)
const spaces = ref([])

function handleAddFeishuWiki() {
  const { callback_url, platform, ...rest } = botSetting.value;
  if (isEmpty(rest)) {
    showTipsModal.value = true;
  } else {
    showLarkWikiModal.value = true;
    getFeishuWiki().then(({data}) => {
      const spaceList = data.data.map(i => ({ value: i.space_id, label: i.name }))
      console.log('data', data, spaceList)
      spaces.value = spaceList
    })
  }
}
async function getDatasetBotConfig() {
  try {
    uploadCloudDocLoading.value = true;
    const { data } = await fetchDatasetBotConfig();
    botSetting.value = {
      ...data?.data,
      platform: 'feishu'
    };
    uploadCloudDocLoading.value = false;
  } catch (error) {
    uploadCloudDocLoading.value = false;
  }
}
function handleAfterUpload({ name, taskId }: { name: string; taskId: string }) {
  console.log('handleAfterUpload', name, taskId)
  getDatasetList().then(() => {
    showLarkWikiModal.value = false
  })
}

provide('close', () => (showTipsModal.value = false));

async function getDatasetList(keyword?: string) {
  try {
    const res = await fetchDatasetList({
      page: 1,
      size: 99999,
      keyword
    });
    data.value = res.data?.data || [];
  } catch (err) {
    console.error(err);
  }
}

function handleSearch(e: Event) {
  e.preventDefault();

  getDatasetList(keyword.value);
}

function handleAddPrompt() {
  showModal.value = true;
}

function handleClose() {
  showModal.value = false;
  formValue.value = {
    id: '',
    name: '',
    description: ''
  };
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (formValue.value.id) {
        const { id, ...data } = formValue.value;
        await updateDataset({ id, data });
      } else {
        await createDataset(formValue.value);
      }
      message.success(t('message.msg.bccg'));
      handleClose();
      getDatasetList();
    }
  });
}

function handleEdit({ id, name, description }: ApiDataset.Dataset) {
  formValue.value = {
    id,
    name,
    description
  };
  showModal.value = true;
}

function handleDelete({ id }: ApiDataset.Dataset) {
  dialog.warning({
    title: t('message.knowledge.warn'),
    content: t('message.knowledge.qdsc'),
    positiveText: t('message.knowledge.qd'),
    negativeText: t('message.knowledge.qx'),
    positiveButtonProps: {
      class: 'bg-[var(--n-color)]' // 解决button默认样式被flowbite覆盖
    },
    onPositiveClick: async () => {
      await deleteDataset({ id });
      getDatasetList();

      message.success(t('message.msg.sccg'));
    }
  });
}

onMounted(() => {
  getDatasetList();
  getDatasetBotConfig();
});
</script>
