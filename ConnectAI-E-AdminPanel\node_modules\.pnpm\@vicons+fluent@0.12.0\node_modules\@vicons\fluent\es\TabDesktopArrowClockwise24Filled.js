import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.75 3A2.75 2.75 0 0 0 3 5.75v5.351A6.979 6.979 0 0 1 8 9c.983 0 1.92.203 2.768.569A1.749 1.749 0 0 1 14 10.5v1.892A6.968 6.968 0 0 1 15 16c0 1.959-.805 3.73-2.101 5h5.351A2.75 2.75 0 0 0 21 18.25V8h-7.75A2.25 2.25 0 0 1 11 5.75V3H5.75zm6.75 0v2.75c0 .414.336.75.75.75H21v-.75A2.75 2.75 0 0 0 18.25 3H12.5zm.5 10.25a.75.75 0 0 1-.75.75H9.5a.75.75 0 0 1 0-1.5h1.33a4.478 4.478 0 0 0-2.83-1a4.5 4.5 0 1 0 4.5 4.5a.75.75 0 0 1 1.5 0a6 6 0 1 1-2.5-4.874V10.5a.747.747 0 0 1 .75-.75a.75.75 0 0 1 .75.75v2.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TabDesktopArrowClockwise24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
