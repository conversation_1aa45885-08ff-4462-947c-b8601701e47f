import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6 2a.5.5 0 0 1 .466.318l3.5 8.956a.5.5 0 0 1-.931.364l-1.03-2.635H4.008l-1.042 2.68a.5.5 0 1 1-.932-.363l3.5-9.001A.5.5 0 0 1 6 2zm.002 1.877L4.397 8.003h3.217L6.002 3.877zM11.5 2a.5.5 0 0 1 .5.5v3.523h.004c.225-.406.534-.723.926-.953c.397-.23.84-.345 1.332-.345c.888 0 1.605.331 2.15.994c.546.662.818 1.544.818 2.643c0 .142-.004.28-.013.414l-.044-.044a2.486 2.486 0 0 0-1.154-.656c-.04-.665-.217-1.202-.533-1.614c-.36-.468-.852-.703-1.474-.703c-.6 0-1.09.241-1.474.724c-.291.367-.47.823-.538 1.366v1.03c.053.43.176.804.367 1.123L11 11.37V2.5a.5.5 0 0 1 .5-.5zm4.966 7.439l2.095 2.095a1.5 1.5 0 0 1 0 2.122l-3.6 3.599l-4.215-4.217l3.598-3.598a1.502 1.502 0 0 1 2.122-.001zm-2.211 8.523l-4.216-4.216l-.599.598a1.5 1.5 0 0 0 0 2.122l2.094 2.095c.308.308.717.454 1.121.438V19h4.092a.5.5 0 1 0 0-1h-2.53l.038-.038z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextClearFormatting20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
