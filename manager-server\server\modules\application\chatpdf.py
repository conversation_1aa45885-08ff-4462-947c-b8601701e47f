class ChatTool(CTool):
    name: str = 'chat'
    description: str = 'chat pdf app'

    def _run(self, *args, run_manager=None, input='', question='', context='', **kwargs):
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class CallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('正在思考，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # 同时添加当前用户输入的问题，以及ai回复问题
                content = response.generations[0][0].text
                session.add_message({'role': 'human', 'content': input.strip()})
                session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('不知道如何提问？\n话题中回复「概要」查看内容概要，以及推荐问题'))),
                        ),
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))

        model.callbacks = [CallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])

        # 支持文心一言+openai+azure
        if model.openai_api_type == '文心一言':
            chat = WenXinChat(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        elif model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )

        system_message = SystemMessage(content=f"""You are a good concise summary assistant. You can communicate in user language "{data.lang}" or based on following content. """)
        human_message = HumanMessage(
            content=textwrap.dedent(
                """
                Important Notice:
                You are a good concise summary assistant who can know the language to reply to users based on their question or context. If the user only inputs numbers or you do not know the language, then using default language '{data.lang}'.
                You are a rigorous assistant and do not use content outside of context to answer user.
                Use the following context to answer the user's question.

                ```
                {context}
                ```

                Question: {question}

                Helpful Answer:
                """
            ).strip().format(context=context or _("内容为空，请直接告诉用户你不知道答案"), question=question, data=data)
        )
        logging.debug("debug chat %r", human_message)
        return chat.invoke([system_message] + chat_history + [human_message])


class ChatPDFRootMessageTool(CTool):
    name: str = 'chatpdf_root_message'
    description: str = 'chatpdf root message'

    def get_file_type(self, file_name):
        file_type = file_name.split('.').pop()
        if file_type in ['docx', 'doc']:
            file_type = 'word'
        elif file_type in ['ppt', 'pptx']:
            file_type = 'ppt'
        elif file_type in ['xls', 'xlsx']:
            file_type = 'excel'
        elif file_type == 'md':
            file_type = 'markdown'

        if file_type not in ['pdf', 'word', 'excel', 'markdown', 'ppt', 'txt', 'html']:
            send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('支持上传pdf、word、excel、ppt、txt格式文档'), tag='lark_md'),
                    # FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('暂不支持您上传的文档类型'), template='blue'),
                )
            )
            raise Exception('NOT_SURPPORT')
        return file_type

    @stalecache(stale=0, expire=options.CHATPDF_EXPITED)
    def import_file(self, message_id, file_key, file_name, file_type):
        # 从飞书下载文件
        bytes = syncify(client.get_message_resource)(message_id, file_key, "file")
        logging.debug("get_message_resource %r", (message_id, file_type, len(bytes)))

        know_client = KnowClient(options.CHATPDF_USER)
        # 上传文件到知识库服务器
        url = know_client.upload(file_name, bytes, 'application/octet-stream')
        # 使用上传后的url导入知识库
        result = know_client.add_document_to_collection(
            options.CHATPDF_COLLECTION_ID,
            file_name, url, file_type,
            # file_key,  # 现在使用file_key作为uniqid
            # TODO 使用文件内容hash之后做uniqid
            uniqid=hashlib.sha1(bytes).hexdigest(),
        )
        # 使用导入后的task_id监控何时导入成功
        now = time.time()
        while True:
            if 'document_id' in result:
                send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('处理文档成功...')))
                    )
                )
                return result['document_id']

            data = know_client.fetch_task_result(options.CHATPDF_COLLECTION_ID, result['task_id'])
            logging.debug("debug task result %r", data)
            status = data['status']
            # 使用task_id获取任务成功状态之后，拿到document_id返回
            if status == 'SUCCESS':
                send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('处理文档成功...')))
                    )
                )
                result = data['result']
                if isinstance(result, list):
                    return result
                elif isinstance(result, str):
                    return [result]
            if time.time() - now > 600:  # timeout
                break
            time.sleep(3)

        send_message(
            AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv('', tag='lark_md'),
                FeishuMessageNote(FeishuMessagePlainText(_('处理文档失败..')))
            )
        )
        # 没有处理成功，就报错，下次可以重新处理这个文件
        raise Exception('timeout')

    def _run(self, *args, run_manager=None, input='', **kwargs):
        document_id = []
        if 'root_id' in data.extra.extra and data.extra.extra.root_id:
            root_message = syncify(client.get_messages_by_id)(data.extra.extra.root_id)
            logging.debug('chat messages %r', root_message)
            # 获取root_message，同时使用file_key导入知识库，解析并获得document_id
            # 这里可以使用缓存
            if not root_message['deleted'] and root_message.get('msg_type') == 'file':
                send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('开始处理文档...')))
                    )
                )
                message_id = root_message['message_id']
                content = json.loads(root_message['body']['content'])
                file_key = content['file_key']
                file_name = content['file_name']
                file_type = self.get_file_type(file_name)
                document_id = self.import_file(message_id, file_key, file_name, file_type)
                return dict(next_tool_name='chatpdf_query', document_id=document_id)

        # 没有root_id或者root_message不是file类型，就提示用户
        send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('1. 在对话框中上传文档'), tag='lark_md'),
                FeishuMessageDiv(_('2. 使用文件消息创建话题'), tag='lark_md', extra=FeishuMessageButton(_('如何创建话题'), url='https://www.feishu.cn/hc/zh-CN/articles/423295624296-%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E8%AF%9D%E9%A2%98%E5%9B%9E%E5%A4%8D')),
                FeishuMessageDiv(_('3. 在话题内进行回复，与文档对话'), tag='lark_md'),
                # FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('请在对应文件话题下进行回复'), template='blue'),
            )
        )
        return 'empty'


class ChatPDFQueryTool(CTool):
    name: str = 'chatpdf_query'
    description: str = 'chatpdf query tool'

    def get_llm(self):
        # TODO 文档summary 使用llm
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])
        # openai+azure
        if model.openai_api_type == 'azure':
            llm = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                openai_api_type="azure",
                openai_api_key=model.openai_api_key,
                openai_api_version=model.openai_api_version,
                azure_endpoint=model.azure_endpoint,
                deployment_name=model.deployment_name,
            )
        elif model.openai_api_type == 'OpenAI':
            llm = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                openai_api_key=model.openai_api_key,
                openai_api_base=model.openai_api_base,
            )
        else:
            raise Exception()
        return llm

    def send_summary(self, summary, questions, is_update=True):
        send_message(
            AppResult.UpdateCard if is_update else AppResult.ReplyNewCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('**💡内容摘要**'), tag='lark_md'),
                FeishuMessageDiv(summary, tag='lark_md'),
                FeishuMessageDiv(_('**📖推荐问题**'), tag='lark_md'),
                FeishuMessageDiv(questions, tag='lark_md'),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🎉 您可以参照以下内容进行提问对话'), template='blue'),
            ),
        )

    def parallelize_summaries(self, summary_docs, initial_chain, max_workers=10):
        doc_summaries = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            features = [executor.submit(initial_chain.run, [doc]) for doc in summary_docs]
            for future in as_completed(features):
                try:
                    summary = future.result()
                except Exception as e:
                    logging.exception(e)
                else:
                    doc_summaries.append(summary)
        return doc_summaries

    def get_summary(self, document_id):
        llm = self.get_llm()
        know_client = KnowClient(options.CHATPDF_USER)
        # 如果问题没有命中，尝试查询summary内容
        info = know_client.get_document_info(document_id[0])
        summary = info['data']['summary']
        if not summary:
            result = know_client.get_document(document_id[0])
            logging.debug("debug get documents %r", result)
            docs = [Document(page_content=doc['document']) for doc in result]
            prompt_template = f"""Write a concise summary (using default language {data.lang}) of the following content:""" + """

```
{text}
```
CONCISE SUMMARY:"""
            prompt = PromptTemplate.from_template(prompt_template)
            chain = load_summarize_chain(
                llm,
                chain_type="map_reduce",
                map_prompt=prompt,
                combine_prompt=prompt,
                verbose=True
            )
            # summary = chain.run(docs)
            # 1. 使用线程并行处理总结
            # 2. 处理第一次总结之后的数据重新总结
            doc_summaries = self.parallelize_summaries(docs, chain)
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
            split_docs = text_splitter.split_documents([Document(page_content='\n'.join(doc_summaries))])
            summary = chain.run(split_docs)
            logging.debug("debug chat %r", summary)
            # 保存summary
            response = know_client.save_summary(document_id[0], summary)
            logging.debug("debug save summary %r", response)
        return summary

    def get_questions(self, summary):
        llm = self.get_llm()
        # TODO 教用户提问
        system = f"""You are an assistant designed to help user to understand a document with document summary. 
Try help user to ask question on this document.
Ensure follow the following instructions:
Instruction:
- Your response should write in user language {data.lang} or based on document summary.
- Your response should have the following structure: 
1. question one?
2. question two?
3. question three?"""
        answer = llm([SystemMessage(content=system), HumanMessage(content='Document summary: {}'.format(summary))])
        logging.debug('answer %r', answer)
        questions = answer.content
        return questions

    def _run(self, *args, run_manager=None, input='', document_id=list(), **kwargs):
        send_message(
            AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv('', tag='lark_md'),
                FeishuMessageNote(FeishuMessagePlainText(_('查询相关文档信息...')))
            )
        )
        try:
            # 按文档进行查询
            context = ''
            if len(document_id) > 0:
                know_client = KnowClient(options.CHATPDF_USER)
                # 按问题查找关联内容
                result = know_client.query_document(document_id[0], input)
                logging.debug("debug query %r", result)
                if len(result['data']) > 0:
                    context = '\n\n'.join(['Context:\n{}'.format(document['document']) for document in result['data']])
                else:
                    summary = self.get_summary(document_id)
                    # TODO 教用户提问
                    questions = self.get_questions(summary)
                    self.send_summary(summary, questions, is_update=False)
        except Exception as e:
            logging.error(e)
        return dict(next_tool_name='chat', context=context, question=input)


class ChatPDFSendSummary(ChatPDFQueryTool, ChatPDFRootMessageTool):
    name: str = 'chatpdf_send_summary'
    description: str = 'chatpdf send summary tool'

    def _run(self, *args, run_manager=None, input='', document_id=list(), **kwargs):
        document_id = []
        if 'root_id' in data.extra.extra and data.extra.extra.root_id:
            root_message = syncify(client.get_messages_by_id)(data.extra.extra.root_id)
            logging.debug('chat messages %r', root_message)
            # 获取root_message，同时使用file_key导入知识库，解析并获得document_id
            # 这里可以使用缓存
            if not root_message['deleted'] and root_message.get('msg_type') == 'file':
                # 先发一个处理中
                send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                    )
                )
                message_id = root_message['message_id']
                content = json.loads(root_message['body']['content'])
                file_key = content['file_key']
                file_name = content['file_name']
                file_type = self.get_file_type(file_name)
                document_id = self.import_file(message_id, file_key, file_name, file_type)
                summary = self.get_summary(document_id)
                questions = self.get_questions(summary)
                self.send_summary(summary, questions, is_update=True)
                return AIMessage(content=summary, additional_kwargs={'questions': questions})
        raise Exception()


class ChatPDFSummary(ChatPDFQueryTool, ChatPDFRootMessageTool):
    name: str = 'chatpdf_summary'
    description: str = 'chatpdf summary tool'

    def _run(self, *args, run_manager=None, input='', document_id=list(), **kwargs):
        try:
            # 导入文件
            message_id = data.extra.message_id
            content = data.extra.extra.platform_content
            file_key = content['file_key']
            file_name = content['file_name']
            file_type = self.get_file_type(file_name)
            # 先发一个处理中
            send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv('', tag='lark_md'),
                    FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                )
            )
            document_id = self.import_file(message_id, file_key, file_name, file_type)
            # 中间调用知识库处理完文档，再发一个处理summary的提示信息，否则出现文档处理完成，还需要继续等很久
            send_message(
                AppResult.UpdateCard,
                FeishuMessageCard(
                    FeishuMessageDiv('', tag='lark_md'),
                    FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                )
            )
            summary = self.get_summary(document_id)
            # TODO 发送提示文案
            img_url = 'https://pic1.forkway.cn/cdn/chatpdf_helper.jpg'
            img_key = syncify(client.upload_image)(img_url)
            # 这里发送一个提示消息，就不发送摘要和提示问题
            return send_message(
                AppResult.UpdateCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('**请在上方「文件消息」处点击「创建话题」并回复「概要」后进行对话**'), tag='lark_md'),
                    FeishuMessageHr(),
                    FeishuMessageDiv(_('将鼠标悬浮至某条消息上，选择创建话题，在右侧话题详情页的输入框中即可回复该话题；发送回复后，针对该消息的话题即已创建完成。'), tag='lark_md'),
                    FeishuMessageImage(img_key=img_key, alt='图片'),
                    FeishuMessageAction(
                        FeishuMessageButton(
                            _('查看如何创建话题'),
                            url='https://www.feishu.cn/hc/zh-CN/articles/423295624296-%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E8%AF%9D%E9%A2%98%E5%9B%9E%E5%A4%8D',
                        )
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🎉 文件解析完成，可以开始跟文件对话啦'), template='blue'),
                ),
            )
        except Exception as e:
            logging.error(e)
        return True


class FeishuCommand(CommandTool):

    next_tool_name: str = 'chatpdf_root_message'
    name: str = 'feishu_command'
    description: str = 'chatpdf feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<发散模式> 或 /ai_mode 即可选择发散模式',
    ]

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def ai_mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in ai_mode],
            placeholder=_('选择模式'),
            initial_option=str(float(session.temperature)),
            value={'command': 'ai_mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改发散模式吗？'),
                text=_('选择内置模式，可以让AI更好的理解您的需求。'),
            )
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是ChatPDF小助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **发散模式选择**\n文本回复 *发散模式* 或 */ai_mode*'),
                    tag='lark_md',
                    extra=self.ai_mode_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:2] == '概要':
            self.next_tool_name = 'chatpdf_send_summary'
            return None,
        elif input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            return 'ai_mode',
        elif not input and action:
            if action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.extra.get('root_id'):
            # 话题内 仅接收文本
            if data.extra.message_type in ['text']:
                return None,
            return 'note', _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!'), _('不支持的消息，请更改发送文本内容！')
        else:
            # 话题外 仅接收文本和文件
            if data.extra.message_type in ['text']:
                return None,
            elif data.extra.message_type in ['file']:
                self.next_tool_name = 'chatpdf_summary'
                return None,
            return 'note',

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('已选择模型：**%(model)s**', model=m.get(model_name, model_name)), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_ai_mode(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模式，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 发散模式选择'), template='blue'),
                )
            )
        '''已选择发散模式:平衡'''
        m = {str(value): content for value, content in ai_mode}
        session['temperature'] = float(mode_name)
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已选择发散模式：**%(mode)s**', mode=m.get(mode_name, mode_name)), tag="lark_md"),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_file_help(self):
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv('1. 上传文件', tag='lark_md'),
                FeishuMessageDiv('2. 使用文件创建话题', tag='lark_md', extra=FeishuMessageButton(_('如何创建话题'), url='https://www.feishu.cn/hc/zh-CN/articles/423295624296-%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E8%AF%9D%E9%A2%98%E5%9B%9E%E5%A4%8D')),
                FeishuMessageDiv('3. 在话题内与文档对话', tag='lark_md'),
                # FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🎒使用帮助'), template='blue'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或文件(仅支持pdf、word、excel、ppt、txt格式），不支持链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/文件！')
        return send_note(text, title)


class ChatPDFAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'ChatPDF'
        title: str = 'ChatPDF'
        title_en: str = 'ChatPDF'
        description: str = '📖 与任何文档聊天，快速获取文档内容信息，支持pdf、word、excel、txt多种格式'
        description_en: str = '📖 Chat with any document and quickly obtain document content information, supporting multiple formats such as pdf, word, excel, and txt'
        problem: str = '与任何文档聊天，快速获取文档内容信息，支持pdf、word、excel、txt多种格式'
        problem_en: str = 'Chat with any document and quickly obtain document content information, supporting multiple formats such as pdf, word, excel, and txt'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/BQDqdRqumoBn1axzyNhceOAYn2c?from=from_copylink'
        manual_en: str = 'https://connect-ai.feishu.cn/docx/YSQzdSk7uoCiRjxnJPZcu3Uknbh?from=from_copylink'
        icon: str = 'https://pic1.forkway.cn/cdn/20230907150225.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/20230907150225.png?imageMogr2/thumbnail/720x'
        sorted: int = 6
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['OpenAI', 'Azure']
        )]
        support_bots: List[str] = ['feishu']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnukcwA0Mmoqt6hzZlkTCsmc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/OzPswAObxigCTlkUGR1cyC9Jnze'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result:
                if isinstance(last_result, str) and last_result in self.allowed_tools:
                    return AgentAction(tool=last_result, tool_input=kwargs, log="")
                elif isinstance(last_result, dict):
                    # 如果是AIMessage
                    # 并且从additional_kwargs拿到next_tool_name，就继续到下一个Tool
                    next_tool_name = last_result.get('next_tool_name')
                    if next_tool_name:
                        return AgentAction(tool=next_tool_name, tool_input={**kwargs, **last_result}, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='feishu_command', tool_input=kwargs, log="")
        return AgentAction(tool='chatpdf', tool_input=kwargs, log="")


