import json
import math
import logging
from typing import Any, Optional, List

import httpx
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import BaseMessage, ChatResult, AIMessage, ChatGeneration


class ElevenLabsClient(object):
    def __init__(self, api_base, api_key=''):
        self.api_base = api_base
        self.api_key = api_key

    def build_query(self, voice_id='', audio_type='mp3', model_id='eleven_multilingual_v2', **kwargs):
        headers = {
            'api-key': self.api_key,
            'xi-api-key': self.api_key,
            'Content-Type': 'application/json',
            'accept': 'audio/{}'.format(audio_type)
        }
        if 'https://elevenlabs' != self.api_base[:18]:
            headers['api-base'] = self.api_base
            from core.api_base import NewApiBase
            self.api_base = NewApiBase('elevenlabs').url
        kwargs['model_id'] = model_id
        stream = '/stream' if kwargs.get('stream') else ''
        if 'stream' in kwargs:
            del kwargs['stream']
        if 'voice_name' in kwargs:
            # 优先处理 voice_name
            url = '{}/v1/text-to-speech{}'.format(self.api_base, stream)
        else:
            url = '{}/v1/text-to-speech/{}{}'.format(self.api_base, voice_id, stream)
        body = json.dumps(kwargs)
        return url, body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        # b'{"detail":{"status":"quota_exceeded","message":"This request exceeds your quota. You have 9 characters remaining, while 102 characters are required for this request.","character_used":10093,"character_limit":10000}}'
        with httpx.stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            for chunk in r.iter_raw():
                yield chunk

    def create(self, stream=False, timeout=600, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        logging.info('el query: %r', (url, data, headers))
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            if response.status_code != httpx.codes.OK:
                logging.error('el error: %r', response.json())
                response.raise_for_status()
            return response.content


class ElevenLabsChat(SimpleChatModel):
    client: Any
    api_base: Optional[str] = ''
    api_key: Optional[str] = ''
    streaming: bool = False

    def _llm_type(self) -> str:
        return 'ElevenLabs'

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        params = {'stream': self.streaming, **kwargs}
        message = messages.pop()
        params.update({**message.additional_kwargs})
        self.client = ElevenLabsClient(
            api_base=self.api_base,
            api_key=self.api_key
        )
        response = {}
        data = b''
        if self.streaming:
            for resp in self.client.create(**params):
                data += resp
        else:
            data = self.client.create(**params)
        response['audio_bin'] = data
        response['audio_type'] = params.get('audio_type', 'mp3')
        message = AIMessage(content='', additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])


if __name__ == '__main__':
    import asyncio

    async def main():
        api_key = ''
        api_base = 'https://api.elevenlabs.io'
        # api_base = 'http://0.0.0.0:10086'
        from langchain.schema import HumanMessage
        chat = ElevenLabsChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        text = 'hello'
        params = {
            # 'voice_id': 'nGZlncqLfuHYC0FItO9U',
            'voice_name': 'Myra',
            'text': text,
            'model_id': 'eleven_multilingual_v2',
        }
        messages = [HumanMessage(content=text, additional_kwargs=params)]
        result = chat(messages)
        result = result.additional_kwargs['audio_bin']
        import time
        with open('/home/<USER>/下载/' + str(time.time() * 1000) + '.mp3', 'wb') as f:
            f.write(result)
    asyncio.run(main())
