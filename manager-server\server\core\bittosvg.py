import logging
from time import time
from typing import Any, List, Optional

import httpx
from langchain.callbacks.manager import CallbackManagerFor<PERSON>MRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import AIMessage, BaseMessage, ChatGeneration, ChatResult

logger = logging.getLogger(__name__)

class BSClient(object):
    def __init__(self, api_base, api_key=''):
        self.api_key = api_key
        self.api_base = api_base

    def create(self, content, timeout=600, **kwargs) -> Any:

        url = f"{self.api_base}/api/v1/vectorize?output.svg.adobe_compatibility_mode=true"

        with httpx.Client(
            timeout=timeout,
            transport=httpx.HTTPTransport(
                retries=5,
            ),
        ) as client:
            try:
                create_result = client.post(
                    url=url,
                    files={'image': content},
                    headers={'Authorization': f"Basic {self.api_key}"},
                    timeout=timeout,
                )

                if create_result.status_code != httpx.codes.OK:
                    logger.error('Error: {}'.format(create_result.text))
                    return None

                return create_result.content

            except Exception as e:
                logger.error('Error: {}'.format(e))
                return None


class BSChat(SimpleChatModel):
    """
    BitToSvg
    """

    client: Any
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6

    def _llm_type(self) -> str:
        return "bittosvg_chat"
    
    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:

        params = {
            'api_key': self.api_key,
            'api_base': self.api_base,
        }

        # 只获取一条 message
        message = messages.pop()
        # 这意味着图片的二进制数据要通过 message 传输
        params.update(**message.additional_kwargs)

        self.client = BSClient(
            api_base=self.api_base,
            api_key=self.api_key
        )
        response = {}
    
        response['data'] = self.client.create(**params)
        response['length'] = len(response['data'])
        
        if response['data'] is None:
            raise Exception("Error: {}".format(response['result']))

        logger.info(f"chat res: recive {response['length']} bits from vectorize")
        
        message = AIMessage(content="", additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])

def message_test():

    import asyncio
    import os

    async def main():
        
        pwd = os.getcwd()
        pic_path = os.path.join(pwd, "server/core/test.png")
        result_2_path = os.path.join(pwd, "server/core/test.svg")

        from langchain.schema import HumanMessage


        def test_chat() -> None:
            params = {
                'content': open(pic_path, 'rb'),
            }

            chat = BSChat(
                    api_base=os.getenv("BITTOSVG_API_BASE"),
                    api_key=os.getenv("BITTOSVG_API_KEY"),
            )

            messages = [HumanMessage(content="", additional_kwargs=params)]
            response = chat(messages=messages)

            # 注意这里调用结果的方式和外部调用不同
            # 外部调用会再包一层
            additional_kwargs = response.additional_kwargs

            data = additional_kwargs['data']
            with open(result_2_path, 'wb') as f:
                f.write(data)

            print(f"Test complete")

        test_chat()

    asyncio.run(main())


if __name__ == "__main__":
    import dotenv
    dotenv.load_dotenv("server/core/.env")

    message_test()
