import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.268 4.66c.3.299.788.299 1.089 0c.3-.298.3-.782 0-1.081a.774.774 0 0 0-1.09 0a.76.76 0 0 0 0 1.081zM1.536 6.33a1.992 1.992 0 0 0 0 2.83l3.31 3.289c.207.206.448.357.706.455a5.461 5.461 0 0 1-.503-1.668L2.248 8.452a.996.996 0 0 1 0-1.415l4.76-4.73a1.01 1.01 0 0 1 .707-.293L10.979 2c.56-.002 1.015.451 1.01 1.008L11.975 5.2c.35.097.685.227 1.003.388l.02-2.573A2.007 2.007 0 0 0 10.974 1l-3.264.014a2.02 2.02 0 0 0-1.416.586l-4.76 4.73zM15 10.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zM10.5 8a.5.5 0 0 0-.5.5v2a.5.5 0 1 0 1 0v-2a.5.5 0 0 0-.5-.5zm0 5.125a.625.625 0 1 0 0-1.25a.625.625 0 0 0 0 1.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagError16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
