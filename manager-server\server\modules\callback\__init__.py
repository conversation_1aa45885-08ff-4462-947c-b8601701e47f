from .handler import (
    FeishuCallbackHandler,
    DingDingCallbackHandler,
    WXWorkCallbackHandler,
    WXWorkAuthHandler,
    WEWorkCallbackHandler,
    FeishuImageHandler,
    DingDingImageHandler,
    FeishuFileHandler,
    FeishuActionHandler,
    FileUploadHandler,
)

urls = [
    # 飞书以及钉钉回调
    (r"/feishu/([0-9a-z]{24})/event", FeishuCallbackHandler),
    (r"/feishu/([0-9a-z]{24})/card", FeishuCallbackHandler),
    (r"/dingding/bot/([0-9a-z]{24})/chat", DingDingCallbackHandler),
    (r"/dingding/([0-9a-z]{24})/event", DingDingCallbackHandler),
    # 移除bot_id之后的回调地址：其中飞书的消息回调与卡片消息回调也进行了合并
    (r"/feishu/event", FeishuCallbackHandler),
    (r"/dingding/event", DingDingCallbackHandler),
    (r"/api/feishu/file/message", FeishuFileHandler),
    # 路径带文件类型，但不附带该参数
    (r"/api/feishu/file/message.[0-9a-zA-Z]{1,5}", FeishuFileHandler),

    # 平台消息的图片
    (r"/api/feishu/image/message", FeishuImageHandler),
    (r"/api/dingding/image/message", DingDingImageHandler),
    # 企业微信第三方服务商
    (r"/api/wxwork", WXWorkCallbackHandler),
    # 企业微信第三方服务商授权安装
    (r"/api/wxwork/auth/([0-9a-z]{24})/([0-9a-z]{24})", WXWorkAuthHandler),
    # 企业微信自建应用
    (r"/wework/([0-9a-z]{24})/event", WEWorkCallbackHandler),

    # 给客服使用，前缀是chat
    (r"/chat/feishu/message/file", FeishuFileHandler),
    (r"/chat/feishu/message/image", FeishuImageHandler),
    # 飞书快捷应用
    (r"/feishu/([0-9a-z]{24})/action", FeishuActionHandler),
    # 一个公共的上传文件的接口
    (r"/api/oss/upload", FileUploadHandler),
]
