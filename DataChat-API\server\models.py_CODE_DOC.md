# 代码文档 - server/models.py

## 文件作用
定义基于Elasticsearch的数据模型和业务逻辑，提供知识库、文档、用户、机器人等核心实体的数据访问层。

## 逐行代码解释

### 导入模块 (1-38行)
```python
import logging                           # 日志记录
import bson                              # MongoDB ObjectId支持
import json                              # JSON数据处理
from time import time                    # 时间戳
from uuid import uuid4                   # UUID生成
from copy import deepcopy                # 深拷贝
from datetime import datetime            # 日期时间处理

# LangChain相关导入
from langchain.schema import Document, BaseRetriever  # 文档和检索器基类
from langchain.chat_models import ChatOpenAI, AzureChatOpenAI  # 聊天模型
from langchain.prompts.chat import (     # 聊天提示模板
    ChatPromptTemplate, SystemMessagePromptTemplate,
    HumanMessagePromptTemplate, AIMessagePromptTemplate,
)

# Elasticsearch相关导入
from elasticsearch_dsl import (
    UpdateByQuery, Search, Q, <PERSON><PERSON><PERSON>, Date, Integer,
    Document as ESDocumentBase, InnerDoc, Join, Keyword,
    Long, Nested, Object, Text, connections, DenseVector
)

# LangChain链和回调
from langchain.chains import ConversationalRetrievalChain, RetrievalQA
from langchain.callbacks.base import BaseCallbackHandler
from langchain.callbacks.manager import CallbackManager
from langchain.callbacks import get_openai_callback

from app import app                      # Flask应用实例
```

### 工具类定义 (40-47行)
```python
class ObjID():
    """ObjectID工具类"""
    def new_id():
        """生成新的ObjectID字符串"""
        return str(bson.ObjectId())
    
    def is_valid(value):
        """验证ObjectID是否有效"""
        return bson.ObjectId.is_valid(value)

class NotFound(Exception): 
    """资源未找到异常"""
    pass
```

### Elasticsearch连接配置 (50-53行)
```python
connections.create_connection(
    hosts=f"http://{app.config['ES_HOST']}:{app.config['ES_PORT']}",
    # basic_auth=('elastic', '')  # 可选的基础认证
)
```
**说明**: 创建到Elasticsearch的连接，使用Flask配置中的主机和端口

### ESDocument基础模型 (56-77行)
```python
class ESDocument(ESDocumentBase):
    """Elasticsearch文档基类"""
    
    status = Integer()    # 状态字段：0-正常，1-禁用，-1-删除
    created = Date()      # 创建时间
    modified = Date()     # 修改时间

    @property
    def id(self):
        """获取文档ID"""
        return self.meta.id

    @property
    def created_at(self):
        """获取创建时间戳（毫秒）"""
        return int(self.created.timestamp() * 1000)

    def save(self, *args, **kwargs):
        """保存文档时自动设置时间戳"""
        if self.created is None:
            self.created = datetime.now()  # 首次创建时设置创建时间
        if self.status is None:
            self.status = 0                # 默认状态为正常
        self.modified = datetime.now()     # 每次保存都更新修改时间
        super().save(*args, **kwargs)
```

### User用户模型 (79-86行)
```python
class User(ESDocument):
    """用户模型"""
    openid = Keyword()                           # 用户唯一标识
    name = Text(fields={"keyword": Keyword()})   # 用户名称（支持全文搜索和精确匹配）
    extra = Object()                             # 扩展信息（JSON格式）

    class Index:
        name = 'user'  # Elasticsearch索引名称
```

### Collection知识库模型 (88-99行)
```python
class Collection(ESDocument):
    """知识库模型"""
    user_id = Keyword()                          # 用户ID
    name = Text(analyzer='ik_max_word')          # 知识库名称（中文分词）
    description = Text(analyzer='ik_max_word')   # 知识库描述
    summary = Text(analyzer='ik_max_word')       # 知识库总结
    
    # 飞书导入相关字段
    type = Keyword()                             # 知识库类型（不分词）
    space_id = Keyword()                         # 飞书空间ID

    class Index:
        name = 'collection'
```

### Documents文档模型 (101-114行)
```python
class Documents(ESDocument):
    """文档模型（区别于LangChain的Document）"""
    collection_id = Keyword()                    # 所属知识库ID
    type = Keyword()                             # 文档类型（不分词）
    path = Keyword()                             # 文档路径
    name = Text(analyzer='ik_max_word')          # 文档名称（中文分词）
    chunks = Integer()                           # 文档分片数量
    uniqid = Keyword()                           # 去重唯一ID
    summary = Text(analyzer='ik_max_word')       # 文档摘要
    version = Integer()                          # 文档版本号

    class Index:
        name = 'document'
```

### Embedding向量模型 (116-126行)
```python
class Embedding(ESDocument):
    """向量嵌入模型"""
    document_id = Keyword()                      # 文档ID
    collection_id = Keyword()                    # 知识库ID
    chunk_index = Keyword()                      # 分片索引
    chunk_size = Integer()                       # 分片大小
    document = Text(analyzer='ik_max_word')      # 分片内容
    # 768维向量，使用余弦相似度
    embedding = DenseVector(dims=768, index=True, similarity="cosine")

    class Index:
        name = 'embedding'
```

### Bot机器人模型 (128-136行)
```python
class Bot(ESDocument):
    """机器人模型"""
    user_id = Keyword()                          # 用户ID
    collection_id = Keyword()                    # 关联的知识库ID
    hash = Keyword()                             # 机器人哈希值
    extra = Object()                             # 机器人配置信息（JSON格式）

    class Index:
        name = 'bot'
```

### 初始化函数 (138-144行)
```python
def init():
    """初始化所有Elasticsearch索引"""
    User.init()
    Collection.init()
    Documents.init()
    Collection.init()    # 重复调用（可能是错误）
    Embedding.init()
    Bot.init()
```

### 用户操作函数 (147-160+行)
```python
def get_user(user_id):
    """根据ID获取用户"""
    user = User.get(id=user_id)
    if not user:
        raise NotFound()
    return user

def save_user(openid='', name='', **kwargs):
    """保存或更新用户"""
    # 查询是否已存在该openid的用户
    s = Search(index="user").filter("term", status=0).filter("term", openid=openid)
    response = s.execute()
    
    if not response.hits.total.value:
        # 用户不存在，创建新用户
        user = User(
            # 可以直接使用openid作为ID，避免重复创建
            openid=openid,
            name=name,
            **kwargs
        )
        user.save()
        return user
    else:
        # 用户已存在，返回现有用户
        return response.hits[0]
```

## 技术特点

### Elasticsearch集成
- **文档存储**: 使用Elasticsearch作为主要数据存储
- **全文搜索**: 支持中文分词和全文搜索
- **向量搜索**: 支持768维向量的相似度搜索

### 数据模型设计
- **继承体系**: 所有模型继承自ESDocument基类
- **字段类型**: 合理使用Keyword、Text、Integer等字段类型
- **中文支持**: 使用ik_max_word分析器支持中文分词

### LangChain集成
- **文档处理**: 集成LangChain的文档处理能力
- **聊天模型**: 支持OpenAI和Azure OpenAI
- **检索链**: 支持对话式检索和问答

### 业务功能
- **知识库管理**: 完整的知识库生命周期管理
- **文档处理**: 文档上传、分片、向量化
- **用户管理**: 用户注册、认证、权限控制
- **机器人配置**: 可配置的聊天机器人

## 使用场景
- **知识库问答**: 基于文档的智能问答系统
- **文档检索**: 语义化的文档搜索和检索
- **聊天机器人**: 基于知识库的对话机器人
- **内容管理**: 企业知识内容的统一管理
