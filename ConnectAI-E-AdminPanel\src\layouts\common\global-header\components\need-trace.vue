<template>
  <hover-container
    :tooltip-content="$t('message.header.xqzz')"
    class="w-40px h-full"
    :inverted="theme.header.inverted"
    @click="handleClickLink"
  >
    <icon-fa6-solid-bacon class="text-20px" />
  </hover-container>
</template>

<script lang="ts" setup>
import { useThemeStore } from '@/store';

defineOptions({ name: 'GithubSite' });

const theme = useThemeStore();
function handleClickLink() {
  window.open(
    'https://fork-way.feishu.cn/base/CvaNbmt1KaUIIOsU8xiciqylnTd?table=tblnYLE3oB2MQUo7&view=vew9NcIJ8Z',
    '_blank'
  );
}
</script>

<style scoped></style>
