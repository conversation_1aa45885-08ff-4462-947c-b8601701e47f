import os
import asyncio
import logging
import json
import base64
import queue
import requests
import threading
from functools import partial
from uuid import uuid4
from time import time
from datetime import datetime
from urllib.parse import quote, unquote
from flask import request, session, jsonify, Response, copy_current_request_context, redirect, make_response, send_file
from app import app
from models import (
    ObjID, User, Collection, Documents, Embedding,
    get_user,
    save_user,
    get_collections,
    get_collection_by_id,
    get_collection_id_by_hash,
    get_hash_by_collection_id,
    get_data_by_hash,
    save_collection,
    update_collection_by_id,
    delete_collection_by_id,
    get_documents_by_collection_id,
    remove_document_by_id,
    query_by_collection_id,
    chat_on_collection,
    get_bot_list,
    get_bot_by_hash,
    create_bot,
    update_bot_by_hash,
    query_by_document_id,
    get_docs_by_document_id,
    purge_document_by_id,
    get_document_id_by_uniqid,
    set_document_summary,
    get_document_by_id,
    get_relation_count_by_id,
)
from celery_app import embed_documents, get_status_by_id, embed_feishu<PERSON>ki
from sse import ServerSentEvents
from tasks import LarkDocLoader, YuqueDocLoader, NotionDocLoader, LarkWikiLoader


class InternalError(Exception): pass
class PermissionDenied(Exception): pass
class NeedAuth(Exception): pass


def create_access_token(user):
    extra = user.extra.to_dict()
    print('extra', extra)
    app.logger.info("extra %r %r", extra, dict(extra))
    # 同时兼容<has_privilege, expires>和<active, exp_time>
    expires = extra.get('exp_time', extra.get('permission', {}).get('expires', 0))
    privilege = extra.get('active', extra.get('permission', {}).get('has_privilege', False))
    app.logger.debug("create_access_token %r expires %r time %r", user.extra, expires, time())
    if privilege and expires > time():
        return session.sid, int(expires)
    raise PermissionDenied()


@app.after_request
def after_request_callback(response):
    app.logger.info(
        "%s - %s %s %s %s %sms",
        request.remote_addr,
        request.method,
        request.path,
        response.status_code,
        response.content_length,
        int((time() - request.environ['REQUEST_TIME']) * 1000),
    )
    return response


@app.before_request 
def before_request_callback(): 
    request.environ['REQUEST_TIME'] = time()
    if request.path in [
        '/api/access_token',
        '/api/login', '/login', '/api/code2session',
        '/', '/favicon.ico',
        '/apispec_1.json', '/apidocs/'
    ]:
        return
    if 'flasgger_static' in request.path:
        return
    if '/embed' in request.path and '/chat/completions' in request.path:
        # 这个接口不使用session校验，而是通过hash判断是否可用
        return
    if '/api/file' in request.path:
        return
    access_token = session.get('access_token', '')
    expired = session.get('expired', 0)
    user_id = session.get('user_id', '')
    if access_token and user_id:
        if expired > time():
            pass
        else:
            raise PermissionDenied()
    else:
        raise NeedAuth()
        # return jsonify({'code': -1, 'msg': 'auth required'})

# 这里的几个页面是模拟接入站点的接口: /login, /api/code2session
@app.route('/login', methods=['GET', 'POST'])
def login_form():
    # 模拟客户的登录页面，
    if request.method == 'GET':
        return '''
<h1>登录</h1>
<form action="/login" method="post">
  <input name="name" /><br />
  <input name="passwd" type="password" /><br />
  <button type="submit">登录</button>
</form>
    '''
    elif request.method == 'POST':
        name = request.form.get('name')
        passwd = request.form.get('passwd')
        app.logger.info("debug %r", (name, passwd))
        # TODO 这里模拟登录，不校验用户名密码，只要能
        # TODO 后面需要完善注册登录逻辑
        user = {
            'name': name,
            'openid': base64.urlsafe_b64encode(name.encode()).decode(),
            'permission': {
                'has_privilege': True,
                'expires': time() + 3600,
                # TODO
                # 'collection_size': 10,
                # 'bot_size': 1,
            }
        }
        code = base64.b64encode(json.dumps(user).encode()).decode()
        return redirect('{}/api/login?code={}'.format(app.config['DOMAIN'], code))

@app.route('/favicon.ico', methods=['GET'])
def faviconico():
    return ''

@app.route('/', methods=['GET'])
def home():
    return '<h1>首页</h1><a href="/api/login">登录</a>'

@app.route('/api/code2session', methods=['GET'])
def code2session():
    """
    code2session
    ---
    tags:
      - 外部集成接口
    parameters:
      - name: code
        in: query
    responses:
      200:
        description: 用户信息
    """
    # 模拟客户的code2session接口
    code = request.args.get('code', default='', type=str)
    user = json.loads(base64.urlsafe_b64decode(code).decode())
    app.logger.debug('user %r', user)
    return jsonify({'data': user})


# 以下是自己的url
@app.route('/api/login', methods=['GET'])
def login_check():
    """
    登录
    ---
    tags:
      - 用户相关接口
    parameters:
      - name: code
        in: query
    responses:
      301:
        description: 未登录
      200:
        description: 登录成功
    """
    # 如果没有权限，
    # user_id = session.get('user_id', '')
    # if user_id:
    #     return redirect('/api/login?code={}'.format(code))
    code = request.args.get('code', default='', type=str)
    if not code:
        # 这里使用配置的站点的登录url
        return redirect(app.config['SYSTEM_LOGIN_URL'])

    user_info = requests.get('{}?code={}'.format(
        app.config['SYSTEM_URL'], code,
    )).json()

    try:
        assert 'data' in user_info and 'openid' in user_info['data'], '获取用户信息失败'
        user = save_user(**user_info['data'])

        access_token, expired = create_access_token(user)
        # set session
        session['access_token'] = access_token
        session['expired'] = expired
        session['openid'] = user.openid
        session['user_id'] = str(user.meta.id)

        # return redirect('/')
        # 使用html进行跳转
        resp = make_response('<meta http-equiv="refresh" content="0;url={}/">'.format(app.config['DOMAIN']))
        resp.set_cookie("__sid__", session.sid, max_age=86400)
        app.logger.info("session %r", session)
        # 登录成功，返回前端首页
        return resp
    except Exception as e:
        app.logger.error(e)
        return make_response('<h1>无访问权限</h1>')


@app.route('/api/access_token', methods=['GET'])
def get_access_token():
    """
    获取access_token
    ---
    tags:
      - 用户相关接口
    parameters:
      - name: code
        in: query
    responses:
      500:
        description: 失败
      200:
        description: 获取成功
    """
    code = request.args.get('code', default='', type=str)
    # TODO mock
    if code == 'JhHogaYEJId1lWLN':
        user_info = {'data': {'openid': 'JhHogaYEJId1lWLN', 'name': 'mock user'}}
    else:
        # user_info = requests.get('{}/api/code2session?code={}'.format(
        user_info = requests.get('{}?code={}'.format(
            # support using custom url
            request.headers.get('X-System-Url', app.config['SYSTEM_URL']), code,
        )).json()

    assert 'data' in user_info and 'openid' in user_info['data'], '获取用户信息失败'
    user = save_user(**user_info['data'])

    access_token, expired = create_access_token(user)
    # set session
    session['access_token'] = access_token
    session['expired'] = expired
    session['openid'] = user.openid
    session['user_id'] = str(user.id)
    app.logger.info("session %r", session)

    return jsonify({'code': 0, 'msg': 'success', 'access_token': access_token, 'expired': expired})


@app.route('/api/account', methods=['GET'])
def get_account():
    user = get_user(session.get('user_id', ''))
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'id': user.meta.id,
            'name': user.name,
            'openid': user.openid,
        },
    })


@app.route('/api/collection/<regex("(client|yuque|notion)"):platform>', methods=['GET'])
def api_get_collection_client(platform):
    if platform not in ['client', 'yuque', 'notion']:
        raise InternalError('error platform')
    user = get_user(session.get('user_id', ''))
    extra = user.extra.to_dict()
    client = extra.get(platform, {})
    if platform == 'client':
        callback_url = f'{app.config["SYSTEM_DOMAIN"]}/feishu/{user.openid}'
        client['callback_url'] = {
            'card': callback_url + '/card',
            'event': callback_url + '/event',
        }
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': client,
    })


@app.route('/api/collection/<platform>', methods=['POST'])
def api_save_collection_client(platform):
    if platform not in ['client', 'yuque', 'notion']:
        raise InternalError('error platform')
    app_id = request.json.get('app_id')
    user = get_user(session.get('user_id', ''))
    extra = user.extra.to_dict() if user.extra else {}
    client = extra.get(platform, {})
    client.update(request.json)
    save_user(openid=user.openid, name=user.name, **{platform: client})
    return jsonify({
        'code': 0,
        'msg': 'success',
    })


@app.route('/api/collection', methods=['GET'])
def api_collections():
    page = request.args.get('page', default=1, type=int)
    size = request.args.get('size', default=20, type=int)
    keyword = request.args.get('keyword', default='', type=str)
    size = 10000 if size > 10000 else size
    user_id = session.get('user_id', '')
    collections, total = get_collections(user_id, keyword, page, size)

    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': [{
            'id': collection.meta.id,
            'name': collection.name,
            'description': collection.description,
            'document_count': get_relation_count_by_id("document", collection_id=collection.meta.id, status=0),
            'created': int(datetime.fromisoformat(collection.created).timestamp() * 1000),
        } for collection in collections],
        'total': total,
    })


@app.route('/api/collection/feishu/wiki', methods=['GET'])
def api_get_feishu_wiki():
    user_id = session.get('user_id', '')
    user = get_user(user_id)
    extra = user.extra.to_dict()
    client = extra.get('client', {})
    loader = LarkWikiLoader('', **client)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': list(loader.get_spaces()),
    })


@app.route('/api/collection', methods=['POST'])
def api_save_collection():
    user_id = session.get('user_id', '')
    type = request.json.get('type', '')
    space_id = request.json.get('space_id', '')
    name = request.json.get('name')
    description = request.json.get('description')
    app.logger.info("debug %r", [name, description, type, space_id])
    if type == 'feishuwiki':
        try:
            user = get_user(user_id)
            extra = user.extra.to_dict()
            client = extra.get('client', {})
            loader = LarkWikiLoader(space_id, **client)
            info = loader.get_info()
            name = info['data']['space']['name']
            description = info['data']['space']['description']
            collection_id = save_collection(user_id, name, description, type=type, space_id=space_id)
            # 异步支持飞书导入任务
            task = embed_feishuwiki.delay(collection_id, False)
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'id': collection_id,
                    'collection_id': collection_id,
                },
            })
        except Exception as e:
            app.logger.error(e)
            return jsonify({
                'code': -1,
                'msg': str(e)
            })

    collection_id = save_collection(user_id, name, description)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'id': collection_id,
            'collection_id': collection_id,
        },
    })


@app.route('/api/collection/<collection_id>', methods=['GET'])
def api_collection_by_id(collection_id):
    user_id = session.get('user_id', '')
    collection = get_collection_by_id(user_id, collection_id)
    assert collection, '找不到知识库或者没有权限'

    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'id': collection.meta.id,
            'type': collection.type if hasattr(collection, 'type') else '',
            'name': collection.name,
            'description': collection.description,
            'created': collection.created_at,
        },
    })


@app.route('/api/collection/<collection_id>', methods=['PUT'])
def api_update_collection_by_id(collection_id):
    user_id = session.get('user_id', '')
    name = request.json.get('name')
    description = request.json.get('description')
    update_collection_by_id(user_id, collection_id, name, description)

    return jsonify({
        'code': 0,
        'msg': 'success',
    })


@app.route('/api/collection/<collection_id>', methods=['DELETE'])
def api_delete_collection_by_id(collection_id):
    user_id = session.get('user_id', '')
    delete_collection_by_id(user_id, collection_id)

    return jsonify({
        'code': 0,
        'msg': 'success',
    })


@app.route('/api/collection/<collection_id>/documents', methods=['GET'])
def api_get_documents_by_collection_id(collection_id):
    page = request.args.get('page', default=1, type=int)
    size = request.args.get('size', default=20, type=int)
    keyword = request.args.get('keyword', default='', type=str)
    size = 10000 if size > 10000 else size
    user_id = session.get('user_id', '')
    documents, total = get_documents_by_collection_id(user_id, collection_id, keyword, page, size)

    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': [{
            'id': document.meta.id,
            'name': document.name,
            'path': document.path,
            'type': document.type,
            'created': int(datetime.fromisoformat(document.created).timestamp() * 1000),
        } for document in documents],
        'total': total,
    })


@app.route('/api/collection/<collection_id>/document/<document_id>', methods=['DELETE'])
def api_remove_document_by_id(collection_id, document_id):
    user_id = session.get('user_id', '')
    remove_document_by_id(user_id, collection_id, document_id)

    return jsonify({
        'code': 0,
        'msg': 'success',
    })


@app.route('/api/collection/<collection_id>/documents', methods=['POST'])
def api_embed_documents(collection_id):
    fileName = request.json.get('fileName')
    fileUrl = request.json.get('fileUrl')
    fileType = request.json.get('fileType')
    uniqid = request.json.get('uniqid')
    user_id = session.get('user_id', '')
    collection = get_collection_by_id(user_id, collection_id)
    assert collection, '找不到知识库或者没有权限'
    if not fileName:
        fileName = unquote(fileUrl.split('/').pop().split('?')[0])
    # 增加额外的单个知识库去重处理(documents.collection_id，一个文档只能在单个知识库存在)
    # 只有主动传了这个参数，才会去重，否则走之前的逻辑不去重，免得有的旧版本出问题
    if uniqid:
        document_id = get_document_id_by_uniqid(collection_id, uniqid)
        if document_id and len(document_id) > 0:
            return jsonify({
                'code': 0,
                'msg': 'success',
                'data': {
                    'document_id': document_id,
                },
            })

    if fileType == 'feishudoc':
        # 如果是飞书文档，同步尝试load一下，失败了就同步报错
        try:
            collection = get_collection_by_id(None, collection_id)
            user = get_user(user_id)
            extra = user.extra.to_dict()
            client = extra.get('client', {})
            loader = LarkDocLoader(fileUrl, None, **client)
            doc = loader.load()
        except Exception as e:
            app.logger.error(e)
            return jsonify({
                'code': -1,
                'msg': str(e)
            })
    elif fileType == 'yuque':
        # 如果是语雀文档，同步尝试load一下，失败了就同步报错
        try:
            collection = get_collection_by_id(None, collection_id)
            user = get_user(user_id)
            extra = user.extra.to_dict()
            yuque = extra.get('yuque', {})
            loader = YuqueDocLoader(fileUrl, **yuque)
            doc = loader.load()
        except Exception as e:
            app.logger.error(e)
            return jsonify({
                'code': -1,
                'msg': str(e)
            })
    elif fileType == 'notion':
        # 如果是notion文档，同步尝试load一下，失败了就同步报错
        try:
            collection = get_collection_by_id(None, collection_id)
            user = get_user(user_id)
            extra = user.extra.to_dict()
            notion = extra.get('notion', {})
            loader = NotionDocLoader(fileUrl, **notion)
            doc = loader.load()
        except Exception as e:
            app.logger.error(e)
            return jsonify({
                'code': -1,
                'msg': str(e)
            })
    # isopenai=False
    task = embed_documents.delay(fileUrl, fileType, fileName, collection_id, False, uniqid=uniqid)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'task_id': task.id,
        },
    })


@app.route('/api/collection/<collection_id>/task/<task_id>', methods=['GET'])
def api_get_task_by_id(collection_id, task_id):
    user_id = session.get('user_id', '')
    collection = get_collection_by_id(user_id, collection_id)
    assert collection, '找不到知识库或者没有权限'

    task = get_status_by_id(task_id)

    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'task_id': task.id,
            'status': task.status,
            'result': task.result if isinstance(task.result, list) else str(task.result),
        },
    })


@app.route('/api/collection/<collection_id>/query', methods=['GET'])
def api_query_by_collection_id(collection_id):
    q = request.args.get('q', default='', type=str)
    page = request.args.get('page', default=1, type=int)
    size = request.args.get('size', default=20, type=int)
    size = 10000 if size > 10000 else size
    user_id = session.get('user_id', '')
    # using array
    collection_id = collection_id.split(',')
    for cid in collection_id:
        collection = get_collection_by_id(user_id, cid)
        assert collection, '找不到知识库或者没有权限'

    documents, total = query_by_collection_id(collection_id, q, page, size)

    app.logger.info("%r %r", documents, total)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': [{
            'document_id': document.document_id,
            # 'document_name': document.document_name,
            'document': document.document,
            'distance': distance,
            'collection_id': collection_id,
        } for document, distance in documents],
        'total': total,
    })


@app.route('/api/document/<document_id>/query', methods=['GET'])
def api_query_by_document_id(document_id):
    q = request.args.get('q', default='', type=str)
    page = request.args.get('page', default=1, type=int)
    size = request.args.get('size', default=20, type=int)
    size = 10000 if size > 10000 else size
    user_id = session.get('user_id', '')
    if q:
        documents, total = query_by_document_id(document_id, q, page, size)
    else:
        documents, total = get_docs_by_document_id(document_id, page, size)

    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': [{
            'document_id': document.document_id,
            # 'document_name': document.document_name,
            'document': document.document,
            'distance': distance,
        } for document, distance in documents],
        'total': total,
    })


@app.route('/api/document/<document_id>', methods=['DELETE'])
def api_purge_document_by_id(document_id):
    purge_document_by_id(document_id)
    return jsonify({'code': 0, 'msg': 'success'})


@app.route('/api/document/<document_id>', methods=['POST'])
def api_set_document_summary(document_id):
    summary = request.json.get('summary')
    set_document_summary(document_id, summary)
    return jsonify({'code': 0, 'msg': 'success'})


@app.route('/api/document/<document_id>', methods=['GET'])
def api_get_document(document_id):
    document = get_document_by_id(document_id)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'id': document.id,
            'name': document.name,
            'type': document.type,
            'chunks': document.chunks,
            'uniqid': document.uniqid,
            'summary': document.summary,
        }
    })


class ThreadTask(threading.Thread):
     def __init__(self, main, *args, **kwargs):
         super(ThreadTask, self).__init__()
         self.main = partial(main, *args, **kwargs)
 
     def run(self):
         return self.main()


@app.route('/api/collection/<collection_id>/openai/deployments/<deployment_name>/chat/completions', methods=['POST'])
def azure_chat_on_collection(collection_id, deployment_name):
    data = request.json
    # user_id = session.get('user_id', '')
    # collection = get_collection_by_id(user_id, collection_id)
    # assert collection, '找不到知识库或者没有权限'

    sse = ServerSentEvents()

    request.environ['STARTED'] = False
    def on_llm_new_token(token):
        if not request.environ['STARTED']:
            sse.send('assistant', event='role')
            app.logger.info("send role")
            request.environ['STARTED'] = True

        # app.logger.info("streaming on_llm_new_token %r", token)
        sse.send(token)

    @copy_current_request_context
    def main():
        result = chat_on_collection(
            collection_id, deployment_name,
            on_llm_new_token=on_llm_new_token,
            **data,
        )
        app.logger.info("%r", result)
        sse.send(None, event='end')
        return result

    # stream模式
    if data.get('stream'):
        t = ThreadTask(main)
        t.start()
        return sse.response()

    result = main()
    return jsonify({
        'id': uuid4(),
        'object': 'chat.completion',
        'created': int(time()),
        'choices': [{
            'index': 0,
            'message': {
                'role': 'assistant',
                'content': result.get('answer', result.get('result', '')),  # 兼容处理
            },
            'finish_reason': 'stop',
        }],
        'usage': result.get('usage', {}),
        # 这里返回相关文档信息
        'source_documents': [{
            'content': i.page_content,
            'metadata': i.metadata,
        } for i in result.get('source_documents', [])],
    })


# https://api.openai.com/v1/chat/completions
@app.route('/embed/<hash>/v1/chat/completions', methods=['POST'])
@app.route('/api/collection/<collection_id>/v1/chat/completions', methods=['POST'])
def openai_chat_on_collection(collection_id='', hash=''):
    if hash:
        # 这里使用hash判断是否有权限，并且
        collection_id = get_collection_id_by_hash(hash)
        if not collection_id:
            raise PermissionDenied()
        data = get_data_by_hash(hash, request.json)
    else:
        data = request.json
    # user_id = session.get('user_id', '')
    # collection = get_collection_by_id(user_id, collection_id)
    # assert collection, '找不到知识库或者没有权限'

    sse = ServerSentEvents()

    request.environ['STARTED'] = False
    def on_llm_new_token(token):
        if not request.environ['STARTED']:
            sse.send('assistant', event='role')
            app.logger.info("send role")
            request.environ['STARTED'] = True

        # app.logger.info("streaming on_llm_new_token %r", token)
        sse.send(token)

    @copy_current_request_context
    def main():
        result = chat_on_collection(
            collection_id, None,
            on_llm_new_token=on_llm_new_token,
            **data,
        )
        app.logger.info("%r", result)
        sse.send('', event='end')
        return result

    # stream模式
    if data.get('stream'):
        t = ThreadTask(main)
        t.start()
        return sse.response()

    result = main()
    return jsonify({
        'id': uuid4(),
        'object': 'chat.completion',
        'created': int(time()),
        'choices': [{
            'index': 0,
            'message': {
                'role': 'assistant',
                'content': result.get('answer', result.get('result', '')),  # 兼容处理
            },
            'finish_reason': 'stop',
        }],
        'usage': result.get('usage', {}),
        # 这里返回相关文档信息
        'source_documents': [{
            'content': i.page_content,
            'metadata': i.metadata,
        } for i in result.get('source_documents', [])],
    })


@app.route('/api/file/<user_id>/<filename>', methods=['GET'])
def get_file(user_id, filename):
    app.logger.info('filename %r', filename)
    return send_file(app.config['UPLOAD_PATH'] + '/' + user_id + '/' + filename)


@app.route('/api/upload', methods=['POST'])
def upload():
    # app.logger.info("file %r", request.files['file'])
    if 'file' not in request.files:
        raise InternalError()
    file = request.files['file']
    user_id = session.get('user_id', '')
    directory = app.config['UPLOAD_PATH'] + '/' + user_id
    filename = file.filename.replace('/', '_')
    if not os.path.exists(directory):
        os.makedirs(directory)
    file.save(directory + '/' + filename)
    return {
        'url': app.config['DOMAIN'] + '/api/file/' + user_id + '/' + quote(filename) + '?__sid__=' + session.sid,
    }


@app.route('/api/bot', methods=['GET'])
def get_bot_list_handler():
    page = request.args.get('page', default=1, type=int)
    size = request.args.get('size', default=20, type=int)
    size = 10000 if size > 10000 else size
    user_id = session.get('user_id', '')
    bots, total = get_bot_list(user_id, '', page, size)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': [{
            'bot_id': bot.id,
            'user_id': bot.user_id,
            'collection_id': bot.collection_id if bot.collection_id not in [b'undefined', b''] else None,
            'status': bot.status,
            'hash': bot.hash,
            'extra': bot.extra,
        } for bot in bots],
        'total': total,
    })


@app.route('/api/bot/<hash>', methods=['GET'])
def get_bot_by_hash_handler(hash):
    bot = get_bot_by_hash(hash)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': {
            'bot_id': bot.id,
            'user_id': bot.user_id,
            'collection_id': bot.collection_id if bot.collection_id not in [b'undefined', b''] else None,
            'status': bot.status,
            'hash': bot.hash,
            'extra': bot.extra,
        },
    })


@app.route('/api/collection/<collection_id>/bot', methods=['GET'])
def get_hash_by_collection_id_handler(collection_id):
    hash = get_hash_by_collection_id(collection_id)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': hash,
    })


@app.route('/api/collection/<collection_id>/bot', methods=['POST'])
def create_bot_handler(collection_id):
    user_id = session.get('user_id', '')
    hash = create_bot(user_id, collection_id, **request.json)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': hash,
    })


@app.route('/api/bot/<hash>', methods=['PUT'])
def update_bot_handler(hash):
    # action = request.json.get('action', '')  # action=start/stop/remove/refresh
    # collection_id = request.json.get('collection_id', '')  # 跟新机器人对应的知识库
    hash = update_bot_by_hash(hash, **request.json)
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': hash,
    })

