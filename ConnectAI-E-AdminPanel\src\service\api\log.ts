import axios from 'axios';
import qs from 'qs';
import { request } from '~/src/service/request';

export interface DataSource<T> {
  total?: number;
  list?: T[];
}

export function fetchMJLogList(data: ApiLog.Req.LogList) {
  return request.post<DataSource<ApiLog.Resp.LogList>>('/api/midjourney/list', data);
}

export function exportMJLogList(data?: ApiLog.Req.Export) {
  return request.post<{ backend: any; disp: string }>('/api/midjourney/export', data);
}

export function fetchLogList(params: ApiLog.Req.LogList) {
  return request.get<ApiLog.Resp.LogList>('/api/chatlog', { params });
}

export function exportLogList(params: {
  keyword?: string;
  app?: string;
  user?: string;
  start_date?: string;
  end_date?: string;
}) {
  const prefix = import.meta.env.PROD === false ? '/api/api/chatlog/export' : '/api/chatlog/export';
  return open(`${prefix}?${qs.stringify(params)}`);
}

export function fetchSensList(params: ApiSensitive.Req.List) {
  return request.get<ApiSensitive.Resp.SensitiveList>('/api/sensitive', { params });
}
export function addSensitive(data: ApiSensitive.Req.Create) {
  return request.post<{ code: number }>('/api/sensitive', data);
}
export function delSensitive({ id }: ApiSensitive.Req.Delete) {
  return request.delete<{ code: number }>(`/api/sensitive/${id}`);
}
export function editSensitive({ id, data }: ApiSensitive.Req.Update) {
  return request.put<{ code: number }>(`/api/sensitive/${id}`, data);
}

export function updateSensitiveStatus({ id, action }: ApiSensitive.Req.UpdateStatus) {
  return request.put(`/api/sensitive/${id}/${action}`);
}

export function exportSensList(params) {
  return open(`/api/sensitive/export?${qs.stringify(params)}`);
}
