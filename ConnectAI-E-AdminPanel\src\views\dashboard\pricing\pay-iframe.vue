<template>
  <div class="relative">
    <iframe
      ref="payIframe"
      frameborder="0"
      width="252"
      height="252"
      class="border border-2 dark:border-white border-dark p-2 rounded-lg scale-90 relative"
    ></iframe>
    <transition name="fade" mode="out-in">
      <div
        v-show="showMask"
        class="absolute inset-0 bg-gray-600 dark:bg-dark-800 opacity-90 flex justify-center items-center cursor-pointer rounded-xl"
        @click="reloadIframe"
      >
        <icon-local-reload class="text-50px text-white" />
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, h } from 'vue';
import { useMessage } from 'naive-ui';
import qs from 'qs';
import { refreshHandler } from '@/hooks';
import { IRpc } from '@/utils';
import { t } from '@/locales';

const message = useMessage();

const emit = defineEmits(['pay-success']);
const payIframe = ref();

const showMask = ref(false);

const reloadIframe = () => {
  reload();
};

const props = defineProps<{
  tenantProductId: string;
}>();

function reload() {
  showMask.value = false; // 隐藏遮罩层
  const BASE_URL = import.meta.env.PROD === false ? 'https://ai-zhoulin.forkway.cn' : '';
  const container = payIframe.value;
  IRpc({
    container,
    action: `${BASE_URL}/api/wepay?${qs.stringify({ tenant_product_id: props.tenantProductId })}`,
    timeout: 55000
  })
    .then((result) => {
      console.log('result', result);
      emit('pay-success');
    })
    .catch((error) => {
      console.error('error', error);
      message.error(error || t('message.msg.zfsb'));
      showMask.value = true;
    })
    .finally(() => {
      refreshHandler();
    });
}

onMounted(() => {
  reload();
});
</script>
<style scoped>
.fade-enter-active,
.fade-leave-active {
  @apply transition-opacity duration-3000;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0;
}
</style>
