import type { PlasmoMessaging } from "@plasmohq/messaging";
import { getCookieAndToken } from "~utils/browser";
import { Configuration } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  // TODO
  // 这个地方如果报错，说明企联AI那个地方的APP_ID 在飞书的租户那边无法查找到
  const { appId, domain } = req.body;
  const params = await getCookieAndToken(domain);
  console.log("get app info", params, req);
  const configCookie = new Configuration({ domain } as any);
  // 这里手动设置csrftoken
  // @ts-ignore
  configCookie.csrfToken = params.csrf_token;
  const app = new OpenApp(configCookie);
  try {
    const secret = await app.getAppSecret(appId);
    const eventInfo = await app.eventManager.getEventInfo(appId);
    const { encryptKey, verificationUrl, verificationToken } = eventInfo;
    res.send({ secret, encryptKey, verificationUrl, verificationToken });
  } catch (e) {
    console.log("get app info error", e);
    res.send({ error: e.message });
  }
};

export default handler;

