import logging
import time
from typing import Any, List, Optional

import httpx
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import AIMessage, BaseMessage, ChatGeneration, ChatResult

logger = logging.getLogger(__name__)


class RVClient(object):
    def __init__(self, api_base, api_key=""):
        self.api_key = api_key
        self.api_base = api_base

    def create(self, url, timeout=12000, **kwargs) -> Any:
        # TODO: 原 content：binary，所以需要修改掉要调用的地方

        #  创建任务
        with httpx.Client(
            timeout=timeout,
            transport=httpx.HTTPTransport(
                retries=5,
            ),
        ) as client:

            res = client.post(
                f"{self.api_base}/job",
                headers={
                    "Authorization": f"{self.api_key}",
                    "Content-Type": "application/json",
                },
                json={
                    "name": str(time.time()),
                    "workflow": "removevocal",
                    "params": {
                        "inputUrl": url,
                    },
                },
            )

        if res.status_code != httpx.codes.OK:
            logger.error("Error: {}".format(res.text))
            return None

        taskId = res.json()["id"]

        # 获取任务结果

        result = {}
        while True:
            with httpx.Client(
                timeout=timeout,
                transport=httpx.HTTPTransport(
                    retries=5,
                ),
            ) as client:
                res = client.get(
                    f"{self.api_base}/job",
                    headers={
                        "taskId": taskId,
                        "Authorization": f"{self.api_key}",
                    },
                )

                if res.status_code != httpx.codes.OK:
                    logger.error("Error: {}".format(res.text))
                    return None

                if isinstance(res.json(), list):
                    for item in res.json():
                        if item["id"] == taskId:
                            result = item

                if result["status"] == "SUCCEEDED":
                    return result["result"]["Vocal"], result["result"]["Other"]

                elif result["status"] == "FAILED":
                    result["result"] = result["status"]
                    raise Exception("Failed")
                    return None, None

            time.sleep(1)


class RVChat(SimpleChatModel):
    """
    Removevocal
    """

    client: Any
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    # streaming: bool = False # TODO: 记的移除所有的 streaming 参数

    def _llm_type(self) -> str:
        return "removevocal_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> ChatResult:
        params = {
            "api_key": self.api_key,
            "api_base": self.api_base,
            # 'streaming': self.streaming,
        }

        # 只获取一条 message
        message = messages.pop()
        # 将 file_url 通过 additional_kwargs 传入
        params.update(**message.additional_kwargs)

        self.client = RVClient(api_base=self.api_base, api_key=self.api_key)
        response = {}

        # 在这一步调用了 create 方法
        response["url"], response["url_others"] = self.client.create(**params)

        if response["url"] is None or response["url_others"] is None:
            raise Exception("Error: url is None")

        # log 的格式是什么？
        logger.info(f"chat res: succeeded reciving from removevocal")

        message = AIMessage(content="", additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])


def message_test():
    import asyncio
    import os

    async def main():
        pwd = os.getcwd()

        # TODO: 记得改成音频文件
        music_path = os.path.join(
            pwd,
            "/home/<USER>/workspace/repos/manager-server/server/core/result.mp3",
        )

        from langchain.schema import HumanMessage

        params = {"url": os.getenv("REMOVEVC_URL")}

        chat = RVChat(
            api_base=os.getenv("REMOVEVC_API_BASE"),
            api_key=os.getenv("REMOVEVC_API_KEY"),
        )

        messages = [HumanMessage(content="", additional_kwargs=params)]
        response = chat(messages=messages)

        # 注意这里调用结果的方式和外部调用不同
        # 外部调用会再包一层
        additional_kwargs = response.additional_kwargs

        data = additional_kwargs["url"]
        # 下载文件
        async with httpx.AsyncClient() as client:
            res = await client.get(data)
            with open(music_path, "wb") as f:
                f.write(res.content)

        print("Test complete")

    asyncio.run(main())


if __name__ == "__main__":
    import dotenv

    dotenv.load_dotenv(
        "/home/<USER>/workspace/repos/manager-server/server/core/.env"
    )

    message_test()
