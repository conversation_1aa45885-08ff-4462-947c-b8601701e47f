<template>
  <div class="w-full">
    <div class="grid grid-cols-1 px-4 pt-6 xl:grid-cols-12 xl:gap-4 overflow-auto h-full">
      <div class="col-span-full xl:col-span-4 gap-10">
        <HeadInfo :loading="loading" :data="setting" :app-info="appInfo" />
        <ResourceInfo
          v-model:data="setting"
          :loading="loading"
          :resource="resource"
          :resources="resources"
          :is-dataset="isDataset"
          @change="handleResourceChange"
        />
        <ChatHistory v-model:data="setting" :loading="loading" />
        <WebSearch v-model:data="setting" :loading="loading" v-if="setting.web_search" />
        <Artifacts v-model:data="setting" :loading="loading" v-if="setting.artifacts" />
      </div>
      <!-- Right Content -->
      <div class="col-span-8">
        <div
          v-if="loading"
          class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
        >
          <n-space vertical>
            <n-skeleton height="40px" width="33%" :sharp="false" />
            <n-skeleton height="60px" :sharp="false" />
            <n-skeleton height="60px" />
            <n-skeleton height="60px" />
            <n-skeleton height="40px" width="100px" :sharp="false" />
          </n-space>
        </div>
        <ServiceSetting :loading="loading" :app-info="appInfo" />
        <BotRightInfo
          v-model:data="setting"
          :loading="loading"
          :models="models"
          :resource="resource"
          :resources="resources"
        />
        <Dataset v-if="isDataset" v-model:data="setting" :loading="loading" />
        <Prompt v-if="isDataset" v-model:data="setting" :loading="loading" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { isEmpty, toPairs } from 'lodash-es';
import { useLoading } from '@/hooks';
import { getAppSetting, getAppResource, getAppInfo, getAppResources } from '@/service/api/app';
import HeadInfo from './component/headInfo.vue';
import ResourceInfo from './component/resourceInfo.vue';
import BotRightInfo from './component/botRightInfo.vue';
import ServiceSetting from './component/serviceSetting.vue';
import Dataset from './component/dataset.vue';
import Prompt from './component/prompt.vue';
import ChatHistory from './component/chatHistory.vue';
import WebSearch from './component/webSearch.vue';
import Artifacts from './component/artifacts.vue';
import { getModelPermission } from './utils';

const route = useRoute();
const { loading, startLoading, endLoading } = useLoading();
const resource = ref<ApiApp.AppResource[]>([]);
const resources = ref<ApiApp.AppResources[]>([]);

const models = ref<ApiApp.AppResource['models']>([]);
const setting = ref<ApiApp.APPSetting>({
  resource_id: '',
  resource_ids: {},
  group_permission: [],
  user_permission: [],
  prompt_id: [],
  sensitive_id: [],
  collection_id: [],
  prompt: '',
  chat_history: 'enable',
  web_search: 'enable',
  artifacts: 'disable',
} as ApiApp.APPSetting);
const appInfo = ref<ApiApp.AppInfo>({} as ApiApp.AppInfo);

// FIXME: 临时解决方案
const isDataset = computed(() => {
  return (
    appInfo.value?.name?.includes('知识库') ||
    appInfo.value?.name?.includes('Knowledge') ||
    appInfo.value?.name?.includes('ChatBase')
  );
});

watch(
  () => route.query.id,
  (appid) => {
    if (appid) {
      getSetting(appid);
    }
  },
  {
    immediate: true
  }
);

async function getSetting(appid: string) {
  startLoading();
  try {
    const res = await getAppSetting({ id: appid });
    const {
      data: { data: resourceData }
    } = await getAppResource({ id: appid });
    const {
      data: { data: resourcesData }
    } = await getAppResources({ id: appid });
    const {
      data: { data: appInfoData }
    } = await getAppInfo({ id: appid });
    resources.value = resourcesData;
    appInfo.value = appInfoData;
    resource.value = resourceData;
    const formatSettion = handleSetting(res.data!.data, resourcesData, resourceData);
    console.log('Dogtiti ~ file: index.vue:118 ~ getSetting ~ formatSettion:', formatSettion);
    setting.value = formatSettion;
    endLoading();
  } catch (err) {}
}

function handleSetting(
  setting: ApiApp.APPSetting,
  resourcesData: ApiApp.AppResources[],
  resourceData: ApiApp.AppResource[]
) {
  // 解构 setting 对象
  const { group_permission, user_permission, resource_ids, resource_id } = setting;

  // 如果 resources 为空
  if (isEmpty(resourcesData)) {
    // 查找 resource_id 对应的模型
    const models = resourceData.find((item) => item.id === resource_id)?.models || [];

    // 如果 group_permission 为空，创建它
    if (isEmpty(group_permission)) {
      setting.group_permission = getModelPermission(models, '', 'allow_groups', 'deny_groups');
    } else {
      // 如果 group_permission 不为空，更新 flag
      setting.group_permission.forEach((item) => {
        item.flag = item.allow_groups.length > 0;
      });
    }

    // 如果 user_permission 为空，创建它
    if (isEmpty(user_permission)) {
      setting.user_permission = getModelPermission(models, '', 'allow_users', 'deny_users');
    } else {
      // 如果 user_permission 不为空，更新 flag
      setting.user_permission.forEach((item) => {
        item.flag = item.allow_users.length > 0;
      });
    }
  } else {
    // 如果 resources 不为空
    // 如果 group_permission 为空，创建它
    if (isEmpty(group_permission)) {
      setting.group_permission = isEmpty(resource_ids)
        ? group_permission
        : toPairs(resource_ids)
            .map(([scene, resource_id]) => {
              // 查找 resource_id 对应的模型
              const { models, name, description } =
                resourcesData.find((r) => r.scene === scene)?.resource?.find((r) => r.id === resource_id) || {};
              return models
                ? getModelPermission(models, resource_id, 'allow_groups', 'deny_groups', name, description)
                : [];
            })
            .flat();
    } else {
      // 如果 group_permission 不为空，更新 flag
      setting.group_permission.forEach((item) => {
        item.flag = item.allow_groups.length > 0;
      });
    }

    // 如果 user_permission 为空，创建它
    if (isEmpty(user_permission)) {
      setting.user_permission = isEmpty(resource_ids)
        ? user_permission
        : toPairs(resource_ids)
            .map(([scene, resource_id]) => {
              // 查找 resource_id 对应的模型
              const { models, name, description } =
                resourcesData.find((r) => r.scene === scene)?.resource?.find((r) => r.id === resource_id) || {};
              return models
                ? getModelPermission(models, resource_id, 'allow_users', 'deny_users', name, description)
                : [];
            })
            .flat();
    } else {
      // 如果 user_permission 不为空，更新 flag
      setting.user_permission.forEach((item) => {
        item.flag = item.allow_users.length > 0;
      });
    }
  }
  // 返回更新后的 setting
  return setting;
}
function handleResourceChange(values: ApiApp.AppResource['models']) {
  models.value = values;
}
</script>

<style scoped></style>
