import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.002 5.001a2 2 0 0 1 2-2h9.997a2 2 0 0 1 2 2v6.866l-.982-1.702a1.907 1.907 0 0 0-.018-.031V5a1 1 0 0 0-1-1H5.002a1 1 0 0 0-1 1v9.997a1 1 0 0 0 1 1H8.61l-.305.53c-.086.149-.155.307-.205.47H5.002a2 2 0 0 1-2-2V5.001zM9.762 14l.578-1H8.496a.5.5 0 0 0 0 1h1.267zm2.02-3.5l.193-.335c.152-.265.349-.487.575-.665H8.496a.5.5 0 0 0 0 1h3.285zM7 6.497a.75.75 0 1 1-1.5 0a.75.75 0 0 1 1.5 0zm-.75 4.255a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5zm.75 2.75a.75.75 0 1 1-1.5 0a.75.75 0 0 1 1.5 0zm1.496-7.507a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zm5.15 4.051a1.335 1.335 0 0 1 1.505.618l3.67 6.363a1.314 1.314 0 0 1-.493 1.797a1.34 1.34 0 0 1-.663.176h-7.338c-.37 0-.701-.148-.942-.388a1.307 1.307 0 0 1-.214-1.585l3.67-6.363c.182-.316.475-.529.806-.618zm.85 2.453a.5.5 0 1 0-1 .002l.005 2.502a.5.5 0 1 0 1-.002l-.005-2.502zm-.495 5.5a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListSquareWarning20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
