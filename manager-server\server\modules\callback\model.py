import asyncio
import logging
import xmltodict
import json
import uuid
from urllib.parse import quote
from tornado.options import options
from sqlalchemy import alias, and_, or_, select, func, distinct, text
from wechatpy.work import WeChatClient
from wechatpy.work.crypto import WeChatCrypto
from wechatpy.work.exceptions import InvalidCorpIdException
from wechatpy.work.services import WeChatServiceClient
from wechatpy.exceptions import InvalidSignatureException
from wechatpy.utils import to_text
from wechatpy.session.redisstorage import RedisStorage
from core.base_model import MysqlModel, RedisModel
from core.exception import NotFound, PermissionDenied, InternalError, Duplicate, SensitiveError
from core.utils import row2dict, ObjectDict
from core.feishu import Lark
from core.redisdb import redis_cli, stalecache
from core.rabbitmq import MqSession
from core.dingding import DingDing, DingDingCallbackError, TextMessage as DTextMessage
from core.wework import WEWorkCallbackError
from core.web import Web
from core.know import KnowClient
from core.translate_document import AliyunChat
from core.schema import (
    ObjID,
    Application, ApplicationWithWXSuite, ApplicationWithActionTemplate,
    Tenant,
    Bot,
    BotInstance,
    AppInstance,
    AppInstanceBotInstance,
    UserAccessAppInstance, Policy,
    Resource, Model,
    ChatLog,
    Sensitive,
    AppInstanceSensitive,
    TenantProduct,
    TenantSeat,
    User
)

from core.downloader import ChunkedDownloader


class BotInstanceModel(MysqlModel):
    """
    通过bot_instance获取对应的app_instance
    """
    def __init__(self, bot, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.bot = bot

    def cache_key(self, open_id):
        return 'bot_instance2app:{}:{}'.format(self.bot.id, open_id)

    def get_current_app_instance(self, open_id):
        instance_id = redis_cli().get(self.cache_key(open_id)) or self.set_cuttent_bot_instance(open_id, app_id='')
        app_instance = self.session.query(AppInstance).filter(
            AppInstance.id == instance_id,
        ).first()
        if not app_instance:
            raise NotFound('找不到应用')
        return app_instance

    def get_application_name(self, application_id):
        return self.session.query(Application.name).filter(Application.id == application_id).limit(1).scalar()

    def get_application_template(self, application_id):
        return self.session.query(ApplicationWithActionTemplate.action_template).filter(Application.id == application_id).limit(1).scalar()

    def set_cuttent_bot_instance(self, open_id, app_id=''):
        query = self.session.query(AppInstanceBotInstance).filter(
            AppInstanceBotInstance.bot_instance_id == self.bot.id,
        ).order_by(
            AppInstanceBotInstance.modified.desc(),  # TODO
            AppInstanceBotInstance.created.desc(),
        )
        if app_id:
            query = query.join(
                AppInstance,
                AppInstance.id == AppInstanceBotInstance.instance_id,
            ).filter(
                AppInstance.application_id == app_id,
            )
        bot_instance = query.first()
        # 设置缓存
        if bot_instance:
            redis_cli().set(self.cache_key(open_id), bot_instance.instance_id)
        return bot_instance.instance_id


class UserAccessModel(MysqlModel):

    """
    1.1版本使用Policy控制
    2.0版本使用新的座席进行控制
    """

    def __init__(self, bot, app_instance, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.bot = bot
        self.app_instance = app_instance

    def _get_available_model(self, permission, name):
        # 如果名字是空，直接返回空字符串
        if not name:
            return []
        models = set()
        for model_id, allow, deny in permission:
            # 先判断允许的情况，加入进去。再判断屏蔽的情况，移除
            if name in allow:
                models.add(model_id)
            elif name in deny:
                if model_id in models:
                    models.remove(model_id)
            elif len(allow) == 0:
                # 只要不在deny列表，假如allow为空，也算允许
                models.add(model_id)
        return list(models)

    async def get_available_model(self, chat_type, username, groupname):
        if not self.app_instance:
            return []
        # 如果是客服，直接配置了model_id，用这个就好了
        if self.app_instance.extra.get('model_id'):
            return [self.app_instance.extra.get('model_id')]
        # TODO 2.0版本按照新的user_access_app_instance表的逻辑判断
        from modules.application.resource_model import AppSettingModel
        setting = await AppSettingModel().get_setting_by_application_id(
            self.app_instance.tenant_id,
            None, # self.app_instance.user_id,
            '',
            self.app_instance.id,
        )

        if chat_type == 'group':
            return self._get_available_model([(
                item['model_id'], item['allow_groups'], item['deny_groups']
            ) for item in setting['group_permission']], groupname)
        else:
            return self._get_available_model([(
                item['model_id'], item['allow_users'], item['deny_users']
            ) for item in setting['user_permission']], username)

    async def check_subscribe(self):
        return True  # 定制产品，不校验套餐权限
        # return self.session.query(TenantProduct.id).filter(
        #     TenantProduct.tenant_id == self.app_instance.tenant_id,
        #     TenantProduct.status == 1,
        #     TenantProduct.expired > func.now(),  # 没有过期的套餐
        # ).order_by(
        #     TenantProduct.expired.desc(),
        # ).limit(1).scalar()

    async def check_policy(self, chat_type, username, groupname):
        # 不管是私聊还是群聊，都需要先检验是否在座席列表里面
        if groupname != 'messenger' and not self.session.query(TenantSeat.id).filter(
            TenantSeat.tenant_id == self.bot.tenant_id,
            TenantSeat.name == username,
            TenantSeat.status == 1,
        ).limit(1).scalar():
            logging.warn("user %r not in seats when chat in group %r", username, groupname)
            if self.session.query(Tenant.auto_add_seat).filter(
                Tenant.id == self.bot.tenant_id,
            ).limit(1).scalar() == 1:
                from modules.account.model import TenantSeatModel
                try:
                    with TenantSeatModel() as model:
                        await model.add_seat(self.bot.tenant_id, self.bot.user_id, username, '', '')
                except Exception as e:
                    # 添加不成功的时候，就说明没有权限（有可能在企业中，没有启用）
                    return None, []
            else:
                return None, []
        available_models = await self.get_available_model(chat_type, username, groupname)
        if len(available_models) > 0:
            # TODO 这里需要使用选择的那一个，而不是第一个
            return available_models[0], available_models
        return None, []


class MessageModel(MysqlModel):

    def __init__(self, bot, app_instance, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.bot = bot
        self.app_instance = app_instance

    async def check_sensitive(self, content, tenant_id = '', instance_id=''):
        for sensitive in self.session.query(Sensitive).join(
            AppInstanceSensitive,
            AppInstanceSensitive.sensitive_id == Sensitive.id,
        ).filter(
            Sensitive.tenant_id == tenant_id,
            Sensitive.status == 1,  # 激活状态
            AppInstanceSensitive.instance_id == instance_id,
            AppInstanceSensitive.status == 0,
        ).all():
            for n in sensitive.name:
                if n in content:
                    return sensitive.id

    async def _save_message(
        self,
        tenant_id=None, connectai_user_id=None,
        bot_instance_id=None, instance_id=None,
        username='',
        chat_id='', message_id='', message_type='',
        sender_id='',
        content='',
        # create_time='',
        **extra,
    ):
        # 这里统一判断敏感词
        sensitive_id = await self.check_sensitive(content, tenant_id, instance_id)
        log_id = ObjID.new_id()
        self.session.begin_nested()
        log = ChatLog(
            id=log_id,
            tenant_id=tenant_id,
            user_id=connectai_user_id,
            bot_instance_id=bot_instance_id,
            instance_id=instance_id,
            sensitive_id=sensitive_id,
            username=username,
            chat_id=chat_id,
            message_id=message_id,
            message_type=message_type,
            sender_id=sender_id,
            content=content,
            extra=extra,
            status=1 if sensitive_id else 0,  # 匹配到敏感词，直接存储展示
        )
        self.session.add(log)
        self.session.commit()
        # 匹配到敏感词抛出异常
        if sensitive_id:
            raise SensitiveError()
        return log

    async def save_dingding_message(self, senderNick='', msgtype='', conversationId='', msgId='', senderId='', model_id='', models=list(), lang='zh_CN', **message):
        content = ''
        platform_content = {}
        if msgtype == 'text':
            content = message['text']['content'].strip()
            del message['text']
        else:
            # 其他类型都带content字段
            platform_content = message['content']
            del message['content']
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=senderNick,
            chat_id=conversationId,
            message_id=msgId,
            message_type=msgtype,
            sender_id=senderId,
            content=content,
            # platform='dingding',
            platform_content=platform_content,
            **message,
        )
        # 处理钉钉mj或者sd重新生成逻辑
        input = content
        try:
            contents = content.split(':')
            # mjapi reroll 不用走到这一步
            if len(contents) > 1 and contents[0] == 'reroll' and contents[1]:
                # 输出给钉钉的卡片消息包含reroll以及log_id
                input = self.session.query(ChatLog.content).filter(ChatLog.id == contents[1]).limit(1).scalar() or ''
        except Exception as e:
            logging.error('save dingding msg err: {}', e)
        # action在应用自己的逻辑里面处理
        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': 'dingding',
            'extra': row2dict(log),
            'input': input,
            'lang': lang,
            'model_id': model_id,  # 这里需要查询出模型以及版本
            'models': models,
        }
        message['platform'] = 'dingding'
        return message

    async def save_feishu_menu_event(self, event, username='', model_id='', models=list(), lang='zh_CN'):
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=username,
            # 飞书的这个层级有点深
            sender_id=event.operator.operator_id.open_id,
            event_key=event.data.event.event_key,
            **event.operator.operator_id,
        )
        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': 'feishu',  # 这里是FeishuModel，所以回复消息也一定是feishu
            'extra': row2dict(log),
            'input': '',
            'lang': lang,
            'model_id': model_id,
            'models': models,
        }
        message['platform'] = 'feishu'  # 这里是FeishuModel，所以回复消息也一定是feishu
        return message

    async def save_feishu_chat_create(self, event, username='', model_id='', models=list(), lang='zh_CN'):
        # 默认发一个帮助消息
        content = '/help p2p_chat_create'
        event_data = event.data.event
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=username,
            # 飞书的这个层级有点深
            sender_id=event_data.user.open_id,
            content=content,
            chat_id=event_data.chat_id,
            message_type='text',
            operator_id=event_data.operator.open_id,
        )
        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': 'feishu',  # 这里是FeishuModel，所以回复消息也一定是feishu
            'extra': row2dict(log),
            'input': content,
            'lang': lang,
            'model_id': model_id,
            'models': models,
        }
        message['platform'] = 'feishu'  # 这里是FeishuModel，所以回复消息也一定是feishu
        return message

    async def save_feishu_card_event(self, event, username='', model_id='', models=list(), lang='zh_CN'):
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=username,
            sender_id=event.data.open_id,
            message_id=event.data.open_message_id,
            chat_id=event.data.open_chat_id,
            **event.data,
        )

        # 针对midjourney或者sd进行重新生成的逻辑，尝试将上一次的输入放到input里面传递进去
        input = ''
        parent_log_id = event.data.get('action', {}).get('value', {}).get('logId')
        if parent_log_id:
            input = self.session.query(ChatLog.content).filter(ChatLog.id == parent_log_id).limit(1).scalar() or ''

        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': 'feishu',  # 这里是FeishuModel，所以回复消息也一定是feishu
            'extra': row2dict(log),
            'input': input,
            'lang': lang,
            'model_id': model_id,
            'models': models,
        }
        message['platform'] = 'feishu'  # 这里是FeishuModel，所以回复消息也一定是feishu
        return message

    async def save_feishu_action(self, data, username='', model_id='', models=list(), lang='zh_CN'):
        message = data.message
        content = ''
        platform_content = {}
        if message.msg_type == 'text':
            content = message.body.content.text
        else:
            platform_content = message.body.content  # 包含其他所有类型的源消息
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=username,
            sender_id=message.sender.id,
            message_id=message.message_id,
            chat_id=message.chat_id,
            content=content,
            create_time=message.create_time,
            message_type=message.msg_type,
            platform_content=platform_content,
            parent_id='',
            root_id='',
            shortcut_action=data.action,
            **{k: v for k, v in data.items() if k not in ['messageId', 'action', 'user', 'message']}
        )
        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': 'feishu',  # 这里是FeishuModel，所以回复消息也一定是feishu
            'extra': row2dict(log),
            'input': content,
            'lang': lang,
            'model_id': model_id,
            'models': models,
        }
        message['platform'] = 'feishu'  # 这里是FeishuModel，所以回复消息也一定是feishu
        return message

    async def save_feishu_message(self, event, username='', model_id='', models=list(), lang='zh_CN'):
        content = ''
        platform_content = {}
        if event.message.message_type == 'text':
            # {text: ''}
            content = event.message.content.text
        else:
            platform_content = event.message.content  # 包含其他所有类型的源消息
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=username,
            # 飞书的这个层级有点深
            sender_id=event.sender.sender_id.open_id,
            content=content,
            chat_id=event.message.chat_id,
            chat_type=event.message.chat_type,
            create_time=event.message.create_time,
            message_id=event.message.message_id,
            message_type=event.message.message_type,
            platform_content=platform_content,
            parent_id=event.message.parent_id if 'parent_id' in event.message else '',
            root_id=event.message.root_id if 'root_id' in event.message else '',
            **event.sender.sender_id,
        )
        # action在应用内部自己处理
        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': 'feishu',  # 这里是FeishuModel，所以回复消息也一定是feishu
            'extra': row2dict(log),
            'input': content,
            'lang': lang,
            'model_id': model_id,
            'models': models,
        }
        message['platform'] = 'feishu'  # 这里是FeishuModel，所以回复消息也一定是feishu
        return message

    async def save_wework_message(
        self, ToUserName='', FromUserName='', AgentID='',
        MsgType='', MsgId='', PicUrl='', MediaId='', Content='', CreateTime='',
        model_id='', models=list(),
        platform='wework', lang='zh_CN', **kwargs
    ):
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=FromUserName,
            chat_id=AgentID,  # 当前企微只有和机器人私聊
            message_id=MsgId,
            message_type=MsgType,
            sender_id=FromUserName,
            content=Content,
            platform_content=dict(
                ToUserName=ToUserName,
                PicUrl=PicUrl,
                MediaId=MediaId,
                platform=platform,
            ),
            **kwargs,
        )
        # TODO 处理钉钉mj或者sd重新生成逻辑
        # input = content
        # try:
        #     action, position, jobId, messageId = content.split(':')
        #     # mjapi reroll 不用走到这一步
        #     if action == 'reroll' and position:
        #         # 输出给钉钉的卡片消息包含reroll以及log_id
        #         input = self.session.query(ChatLog.content).filter(ChatLog.id == position).limit(1).scalar() or ''
        # except Exception as e:
        #     logging.error('save wework msg err: {}', e)
        # action在应用自己的逻辑里面处理
        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': platform,  # wework/wxwork
            'extra': row2dict(log),
            'input': Content,
            'lang': lang,
            'model_id': model_id,  # 这里需要查询出模型以及版本
            'models': models,
        }
        return message

    async def save_wework_template_card_event(
        self, ToUserName='', FromUserName='', AgentID='',
        MsgType='', MsgId='', EventKey='',
        model_id='', models=list(),
        platform='wework', lang='zh_CN', **kwargs
    ):
        log = await self._save_message(
            tenant_id=self.bot.tenant_id,
            connectai_user_id=self.bot.user_id,
            bot_instance_id=self.bot.id,
            instance_id=self.app_instance.id,
            username=FromUserName,
            chat_id=AgentID,  # 当前企微只有和机器人私聊
            message_id=MsgId,
            message_type=MsgType,
            sender_id=FromUserName,
            content=EventKey,
            **kwargs,
        )

        # TODO 针对midjourney或者sd进行重新生成的逻辑，尝试将上一次的输入放到input里面传递进去
        # input = ''
        # parent_log_id = event.data.get('action', {}).get('value', {}).get('logId')
        # if parent_log_id:
        #     input = self.session.query(ChatLog.content).filter(ChatLog.id == parent_log_id).limit(1).scalar() or ''

        message = {
            'log_id': log.id,  # 消费者记录日志的时候，尽量带这个id，方便后期查询日志
            'platform': platform,
            'extra': row2dict(log),
            'input': EventKey,
            'lang': lang,
            'model_id': model_id,
            'models': models,
        }
        return message

    async def update_reply_message_id(self, log_id, reply_message_id):
        self.session.begin_nested()
        self.session.query(ChatLog).filter(
            ChatLog.id == log_id,
        ).update(dict(reply_message_id=reply_message_id), synchronize_session=False)
        self.session.commit()


class FeishuModel(MysqlModel):

    def init_by_message_id(self, message_id):
        bot_instance_id = self.session.query(ChatLog.bot_instance_id).filter(
            ChatLog.reply_message_id == message_id,
        ).limit(1).scalar()
        if bot_instance_id:
            self.init_by_bot_id(bot_instance_id)

    def init_by_origin_message_id(self, message_id):
        bot_instance_id = self.session.query(ChatLog.bot_instance_id).filter(
            ChatLog.message_id == message_id,
            ).limit(1).scalar()
        if bot_instance_id:
            self.init_by_bot_id(bot_instance_id)

    def init_by_app_id(self, app_id):
        self.bot = self.session.query(BotInstance).filter(
            BotInstance.app_id == app_id,
            BotInstance.status == 0,
        ).first()
        if self.bot:
            self.client = Lark(self.bot.app_id, self.bot.app_secret)
            self.bot_id = self.bot.id

    def init_by_bot_id(self, bot_id):
        self.bot_id = bot_id
        self.bot = self.session.query(BotInstance).filter(
            BotInstance.id == bot_id,
            BotInstance.status == 0,
        ).first()
        if self.bot:
            self.client = Lark(self.bot.app_id, self.bot.app_secret)
        else:
            # 这里的bot_id实际上是tenant_id
            know = KnowClient(bot_id)
            client = know.get_client_info().get('data', {})
            if client.get('app_id'):
                self.bot = BotInstance(
                    id=bot_id,
                    tenant_id=bot_id,
                    app_id=client.get('app_id'),
                    app_secret=client.get('app_secret'),
                    encript_key=client.get('encript_key'),
                    validation_token=client.get('validation_token'),
                )

    async def get_image_bytes_from_log(self, reply_message_id):
        chat_result = self.session.query(ChatLog.result).filter(
            ChatLog.reply_message_id == reply_message_id,
        ).limit(1).scalar()
        if not chat_result:
            return None
        # 保存图片url的内容，暂不考虑图片二进制数据的内容
        url_contents = [chat_result.get('content', '')]
        if chat_result.get('additional_kwargs'):
            # TODO 统一 additional_kwargs 的 image url
            keys = ['image_url', 'imageUrl']
            additional_kwargs = chat_result['additional_kwargs']
            [url_contents.append(additional_kwargs.get(k, '')) for k in keys]
        image_url = ''
        for content in url_contents:
            if isinstance(content, str) and content.startswith('http'):
                image_url = content
                break
        if image_url:
            try:
                image_bytes = await ChunkedDownloader().download(image_url)
            except Exception as e:
                logging.exception(e)
                image_bytes = await ChunkedDownloader().download(image_url)
            return image_bytes
        return None


    async def handler(self, event):
        if not hasattr(self, 'bot'):
            # 除了url_verification需要返回{'challenge': 'xxx'}其他的都不需要返回字段
            logging.error('找不到对应的机器人 %r', getattr(self, 'bot_id', 'no bot_id'))
            return

        # 按照event_type找出回调事件处理逻辑，再处理
        if not hasattr(self, event.event_handler_name):
            logging.error('找不到事件处理回调 %r', event.event_handler_name)
            return

        return await getattr(self, event.event_handler_name)(event)

    async def url_verification(self, event):
        return {'challenge': event.data.challenge}

    async def im_message_receive_v1(self, event):
        logging.info("debug im.message.receive_v1 %r", event.data)
        # 按照机器人对应的实例，判断权限控制逻辑
        chat_type = event.message.chat_type
        username = await self.client.get_user_name(event.sender.sender_id.open_id)
        if chat_type == 'group':
            try:
                if event.at_all:
                    # @_all的时候不会传mentions数组，所以不能按open_id进行判断
                    user_email = self.session.query(User.email).filter(
                        User.tenant_id == self.bot.tenant_id
                    ).limit(1).scalar()
                    whitelist = ['<EMAIL>', '<EMAIL>']
                    if user_email not in whitelist:
                        logging.error('%r 在群内 @all 机器人不响应', user_email)
                        return
                else:
                    if 'mentions' not in event.message:
                        chat_mode = await self.client.get_chat_mode(event.message.chat_id)
                        if chat_mode == 'topic':
                            if 'root_id' not in event.message:
                                # 话题群创建新话题的时候，当前这一条消息是没有root_id的，只有回复消息才有
                                pass
                            else:
                                logging.error('skip user reply and not mention')
                                # TODO 将消息转发给对应的机器人
                                from modules.messenger.model import MessengerSession, MessengerModel
                                with MessengerSession(self.bot_id, root_message_id=event.message.root_id) as session:
                                    # message_id唯一，直接匹配
                                    if event.message.root_id == session.root_message_id:
                                        if not session.visitor_id:
                                            logging.error('ERROR: %r', session.data)
                                            return
                                        with MessengerModel() as webmodel:
                                            message = Web.create_message_from_event(event.data.event)
                                            session.add_message(message)
                                            webmodel.init_by_bot_id(self.bot_id)
                                            await webmodel.client.pubsub('POST', self.bot_id, session.visitor_id, message)

                                return

                        # elif 'root_id' in event.message and event.message.root_id == event.message.parent_id:
                        #     # 如果是普通群，先发一条消息，再从消息创建话题的时候，这一条消息的parent_id和root_id是一样的，相当于话题的第一条回复
                        #     # TODO 这个时候，判断当前机器人是否是话题机器人？
                        #     # 为了避免话题群其他机器人乱回复，还是使用@
                        #     pass
                        else:
                            logging.error('skip no mentions in group')
                            return
                    if isinstance(self.bot.info, dict) and 'open_id' in self.bot.info:
                        open_id = self.bot.info['open_id']
                    else:
                        info = await self.client.bot_info(self.bot.app_id)
                        # TODO 这里可以尝试保存bot.info
                        open_id = info['open_id']
                    # 'mentions': [{'id': {'open_id': 'ou_934992624e3180283c84f5d9f21dcb81', 'union_id': 'on_5a41bfc71ee91717086319f19a5c7df0', 'user_id': ''}, 'key': '@_user_1', 'name': '文心一言', 'tenant_key': '11282b41a7d4975d'}]
                    mentions = [i['id']['open_id'] for i in event.message.mentions]
                    if open_id not in mentions:
                        logging.error('skip not mention current bot in group')
                        return
            except Exception as e:
                logging.error(e)

            groupname = await self.client.get_chat_name(event.message.chat_id)
        else:
            groupname = ''

        app_instance = BotInstanceModel(self.bot).get_current_app_instance(event.sender.sender_id.open_id)
        if not await UserAccessModel(self.bot, app_instance).check_subscribe():
            return asyncio.gather(self.client.reply_text(event.message.message_id, '订阅已失效，请联系本企业管理员处理'))
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy(chat_type, username, groupname)
        if not model_id:
            # 但获取用户名失败，就提示用户
            if chat_type != 'group' and not username:
                asyncio.gather(self.client.reply_text(event.message.message_id, '应用需要读取通讯录权限'))
            else:
                asyncio.gather(self.client.reply_text(event.message.message_id, '暂无访问权限'))
        send_to_mq = False
        if model_id and event.message and event.message.message_type in ['text', 'post', 'image', 'file', 'audio', 'media']:
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            send_to_mq = True

        if send_to_mq:
            try:
                lang = await self.client.get_user_lang(event.sender.sender_id.open_id)
                message = await MessageModel(self.bot, app_instance).save_feishu_message(event, username=username, model_id=model_id, models=models, lang=lang)
                pikachu = MqSession()
                pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
                pikachu.close()
            except SensitiveError as e:
                asyncio.gather(self.client.reply_text(event.message.message_id, '涉及敏感词，请调整提问'))

        return

    async def im_message_message_read_v1(self, event):
        # logging.info("debug im.message.message_read_v1 %r", event.data)
        pass

    async def im_chat_member_bot_added_v1(self, event):
        logging.info("debug im.chat.member.bot.added_v1 %r", event.data)
        pass

    # {'uuid': 'db0f27359bc95a7b9548387fe76acfa0', 'event': {'app_id': 'cli_a5e73c51307a900e', 'chat_id': 'oc_1051939b27160ca114d0b739bdf7d8e6', 'operator': {'open_id': 'ou_65f2c692de1ae48388845cb56563cb65', 'user_id': '98g28c58'}, 'tenant_key': '17145eff2e0bd75e', 'type': 'p2p_chat_create', 'user': {'name': '柯其谱', 'open_id': 'ou_65f2c692de1ae48388845cb56563cb65', 'user_id': '98g28c58'}}, 'token': 'v-Ohw8k6KwVynNmzXX', 'ts': '1701675180.164949', 'type': 'event_callback'}
    async def p2p_chat_create(self, event):
        logging.info("debug p2p_chat_create %r", event.data)
        # 用户和机器人的会话首次被创建
        open_id = event.data.event.user.open_id
        username = event.data.event.user.name
        chat_type, groupname = '', ''

        app_instance = BotInstanceModel(self.bot).get_current_app_instance(open_id)
        if not await UserAccessModel(self.bot, app_instance).check_subscribe():
            return asyncio.gather(self.client.send_text(open_id, '订阅已失效，请联系本企业管理员处理'))
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy('', username, groupname)
        if not model_id:
            asyncio.gather(self.client.send_text(open_id, '暂无访问权限'))
            return
        if model_id:
            lang = await self.client.get_user_lang(open_id)
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            message = await MessageModel(self.bot, app_instance).save_feishu_chat_create(event, username=username, model_id=model_id, models=models, lang=lang)
            pikachu = MqSession()
            pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
            pikachu.close()


    # {'schema': '2.0', 'header': {'event_id': '08dd5f4222ae77f8f270cd71d5b91f98', 'token': '5cUMPBLVfkOaW1ZjESZgUfCfMSOtWjF1', 'create_time': '1688125648000', 'event_type': 'application.bot.menu_v6', 'tenant_key': '11282b41a7d4975d', 'app_id': 'cli_a4f00f68c9799013'}, 'event': {'event_key': 'sendcard', 'operator': {'operator_id': {'open_id': 'ou_8d395f2f6ca1a5b90d89eff41b0a1a44', 'union_id': 'on_db8be15bcbe904337a07b009f776d4f9', 'user_id': '96g9b9cg'}}, 'timestamp': 1688125648}}
    async def application_bot_menu_v6(self, event):
        # 机器人菜单点击事件回调，属于订阅消息
        # TODO 事件回调也需要保存
        logging.info("debug application_bot_menu_v6 %r", event.data)
        open_id = event.operator.operator_id.open_id
        username = await self.client.get_user_name(open_id)
        chat_type, groupname = '', ''

        app_instance = BotInstanceModel(self.bot).get_current_app_instance(open_id)
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy('', username, groupname)
        if not model_id:
            asyncio.gather(self.client.send_text(open_id, '暂无访问权限'))
            return
        if model_id:
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            message = await MessageModel(self.bot, app_instance).save_feishu_menu_event(event, username=username, model_id=model_id, models=models)
            # pikachu = MqSession()
            # pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
            # pikachu.close()
        if event.data.event.event_key == 'sendcard':
            response = await self.client.send_card(
                event.operator.operator_id.open_id,
                'card title',
                {
                    'tag': 'img', 'img_key': 'img_v2_9b2a46dd-7f05-48b5-a988-377fa11de8dg',
                    'alt': {'tag': 'plain_text', 'content': '图片'}
                },
                actions=[
                    {
                        'tag': 'button',
                        'text': {'tag': 'plain_text', 'content': 'button1'},
                        'value': {'value1': 1}
                    }
                ]
            )
            logging.debug("reply_message_id %r", response)
            reply_message_id = response.data.message_id
            await self.update_reply_message_id(message['log_id'], reply_message_id)
        else:
            await self.client.send_text(
                event.operator.operator_id.open_id,
                'click menu, event_key {}'.format(event.data.event.event_key),
            )

    # {"open_id":"ou_8d395f2f6ca1a5b90d89eff41b0a1a44","user_id":"96g9b9cg","open_message_id":"om_d40e9bffaa78781b23100724cc665eff","open_chat_id":"oc_90dc2bc444e3f4e5ba2f3d98a0d35ca6","tenant_key":"11282b41a7d4975d","token":"c-86365c6c124f1a409f750c8068e25a0d6a834acf","action":{"value":{"value1":1},"tag":"button"}}
    async def action(self, event):
        # 这个是卡片消息回调消息
        # TODO 事件回调也需要保存，否则就找不到记录了
        # 这个事件处理的时候，需要先按照open_message_id找到之前发出去的消息
        logging.info("debug card action %r", event.data)
        # 这里暂时直接回复消息了，后续会使用这个做MJ的按钮点击
        open_id = event.data.open_id
        username = await self.client.get_user_name(open_id)
        chat_type, groupname = '', ''

        app_instance = BotInstanceModel(self.bot).get_current_app_instance(open_id)
        if not await UserAccessModel(self.bot, app_instance).check_subscribe():
            return asyncio.gather(self.client.send_text(open_id, '订阅已失效，请联系本企业管理员处理'))
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy('', username, groupname)
        if not model_id:
            asyncio.gather(self.client.send_text(open_id, '暂无访问权限'))
            return
        if model_id:
            lang = await self.client.get_user_lang(open_id)
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            message = await MessageModel(self.bot, app_instance).save_feishu_card_event(event, username=username, model_id=model_id, models=models, lang=lang)
            pikachu = MqSession()
            pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
            pikachu.close()
        # await self.client.reply_text(
        #     event.data.open_message_id,
        #     'click button, value {}'.format(event.data.action.value),
        # )

    # shortcut action
    async def handler_action(self, data):
        logging.info("debug shortcut %r", data)
        # {'messageId': 'om_0c847e67a4b3705a478d7cbb4f2b8d1e', 'message': {'message_id': 'om_0c847e67a4b3705a478d7cbb4f2b8d1e', 'msg_type': 'image', 'create_time': 1697450296, 'chat_id': '', 'body': {'content': {'image_key': 'img_v2_a3f722f0-734e-4134-8b07-17dd2ef780dg'}}, 'sender': {'id': 'ou_5f2cf38affbdc19768807708efcb66b0', 'id_type': 'open_id', 'username': '柯其谱'}, 'root_sender': {'id': 'ou_5f2cf38affbdc19768807708efcb66b0', 'id_type': 'open_id', 'username': '柯其谱'}, 'chat_type': ''}, 'user': {'session_key': 'bbe131bdc8b6c2ebd041bd98f5d5bdbe', 'errMsg': 'getUserInfo:ok', 'userInfo': {'avatarUrl': 'https://s1-imfile.feishucdn.com/static-resource/v1/v2_d7a68907-3354-470f-a264-6526cc5c999g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp', 'city': '', 'country': '', 'displayName': '柯其谱', 'gender': '', 'i18nDisplayName': {'en_us': 'chips', 'ja_jp': '', 'zh_cn': '柯其谱'}, 'i18nName': {'en_us': 'chips', 'ja_jp': '', 'zh_cn': '柯其谱'}, 'language': 'en_US', 'nickName': '柯其谱'}, 'rawData': '{"nickName":"柯其谱","avatarUrl":"https://s1-imfile.feishucdn.com/static-resource/v1/v2_d7a68907-3354-470f-a264-6526cc5c999g~?image_size=72x72\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp","gender":"","city":"","country":"","language":"en_US","i18nName":{"en_us":"chips","ja_jp":"","zh_cn":"柯其谱"},"displayName":"柯其谱","i18nDisplayName":{"en_us":"chips","ja_jp":"","zh_cn":"柯其谱"}}', 'signature': 'e60f324e712727abce125ebd21cb96a2f00dc395', 'iv': 'e41c1e8f0e26b1b6b05ec06943d3b4ed', 'encryptedData': 'I9ToCHAQTboLYB3+OW11ytVmHf7uMOdB378Sy1CAoOzsq9lGoAC4ITafDtK2s4BjQhxfRTvd5sQzkidaMiyhLmGnu5TrI0xGf7F+AiTkJRbcHIu4yuBCIHF6Y6GvqCoQbbIYvfnJt5+2ObvGOUNA0ePPEk3TrPxLThdD6Kv4H1hzWx84hm1pU7hI8uN9qkb5KsxUgGadEstFnCBYOfDZ8zArZsma3BQw9GsZWolbWj4=', 'decryptedData': {'openId': 'ou_5f2cf38affbdc19768807708efcb66b0', 'unionId': 'ou_5aa8aaa3ff35b36de1770fa5fb4c276e', 'watermark': {'appid': 7287150522220855324, 'timestamp': 1697512256}}}, 'action': 'RemoveBg_shortcut'}
        message_id = data.messageId
        if 'message' in data:
            message = data.message
            msg_type = message.messageType
            content = message.content

            def url_to_image_key(url: str):
                return url.split('image_key=').pop().split('&')[0]
            # 快捷消息的数据内容转为飞书的接收消息内容
            # TODO file type 、、、
            if msg_type == 'image':
                content = {'image_key': url_to_image_key(content.url)}
            elif msg_type == 'post':
                new_content = []
                for x in content.content:
                    new_x = []
                    for y in x.attrs:
                        if y.tag == 'img':
                            y.image_key = url_to_image_key(y.url)
                            del y['url']
                        new_x.append(y)
                    new_content.append(new_x)
                content.content = new_content
            message = {
                'message_id': message_id,
                'msg_type': msg_type,
                'create_time': message.createTime,
                'body': {'content': content},
                'sender': {
                    'id': message.sender.open_id,
                    'id_type': 'open_id',
                }
            }
        else:
            message = await self.client.get_messages_by_id(message_id)
            message['body']['content'] = json.loads(message['body']['content'])
        message = ObjectDict(message)
        data.message = message
        # 获取当前用户信息
        user_name = data.user.userInfo.nickName
        open_id = data.user.decryptedData.openId
        # 原sender
        message.root_sender = message.sender
        # 当前用户作为sender
        message.sender = {
            'id': open_id,
            'id_type': 'open_id',
            'username': user_name
        }
        # 移除群信息
        message.chat_id = ''
        message.chat_type = ''
        logging.info('shortcut action: %r', data)
        app_instance = BotInstanceModel(self.bot).get_current_app_instance(open_id)
        if not await UserAccessModel(self.bot, app_instance).check_subscribe():
            return asyncio.gather(self.client.reply_text(message_id, '订阅已失效，请联系本企业管理员处理'))
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy('', user_name, '')
        if not model_id:
            asyncio.gather(self.client.reply_text(message_id, '暂无访问权限'))
        send_to_mq = False
        if model_id and message.msg_type in ['text', 'post', 'image', 'file', 'audio', 'media', 'interactive']:
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            send_to_mq = True

        if send_to_mq:
            try:
                lang = await self.client.get_user_lang(open_id)
                message = await MessageModel(self.bot, app_instance).save_feishu_action(data, username=user_name, model_id=model_id, models=models, lang=lang)
                pikachu = MqSession()
                pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
                pikachu.close()
            except SensitiveError as e:
                asyncio.gather(self.client.reply_text(message_id, '涉及敏感词，请调整提问'))
        return

    async def update_reply_message_id(self, log_id, reply_message_id):
        self.session.begin_nested()
        self.session.query(ChatLog).filter(
            ChatLog.id == log_id,
        ).update(dict(reply_message_id=reply_message_id), synchronize_session=False)
        self.session.commit()

    async def get_reply_message_id(self, log_id):
        return self.session.query(ChatLog.reply_message_id).filter(
            ChatLog.id == log_id,
        ).limit(1).scalar()


class DingDingModel(MysqlModel):

    def init_by_message_id(self, message_id):
        bot_instance_id = self.session.query(ChatLog.bot_instance_id).filter(
            ChatLog.reply_message_id == message_id,
            ).limit(1).scalar()
        if bot_instance_id:
            self.init_by_bot_id(bot_instance_id)

    def init_by_origin_message_id(self, message_id):
        bot_instance_id = self.session.query(ChatLog.bot_instance_id).filter(
            ChatLog.message_id == message_id,
            ).limit(1).scalar()
        if bot_instance_id:
            self.init_by_bot_id(bot_instance_id)

    def init_by_robot_code(self, robot_code):
        self.bot = self.session.query(BotInstance).filter(
            BotInstance.app_id == robot_code,
            BotInstance.status == 0,
        ).first()
        if self.bot:
            self.client = DingDing(
                self.bot.agent_id,
                self.bot.app_id,  # 这里实际上存的是钉钉的app_key，或者说是robotCode
                self.bot.app_secret,
            )
            self.bot_id = self.bot.id
        else:
            raise DingDingCallbackError('找不到机器人')

    def init_by_bot_id(self, bot_id):
        self.bot_id = bot_id
        self.bot = self.session.query(BotInstance).filter(
            BotInstance.id == bot_id,
            BotInstance.status == 0,
        ).first()
        if self.bot:
            self.client = DingDing(
                self.bot.agent_id,
                self.bot.app_id,  # 这里实际上存的是钉钉的app_key
                self.bot.app_secret,
            )
        else:
            raise DingDingCallbackError('找不到机器人')

    def validate_sign(self, timestamp, sign):
        return self.client.validate_sign(timestamp, sign)

    async def handler(self, message):
        logging.info("debug %r", message)
        if message.conversationType == "2":
            # dingding 群内 @all，机器人不会响应
            pass
        chat_type = 'group' if message.conversationType == "2" else 'p2p'
        groupname = message.conversationTitle if message.conversationType == "2" else ''
        app_instance = BotInstanceModel(self.bot).get_current_app_instance(message.senderId)
        if not await UserAccessModel(self.bot, app_instance).check_subscribe():
            raise DingDingCallbackError('订阅已失效，请联系本企业管理员处理')
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy(chat_type, message.senderNick, groupname)
        if not model_id:
            raise DingDingCallbackError('暂无访问权限')
        if model_id and message.msgtype in ['text', 'richText', 'picture', 'file', 'audio', 'video']:
            # if message.text.content.strip() == 'test':
            #     await self.client.reply_text(message.sessionWebhook, 'reply ' + message.text.content)
            #     return
            # 当前版本，只支持文本输入
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            try:
                message = await MessageModel(self.bot, app_instance).save_dingding_message(**message, model_id=model_id, lang=options.DEFAULT_LOCALE, models=models)
                pikachu = MqSession()
                pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
                pikachu.close()
            except SensitiveError as e:
                # 直接返回，一个消息给钉钉
                raise DingDingCallbackError('涉及敏感词，请调整提问')

        return


class WEWorkModel(MysqlModel):

    platform = 'wework'
    crop_id = ''
    agent_id = ''

    def init_by_bot_id(self, bot_id):
        self.bot_id = bot_id
        self.bot = self.session.query(BotInstance).filter(
            BotInstance.id == bot_id,
            BotInstance.status == 0,
        ).first()

    @property
    def validation_token(self):
        return self.bot.validation_token

    @property
    def encript_key(self):
        return self.bot.encript_key

    def check_signature(self, msg_signature, timestamp, nonce, echostr):
        try:
            crypto = WeChatCrypto(self.validation_token, self.encript_key, self.bot.crop_id)
            echostr = crypto.check_signature(
                msg_signature,
                timestamp,
                nonce,
                echostr
            )
            return echostr
        except InvalidSignatureException:
            return ''

    async def handler(self, body, signature, timestamp, nonce):
        try:
            parsed_body = xmltodict.parse(body)['xml']
            # self.receiveid = parsed_body.get('AgentID') or parsed_body.get('ToUserName')
            self.crop_id = parsed_body.get('ToUserName')
            self.agent_id = parsed_body.get('AgentID')
            # 如果是服务商模式，这里需要使用receiveid，但是前面的check_signature需要使用服务商自己的crop_id
            # 如果是企业自建应用，两个crop_id是相同的
            crypto = WeChatCrypto(self.validation_token, self.encript_key, self.crop_id)
            decrypted_xml = crypto.decrypt_message(
                body,
                signature,
                timestamp,
                nonce
            )
        except (InvalidSignatureException, InvalidCorpIdException):
            raise  # 处理异常情况
        else:
            message = xmltodict.parse(to_text(decrypted_xml))["xml"]
            action_name = 'on_{}'.format(message.get('InfoType') or message.get('Event') or message.get('MsgType', '').lower())
            if hasattr(self, action_name):
                try:
                    return await getattr(self, action_name)(**message)
                except WEWorkCallbackError as e:
                    logging.error(e)
                    self.client.message.send_text(message.get('AgentID'), message.get('FromUserName'), str(e))
            else:
                logging.error('error handler message %r', message)

    async def on_enter_agent(self, ToUserName='', FromUserName='', AgentID='', **kwargs):
        logging.info("enter_agent %r %r %r %r", ToUserName, FromUserName, AgentID, kwargs)

    async def on_text(self, ToUserName='', FromUserName='', AgentID='', MsgId='', Content='', CreateTime='', MsgType='', **kwargs):
        logging.info("text %r", (ToUserName, FromUserName, AgentID, MsgId, Content, CreateTime, kwargs))
        return await self.handler_message(ObjectDict(
            ToUserName=ToUserName,
            FromUserName=FromUserName,
            AgentID=AgentID,
            MsgId=MsgId,
            MsgType=MsgType,
            Content=Content,
            CreateTime=CreateTime,
            **kwargs
        ))

    async def on_image(self, ToUserName='', FromUserName='', AgentID='', MsgId='', PicUrl='', MediaId='', CreateTime='', MsgType='', **kwargs):
        logging.info("image %r", (ToUserName, FromUserName, AgentID, MsgId, PicUrl, MediaId, CreateTime, kwargs))
        return await self.handler_message(ObjectDict(
            ToUserName=ToUserName,
            FromUserName=FromUserName,
            AgentID=AgentID,
            MsgId=MsgId,
            MsgType=MsgType,
            PicUrl=PicUrl,
            MediaId=MediaId,
            CreateTime=CreateTime,
            **kwargs
        ))

    async def on_template_card_event(self, **kwargs):
        return await self.handler_message(ObjectDict(**kwargs))

    async def handler_message(self, message):
        # 当前只有私聊
        chat_type = 'p2p'
        # 这里，可能存在一个企业微信帐号同时使用内建应用和第三方应用的情况，这种情况下FromUserName就不行
        # 只能使用ToUserName+AgentID+FromUserName组合
        fack_openid = '{}:{}:{}'.format(message.ToUserName, message.AgentID, message.FromUserName)
        app_instance = BotInstanceModel(self.bot).get_current_app_instance(fack_openid)
        if not await UserAccessModel(self.bot, app_instance).check_subscribe():
            raise WEWorkCallbackError('订阅已失效，请联系本企业管理员处理')
        model_id, models = await UserAccessModel(self.bot, app_instance).check_policy(chat_type, message.FromUserName, '')
        if not model_id:
            raise WEWorkCallbackError('暂无访问权限')
        if model_id and message.MsgType in ['text', 'event']:
            # 当前版本，只支持文本输入
            # 按照机器人对应的应用判断用户指令，存储ChatLog，放入对应队列
            try:
                if message.MsgType == 'event':
                    message = await MessageModel(self.bot, app_instance).save_wework_template_card_event(**message, model_id=model_id, lang=options.DEFAULT_LOCALE, models=models, platform=self.platform)
                else:
                    message = await MessageModel(self.bot, app_instance).save_wework_message(**message, model_id=model_id, lang=options.DEFAULT_LOCALE, models=models, platform=self.platform)
                pikachu = MqSession()
                pikachu.put(options.QUEUE_APPLICATION, json.dumps(message))
                pikachu.close()
            except SensitiveError as e:
                # 直接返回，一个消息给企微
                raise WEWorkCallbackError('涉及敏感词，请调整提问')

        return

    @property
    def client(self):
        if not hasattr(self, '_client'):
            session_interface = RedisStorage(
                redis_cli(),
                prefix="wechatpy"
            )
            wechat_client = WeChatClient(
                self.bot.crop_id,
                self.bot.app_secret,
                session=session_interface
            )
            proxies = {
                "http": f"http://{options.WEWORK_PROXY_HOST}:{options.WEWORK_PROXY_PORT}",
                "https": f"http://{options.WEWORK_PROXY_HOST}:{options.WEWORK_PROXY_PORT}",
            }
            # 使用代理，配置企业微信自建应用可信IP
            wechat_client._http.proxies.update(proxies)
            setattr(self, '_client', wechat_client)
        return getattr(self, '_client')


class WXWorkModel(WEWorkModel):

    platform = 'wxwork'
    """
    1. 前面的处理逻辑基本是一致的，只是platform有区别
    """
    def init_by_bot_id(self, bot_instance_id):
        self._bot = self.session.query(BotInstance).filter(
            BotInstance.id == bot_instance_id,
            BotInstance.status == 0,
        ).first()

    def init_by_instance_id(self, instance_id):
        self.instance_id = instance_id
        self.application = self.session.query(ApplicationWithWXSuite).join(
            AppInstance,
            AppInstance.application_id == ApplicationWithWXSuite.id,
        ).filter(
            AppInstance.id == instance_id,
            # AppInstance.status == 0,
            ApplicationWithWXSuite.status == 0,
        ).first()

    @property
    def bot(self):
        if not hasattr(self, '_bot'):
            bot = self.session.query(BotInstance).filter(
                # 企业微信使用两个值来确定是哪一个bot_instance
                BotInstance.crop_id == self.crop_id,
                BotInstance.agent_id == self.agent_id,
                BotInstance.status == 0,
            ).order_by(
                BotInstance.modified.desc(),
            ).first()
            setattr(self, '_bot', bot)
        # self._bot可能为None
        return self._bot

    @property
    def validation_token(self):
        return options.WXWORK_TOKEN

    @property
    def encript_key(self):
        return options.WXWORK_AES_KEY

    @property
    def bot_id(self):
        return self.bot.id

    @property
    def suite_ticket(self):
        return redis_cli().get('suite_ticket:{}'.format(self.crop_id))

    @property
    def service_client(self):
        if not hasattr(self, '_service_client'):
            session_interface = RedisStorage(
                redis_cli(),
                prefix="wechatpy"
            )
            service_client = WeChatServiceClient(
                self.bot.crop_id,
                self.application.wx_suite_id,
                self.application.wx_suite_secret,
                self.application.wx_suite_ticket,
                session=session_interface,
            )
            setattr(self, '_service_client', service_client)
        return getattr(self, '_service_client')

    @stalecache()
    def crop_access_token(self, auth_corpid, permanent_code):
        # TODO <suite_access_token+crop_id+permanent_code> --> access_token
        result = self.service_client.post(url='service/get_corp_token', json=dict(
            auth_corpid=auth_corpid,
            permanent_code=permanent_code,
        ))
        if "access_token" not in result:
            raise Exception('error get crop_access_token')
        return result["access_token"]

    @property
    def client(self):
        # https://developer.work.weixin.qq.com/document/path/90605
        # 1. 通过botinstance.crop_id以及botinstance.permanent_code还有suite_access_token获取企业access_token
        # 2. WeChatClient(crop_id, permanent_code as app_secret, access_token=access_token, session=session_interface, )
        if not hasattr(self, '_client'):
            session_interface = RedisStorage(
                redis_cli(),
                prefix="wechatpy"
            )
            wechat_client = WeChatClient(
                self.bot.crop_id,
                # 这里app_secret实际存的是permanent_code， 包括后面获取crop_access_token也是使用permanent_code
                self.bot.app_secret,
                # 这里只要直接传递access_token，会自动保存起来，请求的时候就会使用这个token
                access_token=self.crop_access_token(self.bot.crop_id, self.bot.app_secret),
                session=session_interface
            )
            setattr(self, '_client', wechat_client)
        return getattr(self, '_client')

    async def on_suite_ticket(self, SuiteId='', SuiteTicket='', **kwargs):
        # save suite_ticket
        # SuiteId对应一个application
        self.session.begin_nested()
        self.session.query(ApplicationWithWXSuite).filter(
            ApplicationWithWXSuite.wx_suite_id == SuiteId,
        ).update(dict(wx_suite_ticket=SuiteTicket), synchronize_session=False)
        self.session.commit()
        redis_cli().set('suite_ticket:{}'.format(SuiteId), SuiteTicket)

    async def on_create_auth(self, SuiteId='', AuthCode='', **kwargs):
        logging.info("create_auth %r %r %r", SuiteId, AuthCode, kwargs)
        self.application = self.session.query(ApplicationWithWXSuite).filter(
            ApplicationWithWXSuite.wx_suite_id == SuiteId,
            ApplicationWithWXSuite.status == 0,
        ).order_by(
            ApplicationWithWXSuite.modified.desc()
        ).first()
        result = self.service_client.auth.get_permanent_code(AuthCode)
        logging.info("debug get_permanent_code %r", result)
        if 'permanent_code' in result:
            # TODO save botinstance.app_secret = permanent_code
            logging.info("debug save_permanent_code %r %r", self.bot.id, result)
            self.session.begin_nested()
            self.session.query(BotInstance).filter(
                BotInstance.id == self.bot.id,
            ).update(dict(
                agent_id=result['auth_info']['agent'][0]['agentid'],
                app_secret=result['permanent_code'],
                crop_id=result['auth_corp_info']['corpid'],
            ), synchronize_session=False)
            self.session.commit()

    async def on_change_app_admin(self, ToUserName='', AgentID='', **kwargs):
        logging.info("change_app_admin %r %r %r", ToUserName, AgentID, kwargs)

    async def get_auth_url(self):
        # 1. https://open.work.weixin.qq.com/3rdapp/install?suite_id=SUITE_ID&pre_auth_code=PRE_AUTH_CODE&redirect_uri=REDIRECT_URI&state=STATE
        pre_auth_code = self.service_client.auth.get_pre_auth_code().get('pre_auth_code')
        # 配置一下当前这一次授权的信息
        self.service_client.auth.set_session_info(
            pre_auth_code,
            # app_id=self.application.wx_suite_id,  # app_id,
            auth_type=1 if options.DEBUG else 0,
        )
        return '{}/3rdapp/install?suite_id={}&pre_auth_code={}&redirect_uri={}&state={}'.format(
            'https://open.work.weixin.qq.com',
            self.application.wx_suite_id,
            pre_auth_code,
            quote(f'{options.DOMAIN}/api/wxwork/auth/{self.instance_id}/{self.bot.id}'),
            self.bot_id,
        )

    async def auth(self, auth_code, expires_in, state):
        logging.info("debug auth success %r", (auth_code, expires_in, state))
        await self.on_create_auth(self.application.wx_suite_id, auth_code, expires_in=expires_in, state=state)

