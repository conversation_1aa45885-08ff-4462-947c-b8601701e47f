import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.952 4.079A4 4 0 0 1 18.684 3H25.5A3.5 3.5 0 0 1 29 6.5v6.757a4 4 0 0 1-.888 2.513a9 9 0 0 0-12.336 12.352a4.25 4.25 0 0 1-5.781-.213l-6.326-6.326a4.25 4.25 0 0 1 .101-6.109L15.952 4.08zM22.5 12a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zM16 23.5a7.5 7.5 0 1 1 15 0a7.5 7.5 0 0 1-15 0zm6.477 4.432a1.023 1.023 0 1 0 2.046 0a1.023 1.023 0 0 0-2.046 0zm-1.704-6.477a.682.682 0 1 0 1.363 0a1.364 1.364 0 1 1 2.728 0c0 .505-.113.79-.5 1.224l-.158.17l-.36.367c-.739.775-1.028 1.333-1.028 2.33a.682.682 0 1 0 1.364 0c0-.506.112-.79.499-1.224l.158-.17l.36-.369c.74-.774 1.028-1.332 1.028-2.329a2.727 2.727 0 0 0-5.454 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagQuestionMark32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
