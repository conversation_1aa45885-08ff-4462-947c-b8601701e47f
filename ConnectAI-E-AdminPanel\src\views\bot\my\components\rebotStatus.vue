<template>
  <div class="flex justify-center items-center cursor-pointer">
    <n-tag v-if="ifLoading" round :bordered="false" class="cursor-pointer">
      {{ $t('message.my.czz') }}
      <template #icon>
        <n-spin size="small" class="scale-60" />
      </template>
    </n-tag>
    <div v-else>
      <n-tag v-if="status === 2" round :bordered="false" type="success" class="cursor-pointer">
        {{ $t('message.my.yxz') }}
        <template #icon>
          <n-icon :component="CheckmarkCircle" />
        </template>
      </n-tag>
      <n-tag v-else-if="status === 1" round :bordered="false" class="cursor-pointer">
        {{ $t('message.my.dpz') }}
        <template #icon>
          <n-icon :component="StopCircleSharp" />
        </template>
      </n-tag>
      <n-tag v-else round :bordered="false" class="cursor-pointer">
        {{ $t('message.my.yty') }}
        <template #icon>
          <n-icon :component="StopCircleSharp" />
        </template>
      </n-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckmarkCircle, StopCircleSharp } from '@vicons/ionicons5';
// 3 运行中 2 已停止 1 待配置
defineProps<{
  status: number;
  ifLoading: boolean;
}>();
</script>

<style scoped></style>
