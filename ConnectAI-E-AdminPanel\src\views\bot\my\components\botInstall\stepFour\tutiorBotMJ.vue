<template>
  <div>
    <div class="block p-8 text-white rounded-lg bg-blue-500 min-h-520px">
      <h2 class="mb-1 text-2xl font-semibold">机器人创建指南</h2>
      <a href="https://open.feishu.cn/app" target="_blank">
        <p class="mb-4 hover:underline text-xs font-bold">点击前往飞书开发者后台</p>
      </a>
      <!-- List -->
      <div class="h-2" />
      <ul role="list" class="space-y-4 text-left">
        <li v-for="(item, index) in steps" :key="index" class="flex items-center space-x-2">
          <!--          <icon-akar-icons-light-bulb v-if="item.step === step" class="text-yellow-300 text-xl" />-->
          <icon-akar-icons-check v-if="item.step <= step" class="text-green-300 text-xl" />
          <icon-akar-icons-more-horizontal v-if="item.step > step" class="text-red-300 text-xl" />
          <span>{{ item.content }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineProps<{ step: number }>();

interface IStep {
  content: string;
  number: number;
  step: number;
  active?: boolean;
}

const steps = computed<IStep[]>(() => [
  {
    content: '点击创建应用, 选择企业自建应用',
    number: 1,
    step: 1
  },
  {
    content: '添加应用能力-机器人',
    number: 2,
    step: 1
  },
  {
    content: '打开权限管理-勾选消息与群组内全部权限',
    number: 3,
    step: 1
  },
  {
    content: '打开权限管理-允许上传图片资源',
    number: 4,
    step: 1
  },
  {
    content: '打开权限管理-以应用身份读取通讯录',
    number: 5,
    step: 1
  },

  {
    content: '前往基础信息-复制应用凭证',
    number: 6,
    step: 2
  },
  {
    content: '前往事件订阅-复制加密秘钥',
    number: 7,
    step: 2
  },
  {
    content: '事件订阅-配置请求地址',
    number: 8,
    step: 3
  },
  {
    content: '机器人-配置消息卡片请求地址',
    number: 9,
    step: 3
  },
  {
    content: '添加事件订阅-消息与群组-消息已读',
    number: 10,
    step: 3
  },
  {
    content: '添加事件订阅-消息与群组-接收消息',
    number: 11,
    step: 3
  }
]);
</script>

<style scoped></style>
