'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M5.996 13.657A5.47 5.47 0 0 1 5.022 11h-2.22a6.02 6.02 0 0 0 3.194 2.657zM2.34 10h2.681c.013-.136.03-.27.052-.403A17.083 17.083 0 0 1 5.117 6H2.34A5.99 5.99 0 0 0 2 8c0 .701.12 1.374.341 2zm3.673-2.683c.363-.51.81-.956 1.323-1.317H6.125c-.054.42-.092.86-.11 1.317zm3.287-3.734c.159.414.297.89.407 1.417H6.292c.11-.527.248-1.003.407-1.417c.213-.554.455-.969.698-1.236C7.64 2.08 7.844 2 8 2c.156 0 .36.08.603.347c.243.267.485.682.698 1.236zM10.728 5h2.47a6.016 6.016 0 0 0-3.421-2.733c.17.285.323.608.457.957c.201.522.368 1.12.494 1.776zM2.803 5h2.47a10.53 10.53 0 0 1 .493-1.776c.134-.349.286-.672.457-.957A6.016 6.016 0 0 0 2.803 5zM15 10.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zM10.5 8a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5H12a.5.5 0 0 0 0-1h-1V8.5a.5.5 0 0 0-.5-.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'GlobeClock16Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
