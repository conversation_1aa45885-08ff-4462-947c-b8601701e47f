import json
import httpx
import logging
import sys
import warnings
from typing import (
    Any,
    Dict,
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)
from tornado.options import options
from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage
from langchain.schema import HumanMessage, AIMessage, SystemMessage


logger = logging.getLogger(__name__)


class GeminiClient(object):
    def build_query(
        self,
        api_base='',
        api_key='',
        model='',
        stream=False,
        **kwargs
    ):
        headers = {
            'Content-Type': 'application/json',
            'api-key': api_key
        }
        if 'https://gemini' != api_base[:14]:
            headers['api-base'] = api_base
            headers['api-key'] = api_key
            from core.api_base import NewApiBase
            api_base = NewApiBase('Gemini').url
        api = kwargs.get('api', 'streamGenerateContent' if stream else 'generateContent')
        body = json.dumps(kwargs)
        return '{}/v1/models/{}:{}'.format(api_base, model, api), body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                yield line

    def create(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(**kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class GeminiChat(SimpleChatModel):
    client: Any  #: :meta private:
    model_name: str = "gemini-pro"
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 5
    streaming: bool = False
    temperature: float = 0.9       # 默认0.9，范围 (0, 1.0]，不能为0
    top_p: float = 0.8             # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    top_k: float = 10
    max_tokens = 800
    stop_sequences = []

    def _llm_type(self) -> str:
        return "gemini_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        configs = {
            'stopSequences': self.stop_sequences,
            'temperature': self.temperature,
            'maxOutputTokens': self.max_tokens,
            'topP': self.top_p,
            'topK': self.top_k,
        }
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'model': self.model_name,
            'generationConfig': configs,
        }

        message_dicts = []
        for i, m in enumerate(messages[:]):
            if isinstance(m, HumanMessage) or isinstance(m, SystemMessage):
                m.content = [{'text': m.content}] if isinstance(m.content, str) else m.content
                if i + 1 == len(messages):
                    message_dicts.append({'role': 'user', 'parts': m.content})
                    continue
                m2 = messages[i + 1]
                if isinstance(m2, AIMessage) and m2.content:
                    message_dicts.append({'role': 'user', 'parts': m.content})
                    message_dicts.append({'role': 'model', 'parts': [{'text': m2.content}] if isinstance(m2.content, str) else m2.content})

        self.client = GeminiClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, contents=message_dicts, **params):
                if '''"text":''' in stream_resp:
                    token = json.loads('{' + stream_resp + '}')['text']
                    response += token
                    if run_manager:
                        run_manager.on_llm_new_token(token)
            return response
        else:
            full_response = completion_with_retry(self, contents=message_dicts, **params)
            return full_response["candidates"][0]['content']['parts'][0]['text']


if __name__ == "__main__":
    import asyncio
    async def main():
        from langchain.schema import HumanMessage

        api_key = ''
        api_base = ''

        chat = GeminiChat(
            api_base=api_base,
            api_key=api_key,
            # streaming=True,
        )
        # messages = [HumanMessage(content='你是谁')]
        messages = [HumanMessage(content='推荐成都旅游线路')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


