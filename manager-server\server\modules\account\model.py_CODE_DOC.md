# 代码文档 - server/modules/account/model.py

## 文件作用
账户管理模块的数据模型层，提供用户认证、租户管理、提示词管理、敏感词检测、聊天日志等核心业务逻辑。

## 逐行代码解释

### 导入模块 (1-49行)
```python
import asyncio                           # 异步编程支持
import httpx                             # HTTP客户端
import logging                           # 日志记录
import hashlib                           # 哈希算法
import random                            # 随机数生成
import math                              # 数学函数
import json                              # JSON处理
import pandas as pd                      # 数据分析库
from io import BytesIO                   # 字节流处理
from datetime import datetime, timedelta  # 日期时间处理
from dateutil.relativedelta import relativedelta  # 相对日期计算
from tornado.httpclient import AsyncHTTPClient, HTTPRequest  # Tornado HTTP客户端
from tornado.options import options      # Tornado配置选项
from urllib.parse import quote           # URL编码
from sqlalchemy import alias, and_, or_, select, func, distinct, text  # SQLAlchemy查询
from sqlalchemy.orm import (             # SQLAlchemy ORM
    relationship, joinedload, foreign, column_property, aliased,
)
from wechatpayv3 import WeChatPay, WeChatPayType  # 微信支付
from core.api_base import NewApiBase     # API基础类
from core.redisdb import redis_cli, stalecache, rate_limit  # Redis和装饰器
from core.tencent import SMSProvider, EmailProvider  # 腾讯云服务
from core.feishu import FeishuTextMessage, Lark  # 飞书集成
from core.base_model import MysqlModel   # MySQL模型基类
from core.utils import row2dict, format_time, gen_verification_code, hash_password, check_password, _  # 工具函数
from core.exception import NotFound, PermissionDenied, InternalError, Duplicate, ParametersError  # 异常类
from core.schema import (                # 数据模型
    ObjID, Tenant, TenantAdmin, TenantWithKey, User, Account, AdminAccount,
    PromptCategory, Prompt, Sensitive, ChatLog, AppInstance, BotInstance,
    Resource, TenantResource, Application, Wallet, Product, TenantProduct,
    TenantSeat, ApplicationCategory,
)
from settings.constant import PROXY_LIST  # 代理服务器列表
```

### ChatLogWithSensitive扩展模型 (51-65行)
```python
class ChatLogWithSensitive(ChatLog):
    """扩展的聊天日志模型，包含应用名称和敏感词信息"""
    
    # 通过子查询获取应用名称
    app_name = column_property(
        select(AppInstance.name).where(and_(
            AppInstance.id == ChatLog.instance_id,  # 关联应用实例ID
        )).limit(1)
    )

    # 关联敏感词表
    sensitive = relationship(
        Sensitive,
        primaryjoin=and_(
            foreign(ChatLog.sensitive_id) == Sensitive.id,  # 外键关联
        ),
    )
```

### AccountModel主要业务模型 (67-80+行)
```python
class AccountModel(MysqlModel):
    """账户管理模型 - 继承自MysqlModel"""

    def random_code(self, size=4):
        """生成随机验证码"""
        return ''.join([str(random.randint(0, 9)) for i in range(size)])

    @rate_limit(rate=1, interval=60)  # 限流：每分钟只能发送一次
    async def send_code(self, email, telephone, expire=300, key=''):
        """发送登录验证码"""
        code = self.random_code(4)  # 生成4位验证码
        key = 'login:{}:{}'.format(email, telephone)  # Redis键名
        
        # 将验证码存储到Redis，设置过期时间
        redis_cli().pipeline().set(key, code).expire(key, expire).execute()
        
        if telephone:
            # 发送短信验证码
            p = SMSProvider(
                options.SMS_SECRET_ID,  # 腾讯云短信密钥ID
                options.SMS_SECRET_KEY, # 腾讯云短信密钥
                options.SMS_REGION,     # 短信服务区域
            )
            await p.send_sms(
                telephone,              # 手机号
                options.SMS_TEMPLATE_ID,# 短信模板ID
                [code, str(expire // 60)]  # 模板参数：验证码和过期分钟数
            )
        
        if email:
            # 发送邮件验证码
            e = EmailProvider(
                options.EMAIL_SECRET_ID,
                options.EMAIL_SECRET_KEY,
                options.EMAIL_REGION,
            )
            await e.send_email(
                email,                  # 收件人邮箱
                "ConnectAI 验证码",      # 邮件主题
                f"您的验证码是：{code}，{expire // 60}分钟内有效。"  # 邮件内容
            )
```

## 核心功能模块

### 1. 用户认证系统
- **验证码发送**: 支持短信和邮件两种方式
- **限流保护**: 防止验证码被恶意刷取
- **多种登录方式**: 邮箱+密码、手机+验证码、邮箱+验证码+密码

### 2. 租户管理系统
- **租户信息管理**: 创建、更新、查询租户信息
- **权限控制**: 租户级别的权限管理
- **座席管理**: 租户座席数量控制

### 3. 提示词管理系统
- **分类管理**: 提示词分类的增删改查
- **提示词管理**: 提示词的完整生命周期管理
- **多语言支持**: 支持不同语言的提示词

### 4. 敏感词检测系统
- **敏感词管理**: 敏感词库的维护
- **内容过滤**: 聊天内容的敏感词检测
- **日志记录**: 敏感词触发的日志记录

### 5. 聊天日志系统
- **日志记录**: 完整的聊天记录存储
- **统计分析**: 聊天数据的统计和分析
- **关联查询**: 与应用、用户、敏感词的关联查询

## 技术特点

### 数据库设计
- **关联查询**: 使用SQLAlchemy的relationship和column_property
- **性能优化**: 通过joinedload优化关联查询
- **事务管理**: 支持数据库事务的完整性

### 缓存策略
- **Redis缓存**: 验证码、会话等临时数据缓存
- **装饰器缓存**: 使用stalecache装饰器实现智能缓存
- **限流控制**: 使用rate_limit装饰器防止接口滥用

### 第三方集成
- **腾讯云服务**: 短信和邮件服务集成
- **飞书集成**: 支持飞书消息推送
- **微信支付**: 集成微信支付功能

### 安全特性
- **密码加密**: 使用bcrypt进行密码哈希
- **验证码保护**: 验证码有效期和使用次数限制
- **权限验证**: 多层次的权限验证机制

## 业务流程

### 用户注册/登录流程
1. **发送验证码**: 用户输入邮箱/手机号，系统发送验证码
2. **验证码校验**: 用户输入验证码，系统验证有效性
3. **账户创建/登录**: 验证通过后创建账户或完成登录
4. **会话管理**: 生成会话ID，维护用户登录状态

### 租户管理流程
1. **租户创建**: 创建新的租户账户
2. **权限分配**: 分配租户管理员权限
3. **资源配置**: 配置租户可用的资源和座席
4. **使用监控**: 监控租户的资源使用情况

## 扩展性设计
- **模块化架构**: 各功能模块相对独立，易于扩展
- **配置驱动**: 通过配置文件控制功能开关
- **插件机制**: 支持第三方服务的插件式集成
- **多租户支持**: 天然支持多租户架构
