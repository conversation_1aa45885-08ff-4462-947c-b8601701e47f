import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.976 1.352a1.4 1.4 0 0 0-1.936.042L1.424 8.011a1.4 1.4 0 0 0 0 1.98l.926.925c.347.348.846.264 1.14.068a1.1 1.1 0 0 1 1.525 1.525c-.195.294-.279.794.068 1.141l.905.904a1.4 1.4 0 0 0 2.01-.03l6.598-7.012a1.4 1.4 0 0 0-.073-1.991l-.886-.813a.842.842 0 0 0-.727-.197c-.227.04-.442.16-.598.33a1.1 1.1 0 1 1-1.622-1.487c.157-.168.258-.393.279-.623a.841.841 0 0 0-.26-.706l-.733-.673z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
