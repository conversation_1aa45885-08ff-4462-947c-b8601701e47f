class ChatTool(CTool):
    name: str = 'feishu_topic_summary'
    description: str = 'feishu topic summary'

    summary_prompt: str ="""You are a good topic summary assistant. The server locale is "{lang}". You can communicate in any language, and good at inferring what language you should use based on Topic:
{root_message_content}

Here are some user communication information:
{chat_message_content}

Please make a summary based on the above content

Please Answer the question:
{question}
"""

    root_summary_prompt: str ="""You are a good topic summary assistant. The server locale is "{lang}". You can communicate in any language, and good at inferring what language you should use based on Topic:
{root_message_content}

Helpful Answer:
"""

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageDiv(self.result, tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                    )
                )

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if len(self.result) - self.send_length < 25:
                    logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                    return
                # 至少有新的25个字符开始发送，发送之后，重新赋值
                self.send_length = len(self.result)
                send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageDiv(self.result, tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                    )
                )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # 同时添加当前用户输入的问题，以及ai回复问题
                content = response.generations[0][0].text
                # session.add_message({'role': 'human', 'content': input.strip()})
                # session.add_message({'role': 'ai', 'content': content})

                time.sleep(1)
                # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageDiv(content, tag='lark_md'),
                        FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    ),
                )

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageDiv(str(error), tag='lark_md'),
                    )
                )

        model.callbacks = [OpenAICallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])

        if model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )

        # 群总结，获取群消息组合prompt模板
        try:
            loop = asyncio.get_event_loop()
        except Exception as e:
            logging.error(e)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        def parse_message(message):
            # remove mentions
            def remove_mentions(content):
                for mention in message.get('mentions', []):
                    content = content.replace(mention['key'] + ' ', '')
                return content.replace('@_all ', '')

            if not message['deleted'] and message['msg_type'] == 'text':
                content = json.loads(message['body']['content'])
                return message['message_id'], message.get('root_id'), remove_mentions(content['text'])
            elif not message['deleted'] and message['msg_type'] == 'post':
                content = []
                for line in json.loads(message['body']['content']).get('content', []):
                    for tag in line:
                        if tag.get('tag') == 'text':
                            content.append(tag.get('text'))
                return message['message_id'], message.get('root_id'), remove_mentions('\n'.join(content))
            elif not message['deleted'] and message['msg_type'] == 'interactive':
                # 机器人卡片消息是这个类型
                content = []
                for line in json.loads(message['body']['content']).get('elements', []):
                    for tag in line:
                        if tag.get('text'):
                            content.append(tag.get('text'))
                return message['message_id'], message.get('root_id'), remove_mentions('\n'.join(content))
            return message['message_id'], message.get('root_id'), ' '  # 空字符串

        if 'root_id' in data.extra.extra and data.extra.extra.root_id:
            chat_messages = loop.run_until_complete(functools.partial(client.get_recent_messages_by_chat, data.extra.chat_id, 100)())
            logging.debug('chat messages %r', chat_messages)
            root_message = loop.run_until_complete(functools.partial(client.get_messages_by_id, data.extra.extra.root_id)())
            logging.debug('chat messages %r', root_message)

            root_message_id, r_, root_message_content = parse_message(root_message)
            logging.debug('root_message %r', (root_message_id, root_message_content))
            chat_message_content = [parse_message(message) for message in reversed(chat_messages)]
            chat_message_content = '\n'.join([content for message_id, root_id, content in chat_message_content if message_id != data.extra.extra.root_id and root_id == data.extra.extra.root_id and content.strip()][-10:])
            logging.debug('chat_message_content %r', chat_message_content)

            message_content = self.summary_prompt.format(
                lang=data.lang,
                root_message_content=root_message_content,
                chat_message_content=chat_message_content,
                question=input,
            )
        else:
            # 话题群创建话题，本身就是root_message
            root_message_id, r_, root_message_content = parse_message(dict(
                msg_type=data.extra.message_type,
                message_id=data.extra.message_id,
                deleted=False,
                body=dict(
                    content=json.dumps(dict(text=data.extra.content) if data.extra.message_type == 'text' else data.extra.extra.platform_content),
                )
            ))
            message_content = self.root_summary_prompt.format(
                lang=data.lang,
                root_message_content=root_message_content,
            )

        messages = [SystemMessage(content=message_content)]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class FeishuCommand(CommandTool):

    next_tool_name: str = 'feishu_topic_summary'
    name: str = 'feishu_topic_summary_command'
    description: str = 'feishu topic summary command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<发散模式> 或 /ai_mode 即可选择发散模式'
    ]

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def ai_mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in ai_mode],
            placeholder=_('选择模式'),
            initial_option=str(float(session.temperature)),
            value={'command': 'ai_mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改发散模式吗？'),
                text=_('选择内置模式，可以让AI更好的理解您的需求。'),
            )
        )

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        tech = 'ChatGpt'
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是小飞机，一款基于%(tech)s技术的智能聊天机器人！**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉', tech=tech),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **发散模式选择**\n文本回复 *发散模式* 或 */ai_mode*'),
                    tag='lark_md',
                    extra=self.ai_mode_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            return 'ai_mode',
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择模型：**%(model)s**', model=m.get(model_name, model_name)),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_ai_mode(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模式，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 发散模式选择'), template='blue'),
                )
            )
        '''已选择发散模式:平衡'''
        m = {str(value): content for value, content in ai_mode}
        session['temperature'] = float(mode_name)
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('已选择发散模式：**%(mode)s**', mode=m.get(mode_name, mode_name)),
                    tag="lark_md",
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class OpenaiAgent(CAgent):

    class TopicSummaryAppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'TopicSummary'
        title: str = '飞书话题AI助手'
        title_en: str = 'Topic Summary Assistant'
        description: str = '🎯  自动总结话题内容，帮助快速理解了解话题内容概要'
        description_en: str = '🎯  Summarize the topic content automatically, helping to quickly understand and grasp the gist of the topic.'
        problem: str = '可以自动总结飞书的长话题内容吗'
        problem_en: str = 'Can lark automatically summarize long topic content?'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/Bti9dGDpIopm6vxagWHcC7REnme?from=from_copylink'
        manual_en: str = 'https://connect-ai.feishu.cn/docx/U7rYdVhNxop2MJxSQIXcQDulnyg?from=from_copylink'
        icon: str = 'https://pic1.forkway.cn/cdn/202308281730117.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/202308281730117.png?imageMogr2/thumbnail/720x'
        sorted: int = 107
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['Claude', 'OpenAI', 'Azure']
        )]
        support_bots: List[str] = ['feishu']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnG0oOmymHnjRE5RPqjaPAjf'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/EfsewjttLi7jw4ktsuaccwoZnxd'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='feishu_topic_summary_command', tool_input=kwargs, log="")
        return AgentAction(tool='feishu_topic_summary', tool_input=kwargs, log="")


