import { computed } from 'vue';
import { REGEXP_PHONE, REGEXP_EMAIL } from '@/config';
import { fetchSmsCode } from '@/service';
import { useLoading } from '../common';
import useCountDown from './use-count-down';
import { t, getLocale } from '@/locales';

export default function useSmsCode(isEmail?: boolean) {
  const { loading, startLoading, endLoading } = useLoading();
  const { counts, start, isCounting } = useCountDown(60);

  const initLabel = t('message.system.get');
  const countingLabel = (second: number) => getLocale().value == 'zh_CN' ? `${second + t('message.system.lasttime')}` : `${t('message.system.lasttime') + second + 's'}`;
  const label = computed(() => {
    let text = initLabel;
    if (loading.value) {
      text = '';
    }
    if (isCounting.value) {
      text = countingLabel(counts.value);
    }
    return text;
  });

  /** 判断手机号码格式是否正确 */
  function isPhoneValid(phone: string) {
    let valid = true;
    console.log('phone', phone);
    if (phone.trim() === '') {
      window.$message?.error(t('message.system.nophone'));
      valid = false;
    } else if (!REGEXP_PHONE.test(phone)) {
      window.$message?.error(t('message.system.phoneerror'));
      valid = false;
    }
    return valid;
  }

  /** 判断邮箱格式是否正确 */
  function isEmailValid(email: string) {
    let valid = true;
    if (email.trim() === '') {
      window.$message?.error(t('message.system.noemail'));
      valid = false;
    } else if (!REGEXP_EMAIL.test(email)) {
      window.$message?.error(t('message.system.emailerror'));
      valid = false;
    }
    return valid;
  }

  /**
   * 获取短信验证码
   * @param phone - 手机号
   * 增加兼容email模式
   */
  async function getSmsCode(phone: string) {
    const valid = isEmail ? isEmailValid(phone) : isPhoneValid(phone);
    if (!valid || loading.value) return;

    startLoading();
    const { data } = await fetchSmsCode(isEmail ? '' : phone, isEmail ? phone : '');
    if (data) {
      window.$message?.success(t('message.system.codesuccess'));
      start();
    }
    endLoading();
  }

  return {
    label,
    start,
    isCounting,
    getSmsCode,
    loading
  };
}
