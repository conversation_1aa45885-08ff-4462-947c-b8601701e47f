'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M12 8V2H6a2 2 0 0 0-2 2v7.498A6.479 6.479 0 0 1 6.5 11a6.48 6.48 0 0 1 2.504.5h6.746a.75.75 0 0 1 0 1.5h-4.56c.361.376.678.796.94 1.25h3.62a.75.75 0 0 1 0 1.5h-2.988c.112.402.186.82.22 1.25h2.768a.75.75 0 0 1 0 1.5h-2.826A6.48 6.48 0 0 1 11.19 22H18a2 2 0 0 0 2-2V10h-6a2 2 0 0 1-2-2zm1.5 0V2.5l6 6H14a.5.5 0 0 1-.5-.5zM12 17.5a5.5 5.5 0 1 0-11 0a5.5 5.5 0 0 0 11 0zm-5.5 0h2a.5.5 0 1 1 0 1H6a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 1 0v2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DocumentTextClock24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
