version: '2'
services:
  manager:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./dist:/dist
      - ./etc/web_config.conf:/etc/web_config.conf
    ports:
      - "3000"
    environment:
      - VIRTUAL_HOST=manager.connect.ai

  admin:
    restart: always
    extends: manager
    volumes:
      - ./etc/admin_config.conf:/etc/web_config.conf
    environment:
      - VIRTUAL_HOST=admin.connect.ai

  appconsumer:
    restart: always
    image: connectai-manager
    volumes:
      - ./server:/server
      - ./dist:/dist
      - ./etc/web_config.conf:/etc/web_config.conf
    command: python3 /server/scripts/application_consumer.py

  feishuconsumer:
    extends: appconsumer
    command: python3 /server/scripts/feishu_consumer.py

  dingdingconsumer:
    extends: appconsumer
    command: python3 /server/scripts/dingding_consumer.py

  weworkconsumer:
    extends: appconsumer
    command: python3 /server/scripts/wework_consumer.py

  messengerconsumer:
    extends: appconsumer
    command: python3 /server/scripts/messenger_consumer.py

  nchan:
    restart: always
    image: lloydzhou/nchan
    ports:
      - "80"
    environment:
      - VIRTUAL_HOST=nchan.connect.ai
    volumes:
      - ./nchan.conf:/etc/nginx/conf.d/default.conf

  redis:
    restart: always
    image: redis:alpine
    ports:
      - "6379"

  rabbitmq:
    restart: always
    image: rabbitmq:3.7-management-alpine
    environment:
      RABBITMQ_ERLANG_COOKIE: "SWQOKODSQALRPCLNMEQG"
      RABBITMQ_DEFAULT_USER: "rabbitmq"
      RABBITMQ_DEFAULT_PASS: "rabbitmq"
      RABBITMQ_DEFAULT_VHOST: "/"
      VIRTUAL_HOST: rabbitmq-manager.connectai.ai
      VIRTUAL_PORT: 15672
    ports:
      - "15672"
      - "5672"
    volumes:
      - ./data/rabbitmq:/data/mnesia

  mysql:
    platform: linux/x86_64
    restart: always
    image: mysql:5.7
    volumes:
      - ./data/mysql/data:/var/lib/mysql
      - ./data/mysql/conf.d:/etc/mysql/conf.d
    environment:
      MYSQL_ROOT_PASSWORD: 'connectai2023'
      MYSQL_DATABASE: 'connectai-manager'
      TZ: 'Asia/Shanghai'
    ports:
      - "3306"
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  proxy:
    image: jwilder/nginx-proxy:alpine
    ports:
      - "8081:80"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./proxy.conf:/etc/nginx/proxy.conf
      - ./docker/manager.connect.ai:/etc/nginx/vhost.d/manager.connect.ai:ro
      - ./dist:/var/www/html:ro



