import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.25 3A2.25 2.25 0 0 0 3 5.25v13.499a2.25 2.25 0 0 0 2.25 2.25h5.914l.356-1.424l.02-.076H5.25a.75.75 0 0 1-.75-.75v-13.5a.75.75 0 0 1 .75-.75h13.499a.75.75 0 0 1 .75.75v5.983c.478-.19.993-.264 1.5-.22V5.25A2.25 2.25 0 0 0 18.748 3h-13.5zm10.104 11.999h-4.105a.75.75 0 1 0 0 1.5h2.605l1.5-1.5zm-6.605-6.75a1 1 0 1 1-2 0a1 1 0 0 1 2 0zm2.5-.75a.75.75 0 1 0 0 1.5h5.5a.75.75 0 1 0 0-1.5h-5.5zm0 3.75a.75.75 0 1 0 0 1.5h5.5a.75.75 0 1 0 0-1.5h-5.5zM7.75 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2zm1 2.75a1 1 0 1 1-2 0a1 1 0 0 1 2 0zm10.35-3.08l-5.902 5.901a2.685 2.685 0 0 0-.707 1.248l-.457 1.83c-.2.797.522 1.518 1.318 1.319l1.83-.458a2.685 2.685 0 0 0 1.248-.706L22.33 15.9a2.286 2.286 0 0 0-3.233-3.232z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListSquareEdit24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
