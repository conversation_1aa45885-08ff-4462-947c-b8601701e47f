# 🚀 ConnectAI 本地环境启动指南

## 📋 概述

本指南帮助您在本地环境中快速启动ConnectAI项目，无需Docker。

## ✅ 环境状态

- ✅ **Manager Server**: Flask版本，端口3000
- ✅ **DataChat API**: Flask版本，端口5000  
- ✅ **数据库**: SQLite，包含预置管理员账号
- ✅ **依赖**: 所有Python依赖已安装完成

## 🚀 快速启动

### 1. 环境检查
```bash
python check_environment.py
```

### 2. 启动所有服务
```bash
python start_connectai.py
```

### 3. 访问服务
- **Manager Server**: http://localhost:3000
- **DataChat API**: http://localhost:5000
- **健康检查**: http://localhost:5000/health

## 👤 预置管理员账号

- **邮箱**: `<EMAIL>`
- **密码**: `admin123`
- **租户**: `default-tenant`

## 🧪 API测试

### 登录测试
```bash
curl -X POST http://localhost:3000/api/login \
  -H 'Content-Type: application/json' \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

### 健康检查
```bash
curl http://localhost:5000/health
curl http://localhost:3000/health
```

### 获取分类列表
```bash
curl http://localhost:3000/api/categories
```

### 搜索测试
```bash
curl -X POST http://localhost:5000/api/search \
  -H 'Content-Type: application/json' \
  -d '{"query": "test"}'
```

## 📁 项目结构

```
project-manager/
├── manager-server/              # 主服务器
│   ├── venv/                   # Python虚拟环境 ✅
│   ├── simple_flask_server.py  # Flask服务器 ✅
│   └── requirements.txt        # 依赖列表 ✅
├── DataChat-API/               # 知识库API
│   ├── venv/                   # Python虚拟环境 ✅
│   ├── simple_app.py           # Flask API ✅
│   └── requirements.txt        # 依赖列表 ✅
├── data/                       # 数据存储 ✅
│   ├── connectai.db           # SQLite数据库 ✅
│   ├── admin_info.json        # 管理员信息 ✅
│   ├── files/                 # 文件存储 ✅
│   └── search_index/          # 搜索索引 ✅
├── start_connectai.py         # 一键启动脚本 ✅
├── check_environment.py       # 环境检查脚本 ✅
└── init_database_with_admin.py # 数据库初始化脚本 ✅
```

## 🔧 可用脚本

| 脚本 | 功能 | 用法 |
|------|------|------|
| `start_connectai.py` | 一键启动所有服务 | `python start_connectai.py` |
| `check_environment.py` | 检查环境状态 | `python check_environment.py` |
| `test_services_running.py` | 测试服务状态 | `python test_services_running.py` |
| `init_database_with_admin.py` | 初始化数据库 | `python init_database_with_admin.py` |

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :3000
   netstat -ano | findstr :5000
   ```

2. **虚拟环境问题**
   ```bash
   # 重新创建虚拟环境
   cd manager-server
   rmdir /s venv
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **依赖问题**
   ```bash
   # 重新安装依赖
   cd manager-server
   ./venv/Scripts/python.exe -m pip install -r requirements.txt
   
   cd DataChat-API
   ./venv/Scripts/python.exe -m pip install -r requirements.txt
   ```

4. **数据库问题**
   ```bash
   # 重新初始化数据库
   python init_database_with_admin.py
   ```

### 环境要求

- **Python**: 3.8+ (当前: 3.13.5)
- **操作系统**: Windows (已解决兼容性问题)
- **端口**: 3000, 5000 (确保未被占用)

## 📊 服务架构

### 本地环境替代方案
| 生产组件 | 本地替代 | 状态 |
|---------|---------|------|
| MySQL | SQLite | ✅ 已配置 |
| Redis | 内存存储 | ✅ 已配置 |
| Elasticsearch | 文件存储 | ✅ 已配置 |
| RabbitMQ | 内存队列 | ✅ 已配置 |
| Docker | 本地进程 | ✅ 已配置 |

### 数据存储
- **数据库**: `./data/connectai.db` (SQLite)
- **文件存储**: `./data/files/`
- **搜索索引**: `./data/search_index/`
- **管理员信息**: `./data/admin_info.json`

## 🎯 下一步

### 立即可以做的
1. ✅ **启动服务** - 运行 `python start_connectai.py`
2. ✅ **测试登录** - 使用预置管理员账号
3. ✅ **API测试** - 验证各种接口功能
4. ✅ **数据管理** - 查看和管理数据库

### 前端开发（需要Node.js）
1. 安装Node.js LTS版本
2. 启动前端管理面板
3. 启动浏览器扩展开发环境

### 生产环境部署
1. 迁移到MySQL数据库
2. 配置Redis缓存
3. 部署Elasticsearch搜索
4. Docker容器化部署

## 📞 技术支持

### 相关文档
- `🎊 部署成功！完整验证报告.md` - 详细验证报告
- `启动指南_验证版.md` - 完整启动指南
- `./data/admin_info.json` - 管理员账号信息

### 验证脚本
- `check_environment.py` - 环境检查
- `test_services_running.py` - 服务状态测试
- `init_database_with_admin.py` - 数据库初始化

---

## 🎉 总结

**您的ConnectAI本地环境已经完全就绪！**

- ✅ **所有服务正常运行**
- ✅ **预置管理员账号可用**
- ✅ **API接口全部可用**
- ✅ **主流程验证通过**

**现在就可以开始您的ConnectAI开发之旅了！** 🚀
