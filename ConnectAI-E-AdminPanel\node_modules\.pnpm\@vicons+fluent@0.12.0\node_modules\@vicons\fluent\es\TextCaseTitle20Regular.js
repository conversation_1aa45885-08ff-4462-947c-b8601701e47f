import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.5 3.5a.5.5 0 0 1 .5.5v4.891C13.53 8.337 14.232 8 15 8c1.657 0 3 1.567 3 3.5S16.657 15 15 15c-.768 0-1.47-.337-2-.891v.391a.5.5 0 0 1-1 0V4a.5.5 0 0 1 .5-.5zM15 14c.966 0 2-.97 2-2.5S15.966 9 15 9s-2 .97-2 2.5s1.034 2.5 2 2.5zM6.957 3.836a.5.5 0 0 0-.94-.013L3.293 11h-.02v.054l-1.24 3.269a.5.5 0 0 0 .935.354L3.984 12h4.754l.926 2.664a.5.5 0 1 0 .945-.328l-3.652-10.5zM4.363 11l2.1-5.537L8.39 11H4.363z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseTitle20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
