import json
import logging

from tornado.httpclient import HTTPRequest, AsyncHTTPClient
from tornado.options import options

from core.utils import ObjectDict


class Web(object):
    def __init__(self, bot_id, access_token=''):
        self.bot_id = bot_id
        self.access_token = access_token

    async def pubsub(self, method, bot_id, visitor_id, content):
        url = f'{options.SCHEMA}://{options.DOMAIN}/chat/pubsub/{bot_id}?visitor_id={visitor_id}'
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.access_token,
        }
        request = HTTPRequest(
            url=url,
            method=method,
            headers=headers,
            body=json.dumps(content, ensure_ascii=False).encode('utf-8') if method == 'POST' else None
        )
        logging.debug('ws req: %r', (request.url, request.method, request.body))
        response = await AsyncHTTPClient().fetch(request, raise_error=False)
        logging.debug('ws res: %r', response.body)
        result = response.body.decode()
        if method == 'POST':
            return result
        return ObjectDict(json.loads(result))

    @classmethod
    def create_message(cls, type: str = 'message', payload=None):
        return {
            'type': type,
            'payload': payload or {}
        }

    @classmethod
    def create_text_message(cls, text='', **kwargs):
        return cls.create_message(payload={'content': {'text': text}, **kwargs})

    @classmethod
    def create_message_from_feishu(cls, data):
        if 'message_id' not in data:
            return cls.create_message()
        payload = {
            'message_id': data['message_id'],
            'root_id': data.get('root_id', ''),
            'parent_id': data.get('parent_id', ''),
            'msg_type': data['msg_type'],
            'create_time': data['create_time'],
            'chat_id': data['chat_id'],
            'sender': data['sender'],
            'content': json.loads(data['body']['content'])
        }
        return cls.create_message(payload=payload)

    @classmethod
    def create_message_from_event(cls, event):
        if 'message' not in event:
            return cls.create_message()
        message = event['message']
        sender = event['sender']
        data = {
            **message,
            'msg_type': message['message_type'],
            'body': {'content': message['content']},
            'sender': {
                'id': sender['sender_id'].get('open_id', ''),
                'id_type': 'open_id',
                'sender_type': sender['sender_type'],
                'tenant_key': sender['tenant_key']
            }
        }
        return cls.create_message_from_feishu(data)

    @classmethod
    def create_system_message(cls, content):
        return cls.create_message(type='system', payload={
            "msg_type": "notice",
            "content": {
                "text": content,
            }
        })

