class BitToSvgTool(CTool):
    name: str = 'BitToSvg'
    description: str = 'bit to svg tool'

    def get_file_type(self, file_name):
        file_type = file_name.split('.').pop()
        if file_type in ["png", "jpg", "jpeg", "bmp", "exif", "webp"]:
            return file_type
        else:
            send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('支持上传png，jpg，jpeg，bmp，exif，webp格式位图文件'), tag='lark_md'),
                    header=FeishuMessageCardHeader(_('暂不支持您上传的文件类型'), template='blue'),
                )
            )
        raise Exception("NOT_SUPPORT")

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class BitToSvgCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            # FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成中，视图像复杂度耗时3-5分不等，请耐心等待！')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：生成中，视图像复杂度耗时3-5分不等，请耐心等待！'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                # 这里的token实际上是进度，可能为None或者是Done
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            # FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：已传输，%(progress)s%% ...', progress=token)))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                #logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs

                file_bin = additional_kwargs['data']
                del additional_kwargs['data'] # 删除data，防止存入数据库的内容过大

                if platform == 'feishu':
                    time.sleep(1)

                    # 上传文件获得 file_key
                    # svg 文件大小很少超过 30MB，姑且先使用这种方式
                    file_key = syncify(client.upload_file_binary)(file_bin, 'download.svg')
                    logging.info("debug bittosvg %r", file_key)
                    additional_kwargs.update({'file_key': file_key}) # 用于存入数据库

                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                            header=FeishuMessageCardHeader(content='转换完成 🎉 请下载下方文件', template='blue'),
                        )
                    )

                    send_message(
                        AppResult.File,
                        FeishuFileMessage(file_key=file_key)
                    )

                else:
                    # 钉钉消息
                    # 暂不支持钉钉
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title='AI转换矢量图小助手 🎉',
                        )
                    )

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [BitToSvgCallbackHandler()]
        chat = BSChat(**model)
        if platform == 'feishu':

            message_id = data.extra.message_id
            content = data.extra.extra.platform_content
            if data.extra['message_type'] == 'file':
                file_key = content.file_key
                file_name = content.file_name
                file_type = self.get_file_type(file_name)

                logging.info("debug %r %r %r", message_id, file_key, file_type)
                logging.info("debug %r", data.extra)

                file_bin = syncify(client.get_message_resource)(message_id, file_key, "file")

                messages = [HumanMessage(content='', additional_kwargs=dict(content=file_bin, file_key=file_key))]
            else:
                if data.extra['message_type'] == 'interactive':
                    # 注意几种消息拿 image_key 的方式不同
                    image_key = data.extra.extra.imageKey
                elif data.extra['message_type'] == 'image':
                    image_key = content.image_key
                elif data.extra['message_type'] == 'post':
                    input_kwargs = data.extra.get('input_kwargs', {})
                    image_key = input_kwargs.get('img_key', '')
                else:
                    raise Exception("NOT_SUPPORT")

                logging.info("debug %r %r", message_id, image_key)
                logging.info("debug %r", data.extra)

                img_bin = syncify(client.get_message_resource)(message_id, image_key, "image")
                messages = [HumanMessage(content='', additional_kwargs=dict(content=img_bin, image_key=image_key))]

            return chat.invoke(messages)

        elif platform == 'dingding':

            raise Exception("NOT_SUPPORT")
            # data_extra = data.get('extra', {})
            # input_kwargs = data_extra.get('input_kwargs', {})
            # with DingDingModel() as ddmodel:
            #     ddmodel.init_by_bot_id(data_extra.get('bot_instance_id'))
            #     robot_code = ddmodel.bot.app_id

            #     img_url = syncify(client.get_message_resource)(robot_code, input_kwargs.get('img_key'))
            #     input_kwargs.update({'content': httpx.get(img_url).content})

            #     messages = [HumanMessage(content='', additional_kwargs=dict(input_kwargs))]
            #     return chat(messages=messages)


# class BitToSvgShortcutTool(CTool):
#     name: str = 'BitToSvg_shortcut'
#     description: str = 'background remove shortcut tool'

#     def _run(self, *args, run_manager=None, input='', **kwargs):
#         # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
#         # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
#         class BitToSvgCallbackHandler(BaseCallbackHandler):
#             result: str = ''
#             send_length: int = 0

#             def on_llm_start(self, *args, **kwargs):
#                 # 开始的时候先发一个消息
#                 if platform == 'feishu':
#                     send_message(
#                         AppResult.ShortcutCard,
#                         FeishuMessageCard(
#                             FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
#                             FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
#                         )
#                     )
#                 else:
#                     send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

#             def on_llm_new_token(self, token, **kwargs) -> None:
#                 if model.streaming and platform == 'feishu':
#                     # 这里的token实际上是进度，可能为None或者是Done
#                     send_message(
#                         AppResult.UpdateCard,
#                         FeishuMessageCard(
#                             FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
#                             FeishuMessageNote(FeishuMessagePlainText(_('🤖️：已传输，%(progress)s%% ...', progress=token)))
#                         )
#                     )

#             def on_llm_end(self, response, *args, **kwargs):
#                 # logging.info("debug %r", response.generations)
#                 additional_kwargs = response.generations[0][0].message.additional_kwargs

#                 img_bin = additional_kwargs['data']
#                 del additional_kwargs['data']  # 删除data，防止存入数据库的内容过大

#                 if platform == 'feishu':
#                     time.sleep(1)
#                     # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
#                     # TODO feishu更换markdown，不用上传图片

#                     img_key = syncify(client.upload_image_binary)(img_bin)
#                     additional_kwargs.update({'img_key': img_key})  # 用于存入数据库

#                     contents = [
#                         FeishuMessageImage(img_key=img_key, alt='图片'),
#                     ]
#                     send_message(
#                         AppResult.UpdateCard,
#                         FeishuMessageCard(
#                             *contents,
#                             FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
#                             header=FeishuMessageCardHeader(content='BitToSvg Bot 🎉', template='blue'),
#                         )
#                     )
#                 else:
#                     pass

#             def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
#                 """Run when LLM errors."""
#                 logging.error(error)
#                 if model.streaming and platform == 'feishu':
#                     # 主动延迟等待更新卡片消息
#                     time.sleep(1)
#                     send_message(
#                         AppResult.UpdateCard,
#                         FeishuMessageCard(
#                             FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
#                         )
#                     )
#                 else:
#                     send_message(AppResult.ReplyText, '🤖️：' + str(error))

#         model.callbacks = [BitToSvgCallbackHandler()]
#         chat = RMChat(**model)
#         if platform == 'feishu':
#             data_extra = data.get('extra', {})
#             img_key = data_extra.get('input_kwargs', {}).get('img_key', '')
#             if data_extra.get('message_type', '') == 'interactive':
#                 message_id = data_extra.get('message_id')
#                 # 还是要压缩，否则容易生成失败
#                 img_bin = syncify(httpx.AsyncClient().get)(f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?image_key={img_key}&reply_message_id={message_id}&compress=true&quality=80&save_format=jpeg&max_size=1024').content
#             else:
#                 img_bin = syncify(client.get_message_resource)('', img_key)
#             messages = [HumanMessage(content='', additional_kwargs=dict(content=img_bin, img_key=img_key))]
#             return chat(messages=messages)


# class DingdingCommand(CommandTool):

#     next_tool_name: str = 'BitToSvg'
#     name: str = 'BitToSvg_dingding_command'
#     description: str = 'BitToSvg dingding command'
#     mode: List[str] = ['pixian-v2']

#     def send_usage(self):
        
#         # TODO 可使用ActionCardMessage替代，dtmdLink放到actionURL内即可
#         return send_message(
#             AppResult.ReplyActionCard,
#             DingDingMarkdownMessage(
#                 text=_('💥 **我是AI背景去除小助手，可以帮您去除图片背景**\n\n---\n🧹 \
#                        **AI 背景去除**\n\n在聊天框中输入图片，BOT将回复已去除背景的图片\n\n'),
#                 title=_('🎒 需要帮助吗？')
#             )
#         )

#     def parse_command(self, input, action):
#         if input[:5] == '/help' or input[:2] == '帮助':
#             return 'help',
#         if data.get('extra', {}).get('message_type', '') == 'post':
#             return 'post',
#         return 'help',
    
    
#     def on_post(self):
#         # 富文本消息
#         data_extra = data.get('extra', {})
#         rich_text = data_extra.get('extra', {}).get('platform_content', {}).get('richText', [])
#         input_img_keys = []
#         for msg in rich_text:
#             if msg.get('type', '') == 'picture':
#                 input_img_keys.append(msg['downloadCode'])
#         input_kwargs = {}
#         if not input_img_keys:
#             send_message(AppResult.ReplyText, _('🤖️：请上传图片！'))
#             return None
#         elif len(input_img_keys) == 1:
#             input_kwargs = {'img_key': input_img_keys[0]}
#         else:
#             send_message(AppResult.ReplyText, _('🤖️：请一次上传一张图片！'))
#             return None

#         if not input_kwargs:
#             send_message(AppResult.ReplyText, _('🤖️：输入格式不合法，请参考帮助！'))
#             return None
#         data_extra['input_kwargs'] = input_kwargs
#         return self.next_tool_name



class FeishuCommand(CommandTool):

    next_tool_name: str = 'BitToSvg'
    name: str = 'BitToSvg_feishu_command'
    description: str = 'BitToSvg feishu command'
    mode: List[str] = ['vectorizer']
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '发送位图文件，BOT回复转换为矢量图的svg文件',
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是AI矢量图转换小助手，可以帮您将位图转换为矢量图**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🧹 **AI 矢量图转换**\n向BOT发送位图文件，BOT将回复已转换为高清矢量图的文件'),
                    tag='lark_md',
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒 需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if 'shortcut_action' in data.extra.extra:
            self.next_tool_name = data.extra.extra.get('shortcut_action', self.next_tool_name)
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        if data.extra.message_type in ['image', 'post', 'file', 'interactive']:
            return data.extra.message_type,
        return 'note',

    def on_image(self):
        # 图片消息
        return self.next_tool_name

    def on_file(self):
        # 文件消息
        return self.next_tool_name

    def on_interactive(self):
        # 交互消息
        if 'imageKey' in data.extra.extra:
            return self.next_tool_name
        return None

    def on_post(self):
        # 富文本消息
        platform_content = data.extra.extra.platform_content
        input_img_keys = []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg.get('tag', '') == 'img':
                    input_img_keys.append(msg['image_key'])
        input_kwargs = {}
        if len(input_img_keys) == 1:
            input_kwargs = {'img_key': input_img_keys[0]}
        if not input_kwargs:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        data['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送图片或文件，不支持自然语言（除帮助外）、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送图片/文件！')
        return send_note(text, title)

class BitToSvgAgent(CAgent):

    class AppConfig(BaseAppConfig):
        name: str = 'BitToSvg'
        title: str = 'AI矢量图转换助手'
        title_en: str = 'Vetorize Helper'
        category: str = '音视频'
        description: str = '🏹发送位图，利用AI算法轻松转换为矢量图'
        description_en: str = '🏹 Quickly Trace Pixels To Vectors in Full Color'
        problem: str = '如何将位图转换为矢量图'
        problem_en: str = 'How to quickly trace pixels to vectors.'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/wiki/EvmSwZnkTiTm3Ik5vDEcilomnZy'
        manual_en: str = 'https://connect-ai.feishu.cn/wiki/Tw6Sw2xXuiDeAvkex7HcfCtBnod'
        icon: str = 'https://pic1.forkway.cn/cdn/20231107165618.png'
        logo: str = 'https://pic1.forkway.cn/cdn/20231107165618.png?imageMogr2/thumbnail/720x'
        sorted: int = 111
        support_resource: List[object] = [dict(
            category=ModelCategory.Image.value,
            scene=ModelCategory.Image.value,
            title='图片处理',
            tip='',
            required=True,
            resource=['Vectorizer.AI']
        )]
        support_bots: List[str] = ['feishu']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnBb6w74sJOwI9zI04grgQsh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/Y6lowBu1Pin0hSkL2zPcCHwKn3b'
        show: int = 0
        action_template = ""
#         action_template = """
# ${detail.content.messages.length > 0
#   ? detail.content.messages.map((item, index) => html`
#     ${parseImageFromMessage(item).map(image => html`
#       <div style="padding:20px">
#       <div style="font-size:13px;color:black;background:white;border-radius:10px">
#         <div style="padding:10px;line-height:2em">处理图片</div>
#         <div style="display: flex;">
#           <quark-image style="width: 100%;" src="${image.url}" radius="10px" alt="loading"></quark-image>
#         </div>
#       </div>
#       <form method="post">
#         <input type="hidden" name="messageId" value="${item.openMessageId}">
#         <input type="hidden" name="action" value="BitToSvg_shortcut">
#         <input type="hidden" name="user" value="${JSON.stringify(user)}">
#         <input type="hidden" name="message" value="${JSON.stringify(item)}">
#         <input type="hidden" name="imageKey" value="${image.image_key}">
#         <quark-button type="primary" size="big" style="position: relative;width: 100%;display: block;margin: 10px auto;">
#           {{_('去除背景', lang=lang)}}
#           <button type="submit" style="position: absolute;width: 100%;height:100;opacity:0;cursor: pointer;">透明按钮触发表单提交事件</button>
#         </quark-button>
#       </form>
#     `)}
#     `)
#   : detail.errMsg || '无图片'
# }
#         """

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='BitToSvg_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='BitToSvg_dingding_command', tool_input=kwargs, log="")
        # if platform == 'feishu' and self.parse_command(**kwargs):
        #     return AgentFinish({"output": "command"}, kwargs.get('input'))
        return AgentAction(tool='BitToSvg', tool_input=kwargs, log="")


