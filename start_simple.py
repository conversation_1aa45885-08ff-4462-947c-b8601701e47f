#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版ConnectAI启动脚本
用于调试和测试
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def start_datachat_api():
    """启动DataChat API"""
    print("启动DataChat API...")
    
    try:
        # 构建命令
        python_path = Path("DataChat-API/venv/Scripts/python.exe").absolute()
        script_path = "simple_app.py"
        cwd = "DataChat-API"
        
        print(f"Python路径: {python_path}")
        print(f"脚本: {script_path}")
        print(f"工作目录: {cwd}")
        
        # 启动进程
        process = subprocess.Popen(
            [str(python_path), script_path],
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"进程已启动，PID: {process.pid}")
        return process
        
    except Exception as e:
        print(f"启动失败: {e}")
        return None

def start_manager_server():
    """启动Manager Server"""
    print("\n启动Manager Server...")
    
    try:
        # 构建命令
        python_path = Path("manager-server/venv/Scripts/python.exe").absolute()
        script_path = "simple_flask_server.py"
        cwd = "manager-server"
        
        print(f"Python路径: {python_path}")
        print(f"脚本: {script_path}")
        print(f"工作目录: {cwd}")
        
        # 启动进程
        process = subprocess.Popen(
            [str(python_path), script_path],
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"进程已启动，PID: {process.pid}")
        return process
        
    except Exception as e:
        print(f"启动失败: {e}")
        return None

def test_service(name, port, timeout=10):
    """测试服务是否启动"""
    print(f"\n测试 {name} (端口 {port})...")
    
    import socket
    import urllib.request
    
    for i in range(timeout):
        try:
            # 测试端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"端口 {port} 已开放")
                
                # 测试HTTP
                try:
                    response = urllib.request.urlopen(f"http://localhost:{port}/", timeout=2)
                    print(f"{name} HTTP响应正常 (状态: {response.status})")
                    return True
                except Exception as e:
                    print(f"{name} HTTP请求失败: {e}")
            else:
                print(f"等待端口 {port} 开放... ({i+1}/{timeout})")
                
        except Exception as e:
            print(f"测试失败: {e}")
        
        time.sleep(1)
    
    print(f"{name} 启动超时")
    return False

def main():
    """主函数"""
    print("ConnectAI 简化启动脚本")
    print("=" * 40)
    
    processes = []
    
    try:
        # 启动DataChat API
        datachat_process = start_datachat_api()
        if datachat_process:
            processes.append(("DataChat API", datachat_process))
            
            # 测试DataChat API
            if test_service("DataChat API", 5000):
                print("√ DataChat API 启动成功")
            else:
                print("× DataChat API 启动失败")
        
        # 启动Manager Server
        manager_process = start_manager_server()
        if manager_process:
            processes.append(("Manager Server", manager_process))
            
            # 测试Manager Server
            if test_service("Manager Server", 3000):
                print("√ Manager Server 启动成功")
            else:
                print("× Manager Server 启动失败")
        
        # 显示结果
        print("\n" + "=" * 40)
        print("服务状态:")
        for name, process in processes:
            if process.poll() is None:
                print(f"√ {name} 正在运行 (PID: {process.pid})")
            else:
                print(f"× {name} 已停止 (返回码: {process.returncode})")
        
        print("\n服务地址:")
        print("  Manager Server: http://localhost:3000")
        print("  DataChat API: http://localhost:5000")
        
        print("\n管理员账号:")
        print("  邮箱: <EMAIL>")
        print("  密码: admin123")
        
        print("\n按回车键停止所有服务...")
        input()
        
    except KeyboardInterrupt:
        print("\n收到中断信号...")
    
    finally:
        # 停止所有进程
        print("停止所有服务...")
        for name, process in processes:
            try:
                if process.poll() is None:
                    print(f"停止 {name}...")
                    process.terminate()
                    process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        print("所有服务已停止")

if __name__ == "__main__":
    main()
