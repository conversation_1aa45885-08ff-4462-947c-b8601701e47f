'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createStaticVNode)('<g fill="none"><path fill="#745125" d="M5 4.25h18V6H5z"></path><path fill="#E62C46" d="M5 6h18v2H5z"></path><path fill="#F36D38" d="M5 8h18v2H5z"></path><path fill="#FFD23E" d="M5 10h18v1H5z"></path><path fill="#61BC51" d="M5 11h18v2H5z"></path><path fill="#1793E8" d="M5 13h18v2H5z"></path><path fill="#B73FBB" d="M5 15h18v2H5z"></path><path d="M4.75 3a.75.75 0 0 0-.75.75v20.5a.75.75 0 0 0 1.5 0V18h17.75a.75.75 0 0 0 .75-.75V3.75a.75.75 0 0 0-.75-.75H4.75zm.75 1.5h17v12h-17v-12z" fill="#212121"></path></g>', 1)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'FlagPride28Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
