
export function openCenteredWindow(url, width = 800, height = 600) {
  // 居中显示的窗口位置
  const left = (window.screen.width - width) / 2;
  const top = (window.screen.height - height) / 2;

  // 窗口特性字符串
  const features = `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`;

  // 打开新窗口
  window.open(url, '_blank', features);
}

export function openCenteredPopup(url='https://open.feishu.cn/app') {
  var screenWidth = window.innerWidth;
  var screenHeight = window.innerHeight;
  var popupWidth = Math.round(screenWidth * 5 / 6);
  var popupHeight = Math.round(screenHeight * 5 / 6);
  var left = (screenWidth - popupWidth) / 2;
  var top = (screenHeight - popupHeight) / 2;

  // 打开一个居中的新弹出窗口
  var popupWindow = window.open(url, 'popupWindow', `width=${popupWidth},height=${popupHeight},left=${left},top=${top}`);
  return popupWindow;
}


const csrfTokenRegex = /window.csrfToken="([^"]+)";/;
const displayNameRegex = /"displayName":{"value":"([^"]+)"/;
const avatarRegex = /"avatar":"([^"]+)"/;
const tenantRegex = /"tenantName":"([^"]+)"/;

export const FEISHU_DOMAIN = 'open.feishu.cn'
export const LARK_DOMAIN = 'open.larksuite.com'

export function getCsrfToken(domain=FEISHU_DOMAIN) {
  return fetch(`https://${domain}/app`, {mode: "cors", credentials: "include", referrerPolicy: "no-referrer"}).then(res => res.text()).then(content => {
    const csrfTokenMatch = content.match(csrfTokenRegex);
    const displayNameMatch = content.match(displayNameRegex);
    const avatarMatch = content.match(avatarRegex);
    const tenantMatch = content.match(tenantRegex);
    if (csrfTokenMatch && displayNameMatch && avatarMatch && tenantMatch) {
      return {
        csrf_token: csrfTokenMatch[1],
        display_name: displayNameMatch[1],
        avatar: avatarMatch[1],
        tenant: tenantMatch[1],
      }
    }
    return Promise.reject()
  })
}

export function feishuLogin(domain=FEISHU_DOMAIN) {
  return new Promise((resolve) => {
    chrome.windows.create({
      url: `https://${domain}/app`,
      type: 'popup',
      width: 400,
      height: 600,
      left: 400,
      top: 200,
    }, function(w){
      const i = setInterval(() => {
        getCsrfToken(domain).then(csrfToken => {
          clearInterval(i)
          console.log('remove window', w.id)
          chrome.windows.remove(w.id, function(res) {
            console.log('remove window', res)
          })
          resolve(csrfToken)
        }).catch(e => console.error(e))
      }, 500)
    })
  })
}

export function getCookieAndToken(domain=FEISHU_DOMAIN) {
  return getCsrfToken(domain).catch(e => {
    // 没有登录，就打开页面扫码登录，并且重新获取csrfToken
    return feishuLogin(domain)
  })
}

