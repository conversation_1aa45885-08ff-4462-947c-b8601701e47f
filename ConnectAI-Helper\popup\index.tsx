import "./style.css";
import LogoIcon from "react:~components/icon/connectai.svg";
import HomeIcon from "react:~components/icon/home.svg";

function IndexPopup() {
  return (
    <div
      style={ {
        minWidth: 300,
        height: 50,
        padding: "0 12px",
        backgroundImage: "linear-gradient(90deg, rgb(34, 211, 238), rgb(37, 99, 235))"
      } }
      className="flex items-center justify-between"
    >
      <div className="flex justify-start  items-center">
        <LogoIcon className="h-[28px]" />
        <div className='flex flex-col ml-1'>
          <div className="text-md text-white font-bold">Connect-AI</div>
          <div className="text-xs text-white font-light scale-75 origin-top-left"> 🤞 AI App One-Click Deployment</div>
        </div>
      </div>
      <HomeIcon
        onClick={ () => {
          chrome.tabs.create({ url: "https://www.connectai-e.com/plugin" });
        } }
        className="w-[28px] cursor-pointer text-white hover:bg-blue-600 p-1 rounded-md" />
    </div>
  );
}

export default IndexPopup;
