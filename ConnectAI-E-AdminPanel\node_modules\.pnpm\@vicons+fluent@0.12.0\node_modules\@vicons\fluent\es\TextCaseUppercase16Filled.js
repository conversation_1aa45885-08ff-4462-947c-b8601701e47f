import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 2.25a.75.75 0 0 1 .704.49l3.5 9.5a.75.75 0 0 1-1.408.52l-.924-2.51H2.628l-.924 2.51a.75.75 0 1 1-1.408-.52l3.5-9.5a.75.75 0 0 1 .704-.49zm0 2.92L3.181 8.75H5.82L4.5 5.17zm5.25-2.92A.75.75 0 0 0 9 3v9.5c0 .414.336.75.75.75h2.5a3.25 3.25 0 0 0 1.57-6.097A3 3 0 0 0 11.5 2.25H9.75zm3.25 3a1.5 1.5 0 0 1-1.5 1.5h-1v-3h1a1.5 1.5 0 0 1 1.5 1.5zm-.75 6.5H10.5v-3.5h1.75a1.75 1.75 0 1 1 0 3.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseUppercase16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
