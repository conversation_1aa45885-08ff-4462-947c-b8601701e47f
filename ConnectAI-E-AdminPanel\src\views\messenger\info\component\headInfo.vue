<template>
  <div
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space v-if="loading" vertical>
      <n-skeleton height="100px" circle class="mla mra" />
      <n-skeleton height="28px" width="80%" :sharp="false" />
      <n-skeleton height="60px" />
      <n-space>
        <n-skeleton height="40px" width="100px" :sharp="false" />
        <n-skeleton height="40px" width="54px" :sharp="false" />
      </n-space>
    </n-space>
    <div v-else class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-2 2xl:space-x-4">
      <div class="p-1 rounded-t-lg flex-center" alt="product image">
        <component
          :is="
            iconRender({
              cdnIcon: messengerInfo.icon
            })
          "
          style="width: 64px; height: 64px"
        />
      </div>
      <div>
        <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">
          {{ messengerInfo.name ?? $t('message.my.zwnc') }}
        </h3>
        <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
          {{ messengerInfo.description ?? $t('message.my.zwms') }}
        </div>
        <div class="flex items-center space-x-4 justify-start">
          <!-- <button
            type="button"
            class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            @click="handleCheckLog(messengerInfo.name)"
          >
            <icon-akar-icons-chat-dots class="mr-2" />
            {{ $t('message.my.ckdhrz') }}
          </button> -->
          <!-- <button
            type="button"
            class="inline-flex items-center text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-3 py-2 text-center dark:bg-red-500 dark:hover:bg-red-600 dark:focus:ring-red-900"
          >
            <icon-lucide-trash-2 class="mr-2" />
            删除应用
          </button> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { routeName } from '@/router';
import { useIconRender, useRouterPush } from '@/composables';
import { onMounted } from 'vue';

const { iconRender } = useIconRender();
const { routerPush } = useRouterPush();

const props = defineProps<{
  messengerInfo: ApiMessenger.MessengerChatInfoDetails;
}>();

onMounted(() => {});
function handleCheckLog(name: string) {
  routerPush({ name: routeName('log_chat'), query: { app: name } });
}
</script>
