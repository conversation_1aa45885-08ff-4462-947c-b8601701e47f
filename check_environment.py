#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConnectAI 环境检查脚本
检查系统环境和服务状态
"""

import os
import sys
import json
import sqlite3
import socket
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                  ConnectAI 环境检查工具                        ║
║                                                              ║
║  🔍 检查系统环境和服务状态                                      ║
║  📊 显示详细的诊断信息                                         ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_python_environment():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    
    # Python版本
    version = sys.version_info
    print(f"  ✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("  ⚠️  建议使用Python 3.8+")
    
    return True

def check_virtual_environments():
    """检查虚拟环境"""
    print("🔍 检查虚拟环境...")
    
    venvs = [
        ("manager-server/venv", "Manager Server虚拟环境"),
        ("DataChat-API/venv", "DataChat API虚拟环境")
    ]
    
    all_good = True
    for venv_path, desc in venvs:
        if Path(venv_path).exists():
            print(f"  ✅ {desc}")
            
            # 检查关键依赖
            try:
                if "manager-server" in venv_path:
                    python_cmd = f"{venv_path}/Scripts/python.exe"
                    result = subprocess.run([
                        python_cmd, "-c", 
                        "import flask, sqlalchemy; print('依赖正常')"
                    ], capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        print(f"    ✅ 依赖包正常")
                    else:
                        print(f"    ❌ 依赖包问题: {result.stderr}")
                        all_good = False
                        
                elif "DataChat-API" in venv_path:
                    python_cmd = f"{venv_path}/Scripts/python.exe"
                    result = subprocess.run([
                        python_cmd, "-c", 
                        "import flask, pandas; print('依赖正常')"
                    ], capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        print(f"    ✅ 依赖包正常")
                    else:
                        print(f"    ❌ 依赖包问题: {result.stderr}")
                        all_good = False
                        
            except Exception as e:
                print(f"    ❌ 依赖检查失败: {e}")
                all_good = False
        else:
            print(f"  ❌ {desc} 不存在")
            all_good = False
    
    return all_good

def check_database():
    """检查数据库"""
    print("🔍 检查数据库...")
    
    db_path = "data/connectai.db"
    if not Path(db_path).exists():
        print("  ❌ 数据库文件不存在")
        print("  💡 请运行: python init_database_with_admin.py")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表
        tables = ['tenant', 'account', 'application_category', 'resource_category']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  ✅ {table} 表: {count} 条记录")
        
        # 检查管理员账号
        cursor.execute("SELECT email, name FROM account WHERE email = '<EMAIL>'")
        admin = cursor.fetchone()
        if admin:
            print(f"  ✅ 管理员账号: {admin[0]} ({admin[1]})")
        else:
            print("  ❌ 管理员账号不存在")
            conn.close()
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库检查失败: {e}")
        return False

def check_data_directories():
    """检查数据目录"""
    print("🔍 检查数据目录...")
    
    directories = [
        ("data", "数据根目录"),
        ("data/files", "文件存储目录"),
        ("data/search_index", "搜索索引目录")
    ]
    
    all_good = True
    for dir_path, desc in directories:
        if Path(dir_path).exists():
            print(f"  ✅ {desc}")
        else:
            print(f"  ❌ {desc} 不存在")
            all_good = False
    
    return all_good

def check_ports():
    """检查端口占用"""
    print("🔍 检查端口状态...")
    
    ports = [
        (3000, "Manager Server"),
        (5000, "DataChat API")
    ]
    
    for port, service in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"  🟡 端口 {port} ({service}) 正在使用")
            else:
                print(f"  ✅ 端口 {port} ({service}) 可用")
        except Exception as e:
            print(f"  ❌ 端口 {port} 检查失败: {e}")

def load_admin_info():
    """加载管理员信息"""
    try:
        with open("data/admin_info.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return None

def show_summary():
    """显示总结信息"""
    print("\n📊 系统状态总结:")
    
    admin_info = load_admin_info()
    if admin_info:
        print("\n👤 预置管理员账号:")
        print(f"   邮箱: {admin_info['admin_email']}")
        print(f"   密码: {admin_info['admin_password']}")
        print(f"   租户: {admin_info['tenant_name']}")
        print(f"   API Key: {admin_info['tenant_apikey']}")
    
    print("\n🚀 启动命令:")
    print("   python start_connectai.py")
    
    print("\n🔗 服务地址:")
    print("   Manager Server: http://localhost:3000")
    print("   DataChat API: http://localhost:5000")
    
    print("\n📁 重要文件:")
    print("   数据库: ./data/connectai.db")
    print("   管理员信息: ./data/admin_info.json")
    print("   Manager Server: ./manager-server/simple_flask_server.py")
    print("   DataChat API: ./DataChat-API/simple_app.py")

def main():
    """主函数"""
    print_banner()
    
    all_checks_passed = True
    
    # 检查Python环境
    if not check_python_environment():
        all_checks_passed = False
    
    print()
    
    # 检查虚拟环境
    if not check_virtual_environments():
        all_checks_passed = False
    
    print()
    
    # 检查数据库
    if not check_database():
        all_checks_passed = False
    
    print()
    
    # 检查数据目录
    if not check_data_directories():
        all_checks_passed = False
    
    print()
    
    # 检查端口
    check_ports()
    
    # 显示总结
    show_summary()
    
    print("\n" + "="*60)
    
    if all_checks_passed:
        print("🎉 所有检查通过！系统已就绪")
        print("\n💡 下一步:")
        print("   运行 'python start_connectai.py' 启动服务")
    else:
        print("❌ 部分检查失败")
        print("\n💡 解决方案:")
        print("   1. 如果虚拟环境或数据库不存在，运行:")
        print("      python init_database_with_admin.py")
        print("   2. 如果依赖包有问题，重新安装:")
        print("      cd manager-server && ./venv/Scripts/python.exe -m pip install -r requirements.txt")
        print("      cd DataChat-API && ./venv/Scripts/python.exe -m pip install -r requirements.txt")
    
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    sys.exit(main())
