# 代码文档 - server/settings/constant.py

## 文件作用
定义系统中使用的常量、枚举类型和配置参数，提供统一的常量管理。

## 逐行代码解释

### 导入模块 (1行)
```python
from enum import Enum  # Python枚举类型支持
```

### ModelCategory枚举 - 模型分类 (4-15行)
```python
class ModelCategory(Enum):
    All = 'All'        # 包含全部模型
    LLM = 'LLM'        # 大语言模型 (Large Language Model)
    Draw = 'AI绘画'     # 如Midjourney等绘画模型
    TTS = 'TTS'        # 语音合成 (Text-to-Speech)
    STT = 'STT'        # 语音识别 (Speech-to-Text)
    Vision = '视觉'     # 视觉模型，如OpenAI vision
    Image = '图片'      # 图片处理，区别于绘画
    Audio = '音频'      # 音频处理
    Video = '视频'      # 视频处理，区别于视觉
    Doc = '文档'        # 文档处理
```

### AppCategory枚举 - 应用分类 (17-25行)
```python
class AppCategory(Enum):
    LLM = 'LLM'        # 大语言模型应用
    Draw = 'AI绘画'     # AI绘画应用
    Office = '日常办公'  # 办公类应用
    AV = '音视频'       # 音视频应用（待拆分）
    # TODO 将音视频拆分
    Audio = '音频'      # 音频处理应用
    Video = '视频'      # 视频处理应用
```

### AppAction枚举 - 应用动作 (27-35行)
```python
class AppAction(Enum):
    Chat = 1           # 聊天动作
    # Midjourney相关动作
    Imagine = 11       # 生成图片
    Upscale = 12       # 放大图片
    Variation = 13     # 变体生成
    Reset = 14         # 重置
    Describe = 15      # 描述图片
```

### AppResult枚举 - 应用结果类型 (37-75行)
```python
class AppResult(Enum):
    # 基础文本类型（部分已废弃）
    Text = 1                    # 普通文本
    ReplyText = 2               # 回复文本
    ReplyStreamText = 3         # 流式回复文本
    UpdateText = 4              # 更新文本（streaming模式）

    # 多媒体类型
    CardWithButton = 100        # 带按钮的卡片
    Image = 101                 # 图片
    Audio = 103                 # 音频
    File = 102                  # 文件（如removevc）

    # 新的卡片类型
    ReplyCard = 201             # 回复卡片
    ReplyNewCard = 204          # 新回复卡片
    UpdateCard = 202            # 更新卡片
    SendCard = 203              # 发送卡片
    ShortcutCard = 205          # 快捷卡片
    ReplyAudio = 206            # 回复音频（飞书进度更新）

    # 群聊专用类型（暂不使用）
    ReplyTuWenCard01 = 211      # 图文卡片类型1
    ReplyTuWenCard02 = 212      # 图文卡片类型2
    ReplyTuWenCard03 = 213      # 图文卡片类型3
    ReplyTuWenCard04 = 214      # 图文卡片类型4
    
    # 平台特定类型
    ReplyActionCard = 215       # 钉钉动作卡片（支持markdown和dtmdLink）
    ReplyTemplateCard = 220     # 企业微信模板卡片
    
    # 客服系统类型
    ForwardMessengerSeat = 230  # 转人工客服
    MessengerSystem = 231       # 客服系统消息
```

### 缓存过期时间常量 (78-80行)
```python
SESSION_CACHE_EXPIRE = 600              # 会话缓存过期时间：10分钟
CHAT_SESSION_CACHE_EXPIRE = 86400 * 10  # 聊天会话缓存：10天
WEBSOCKET_SESSION_CACHE_EXPIRE = 86400  # WebSocket会话缓存：1天
```

### 系统默认配置 (84-87行)
```python
# 默认的key以及token
VALIDATION_TOKEN = "v-Ohw8k6KwVynNmzXX"  # 验证令牌
ENCRIPT_KEY = "e-fJKrqNbSz9NqSWL5"      # 加密密钥

# 知识库默认提示词
KNOWLEDGE_DEFAULT_PROMPT = "As a customer support agent, please provide a helpful and professional response to the user's question or issue，Always output in the language I input"
```

### 代理服务器配置 (90-109行)
```python
PROXY_LIST = [
    {
        "name": "Connect-ai",  # 代理名称
        "url": "",             # 代理URL
        "id": "0",             # 代理ID
        "token": ""            # 代理令牌
    },
    {
        "name": "ai2e",        # ai2e代理
        "url": "",
        "id": "1",
        "token": ""
    },
    {
        "name": "aionekey",    # aionekey代理
        "url": "",
        "id": "2",
        "token": ""
    }
]
```

## 技术特点

### 枚举设计
- **类型安全**: 使用Python Enum确保类型安全
- **可读性**: 枚举值使用有意义的名称和中文描述
- **扩展性**: 易于添加新的分类和类型

### 分类体系
- **模型分类**: 按技术类型分类AI模型
- **应用分类**: 按功能领域分类应用
- **动作分类**: 定义应用支持的操作类型
- **结果分类**: 定义应用输出的消息类型

### 平台适配
- **多平台支持**: 针对飞书、钉钉、企业微信的不同消息类型
- **向后兼容**: 保留废弃类型以支持旧版本
- **功能区分**: 区分群聊和私聊的不同消息类型

### 配置管理
- **缓存策略**: 不同类型数据的缓存过期时间
- **安全配置**: 验证令牌和加密密钥
- **代理配置**: 支持多个代理服务器配置

## 使用场景
- **类型检查**: 在代码中使用枚举进行类型检查
- **消息路由**: 根据结果类型路由到不同的处理器
- **配置读取**: 读取系统默认配置和代理设置
- **缓存管理**: 根据数据类型设置合适的缓存时间
