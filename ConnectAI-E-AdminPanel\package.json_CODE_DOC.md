# 代码文档 - package.json

## 项目信息
- **名称**: connect-ai-admin
- **版本**: 0.1.0
- **描述**: 企联AI管理中心
- **作者**: connect-ai-e (<EMAIL>)
- **许可**: MIT

## 技术栈
- **Vue 3.3**: 前端框架
- **Vite**: 构建工具
- **TypeScript**: 类型系统
- **Naive UI 2.34.3**: UI组件库
- **UnoCSS**: 原子化CSS框架
- **Pinia**: 状态管理
- **Vue Router**: 路由管理

## 核心脚本

### 开发环境
- **dev**: 开发模式 (dev环境)
- **dev:lark**: 飞书版本开发
- **dev:test**: 测试环境开发
- **dev:prod**: 生产环境开发

### 构建脚本
- **build**: 生产环境构建
- **build:lark**: 飞书版本构建
- **build:vercel**: Vercel部署构建
- **build:test**: 测试环境构建

### 代码质量
- **lint**: ESLint代码检查
- **typecheck**: TypeScript类型检查
- **format**: 代码格式化

## 主要依赖

### UI和可视化
- **naive-ui**: 主要UI组件库
- **@antv/g2**: 数据可视化
- **echarts**: 图表库
- **vditor**: Markdown编辑器
- **wangeditor**: 富文本编辑器

### 工具库
- **axios**: HTTP客户端
- **dayjs**: 日期处理
- **lodash-es**: 工具函数
- **crypto-js**: 加密工具
- **vue-i18n**: 国际化

### 开发工具
- **@vitejs/plugin-vue**: Vue支持
- **unplugin-vue-components**: 组件自动导入
- **vite-plugin-mock**: 接口模拟
- **eslint-config-soybeanjs**: 代码规范

## 环境变量
- **VITE_SERVICE_ENV**: 服务环境 (dev/test/prod)
- **VITE_IS_LARK**: 飞书版本标识
- **VITE_HASH_ROUTE**: 哈希路由模式
- **VITE_VERCEL**: Vercel部署标识

## 技术特点
- 现代化Vue3生态
- 多环境构建支持
- 组件自动导入
- 代码质量保障
- 支持飞书定制版本
