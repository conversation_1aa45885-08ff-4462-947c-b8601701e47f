class UpScalerTool(CTool):
    name: str = 'Removetext'
    description: str = 'remove text tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class RemovetextCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(
                                FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(
                                _('🤖️：已传输，%(progress)s%% ...', progress=token)))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                # logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                img_bin = additional_kwargs['image_bin']

                del additional_kwargs['image_bin']  # 删除data，防止存入数据库的内容过大
                if platform == 'feishu':
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    time.sleep(1)
                    img_key = syncify(client.upload_image_binary)(img_bin)
                    additional_kwargs.update({'img_key': img_key})  # 用于存入数据库
                    contents = [
                        FeishuMessageImage(img_key=img_key, alt='图片'),
                    ]
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                            header=FeishuMessageCardHeader(content='RemoveText Bot 🎉', template='blue'),
                        )
                    )

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(
                                '🤖️：' + str(error), tag='lark_md'),
                        )
                    )

        model.callbacks = [RemovetextCallbackHandler()]

        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[1][1])

        chat = ClipdropChat(**model)
        if platform == 'feishu':
            data_extra = data.get('extra', {})
            img_key = data_extra.get('input_kwargs', {}).get('img_key', '')
            message_id = data_extra.get('message_id')

            img_bin = syncify(client.get_message_resource)(message_id, img_key)
            messages = [HumanMessage(content='', additional_kwargs=dict(content=img_bin, model_name=model_name))]
            return chat.invoke(messages)


class RemovetextShortcutTool(CTool):
    name: str = 'Removetext_shortcut'
    description: str = 'Removetext shortcut tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class RemovetextCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ShortcutCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(
                                FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(
                                _('🤖️：已传输，%(progress)s%% ...', progress=token)))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                # logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                img_bin = additional_kwargs['image_bin']
                del additional_kwargs['image_bin']  # 删除data，防止存入数据库的内容过大

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    img_key = syncify(client.upload_image_binary)(img_bin)
                    additional_kwargs.update({'img_key': img_key})  # 用于存入数据库
                    contents = [
                        FeishuMessageImage(img_key=img_key, alt='图片'),
                    ]
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            *contents,
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功'))),
                            header=FeishuMessageCardHeader(content='UpScaler Bot 🎉', template='blue'),
                        )
                    )
                else:
                    pass

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(
                                '🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [RemovetextCallbackHandler()]
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[1][1])

        chat = ClipdropChat(**model)
        if platform == 'feishu':
            data_extra = data.get('extra', {})
            img_key = data_extra.get('input_kwargs', {}).get('img_key', '')
            if data_extra.get('message_type', '') == 'interactive':
                message_id = data_extra.get('message_id')
                img_bin = syncify(httpx.AsyncClient().get)(
                    f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?image_key={img_key}&reply_message_id={message_id}').content
            else:
                img_bin = syncify(client.get_message_resource)('', img_key)
            messages = [HumanMessage(content='', additional_kwargs=dict(
                content=img_bin, model_name=model_name))]
            return chat.invoke(messages)


class FeishuCommand(CommandTool):

    next_tool_name: str = 'Removetext'
    name: str = 'Removetext_feishu_command'
    description: str = 'Removetext feishu command'
    mode: List[str] = ['Remove text']
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '若您配置了该应用的快捷消息，可在图片消息的快捷消息中快速去除文字'
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是AI图片文字去除助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('**👋 需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if 'shortcut_action' in data.extra.extra:
            self.next_tool_name = data.extra.extra.get('shortcut_action', self.next_tool_name)
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        if data.extra.message_type in ['image', 'post', 'interactive']:
            return data.extra.message_type,
        return 'note',

    def on_post(self):
        # 富文本消息
        platform_content = data.extra.extra.platform_content
        input_img_keys = []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg.get('tag', '') == 'img':
                    input_img_keys.append(msg['image_key'])
        input_kwargs = {}
        if len(input_img_keys) == 1:
            input_kwargs = {'img_key': input_img_keys[0]}
        if not input_kwargs:
            return self.on_note(text=_('🤖️：输入格式不合法，请参考帮助！'), title=_('🤖 机器人提醒'))
        data['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_image(self):
        # 图片消息
        platform_content = data.extra.extra.platform_content
        input_kwargs = {'img_key': platform_content['image_key']}
        data['extra']['input_kwargs'] = input_kwargs
        return self.next_tool_name

    def on_interactive(self):
        # 卡片消息
        if 'imageKey' in data.extra.extra:
            input_kwargs = {'img_key': data.extra.extra.imageKey}
            data['extra']['input_kwargs'] = input_kwargs
            return self.next_tool_name
        return

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送图片，不支持自然语言（除帮助外）、文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送图片！')
        return send_note(text, title)


class RemovetextAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'AI绘画'
        name: str = 'Removetext'
        title: str = '去除图片文字'
        title_en: str = 'text removing helper'
        description: str = '🚀 仅需几秒，即可实现图像文本的去除！'
        description_en: str = '🚀 Image text removal in just a few seconds'
        problem: str = '仅需几秒，即可实现图像文本的去除'
        problem_en: str = 'Image text removal in just a few seconds'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/wiki/Psl4w7mgKiL0ZykDwg7cAJ2XnCf'
        manual_en: str = 'https://connect-ai.feishu.cn/wiki/Psl4w7mgKiL0ZykDwg7cAJ2XnCf'
        icon: str = 'https://pic1.forkway.cn/cdn/20231121152429.png'
        logo: str = 'https://pic1.forkway.cn/cdn/20231121152429.png?imageMogr2/thumbnail/720x'
        sorted: int = 110
        support_resource: List[object] = [dict(
            category=ModelCategory.Image.value,
            scene=ModelCategory.Image.value,
            title='图片处理',
            tip='',
            required=True,
            resource=['Clipdrop']
        )]
        support_bots: List[str] = ['feishu']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnBb6w74sJOwI9zI04grgQsh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/BsarwycEOiTPYkkAeRXcBqGynxd'
        action_template = """
        ${detail.content.messages.length > 0
          ? detail.content.messages.map((item, index) => html`
            ${parseImageFromMessage(item).map(image => html`
              <div style="padding:20px">
              <div style="font-size: 13px;color:black;background:white;border-radius:10px">
                <div style="padding:10px;line-height:2em">处理图片</div>
                <div style="display: flex;">
                  <quark-image style="width: 100%;" src="${image.url}" radius="10px" alt="loading"></quark-image>
                </div>
              </div>
              <form method="post">
                <input type="hidden" name="messageId" value="${item.openMessageId}">
                <input type="hidden" name="action" value="Removetext_shortcut">
                <input type="hidden" name="user" value="${JSON.stringify(user)}">
                <input type="hidden" name="message" value="${JSON.stringify(item)}">
                <input type="hidden" name="imageKey" value="${image.image_key}">
                <quark-button type="primary" size="big" style="position: relative;width: 100%;display: block;margin: 10px auto;">
                  {{_('去除文字', lang=lang)}}
                  <button type="submit" style="position: absolute;width: 100%;height:100;opacity:0;cursor: pointer;">透明按钮触发表单提交事件</button>
                </quark-button>
              </form>
            </div>
            `)}
            `)
          : detail.errMsg || '无图片'
        }
                """


    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(
                    last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='Removetext_feishu_command', tool_input=kwargs, log="")
        return AgentAction(tool='Removetext', tool_input=kwargs, log="")
