<template>
  <n-dropdown :options="options" @select="handleDropdown">
    <hover-container class="px-12px" :inverted="theme.header.inverted">
      <div v-if="Info">
        <span class="text-16px font-medium">{{ Info }}</span>
      </div>
      <div v-else>
        <icon-local-avatar-3d class="text-32px" />
        <span class="pl-8px text-16px font-medium">{{ $t('message.system.title') }}</span>
      </div>
    </hover-container>
  </n-dropdown>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import type { DropdownOption } from 'naive-ui';
import { useAuthStore, useThemeStore } from '@/store';
import { useIconRender } from '@/composables';
import useShowExitModel from '@/hooks/common/use-show-exit-model';
import useShowInfoModal from '@/hooks/common/use-show-info';
import { t } from '@/locales';
import { fetchInfo } from '@/service';

defineOptions({ name: 'UserAvatar' });

const Info = ref(null);

onMounted(async () => {
  try {
    const res = await fetchInfo();
    Info.value = res.data.data.display_name;
  } catch (error) {
    console.error('Error', error);
  }
});

const auth = useAuthStore();
const theme = useThemeStore();
const { iconRender } = useIconRender();
const { setShow } = useShowExitModel();
const { setInfoShow } = useShowInfoModal();

// console.log(auth.userInfo);

const options: DropdownOption[] = [
  auth.userInfo.telephone
    ? {
        label: auth.userInfo.telephone,
        key: 'telephone',
        icon: iconRender({ icon: 'carbon:phone' })
      }
    : {
        label: auth.userInfo.email,
        key: 'email',
        icon: iconRender({ icon: 'carbon:email' })
      },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: t('message.system.xgxx'),
    key: 'change-info',
    icon: iconRender({ icon: 'carbon:edit' })
  },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: t('message.header.logout'),
    key: 'logout',
    icon: iconRender({ icon: 'carbon:logout' })
  }
];

type DropdownKey = 'user-center' | 'change-info' | 'logout';

function handleDropdown(optionKey: string) {
  const key = optionKey as DropdownKey;
  if (key === 'logout') {
    setShow();
  } else if (key === 'change-info') {
    setInfoShow();
  } else return;
}
</script>

<style scoped></style>
