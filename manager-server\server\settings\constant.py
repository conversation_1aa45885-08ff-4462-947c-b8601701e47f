from enum import Enum


class ModelCategory(Enum):
    All = 'All'  # 包含全部模型
    LLM = 'LLM'  # 大语言模型
    Draw = 'AI绘画'  # 如Midjourney
    TTS = 'TTS'  # 语音合成
    STT = 'STT'  # 语音识别
    Vision = '视觉'  # 视觉，如OpenAI vision
    Image = '图片'  # 图片处理，区别于绘画
    Audio = '音频'  # 音频处理
    Video = '视频'  # 视频处理，区别于视觉
    Doc = '文档'  # 文档处理


class AppCategory(Enum):
    LLM = 'LLM'
    Draw = 'AI绘画'
    Office = '日常办公'
    AV = '音视频'
    # TODO 将音视频拆分
    Audio = '音频'
    Video = '视频'


class AppAction(Enum):
    Chat = 1
    # mj
    Imagine = 11
    Upscale = 12
    Variation = 13
    Reset = 14
    Describe = 15


class AppResult(Enum):

    # 这些类型，暂时废弃了，飞书chat只需要使用最后两个ReplyCard和UpdateCard即可
    Text = 1
    ReplyText = 2
    ReplyStreamText = 3
    # streaming模式需要更新消息
    UpdateText = 4

    # mj
    CardWithButton = 100
    Image = 101
    Audio = 103

    # removevc
    File = 102

    # 新的类型
    ReplyCard = 201
    ReplyNewCard = 204
    UpdateCard = 202
    SendCard = 203
    ShortcutCard = 205
    # 在飞书里面有发进度，所以使用UpdateCard逻辑
    ReplyAudio = 206

    # 这几种消息只支持群聊，暂时不使用
    ReplyTuWenCard01 = 211
    ReplyTuWenCard02 = 212
    ReplyTuWenCard03 = 213
    ReplyTuWenCard04 = 214
    # 钉钉统一使用这个消息，这个消息的text支持markdown语法，同时btns的actionURL支持dtmdLink
    ReplyActionCard = 215
    # 企业微信卡片消息类型
    ReplyTemplateCard = 220
    # 客服转人工
    ForwardMessengerSeat = 230
    MessengerSystem = 231



SESSION_CACHE_EXPIRE = 600
CHAT_SESSION_CACHE_EXPIRE = 86400 * 10
WEBSOCKET_SESSION_CACHE_EXPIRE = 86400


# 默认的key以及token
VALIDATION_TOKEN = "v-Ohw8k6KwVynNmzXX"
ENCRIPT_KEY = "e-fJKrqNbSz9NqSWL5"

KNOWLEDGE_DEFAULT_PROMPT = "As a customer support agent, please provide a helpful and professional response to the user's question or issue，Always output in the language I input"


PROXY_LIST = [
    {
        "name":"Connect-ai",
        "url":"",
        "id":"0",
        "token": ""
    },
    {
        "name":"ai2e",
        "url":"",
        "id":"1",
        "token": ""
    },
    {
        "name":"aionekey",
        "url":"",
        "id":"2",
        "token": ""
    }
]

