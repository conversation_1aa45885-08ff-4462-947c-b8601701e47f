import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.53 3.59a.75.75 0 0 0-1.06 0l-9.88 9.88a.75.75 0 0 0 0 1.06l1.069 1.068a2.75 2.75 0 0 1 3.742 3.742L9.47 20.41a.75.75 0 0 0 1.061 0l9.879-9.879a.75.75 0 0 0 0-1.06L19.34 8.4a2.75 2.75 0 0 1-3.742-3.742L14.53 3.59zM12.41 2.53a2.25 2.25 0 0 1 3.182 0l1.171 1.172c.511.51.42 1.227.162 1.66a1.25 1.25 0 0 0 1.713 1.713c.433-.257 1.15-.349 1.66.162L21.47 8.41a2.25 2.25 0 0 1 0 3.182l-9.878 9.878a2.25 2.25 0 0 1-3.182 0l-1.172-1.171c-.51-.51-.42-1.228-.162-1.66a1.25 1.25 0 0 0-1.713-1.713c-.433.257-1.15.348-1.66-.163L2.53 15.591a2.25 2.25 0 0 1 0-3.182L12.41 2.53z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
