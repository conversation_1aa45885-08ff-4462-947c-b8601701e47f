import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.506 4.034a1.5 1.5 0 0 0-1.49-1.498l-4.89-.032a1.5 1.5 0 0 0-1.07.438l-6.68 6.664a1.5 1.5 0 0 0-.002 2.123l4.948 4.948a1.5 1.5 0 0 0 1.678.308V14a2 2 0 0 1 1.5-1.937V12a3 3 0 0 1 5.41-1.788l.164-.165a1.5 1.5 0 0 0 .44-1.063l-.008-4.95z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagLockAccent20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
