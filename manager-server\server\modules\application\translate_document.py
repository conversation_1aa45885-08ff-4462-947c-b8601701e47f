class TranslateDocTool(CTool):
    name: str = 'translate_document'
    description: str = 'translate document tool'
    def _run(self, *args, run_manager=None, input='', **kwargs):
        # logging.info("ChatTool %r", (self, args, run_manager, input, kwargs, model))
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class TranslateDocCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0
            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：已传输，%(progress)s%% ...', progress=token)))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                # logging.info("debug %r", response.generations)
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                file_url = additional_kwargs.get('TranslateFileUrl', '')
                if file_url is None:
                    raise Exception("NOT_FOUND")

                if platform == 'feishu':
                    time.sleep(1)
                    file_name = data.extra.extra.action.value["file_name"]
                    file_extension = file_url.split('?')[0].split('.')[-1:]
                    file_name = '.'.join(file_name.split('.')[:-1]) + '.' + file_extension[0]
                    try:
                        with httpx.Client() as httpclient:
                            binary_data = httpclient.get(file_url).content
                    except Exception as e:
                        with httpx.Client() as httpclient:
                            binary_data = httpclient.get(file_url).content
                    file_key = syncify(client.upload_file_binary)(binary_data, file_name)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(_('👇👇请下载下面的文件查看'), tag='lark_md'),
                            header=FeishuMessageCardHeader(content=_('🎉文件翻译成功！'), template='blue'),
                        )
                    )
                    return send_message(
                        AppResult.File,
                        FeishuFileMessage(file_key)
                    )


                else:
                    # 钉钉消息
                    # 暂时不支持钉钉
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title='文档翻译小助手 🎉',
                        )
                    )
            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""

                # TODO：这里也没处理钉钉
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        if platform == 'feishu':
            if not input and 'action' in data.extra.extra:
                if data.extra.extra.action["tag"] == "button":
                    # TODO: 这里切换到拿文件
                    model.callbacks = [TranslateDocCallbackHandler()]
                    chat = AliyunChat(**model)
                    action_value = data.extra.extra.action.value
                    message_id = action_value["message_id"]
                    file_key = action_value["file_key"]
                    file_name = action_value["file_name"]
                    file_type = file_name.split('.')[-1]
                    file_url = f"https://{options.DOMAIN}/api/feishu/file/message.{file_type}?message_id={message_id}&file_key={file_key}&file_type={file_type}"
                    logging.info("debug %r", file_url)
                    target_language = session.extra.get('target_language', 'zh')
                    messages = [HumanMessage(content='', additional_kwargs=dict(file_url=file_url, source_language="auto", target_language=target_language))]
                    return chat.invoke(messages)


class FeishuCommand(CommandTool):
    next_tool_name: str = 'translate_document'
    name: str = 'translate_document_feishu_command'
    description: str = 'translate document feishu command'
    language_options = {
        "zh": "汉语",
        "en": "英语",
        "ru": "俄语",
        "fr": "法语",
        "hi": "印地语",
        "ar": "阿拉伯语",
        "vi": "越南语",
        "es": "西班牙语",
        "ja": "日语",
        "ko": "韩语",
    }
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '发送文件，BOT回复翻译的文件',
    ]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def language_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(content=v, value=k) for k, v in self.language_options.items()],
            placeholder=_('选择目标语言'),
            initial_option=session.extra.get('target_language', list(self.language_options.keys())[0]),
            value={"command": "target"},
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是文档翻译小助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                # FeishuMessageHr(),
                # FeishuMessageDiv(
                #     _('🌏 **目标翻译语言选择**\n文本回复 *语言* 或 */language*'),
                #     tag='lark_md',
                #     extra=self.language_select
                # ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒 需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        # elif input[:9] == '/language' or input[:2] == '语言':
        #     return 'select',
        elif not input and action:
            if action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
            elif action['tag'] == 'button':
                return None,
        if data.extra.message_type in ['file']:
            return data.extra.message_type,
        return 'note',

    def on_file(self):
        # 文件消息
        message_id = data.extra.message_id
        content = data.extra.extra.platform_content
        file_key = content.file_key
        file_name = content.file_name
        file_type = file_name.split('.').pop()
        if file_type not in ["doc", "docx", "txt", "pdf", "ppt", "pptx", "xls", "xlsx", "csv", "html", "htm", "xml", "xhtml", "srt", "json", "xliff", "tmx", "dita", "md", "markdown", "idml"]:
            return self.on_note()
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _(' **点击右侧下拉框，选择想要翻译成的目标语言**\n'),
                    tag='lark_md',
                    extra=self.language_select
                ),
                FeishuMessageAction(
                    FeishuMessageButton(content=_('已确认，开始翻译'), value={"message_id": message_id, "file_name": file_name,"file_key": file_key}),
                ),
                header=FeishuMessageCardHeader(_('文件解析完毕，请选择目标语言'), template='blue'),
            )
        )

    def on_select(self):
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _(' **点击右侧下拉框，选择想要翻译成的目标语言**\n'),
                    tag='lark_md',
                    extra=self.language_select
                ),
            )
        )

    def on_target(self, target_language):
        session.set_extra('target_language', target_language)
        return

    def on_note(self, text='', title=''):
        text = text or _('我是文件小助手，请直接向我发送文件，支持docx(doc)、xlsx(xls)、pptx(ppt)、pdf、txt、csv、markdown等文本文件类型，不支持自然语言对话哦!')
        title = title or _('不支持的消息，请更改发送文件！')
        return send_note(text, title)


class TranslateDocumentAgent(CAgent):

    class AppConfig(BaseAppConfig):
        # TODO: 把所有项换掉
        category: str = '日常办公'
        name: str = 'translate_document'
        title: str = '文档翻译助手'
        title_en: str = 'DocTranslate'
        description: str = '⭐️ 将文本翻译成另一种语言，并将翻译结果重新生成和原始文档格式一样的文件'
        description_en: str = "⭐️ Translate the text into another language and recreate the translated result in the same format as the original document"
        problem: str = '翻译完整Word（.docx）、Powerpoint（.pptx）、Excel（.xlsx）、PDF（.pdf）、文本（.txt）以及HTML（.html）文件'
        problem_en: str = "Translate entire Word (.docx), Powerpoint (.pptx), Excel (.xlsx), PDF (.pdf), text (.txt), and HTML (.html) files"
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/wiki/WgUdwMqjGi6bQ7kr8Mrc81qbnzf'
        manual_en: str = 'https://connect-ai.feishu.cn/wiki/Ces4wBHVniv2jikNGQkcSpnanTf'
        icon: str = 'https://pic.forkway.cn/cdn/20231115140810.png'
        logo: str = 'https://pic.forkway.cn/cdn/20231115140810.png?imageMogr2/thumbnail/720x'
        sorted: int = 8
        support_resource: List[object] = [dict(
            category=ModelCategory.Doc.value,
            scene=ModelCategory.Doc.value,
            title='文档处理',
            tip='',
            required=True,
            resource=['AliyunOpenAPI']
        )]
        support_bots: List[str] = ['feishu'] # DingDing 暂不支持
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnBb6w74sJOwI9zI04grgQsh'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/PfjRwlVu5ikTvekycRecAk22nRf'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='translate_document_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='translate_document_dingding_command', tool_input=kwargs, log="")
        # if platform == 'feishu' and self.parse_command(**kwargs):
        #     return AgentFinish({"output": "command"}, kwargs.get('input'))
        return AgentAction(tool='translate_document', tool_input=kwargs, log="")
