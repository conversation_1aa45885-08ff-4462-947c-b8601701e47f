import { mockRequest, request } from '../request';

/**
 * 获取验证码
 * @param phone - 手机号
 * @returns - 返回boolean值表示是否发送成功
 */
export function fetchSmsCode(phone?: string, email?: string) {
  return request.post<boolean>('/api/account/code', { phone, email });
}

/**
 * 登录
 * @param userName - 用户名
 * @param passwd - 密码
 */
export function fetchLogin(email: string, passwd: string) {
  return request.post<ApiAuth.Token>('/api/account/login', { email, passwd });
}

/**
 * 登录
 * @param phone - 手机号
 * @param code - 验证码
 */
export function fetchLoginCode(phone: string, code: string) {
  return request.post<ApiAuth.Token>('/api/account/login', { phone, code });
}

/**
 * 注册
 * @param email- 用户名
 * @param code - 验证码
 * @param passwd - 密码
 */
export function fetchRegister(email: string, code: string, passwd: string) {
  return request.post<ApiAuth.Token>('/api/account/login', { email, code, passwd });
}

/* 修改企业名称 */
export function changeCompanyname(teamname: string) {
  return request.put('/api/account/update', {display_name: teamname});
}

/* Info */
export function fetchInfo() {
  return request.get('/api/account/info');
}

/**
 *
 * 登出
 */
export function logout() {
  return request.delete('/api/account/logout');
}

/** 获取用户信息 */
export function fetchUserInfo(token: string) {
  return mockRequest.get<ApiAuth.UserInfo>('/api/getUserInfo', {
    headers: { token }
  });
}

/**
 * 获取用户路由数据
 * @param userId - 用户id
 * @description 后端根据用户id查询到对应的角色类型，并将路由筛选出对应角色的路由数据返回前端
 */
export function fetchUserRoutes(userId: string) {
  return mockRequest.post<ApiRoute.Route>('/api/getUserRoutes', { userId });
}

/**
 * 刷新token
 * @param refreshToken
 */
export function fetchUpdateToken(refreshToken: string) {
  return mockRequest.post<ApiAuth.Token>('/api/updateToken', { refreshToken });
}

/**
 * 获取tenant handler
 */
export function fetchTenantHandler() {
  return request.get('/api/tenant/handler');
}

/**
 * 获取套餐
 */
export function fetchProduct() {
  return request.get('/api/product');
}

/**
 * 下单
 */
export function orderProduct(productId: string) {
  return request.post('/api/order', { product_id: productId });
}
