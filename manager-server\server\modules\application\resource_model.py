import logging
import json
from io import By<PERSON><PERSON>
from itertools import groupby
from tornado.httpclient import AsyncHT<PERSON><PERSON>lient, HTTPRequest
from tornado.options import options
from urllib.parse import quote
from sqlalchemy import alias, and_, or_, select, func, distinct, text
from sqlalchemy.orm import (
    relationship,
    joinedload,
    foreign,
    column_property,
    aliased,
)
from core.api_base import NewApiBase, ApiBase
from core.redisdb import redis_cli, stalecache
from core.feishu import Lark
from core.base_model import MysqlModel
from core.utils import row2dict, format_time, gen_verification_code, hash_password, check_password, _
from core.exception import NotFound, PermissionDenied, InternalError, Duplicate
from core.schema import (
    ObjID,
    Tenant,
    ResourceCategory,
    Resource, Model,
    TenantResource,
    ApplicationCategory,
    Application,
    AppInstance,
    ApplicationSupportResource,
    ApplicationSupportBot,
    Bot, BotInstance,
    AppInstancePrompt,
    AppInstanceSensitive,
    Policy,
    AppInstanceBotInstance,
    Messenger,
)
from settings.constant import (
    ModelCategory,
    VALIDATION_TOKEN, ENCRIPT_KEY,
    KNOWLEDGE_DEFAULT_PROMPT,
)


class BotInstanceWithPlatform(BotInstance):
    platform = column_property(
        select(Bot.platform).where(and_(
            BotInstance.bot_id == Bot.id,
        )).limit(1)
    )

    tenant_name = column_property(
        select(Tenant.name).where(and_(
            BotInstance.tenant_id == Tenant.id,
        )).limit(1)
    )


class ResourceModel(MysqlModel):

    async def get_resource_category(self, page, size, lang='zh_CN'):
        query = self.session.query(ResourceCategory).filter(
            ResourceCategory.status == 0
        ).order_by(
            ResourceCategory.created.desc(),
        )
        total = await self.query_total(query)
        return [row2dict(i, lang=lang) for i in await self.query_one_page(query, page, size)], total

    async def get_resource_list(self, tenant_id, category_id, keyword, page, size, lang='zh_CN'):

        class ResourceWithTenant(Resource):
            tenant_resource = relationship(
                TenantResource,
                primaryjoin=and_(
                    TenantResource.resource_id == Resource.id,
                    TenantResource.tenant_id == tenant_id,
                    # TenantResource.status == 0,
                ),
                order_by=TenantResource.modified.desc(),
                uselist=False,
            )

            bot_instance_count = column_property(
                select(func.count(AppInstance.id)).where(and_(
                    AppInstance.tenant_id == tenant_id,
                    AppInstance.resource_id == Resource.id,
                    AppInstance.status > 0,
                ))
            )

        query = self.session.query(ResourceWithTenant).options(
            joinedload(ResourceWithTenant.tenant_resource),
        ).filter(
            Resource.category_id == category_id if category_id else True,
            Resource.name.like("%{}%".format(keyword)) if keyword else True,
            Resource.status == 0
        ).order_by(
            Resource.sorted.asc(),
            Resource.created.desc(),
        )
        total = await self.query_total(query)
        if total == 0:
            return [], 0
        return [await self.format_resource_item(i, lang) for i in await self.query_one_page(query, page, size)], total

    async def format_resource_item(self, item, lang):
        res = row2dict(item, lang=lang)
        # TODO 余额＋充值地址
        summary = await self.get_amount_by_api_key(
            item.tenant_resource and item.tenant_resource.api_key,
            item.tenant_resource and item.tenant_resource.api_base,
        )
        # 从关联对象获取
        res['api_key'] = item.tenant_resource and item.tenant_resource.api_key or ''
        res['api_base'] = item.tenant_resource and item.tenant_resource.api_base or ''
        # 参考application/model.py给填充默认值
        if not res['api_base']:
            res['api_base'] = ApiBase(item.name).url if res['api_key'] else NewApiBase(item.name).url
        res['tenant_status'] = item.tenant_resource and item.tenant_resource.status or 0
        res['amount'] = summary.get('token', 0.0)
        res['recharge_link'] = options.RECHARGE_LINK
        # 国外的几个资源不展示金额
        if item.name in ['Azure', 'OpenAI', 'Claude']:
            res['amount'] = None
            res['recharge_link'] = None
        # 尝试将编辑表单需要的字段填进去，其实有了config里面的数据，后面那些api_base之类的可以不需要了
        if 'title' in res.get('config', {}):
            res['config']['title'] = _(res['config']['title'], lang=lang)
        if 'label' in res.get('config', {}).get('top', {}):
            res['config']['top']['label'] = _(res['config']['top']['label'], lang=lang)
        for index, formitem in enumerate(res.get('config', {}).get('form', [])):
            name = formitem['name']
            res['config']['form'][index]['label'] = _(res['config']['form'][index]['label'], lang=lang)
            if 'placeholder' in res['config']['form'][index]:
                res['config']['form'][index]['placeholder'] = _(res['config']['form'][index]['placeholder'], lang=lang)
            if item.tenant_resource:
                value = item.tenant_resource.extra.get(name)
                if name == 'api_key':
                    value = item.tenant_resource.api_key
                if name == 'api_base':
                    value = item.tenant_resource.api_base
                if name == 'api_base' and not value:
                    value = res['api_base']
                if value:
                    res['config']['form'][index]['value'] = value
        return res

    async def get_amount_by_api_key(self, api_key, api_base):
        try:
            # 这里需要按照填写的域名去处理，如果api_base为空，就默认是老的forkway的代理
            billing_server = 'https://billing.forkway.cn'
            try:
                if api_base:
                    billing_server = '.'.join(['https://billing'] + api_base.split('/')[2].split('.')[1:])
            except Exception as e:
                logging.error(e)
            url = '{}/tenant?access_token={}'.format(billing_server, api_key)
            return await self.get_amount_by_billing_server(url)
        except Exception as e:
            logging.error(e)
            return {}

    @stalecache(expire=10)
    async def get_amount_by_billing_server(self, url):
        try:
            response = await AsyncHTTPClient().fetch(url)
            return json.loads(response.body.decode())
        except Exception as e:
            logging.error(e)
            return {}

    async def get_resource_price(self, resource_id):
        models = self.session.query(Model).filter(
            Model.resource_id == resource_id,
            Model.status == 0,
        ).all()
        return [row2dict(i) for i in models]

    async def action(self, tenant_id, resource_id, action):
        if action == 'start':
            status = 2
        else:
            status = 1
        target_id = self.session.query(TenantResource.id).filter(
            TenantResource.tenant_id == tenant_id,
            TenantResource.resource_id == resource_id,
        ).limit(1).scalar()
        if target_id:
            self.session.begin_nested()
            self.session.query(TenantResource).filter(
                TenantResource.id == target_id,
            ).update(dict(
                status=status,
            ), synchronize_session=False)
            self.session.commit()
        else:
            self.session.begin_nested()
            self.session.add(TenantResource(
                id=ObjID.new_id(),
                tenant_id=tenant_id,
                resource_id=resource_id,
                status=status,
            ))
            self.session.commit()

    async def update_tenant_resource(self, tenant_id, resource_id, api_key, api_base, extra=dict()):
        # 直接就是启动状态
        status = 2
        target_id = self.session.query(TenantResource.id).filter(
            TenantResource.tenant_id == tenant_id,
            TenantResource.resource_id == resource_id,
        ).limit(1).scalar()
        if target_id:
            self.session.begin_nested()
            self.session.query(TenantResource).filter(
                TenantResource.id == target_id,
            ).update(dict(
                status=status,
                api_key=api_key,
                api_base=api_base,
                extra=extra,
            ), synchronize_session=False)
            self.session.commit()
        else:
            self.session.begin_nested()
            self.session.add(TenantResource(
                id=ObjID.new_id(),
                tenant_id=tenant_id,
                resource_id=resource_id,
                status=status,
                api_key=api_key,
                api_base=api_base,
                extra=extra,
            ))
            self.session.commit()


class AppModel(MysqlModel):

    async def get_application_support_bot(self, application_id):

        query = self.session.query(
            distinct(Bot.component)
        ).join(
            ApplicationSupportBot,
            Bot.id == ApplicationSupportBot.bot_id,
        ).filter(
            ApplicationSupportBot.application_id == application_id,
            Bot.status == 0,
        )

        return [bot_component for bot_component, in query]

    async def get_application_category(self, page, size, lang='zh_CN'):
        query = self.session.query(ApplicationCategory).filter(
            ApplicationCategory.status == 0
        ).order_by(
            ApplicationCategory.created.desc(),
        )
        total = await self.query_total(query)
        return [row2dict(i, lang=lang) for i in await self.query_one_page(query, page, size)], total

    def get_application_with_tenant(self, tenant_id):
        class ApplicationWithTenant(Application):
            tenant_status = column_property(
                select(AppInstance.status).where(and_(
                    AppInstance.application_id == Application.id,
                    AppInstance.tenant_id == tenant_id,
                    AppInstance.status > 0,
                )).limit(1)
            )
            support_bot = column_property(
                select(func.group_concat(Bot.platform)).where(and_(
                    Bot.id == ApplicationSupportBot.bot_id,
                    ApplicationSupportBot.application_id == Application.id,
                    ApplicationSupportBot.status == 0,
                ))
            )
        return ApplicationWithTenant

    async def get_application_list(self, tenant_id, category_id, keyword, page, size, lang='zh_CN'):
        ApplicationWithTenant = self.get_application_with_tenant(tenant_id)

        query = self.session.query(ApplicationWithTenant).filter(
            Application.category_id == category_id if category_id else True,
            Application.title.like("%{}%".format(keyword)) if keyword else True,
            Application.show == 1,
            Application.status == 0
        ).order_by(
            Application.sorted.asc(),
            Application.created.desc(),
        )
        total = await self.query_total(query)
        if total == 0:
            return [], 0
        return [self.format_application_item(i, lang=lang) for i in await self.query_one_page(query, page, size)], total

    def format_application_item(self, item, lang='zh_CN'):
        res = row2dict(item)
        if lang != 'zh_CN':
            res['title'] = res['title_en']
            res['description'] = res['description_en']
            res['problem'] = res['problem_en']
            res['manual'] = res['manual_en']
            res['video'] = res['video_en']
        res['support_bot'] = (res.get('support_bot') or '').split(',')
        return res


    async def get_application_list_all(self, lang='zh_CN'):
        # 获取全部应用列表，无需登录
        class ApplicationWithBot(Application):
            support_bot = column_property(
                select(func.group_concat(Bot.platform)).where(
                    and_(
                        Bot.id == ApplicationSupportBot.bot_id,
                        ApplicationSupportBot.application_id == Application.id,
                        ApplicationSupportBot.status == 0,
                    )
                )
            )

        query = (
            self.session.query(ApplicationWithBot)
            .filter(
                Application.show == 1,
                Application.status == 0,
            )
            .order_by(
                Application.id.asc(),
                Application.id.desc(),
            )
        )

        return [self.format_application_item(i, lang=lang) for i in await self.query_all(query)]


    async def get_application_detail(self, tenant_id, application_id, lang='zh_CN'):
        # 没有登录的时候，tenant_id为空，不需要tenant_status字段，直接使用原始的Application作为查询目标
        query_target = self.get_application_with_tenant(tenant_id) if tenant_id else Application
        app = self.session.query(query_target).filter(
            Application.id == application_id,
            Application.status == 0,
        ).first()
        if not app:
            raise NotFound()
        # // 查询类型
        supportBot = await self.get_application_support_bot(application_id)
        out = self.format_application_item(app, lang=lang)
        out['supportBot'] = supportBot
        return out

    async def get_tenant_application_list(self, tenant_id, category_id, keyword, page, size, lang='zh_CN'):
        ApplicationWithTenant = self.get_application_with_tenant(tenant_id)
        AppInstance1 = aliased(AppInstance)

        query = self.session.query(ApplicationWithTenant).join(
            AppInstance1,
            AppInstance1.application_id == Application.id,
        ).filter(
            # 关联一下已经购买的，并且对当前租户进行筛选
            AppInstance1.tenant_id == tenant_id,
            Application.category_id == category_id if category_id else True,
            # 这里按照title进行搜索更合理
            Application.title.like("%{}%".format(keyword)) if keyword else True,
            Application.show == 1,
            Application.status > -1,
        ).order_by(
            Application.sorted.asc(),
            Application.created.desc(),
        )
        total = await self.query_total(query)
        if total == 0:
            return [], 0
        return [self.format_application_item(i, lang=lang) for i in await self.query_one_page(query, page, size)], total

    async def action(self, tenant_id, application_id, action, lang='zh_CN', multi=False, name='', description=''):
        if multi and action == 'buy':
            # 重新购买
            target_id = ObjID.new_id()
            app = self.session.query(Application).filter(Application.id == application_id).first()
            self.session.begin_nested()
            title = app.title if lang == 'zh_CN' else app.title_en
            desc = app.description if lang == 'zh_CN' else app.description_en
            self.session.add(AppInstance(
                id=target_id,
                tenant_id=tenant_id,
                application_id=application_id,
                name=name or title,
                description=description or desc,
                icon=app.icon,
                status=1,
            ))
            self.session.commit()
            return target_id

        if action == 'deploy':
            status = 2
        else:
            status = 1
        target_id = self.session.query(AppInstance.id).filter(
            AppInstance.tenant_id == tenant_id,
            AppInstance.application_id == application_id,
        ).limit(1).scalar()
        if target_id:
            self.session.begin_nested()
            self.session.query(AppInstance).filter(
                AppInstance.id == target_id,
            ).update(dict(
                status=status,
            ), synchronize_session=False)
            self.session.commit()
        else:
            target_id = ObjID.new_id()
            app = self.session.query(Application).filter(Application.id == application_id).first()
            self.session.begin_nested()
            self.session.add(AppInstance(
                id=target_id,
                tenant_id=tenant_id,
                application_id=application_id,
                name=app.title if lang == 'zh_CN' else app.title_en,
                description=app.description if lang == 'zh_CN' else app.description_en,
                icon=app.icon,
                status=status,
            ))
            self.session.commit()
        return target_id


class AppSettingModel(MysqlModel):
    """应用配置"""

    async def get_instance(self, tenant_id, user_id, application_id='', instance_id=''):
        if not application_id and not instance_id:
            raise NotFound('参数错误')
        instance = self.session.query(AppInstance).filter(
            AppInstance.tenant_id == tenant_id,
            AppInstance.application_id == application_id if application_id else True,
            AppInstance.id == instance_id if instance_id else True,
        ).first()
        if not instance:
            raise NotFound('找不到应用')
        return instance

    async def _get_support_resource(self, tenant_id, user_id, application_id='', instance_id='', lang='zh_CN'):
        try:
            instance = await self.get_instance(
                tenant_id, user_id,
                application_id=application_id,
                instance_id=instance_id
            )
            application_id = instance.application_id
        except Exception as e:
            logging.error(e)

        TenantResource1 = aliased(TenantResource)

        class ResourceWithModel(Resource):
            models = relationship(
                Model,
                primaryjoin=and_(
                    Model.resource_id == Resource.id,
                    Model.status == 0,
                ),
                order_by=Model.price.asc(),
            )

            tenant_resource = relationship(
                TenantResource1,
                primaryjoin=and_(
                    TenantResource1.resource_id == Resource.id,
                    TenantResource1.tenant_id == tenant_id,
                ),
                uselist=False,
            )

        resources = self.session.query(ResourceWithModel).options(
            joinedload(ResourceWithModel.models),
            joinedload(ResourceWithModel.tenant_resource),
        ).join(
            TenantResource,
            TenantResource.resource_id == Resource.id,
        ).join(
            ApplicationSupportResource,
            ApplicationSupportResource.resource_id == Resource.id
        ).filter(
            TenantResource.tenant_id == tenant_id,
            TenantResource.status > -1,
            ApplicationSupportResource.application_id == application_id,
            ApplicationSupportResource.status == 0,
        ).all()
        return resources

    async def get_support_resource(self, tenant_id, user_id, application_id='', instance_id='', lang='zh_CN'):
        resources = await self._get_support_resource(tenant_id, user_id, application_id, instance_id, lang=lang)
        return [self.format_resource_item_for_app(i, lang) for i in resources]

    def format_resource_item_for_app(self, item, lang):
        res = row2dict(item, lang=lang)
        # 新版本使用tenant_resource.extra.model过滤一下列表
        model_names = [i['value'] for i in item.tenant_resource.extra.get('model', [])] if item.tenant_resource else []
        if len(model_names) > 0:
            res['models'] = [m for m in res['models'] if m['name'] in model_names]
        return res

    async def get_support_resources(self, tenant_id, user_id, application_id='', instance_id='', lang='zh_CN'):
        resources = await self._get_support_resource(tenant_id, user_id, application_id, instance_id, lang=lang)
        # logging.info('support_resources: %r', resources)
        all_scenes = self.session.query(ApplicationSupportResource).filter(
            ApplicationSupportResource.application_id == application_id,
            ApplicationSupportResource.resource_id.in_([r.id for r in resources]),
            ApplicationSupportResource.scene != '',
            ApplicationSupportResource.status == 0,
        ).order_by(
            ApplicationSupportResource.sorted.asc(),
        ).all()
        # 按 scene 字段进行分组
        scenes = [list(group) for _, group in groupby(all_scenes, key=lambda x: x.scene)]
        items = []
        for scene in scenes:
            s0 = scene[0]
            items.append({
                'scene': s0.scene,
                'category': s0.category,
                'title': _(s0.title, lang=lang),
                'tip': _(s0.tip, lang=lang),
                'required': bool(s0.required),
                'resource': self.get_resource_by_scene_and_category([r for r in resources if r.id in [s.resource_id for s in scene]], s0.category, lang=lang),
            })
        return items

    def get_resource_by_scene_and_category(self, scene_resources, category, lang='zh_CN'):
        result = []
        for resource in scene_resources:
            item = row2dict(resource, lang=lang)
            item['models'] = [row2dict(model, lang=lang) for model in resource.models if model.category == category or category == ModelCategory.All.value]
            model_names = [i['value'] for i in resource.tenant_resource.extra.get('model', [])] if resource.tenant_resource else []
            if len(model_names) > 0:
                item['models'] = [m for m in item['models'] if m['name'] in model_names]
            if len(item['models']) > 0:
                result.append(item)
        return result

    async def get_support_bots(self, tenant_id, user_id, instance_id='', application_id='', lang='zh_CN'):
        instance = await self.get_instance(
            tenant_id, user_id,
            application_id=application_id,
            instance_id=instance_id
        )

        class BotWithTenant(Bot):
            # 当前有没有配置，使用AppInstanceBotInstance记录展示
            tenant_status = column_property(
                select(AppInstanceBotInstance.status).where(and_(
                    AppInstanceBotInstance.instance_id == instance.id,
                    AppInstanceBotInstance.bot_instance_id == BotInstance.id,
                    BotInstance.bot_id == Bot.id,
                    BotInstance.tenant_id == tenant_id,
                    AppInstanceBotInstance.status == 0,
                    BotInstance.status == 0,
                )).limit(1)
            )

        # 支持的机器人只需要查ApplicationSupportBot表关联一下就可以了
        bots = self.session.query(BotWithTenant).join(
            ApplicationSupportBot,
            ApplicationSupportBot.bot_id == Bot.id
        ).filter(
            ApplicationSupportBot.application_id == instance.application_id,
            ApplicationSupportBot.status == 0,
        ).all()
        return [row2dict(i, lang=lang) for i in bots]

    async def get_setting_by_application_id(self, tenant_id, user_id, application_id='', instance_id=''):
        """
        后面将PolicyModel里面校验权限的地方，改成使用get_setting_by_application_id获取权限，再循环调用
        """
        instance = await self.get_instance(
            tenant_id, user_id,
            application_id=application_id,
            instance_id=instance_id,
        )
        resource_ids = instance.extra.get('resource_ids', {})
        # 旧的单资源没有resource_ids，那么可以查到第一个scene进行填充
        if not resource_ids:
            resource_scene = self.session.query(ApplicationSupportResource.scene).filter(
                ApplicationSupportResource.application_id == instance.application_id,
                ApplicationSupportResource.resource_id == instance.resource_id,
                ApplicationSupportResource.scene != '',
                ApplicationSupportResource.status == 0,
            ).limit(1).scalar()
            if resource_scene:
                resource_ids[resource_scene] = instance.resource_id
        # 新的配置在TenantResource保存了model字段放了一个列表，需要从这个里面过滤模型列表
        # tenant_resource = self.session.query(TenantResource).filter(
        #     TenantResource.resource_id.in_(resource_ids.values()) if resource_ids else True,
        #     TenantResource.resource_id == instance.resource_id if not resource_ids else True,
        #     TenantResource.tenant_id == tenant_id,
        # ).order_by(
        #     TenantResource.modified.desc()
        # ).first()
        tenant_resources = self.session.query(TenantResource).filter(
            TenantResource.resource_id.in_(resource_ids.values()) if resource_ids else True,
            TenantResource.resource_id == instance.resource_id if not resource_ids else True,
            TenantResource.tenant_id == tenant_id,
            ).order_by(
            TenantResource.modified.desc()
        ).all()
        prompt_id = [pid for pid, in self.session.query(AppInstancePrompt.prompt_id).filter(
            AppInstancePrompt.instance_id == instance.id,
            AppInstancePrompt.status == 0,
        ).all()]
        sensitive_id = [sid for sid, in self.session.query(AppInstanceSensitive.sensitive_id).filter(
            AppInstanceSensitive.instance_id == instance.id,
            AppInstanceSensitive.status == 0,
        ).all()]
        # model_names = [i['value'] for i in tenant_resource.extra.get('model', [])] if tenant_resource else []
        model_names = []
        for resource in tenant_resources:
            for i in resource.extra.get('model', []):
                model_names.append(i['value'])
        models = self.session.query(Model).filter(
            Model.resource_id.in_(resource_ids.values()) if resource_ids else True,
            Model.resource_id == instance.resource_id if not resource_ids else True,
            Model.status == 0,
            Model.name.in_(model_names) if len(model_names) > 0 else True,
        ).order_by(
            Model.price.asc()
        ).all()
        if resource_ids:
            # 筛选对应 resource_id category 的 models
            scenes = self.session.query(
                ApplicationSupportResource.resource_id,
                ApplicationSupportResource.category,
                ApplicationSupportResource.scene
            ).filter(
                ApplicationSupportResource.application_id == instance.application_id,
                ApplicationSupportResource.resource_id.in_(resource_ids.values()),
                ApplicationSupportResource.scene.in_(resource_ids.keys()),
                ApplicationSupportResource.status == 0,
            ).all()
            _models = []
            for m in models:
                for s in scenes:
                    if m.resource_id == resource_ids[s.scene] and (m.category == s.category or s.category == ModelCategory.All.value):
                        _models.append(m)
                        # 匹配到 resource_id 就停止
                        break
            models = _models
        policies = {i.model_id: row2dict(i) for i in self.session.query(Policy).filter(
            Policy.instance_id == instance.id,
        ).all()}

        user_permission = [{
            'resource_id': m.resource_id,
            'model_id': m.id,
            'name': m.name,
            'allow_users': policies.get(m.id, {}).get('allow_users', []),
            'deny_users': policies.get(m.id, {}).get('deny_users', []),
        } for m in models]
        group_permission = [{
            'resource_id': m.resource_id,
            'model_id': m.id,
            'name': m.name,
            'allow_groups': policies.get(m.id, {}).get('allow_groups', []),
            'deny_groups': policies.get(m.id, {}).get('deny_groups', []),
        } for m in models]

        app_name = self.session.query(Application.name).filter(Application.id == instance.application_id).limit(1).scalar()
        default_prompt = KNOWLEDGE_DEFAULT_PROMPT if instance.name in ['知识库应用', 'Messenger'] else ''
        return {
            'resource_id': instance.resource_id,
            'resource_ids': resource_ids,
            'user_permission': user_permission,
            'group_permission': group_permission,
            'prompt_id': prompt_id,
            'sensitive_id': sensitive_id,
            'model_id': instance.extra.get('model_id', ''),
            'collection_id': instance.extra.get('collection_id', []),
            'chat_history': instance.extra.get('chat_history', 'enable'),
            **({'web_search': instance.extra.get('web_search', 'enable')} if app_name in ['openai', 'azure'] else {}),
            'prompt': instance.extra.get('prompt', default_prompt) or default_prompt,
        }

    async def save_setting_by_application_id(
        self, tenant_id, user_id, application_id,
        resource_id,
        prompt_id, sensitive_id,
        user_permission, group_permission,
        # 后面这两个配置项是知识库应用
        collection_id, prompt, chat_history, web_search,
        instance_id='',
        model_id='',
        resource_ids=dict(),
    ):
        """这里对应前一个接口返回的数据其中prompt_id,sensitive_id,user_permission,group_permission都是数组"""
        instance = await self.get_instance(
            tenant_id, user_id,
            application_id=application_id,
            instance_id=instance_id,
        )
        self.session.begin_nested()
        # 1. 更新resource_id，以及status
        self.session.query(AppInstance).filter(
            AppInstance.id == instance.id,
        ).update(dict(
            status=2,
            resource_id=resource_id if not resource_ids else list(resource_ids.values())[0],  # 还是更新到旧的单资源
            extra=dict(
                collection_id=collection_id,
                prompt=prompt,
                chat_history=chat_history,
                web_search=web_search,
                model_id=model_id,
                resource_ids=resource_ids or {},
            ),
        ), synchronize_session=False)
        # 2. 更新prompt_id以及sensitive_id（这里的配置直接先移除，再插入）
        self.session.query(AppInstancePrompt).filter(
            AppInstancePrompt.instance_id == instance.id,
        ).delete(synchronize_session=False)
        for i in prompt_id:
            self.session.add(AppInstancePrompt(
                id=ObjID.new_id(),
                instance_id=instance.id,
                prompt_id=i,
            ))
        self.session.query(AppInstanceSensitive).filter(
            AppInstanceSensitive.instance_id == instance.id,
        ).delete(synchronize_session=False)
        for i in sensitive_id:
            self.session.add(AppInstanceSensitive(
                id=ObjID.new_id(),
                instance_id=instance.id,
                sensitive_id=i,
            ))
        # 3. 更新Policy(先移除，再插入)
        self.session.query(Policy).filter(
            Policy.instance_id == instance.id,
        ).delete(synchronize_session=False)
        policies = {}
        for i in user_permission:
            if i['model_id'] not in policies:
                policies[i['model_id']] = {
                    'allow_users': [], 'deny_users': [],
                    'allow_groups': [], 'deny_groups': [],
                }
            policies[i['model_id']]['allow_users'] = i.get('allow_users', [])
            policies[i['model_id']]['deny_users'] = i.get('deny_users', [])
        for i in group_permission:
            if i['model_id'] not in policies:
                policies[i['model_id']] = {
                    'allow_users': [], 'deny_users': [],
                    'allow_groups': [], 'deny_groups': [],
                }
            policies[i['model_id']]['allow_groups'] = i.get('allow_groups', [])
            policies[i['model_id']]['deny_groups'] = i.get('deny_groups', [])

        for model_id, item in policies.items():
            self.session.add(Policy(
                id=ObjID.new_id(),
                instance_id=instance.id,
                model_id=model_id,
                **item,
            ))

        self.session.commit()

    def get_bot_name_by_app_name(self, app_name, lang='zh_CN'):
        return _({
            'OpenAI聊天机器人': 'ChatGPT',
            'Midjoureny绘图机器人': 'Midjoureny',
            '文心一言聊天机器人': '文心一言',
            'Claude聊天机器人': 'Claude',
            '讯飞星火聊天机器人': '讯飞星火',
            'ChatGLM聊天机器人': 'ChatGLM',
            'MiniMax聊天机器人': 'MiniMax',
            'RWKV聊天机器人': 'RWKV',
            '抖音带货文案生成': '抖音带货文案助手',
        }.get(app_name, app_name), lang=lang)

    def get_bot_name_by_messenger_name(self, instance_id, lang='zh_CN'):
        messenger_name = self.session.query(Messenger.name).filter(
            Messenger.instance_id == instance_id,
        ).limit(1).scalar() or ''
        return _(messenger_name + ' AI 智能客服', lang=lang) if messenger_name else ''

    async def get_bot_setting_by_appid_and_bit_id(self, tenant_id, user_id, application_id, bot_id, lang='zh_CN', instance_id='', bot_name=''):
        instance = await self.get_instance(
            tenant_id, user_id,
            application_id=application_id,
            instance_id=instance_id,
        )
        # 数据结构是支持单个bot可以配置成多个bot_instance
        # 然后将app_instance与bot_instance关联起来
        # 这里需要先查一下AppInstanceBotInstance表，看有没有数据，没有的时候就创建bot_instance以及关联记录
        bot_instance_id = self.session.query(BotInstance.id).join(
            AppInstanceBotInstance,
            AppInstanceBotInstance.bot_instance_id == BotInstance.id,
        ).filter(
            AppInstanceBotInstance.instance_id == instance.id,
            AppInstanceBotInstance.status == 0,
            BotInstance.bot_id == bot_id
        ).limit(1).scalar()
        if not bot_instance_id:
            bot_instance_id = ObjID.new_id()
            # 这里兼容客服下面的AI机器人，需要将名字固定成messenger.name
            name = self.get_bot_name_by_messenger_name(instance.id, lang=lang) or self.get_bot_name_by_app_name(instance.name, lang=lang)
            self.session.begin_nested()
            self.session.add(BotInstance(
                id=bot_instance_id,
                tenant_id=tenant_id,
                user_id=user_id,
                bot_id=bot_id,
                name=name,
                description=_('你的飞书 AI 助手，助你提升工作效率', lang=lang),
                validation_token=VALIDATION_TOKEN,
                encript_key=ENCRIPT_KEY,
                agent_id='',
            ))
            self.session.flush()
            self.session.add(AppInstanceBotInstance(
                id=ObjID.new_id(),
                instance_id=instance.id,
                bot_instance_id=bot_instance_id,
            ))
            self.session.commit()

        bot_instance = self.session.query(BotInstanceWithPlatform).filter(
            BotInstance.id == bot_instance_id,
        ).first()
        if not bot_instance:
            raise NotFound('找不到机器人')
        result = row2dict(bot_instance, lang=lang)
        # 这里不再使用子域名
        callback_url = f'{options.SCHEMA}://{options.DOMAIN}/{bot_instance.platform}/{bot_instance_id}'
        result['callback_url'] = {
            'card': callback_url + '/card',
            'event': callback_url + '/event',
        }
        result['trusted_ip'] = options.WEWORK_PROXY_HOST
        result['logo'] = self.session.query(Application.logo).filter(
            Application.id == instance.application_id,
        ).limit(1).scalar() or 'https://mpic.forkway.cn/cdn/logo/feishuapp.png'
        return result

    async def save_bot_setting_by_appid_and_bit_id(
        self, tenant_id, user_id, application_id, bot_id,
        name, description,
        app_id, app_secret,
        encript_key, validation_token, agent_id, crop_id,
        instance_id='',
    ):
        instance = await self.get_instance(
            tenant_id, user_id,
            application_id=application_id,
            instance_id=instance_id,
        )
        bot_instance_id = self.session.query(BotInstance.id).join(
            AppInstanceBotInstance,
            AppInstanceBotInstance.bot_instance_id == BotInstance.id,
        ).filter(
            AppInstanceBotInstance.instance_id == instance.id,
            AppInstanceBotInstance.status == 0,
            BotInstance.bot_id == bot_id
        ).limit(1).scalar()
        if not bot_instance_id:
            raise NotFound('找不到机器人')

        # 如果是飞书，尝试获取机器人信息，包含头像以及open_id
        info = {}
        try:
            if app_id[:4] == 'cli_':  # 飞书机器人
                info = await Lark(app_id, app_secret).bot_info(app_id)
        except Exception as e:
            logging.error(e)

        self.session.begin_nested()
        self.session.query(BotInstance).filter(
            BotInstance.id == bot_instance_id,
        ).update(dict(
            name=name,
            description=description or '',
            app_id=app_id or '',
            app_secret=app_secret or '',
            encript_key=encript_key or ENCRIPT_KEY,
            validation_token=validation_token or VALIDATION_TOKEN,
            agent_id=agent_id or '',
            crop_id=crop_id or '',
            info=info,
        ), synchronize_session=False)
        self.session.commit()

    async def get_info_by_application_id(self, tenant_id, user_id, application_id, lang='zh_CN'):
        instance = await self.get_instance(
            tenant_id, user_id,
            application_id=application_id,
        )
        app_deploy = self.session.query(Application.deploy).filter(
            Application.id == application_id
        ).limit(1).scalar()
        res = row2dict(instance, lang=lang)
        res.update(deploy=app_deploy)
        return res

    async def get_info_by_instance_id(self, tenant_id, user_id, instance_id, lang='zh_CN'):
        instance = await self.get_instance(
            tenant_id, user_id,
            instance_id=instance_id,
        )
        return row2dict(instance, lang=lang)

    async def remove_app_by_instance_id(self, tenant_id, user_id, instance_id):
        self.session.begin_nested()
        self.session.query(AppInstance).filter(
            AppInstance.id == instance_id,
            AppInstance.tenant_id == tenant_id,
        ).update(dict(status=-1), synchronize_session=False)
        self.session.commit()


class KnowledgeModel(MysqlModel):
    async def get_knowledge(self, tenant_id, user_id, keyword, page, size, lang='zh_CN'):
        query = self.session.query(AppInstance).join(
            Application,
            Application.id == AppInstance.application_id,
        ).filter(
            AppInstance.tenant_id == tenant_id,
            AppInstance.name.like('%{}%'.format(keyword)) if keyword else True,
            AppInstance.status.in_([1, 2]),
            Application.name == 'Knowledge',
        ).order_by(
            AppInstance.created.desc(),
        )
        total = await self.query_total(query)
        return [row2dict(i, lang=lang) for i in await self.query_one_page(query, page, size)], total

    async def get_knowledge_app(self, lang='zh_CN'):
        app = self.session.query(Application).filter(
            Application.name == 'Knowledge',
            Application.status == 0,
        ).order_by(
            Application.modified.desc(),
        ).first()
        return row2dict(app)


