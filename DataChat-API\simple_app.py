#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版DataChat API服务器
用于本地开发环境
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
from pathlib import Path

app = Flask(__name__)
CORS(app)

# 配置
app.config['UPLOAD_FOLDER'] = os.environ.get('FLASK_UPLOAD_PATH', './data/files')
app.config['INDEX_FOLDER'] = os.environ.get('FILE_STORE_PATH', './data/search_index')

@app.route('/')
def index():
    return jsonify({
        "message": "ConnectAI DataChat API",
        "version": "1.0.0",
        "status": "running"
    })

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})

@app.route('/api/search', methods=['POST'])
def search():
    """简单的文档搜索功能"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        
        # 从文件存储中搜索
        index_file = Path(app.config['INDEX_FOLDER']) / 'documents.json'
        if index_file.exists():
            with open(index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
            
            # 简单的文本匹配搜索
            results = []
            for doc in index_data.get('documents', []):
                if query.lower() in doc.get('content', '').lower():
                    results.append(doc)
            
            return jsonify({
                "results": results[:10],  # 返回前10个结果
                "total": len(results)
            })
        
        return jsonify({"results": [], "total": 0})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload():
    """文件上传功能"""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        # 保存文件
        upload_dir = Path(app.config['UPLOAD_FOLDER'])
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = upload_dir / file.filename
        file.save(str(file_path))
        
        return jsonify({
            "message": "File uploaded successfully",
            "filename": file.filename,
            "path": str(file_path)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 启动DataChat API服务器...")
    print("服务将在 http://localhost:5000 运行")
    app.run(host='0.0.0.0', port=5000, debug=True)
