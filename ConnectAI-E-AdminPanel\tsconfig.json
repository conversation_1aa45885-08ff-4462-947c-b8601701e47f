{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "paths": {"~/*": ["./*"], "@/*": ["./src/*"]}, "types": ["vite/client", "node", "unplugin-icons/types/vue", "naive-ui/volar", "unplugin-vue-define-options/macros-global"]}, "exclude": ["node_modules", "dist"]}