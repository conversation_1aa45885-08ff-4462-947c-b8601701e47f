<template>
  <div class="">
    <n-card class="shadow-sm rounded-16px flex flex-col">
      <template #header>
        <div class="w-full flex justify-between items-center">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
              >Search</label
            >
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="t('message.prompt.srcjgjz')"
              />
              <button
                type="submit"
                class="text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ t('message.prompt.ss') }}
              </button>
            </div>
          </form>
          <div class="flex justify-start gap-4 items-center min-w-280px">
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAddPrompt"
            >
              <icon-akar-icons-circle-plus class="mr-2" />
              {{ t('message.prompt.xzcj') }}
            </button>

            <button
              type="button"
              class="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
              @click="handleExport"
            >
              <icon-akar-icons-cloud-download class="mr-2" />
              {{ t('message.prompt.pldc') }}
            </button>
          </div>
        </div>
      </template>
      <n-tabs type="line" class="flex-col-stretch h-full" pane-class="flex-1-hidden" @update:value="handleSwitchTab">
        <n-tab-pane v-for="item in cates.list" :key="item.id" :name="item.id" :tab="item.name">
          <loading-empty-wrapper class="min-h-350px" :loading="loading" :empty="empty">
            <PromptTable
              v-if="!loading && !empty"
              :data="data"
              :pagination-options="paginationOptions"
              :cates-options="catesOptions"
              @handle-edit="handleEdit"
              @handle-delete="handleDelete"
            />
          </loading-empty-wrapper>
        </n-tab-pane>
      </n-tabs>
    </n-card>
    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        :title="formValue.id ? t('message.prompt.bjcj') : t('message.prompt.xzcj')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form ref="formRef" :label-width="80" :model="formValue" :rules="rules">
          <n-form-item :label="t('message.prompt.lb')" path="category_id">
            <n-select
              v-model:value="formValue.category_id"
              :placeholder="t('message.prompt.qxz')"
              :options="catesOptions"
            />
          </n-form-item>
          <n-form-item :label="t('message.prompt.bt')" path="title">
            <n-input v-model:value="formValue.title" :placeholder="t('message.prompt.qsr')" />
          </n-form-item>
          <n-form-item :label="t('message.prompt.js')" path="description">
            <n-input v-model:value="formValue.description" :placeholder="t('message.prompt.qsr')" />
          </n-form-item>
          <n-form-item :label="t('message.prompt.nr')" path="content">
            <n-input v-model:value="formValue.content" :placeholder="t('message.prompt.qsr')" />
          </n-form-item>
          <n-form-item :label="t('message.prompt.jl')" path="example">
            <n-input v-model:value="formValue.example" :placeholder="t('message.prompt.qsr')" />
          </n-form-item>
        </n-form>
        <template #footer>
          <div class="flex justify-end">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              取消
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleConfirm"
            >
              确定
            </button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst, FormRules } from 'naive-ui';
import { useLoadingEmpty, usePagination } from '@/hooks';
import {
  createPrompt,
  deletePrompt,
  exportPromptList,
  fetchPromptCateList,
  fetchPromptList,
  updatePrompt
} from '~/src/service/api/prompt';
import { exportCSV } from '~/src/utils/common/export';
import PromptTable from './prompt-table.vue';
import { t } from '@/locales';

const { loading, startLoading, endLoading, empty, setEmpty } = useLoadingEmpty();
const data = ref<ApiPrompt.PromptTypes[]>([]);
const cates = reactive({
  list: [
    {
      id: '',
      name: t('message.my.all')
    }
  ]
});
const currentCateId = ref('');
const itemCount = ref<number>(0);
const { pagination, paginationOptions } = usePagination({ itemCount });

const catesOptions = ref<{ label: string; value: string }[]>([]);

const keyword = ref('');

const message = useMessage();
const formValue = ref<{
  id?: string;
  category_id: string;
  title: string;
  description: string;
  content: string;
  example: string;
}>({
  category_id: '',
  title: '',
  description: '',
  content: '',
  example: ''
});
const rules: FormRules = {
  category_id: {
    required: true,
    message: t('message.openai.qsr'),
    trigger: ['input']
  },
  title: {
    required: true,
    message: t('message.openai.qsr'),
    trigger: ['input']
  },
  description: {
    required: true,
    message: t('message.openai.qsr'),
    trigger: ['input']
  },
  content: {
    required: true,
    message: t('message.openai.qsr'),
    trigger: ['input']
  },
  example: {
    required: true,
    message: t('message.openai.qsr'),
    trigger: ['input']
  }
};
const formRef = ref<FormInst | null>(null);
const showModal = ref(false);

watch(pagination, () => {
  getPromptList();
});

async function getPromptCateList() {
  startLoading();
  try {
    const res = await fetchPromptCateList();
    cates.list.push(...(res.data?.data ?? []));

    catesOptions.value =
      res.data?.data.map((item) => {
        return {
          label: item.name,
          value: item.id
        };
      }) || [];
    endLoading();
  } catch (err) {
    console.error(err);
  }
}
async function getPromptList(keyword?: string) {
  startLoading();
  try {
    const res = await fetchPromptList({
      ...pagination,
      keyword,
      category_id: currentCateId.value
    });
    data.value = res.data?.data || [];
    itemCount.value = res.data!.total;
    endLoading();
    setEmpty(data.value.length === 0);
  } catch (err) {
    console.error(err);
  }
}

function handleSearch(e: Event) {
  e.preventDefault();

  getPromptList(keyword.value);
}
function handleSwitchTab(tabId: string) {
  currentCateId.value = tabId;
  getPromptList();
}

async function handleExport() {
  const config = {
    title: keyword.value,
    categoryId: currentCateId.value
  };
  // 直接下载
  window.open(`/api/prompt/export?keyword=${keyword.value}&category_id=${currentCateId.value}`);
  // try {
  //   const res = await exportPromptList({
  //     ...config
  //   });
  //   exportCSV(res.data!);
  // } catch (err) {
  //   console.error(err);
  // }
}

function handleAddPrompt() {
  showModal.value = true;
}

function handleClose() {
  showModal.value = false;
  formValue.value = {
    title: '',
    category_id: '',
    id: '',
    example: '',
    description: '',
    content: ''
  };
}

function handleConfirm(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      if (formValue.value.id) {
        const { id, ...data } = formValue.value;
        await updatePrompt({ id, data });
      } else {
        await createPrompt(formValue.value);
      }
      message.success(t('message.msg.bccg'));
      handleClose();
      getPromptList();
    }
  });
}

function handleEdit({ title, category_id, content, id, example, description }: ApiPrompt.PromptTypes) {
  formValue.value = {
    title,
    category_id,
    id,
    example,
    description,
    content
  };
  showModal.value = true;
}

async function handleDelete({ id }: ApiPrompt.PromptTypes) {
  await deletePrompt({ id });
  message.success(t('message.msg.sccg'));
  getPromptList();
}

onMounted(() => {
  getPromptCateList();
  getPromptList();
});
</script>
