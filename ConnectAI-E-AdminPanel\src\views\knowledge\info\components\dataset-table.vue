<template>
  <n-data-table scroll-x="1500" :columns="columns.filter(c => deleteable ? true : c.key !== 'actions')" :data="data" :bordered="false" />
  <div class="flex justify-end mt-4"><n-pagination v-bind="paginationOptions" /></div>
</template>

<script setup lang="tsx">
import { ref, toRefs } from 'vue';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { NButton, NDivider, NPopconfirm } from 'naive-ui';
import { t } from '@/locales';

const props = defineProps<{
  data: ApiDataset.Document[];
  paginationOptions: PaginationProps;
  deleteable: boolean;
}>();
const { data, paginationOptions, deleteable } = toRefs(props);
const emit = defineEmits(['handle-edit', 'handle-delete', 'handle-active']);
const popconfirm = ref();

const columns: DataTableColumns<ApiDataset.Document> = [
  {
    title: t('message.log.id'),
    key: 'id',
    width: 100,
    align: 'center',
    render(row, index) {
      return <div>{index + 1}</div>;
    }
  },

  {
    title: t('message.log.name'),
    key: 'name',
    resizable: true,
    align: 'center',
    render({ name }, index) {
      return (
        <div class="line-clamp-2" title={name}>
          {name}
        </div>
      );
    }
  },
  {
    title: t('message.log.path'),
    key: 'path',
    resizable: true,
    align: 'center',
    render({ name, path }, index) {
      return (
        <a target="_blank" href={path} class="line-clamp-2 text-blue-600" title={name}>
          {name}
        </a>
      );
    }
  },
  {
    title: t('message.log.type'),
    key: 'type',
    resizable: true,
    width: 100,
    align: 'center'
  },
  {
    title: t('message.log.cjsj'),
    key: 'created',
    width: 200,
    resizable: true,
    align: 'center',
    render({ created }) {
      return (new Date(created)).toISOString().substr(0, 10)
    }
  },
  {
    title: t('message.log.action'),
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render({ id, name }) {
      return (
        <NPopconfirm showIcon={false} negativeText={null} positiveText={null} ref={popconfirm}>
          {{
            action: () => (
              <div class="flex justify-center gap-1 items-center">
                {t('message.log.jjdelete')}『{name}』
                <NDivider vertical />
                <NButton
                  type={'error'}
                  tertiary
                  size={'small'}
                  onClick={() => [emit('handle-delete', { id }), popconfirm.value.setShow(false)]}
                >
                  {t('message.log.qrdelete')}
                </NButton>
              </div>
            ),
            trigger: () => (
              <NButton tertiary size={'small'}>
                {t('message.log.delete')}
              </NButton>
            )
          }}
        </NPopconfirm>
      );
    }
  }
];
</script>
