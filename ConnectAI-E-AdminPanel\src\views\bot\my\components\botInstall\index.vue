<template>
  <n-modal v-model:show="show" :mask-closable="false" :auto-focus="false" @after-leave="afterLeave">
    <div>
      <StepOne v-if="step === 1" v-model:step="step" v-model:data="appClientBotValues" :client="client" :app="app" />
      <StepThree
        v-else-if="step === 2"
        v-model:step="step"
        v-model:data="appSettingValues"
        :resource="resource"
        :resources="resources"
        :app="app"
      />
      <StepFour
        v-else-if="step === 3"
        v-model:data="appClientBotValues"
        :app-setting-values="appSettingValues"
        :app="app"
      />
    </div>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref, provide } from 'vue';
import { useVModel } from '@vueuse/core';
import StepOne from './stepOne.vue';
import StepThree from './stepThree.vue';
import StepFour from './stepFour/index.vue';

const props = defineProps<{
  show: boolean;
  client: ApiApp.AppClient[];
  resource: ApiApp.AppResource[];
  resources: ApiApp.AppResources[];
  app: ApiApp.Application;
  refreshList: () => void;
}>();

provide('close', close);
provide('refreshList', props.refreshList);

const step = ref(1);

const emit = defineEmits(['update:show', 'after-submit']);

const show = useVModel(props, 'show', emit);

const appSettingValues = ref<ApiApp.APPSetting>({
  resource_id: '',
  group_permission: [],
  user_permission: [],
  prompt_id: [],
  sensitive_id: [],
  collection_id: [],
  prompt: '',
  chat_history: 'enable'
});
const appClientBotValues = ref<ApiApp.AppClientBot>({
  tenant_id: '',
  bot_id: '',
  description: '',
  app_secret: '',
  validation_token: '',
  id: '',
  created: '',
  platform: 'feishu',
  tenant_name: '',
  name: '',
  app_id: '',
  encript_key: '',
  status: 0,
  modified: '',
  callback_url: {
    card: '',
    event: ''
  }
});

function close() {
  show.value = false;
}

function afterLeave() {
  step.value = 1;
}
</script>
