declare namespace PageRoute {
  /**
   * the root route key
   * @translate 根路由
   */
  type RootRouteKey = 'root';

  /**
   * the not found route, which catch the invalid route path
   * @translate 未找到路由(捕获无效路径的路由)
   */
  type NotFoundRouteKey = 'not-found';

  /**
   * the route key
   * @translate 页面路由
   */
  type RouteKey =
    | '403'
    | '404'
    | '500'
    | 'constant-page'
    | 'login'
    | 'not-found'
    | 'share-app'
    | 'bot'
    | 'bot_common'
    | 'bot_info'
    | 'bot_market'
    | 'bot_my'
    | 'dashboard'
    | 'dashboard_ai'
    | 'dashboard_pricing'
    | 'dashboard_seats'
    | 'document'
    | 'document_naive'
    | 'document_project-link'
    | 'document_project'
    | 'document_vite'
    | 'document_vue'
    | 'knowledge'
    | 'knowledge_app'
    | 'knowledge_appInfo'
    | 'knowledge_import'
    | 'knowledge_info'
    | 'knowledge_market'
    | 'knowledge_my'
    | 'log'
    | 'log_chat'
    | 'log_image'
    | 'log_word'
    | 'management'
    | 'management_auth'
    | 'management_role'
    | 'management_route'
    | 'management_user'
    | 'messenger'
    | 'messenger_info'
    | 'messenger_list'
    | 'prompt'
    | 'prompt_importexport'
    | 'prompt_market'
    | 'prompt_my';

  /**
   * last degree route key, which has the page file
   * @translate 最后一级路由(该级路有对应的页面文件)
   */
  type LastDegreeRouteKey = Extract<
    RouteKey,
    | '403'
    | '404'
    | '500'
    | 'constant-page'
    | 'login'
    | 'not-found'
    | 'share-app'
    | 'bot_common'
    | 'bot_info'
    | 'bot_market'
    | 'bot_my'
    | 'dashboard_ai'
    | 'dashboard_pricing'
    | 'dashboard_seats'
    | 'document_naive'
    | 'document_project-link'
    | 'document_project'
    | 'document_vite'
    | 'document_vue'
    | 'knowledge_app'
    | 'knowledge_appInfo'
    | 'knowledge_import'
    | 'knowledge_info'
    | 'knowledge_market'
    | 'knowledge_my'
    | 'log_chat'
    | 'log_image'
    | 'log_word'
    | 'management_auth'
    | 'management_role'
    | 'management_route'
    | 'management_user'
    | 'messenger_info'
    | 'messenger_list'
    | 'prompt_importexport'
    | 'prompt_market'
    | 'prompt_my'
  >;
}
