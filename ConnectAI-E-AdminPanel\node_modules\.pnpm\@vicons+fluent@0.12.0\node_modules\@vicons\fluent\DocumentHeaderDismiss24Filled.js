'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M17.746 2a2.25 2.25 0 0 1 2.245 2.096l.005.154v7.248a6.499 6.499 0 0 0-7.19 10.496H6.25a2.25 2.25 0 0 1-2.244-2.096L4 19.744V4.25a2.25 2.25 0 0 1 2.095-2.245L6.25 2h11.497zM8.504 5.004a1.5 1.5 0 0 0 0 2.999h6.998a1.5 1.5 0 0 0 0-3H8.504zM23 17.5a5.499 5.499 0 1 1-10.997 0a5.499 5.499 0 0 1 10.997 0zm-7.145-2.353a.5.5 0 1 0-.707.707l1.646 1.646l-1.646 1.646a.5.5 0 0 0 .707.707l1.646-1.646l1.646 1.646a.5.5 0 0 0 .707-.707l-1.646-1.646l1.646-1.646a.5.5 0 0 0-.707-.707l-1.646 1.646l-1.646-1.646z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DocumentHeaderDismiss24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
