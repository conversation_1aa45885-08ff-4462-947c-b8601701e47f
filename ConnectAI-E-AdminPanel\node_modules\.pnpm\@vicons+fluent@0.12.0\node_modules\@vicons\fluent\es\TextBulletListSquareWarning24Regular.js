import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5.25 3A2.25 2.25 0 0 0 3 5.25v13.5A2.25 2.25 0 0 0 5.25 21h5.8c.053-.26.147-.511.28-.744l.433-.756H5.25a.75.75 0 0 1-.75-.75V5.25a.75.75 0 0 1 .75-.75h13.5a.75.75 0 0 1 .75.75v7.754c.06.08.116.166.166.254L21 15.589V5.25A2.25 2.25 0 0 0 18.75 3H5.25zm12.25 9a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 1 0 0 1.5h4.466A2.496 2.496 0 0 1 17.5 12zm-3.163 3H11.25a.75.75 0 1 0 0 1.5h2.229l.858-1.5zM7.75 9.25a1 1 0 1 0 0-2a1 1 0 0 0 0 2zm3.5-1.75a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zM7.75 13a1 1 0 1 0 0-2a1 1 0 0 0 0 2zm0 3.75a1 1 0 1 0 0-2a1 1 0 0 0 0 2zm9.358-3.697a1.498 1.498 0 0 1 1.69.702l4.004 6.998c.205.36.246.768.145 1.139a1.493 1.493 0 0 1-1.444 1.107h-8.006c-.413 0-.787-.167-1.058-.44a1.495 1.495 0 0 1-.24-1.806l4.002-6.999a1.49 1.49 0 0 1 .907-.701zm.888 2.447a.5.5 0 0 0-1 0v3a.5.5 0 0 0 1 0v-3zm-.496 6a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListSquareWarning24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
