'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4 3.002a2 2 0 0 1 2-2h3.582a1.5 1.5 0 0 1 1.06.44l2.918 2.916a1.5 1.5 0 0 1 .44 1.06v7.58a2 2 0 0 1-2 2H8.666c.403-.284.765-.62 1.078-1H12a1 1 0 0 0 1-1V6.003h-2.5a1.5 1.5 0 0 1-1.5-1.5V2.002H6a1 1 0 0 0-1 1v2.02a5.48 5.48 0 0 0-1 .185V3.002zm6-.79v2.29a.5.5 0 0 0 .5.5h2.291L10 2.213zm0 8.288a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zm-4.854 2.353l.003.003a.5.5 0 0 0 .348.144h.006a.5.5 0 0 0 .35-.146l2-2a.5.5 0 0 0-.707-.708L6 11.293V8.5a.5.5 0 0 0-1 0v2.793l-1.146-1.147a.5.5 0 0 0-.708.708l2 2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DocumentArrowDown16Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
