<template>
  <div>
    <section class="bg-white dark:bg-gray-900 h-full no-scroll">
      <div class="py-4 px-4 mx-auto lg:py-8 lg:px-5 h-full relative" :class=" `max-w-screen-${products.length === 3  ? 'xl' : '2xl'}` ">
        <div class="max-w-screen-md mb-4 lg:mb-6 overflow-y-auto">
          <div class="flex justify-between">
            <h2 class="mb-3 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">
              {{ t('message.dashboard.title') }}
            </h2>
          </div>
          <a
            class="inline-flex absolute right-18 top-10 items-center justify-between px-1 py-1 pr-4 mb-5 text-sm text-gray-700 bg-gray-100 rounded-full dark:bg-gray-800 dark:text-white hover:bg-gray-200"
            role="alert"
            target="_blank"
            href="https://www.connectai-e.com/contact"
            ><span class="text-xs bg-blue-700 dark:bg-blue-600 rounded-full text-white px-4 py-1.5 mr-3">Contact</span
            ><span class="mr-2 text-sm font-medium">{{ t('message.dashboard.zfwt') }}</span>
            <svg
              class="w-2.5 h-2.5"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 6 10"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 9 4-4-4-4"
              ></path>
            </svg>
          </a>

          <p class="mb-3 font-light text-gray-500 sm:text-xl dark:text-gray-400">{{ t('message.dashboard.title2') }}</p>
          <div v-if="!isLark" class="flex items-center">
            <span
              class="text-base font-medium"
              :class="annual ? 'text-gray-500 dark:text-gray-400' : 'text-dark dark:text-white'"
            >
              {{ t('message.dashboard.month') }}
            </span>
            <!-- Switch Container -->
            <div>
              <label for="toggle-example" class="flex relative items-center mx-4 cursor-pointer">
                <input id="toggle-example" v-model="annual" type="checkbox" class="sr-only" />
                <div
                  class="w-11 h-6 bg-gray-200 rounded-full border-2 border-gray-200 toggle-bg dark:bg-gray-700 dark:border-gray-700"
                ></div>
              </label>
            </div>
            <span
              class="text-base font-medium"
              :class="annual ? 'text-dark dark:text-white' : 'text-gray-500 dark:text-gray-400'"
            >
              {{ t('message.dashboard.year') }}
              <span class="text-sm font-light">（{{ t('message.dashboard.yh') }}）</span>
            </span>
          </div>
        </div>
        <div :class="`mb-4 lg:mb-8 space-y-8 lg:grid lg:grid-cols-${products.length} md:gap-12 xl:gap-16 lg:space-y-0`">
          <!-- Pricing Card -->
          <div
            v-for="(product, i) in products"
            :key="i"
            class="flex flex-col items-center max-w-lg text-gray-900 dark:text-gray-400"
          >
            <h3 class="font-semibold text-gray-500 uppercase dark:text-gray-400">{{ product.name }}</h3>
            <div class="flex items-baseline mt-4 mb-2">
              <span
                v-if="product.category == 'privatization'"
                class="mr-2 text-4xl h-48px font-extrabold text-gray-900 dark:text-white"
                >{{ t('message.dashboard.lxwm') }}</span
              >
              <template v-else>
                <span v-if="product.real_price === 0" class="mr-2 text-4xl h-48px font-extrabold text-gray-900 dark:text-white">
                  {{ t('message.dashboard.freeuse') }}
                </span>
                <span v-else class="mr-2 text-5xl font-extrabold text-gray-900 dark:text-white">
                  <span class="text-xl">{{ isLark ? '$' : '¥' }}</span>
                  {{ product.real_price/100}}
                  <span class="text-xl">{{ isLark ? '/year' : '' }}</span>
                </span>
              </template>
            </div>
            <div class="mt-0 mb-2 text-xs font-light text-gray-500 dark:text-gray-400">
              <div v-if="product.category == 'privatization'">{{ t('message.dashboard.gcyh') }}</div>
              <div v-else-if="product.price == 0">{{ t('message.dashboard.seven') }}</div>
              <div v-else class="line-through">
                {{ t('message.dashboard.yj') }}: <span>{{ isLark ? '$' : '¥' }}</span>
                {{ product.price/100}}
                <!-- <span v-if="product.category == 'enterprise'" class=""></span> -->
              </div>
            </div>
            <a
              v-if="isLark || product.category == 'privatization'"
              href="https://www.connectai-e.com/contact"
              target="_blank"
              class="text-white min-w-200px bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900"
              >{{ t('message.dashboard.ljlx') }}</a
            >
            <a
              v-else-if="product.price == 0"
              href="https://www.connectai-e.com/contact"
              target="_blank"
              class="text-white min-w-200px bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900"
              >{{ t('message.dashboard.ljsq') }}</a
            >
            <a
              v-else
              href="#"
              class="text-white min-w-200px bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900"
              @click="handleOrder(product)"
              >{{ t('message.dashboard.buy') }}</a
            >
            <!-- <p class="font-light pt-4 text-gray-500 dark:text-gray-300 mb-2">
              {{ product }}
            </p> -->
            
            <!-- List -->
            <ul role="list" class="mb-6 mt-4 space-y-4 text-left">
              <li v-for="(item, i) in product.items" :key="i" class="flex items-center">
                <icon-local-check class="mr-2" v-if="item[0]" />
                <icon-local-close class="mr-2" v-else />
                <strong><span class="mr-2" v-if="item[2]">{{ item[2] }}</span></strong>
                <span>{{ item[1] }}</span>
                <!-- 解释token小弹框 -->
                <span v-if="i === 1" class="ml-1">
                  <n-popover trigger="hover">
                  <template #trigger>
                    <icon-local-info-out class="w-4 h-4"/>
                  </template>
                  <span>{{ t('message.dashboard.out1') }}<br />
                    {{ t('message.dashboard.out2') }}</span>
                  </n-popover>
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <n-modal display-directive="show" :show="!!orderProductId" :on-mask-click="() => (orderProductId = '')">
        <n-card style="width: 600px" :bordered="false" role="dialog" aria-modal="true">
          <n-tabs
            type="line"
            class="flex-col-stretch h-full"
            pane-class="flex-1-hidden"
            @update:value="handleSwitchTab"
          >
            <n-tab-pane key="pay" name="pay" :tab="t('message.dashboard.zxzf')" display-directive="show">
              <div class="space-y-2 lg:grid lg:grid-cols-1 md:gap-8 xl:gap-8 lg:space-y-0">
                <div class="flex flex-col max-w-lg text-gray-900 dark:text-gray-400 w-[250px] m-auto pt-2 pb-4">
                  <PayIframe v-if="!!orderProductId" :tenant-product-id="orderProductId" @pay-success="paySuccess" />
                  <div class="text-gray-900 text-center pt-2 dark:text-gray-200">
                    {{ t('message.dashboard.wechat') }}
                  </div>
                </div>
                <div v-if="0" class="flex flex-col max-w-lg text-gray-900 dark:text-gray-400">
                  <div class="w-[252px] h-[252px] text-center pt-[45%] border border-gray-300 dark:border-gray-600">
                    comming soon
                  </div>
                  <div class="text-gray-900 text-center py-[10px] dark:text-gray-200">
                    {{ t('message.dashboard.alipay') }}
                  </div>
                </div>
              </div>
              <div class="text-gray-500 text-center py-[10px] w-full flex justify-center">
                <div
                  class="bg-gray-100 w-fit rounded-md px-8 py-2 text-dark hover:bg-gray-200 cursor-pointer dark:text-white dark:bg-gray-500 dark:hover:bg-gray-600"
                >
                  {{ t('message.dashboard.openwechat') }}
                </div>
              </div>
              <div v-if="amount" class="text-base font-medium mb-2 text-center">
                {{ t('message.dashboard.price') }}<span class="text-2xl px-1">{{ amount }}</span
                >{{ t('message.dashboard.rmb') }}
              </div>
              <div class="text-xs pt-6 font-extralight text-center text-gray-500 dark:text-gray-400">
                * {{ t('message.dashboard.tip') }}
              </div>
            </n-tab-pane>
            <n-tab-pane key="company" name="company" :tab="t('message.dashboard.dgzz')">
              <div class="flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2">
                <span>{{ t('message.dashboard.zhmc') }}:</span><span>{{ t('message.dashboard.gsmc') }}</span>
              </div>
              <div class="flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2">
                <span>{{ t('message.dashboard.zhhm') }}:</span><span>416120100100331112</span>
              </div>
              <div class="flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2">
                <span>{{ t('message.dashboard.bankid') }}:</span><span>************</span>
              </div>
              <div class="flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2">
                <span>{{ t('message.dashboard.bank') }}:</span><span>{{ t('message.dashboard.bankname') }}</span>
              </div>
              <div class="space-y-2 lg:grid lg:grid-cols-2 md:gap-8 xl:gap-8 lg:space-y-0 my-4">
                <div class="flex flex-col max-w-lg text-gray-900 dark:text-gray-400">
                  <img class="w-[252px] h-[280px]" :src="weixinQrcode" />
                </div>
                <div class="flex flex-col max-w-lg text-gray-900 dark:text-gray-400">
                  <img class="w-[252px] h-[280px]" :src="feishuQrcode" />
                </div>
              </div>
              <div class="text-gray-500 text-center py-[10px]">{{ t('message.dashboard.payinfo') }}</div>
            </n-tab-pane>
          </n-tabs>
        </n-card>
      </n-modal>
    </section>
    <n-modal v-model:show="showModal" preset="dialog" :title="t('message.dashboard.zfcg')" :show-icon="false">
      <div>
        <div class="my-4">{{ t('message.dashboard.suc') }}</div>
        <div class="text-gray-500">
          {{ t('message.dashboard.info1') }}，<a
            href="https://www.connectai-e.com/contact"
            target="_blank"
            class="text-blue-600 hover:underline"
          >
            {{ t('message.dashboard.info2') }}
          </a>
        </div>
      </div>
      <template #action>
        <div>
          <button
            type="button"
            class="py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
            @click="handleGotoBotMarket"
          >
            {{ t('message.dashboard.aishop') }}
          </button>

          <button
            type="button"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
            @click="handleGototutorial"
          >
            {{ t('message.dashboard.ckjc') }}
          </button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useDialog, useMessage } from 'naive-ui';
import qs from 'qs';
import { fetchProduct, orderProduct } from '@/service';
import weixinQrcode from '@/assets/images/weixinma.png';
import feishuQrcode from '@/assets/images/feishuma.png';
import PayIframe from './pay-iframe.vue';
import { useRouterPush } from '@/composables';
import { t } from '@/locales';
import { isLark } from '@/utils';

const { routerPush } = useRouterPush();

const message = useMessage();
const annual = ref(true);
const data = ref({});
const orderProductId = ref('');
const payIframe = ref(false);
const amount = ref(0);
const showModal = ref(false);

const products = computed(() => {
  if (!data.value.products) {
    return [];
  }
  const itemmap = (data.value.products || []).reduce((s, i) => {
    s[i.id] = i;
    return s;
  }, {});
  const preview = itemmap[data.value.preview];
  const privatization = itemmap[data.value.privatization];
  const product = data.value.types[ isLark ? 0 : (annual.value ? 1 : 0)].items.map((i) => itemmap[i.id]);
  // console.log([preview].concat(product).concat(privatization).filter((i) => i))
  return [preview]
    .concat(product)
    .concat(privatization)
    .filter((i) => i)
    .sort((a, b) => a.real_price - b.real_price)
});

onMounted(() => {
  fetchProduct().then((res) => {
    // console.log('res', res)
    if (!res.error) {
      data.value = res.data.data;
    }
  });
});

const handleOrder = (product) => {
  // orderProductId.value = product.id;
  orderProduct(product.id).then((res) => {
    if (res.error) {
      return message.error(res.error.msg || t('message.dashboard.zfsb'));
    }
    // 控制弹窗
    const { tenant_product_id, amount: real_amount } = res.data.data;
    amount.value = real_amount;
    orderProductId.value = tenant_product_id;
    return null;
  });
};
const paySuccess = () => {
  // 关闭弹窗
  orderProductId.value = '';
  showModal.value = true;
};

function handleGototutorial() {
  open('https://connect-ai.feishu.cn/docx/TvbkdnA4Ao07dSxbUQWcLIofnOb', '_blank');
}

function handleGotoBotMarket() {
  routerPush({ name: 'bot_market' });
}
</script>

<style scoped></style>
