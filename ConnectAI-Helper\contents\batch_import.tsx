// @ts-nocheck
import { useCallback, useEffect, useMemo, useState, useRef } from "react";
import { sendToBackground } from "@plasmohq/messaging";
import { FEISHU_DOMAIN as domain } from '~utils/browser'
import semiCss from 'data-text:@douyinfe/semi-ui/dist/css/semi.min.css';
import cssText from "data-text:~/contents/batch_import.css";
import type {
  PlasmoCSConfig,
  PlasmoGetOverlayAnchor,
  PlasmoWatchOverlayAnchor
} from "plasmo";
import { Toast } from "@douyinfe/semi-ui";
import { Transfer, Modal, Button } from '@douyinfe/semi-ui';

// https://unpkg.com/@douyinfe/semi-ui@2.27.0/dist/css/semi.css
// https://unpkg.com/@douyinfe/semi-icons@latest/dist/css/semi-icons.css

const zIndex = 10000000000

const addCssToDocument = (css, id='semi') => {
  if (document.querySelector('#' + id)) {
    return
  }
  const style = document.createElement("style");
  console.log('css', css)
  style.innerText = css.replace('\n', '');
  style.id = id
  document.head.appendChild(style);
};

export const config: PlasmoCSConfig = {
  // css: ['batch_import.css'],
  matches: ["http://localhost:3200/*", "https://*.forkway.cn/*", "https://connect-ai-e.com/*","https://connectai-e.com/*"]
};

// Inject into the ShadowDOM
export const getStyle = () => {
  const style = document.createElement("style");
  style.textContent = cssText + '\n' // + semiCss;
  return style;
};

export const watchOverlayAnchor: PlasmoWatchOverlayAnchor = (
  updatePosition
) => {
  const interval = setInterval(() => {
    updatePosition();
  }, 10);
  return () => clearInterval(interval);
};

const notify = () => {
  Toast.warning({
    content: "当前租户没有更新应用的权限，请手动部署",
    duration: 3,
    zIndex
  });
};


export const getOverlayAnchor: PlasmoGetOverlayAnchor = async () =>
  document.querySelector(`.connectai-batch-import`) as any;


const BatchImport = ({ anchor }) => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  // const rect = useRef({});
  const [style, setStyle] = useState({});
  const [state, setState] = useState({});
  const [people, setPeople] = useState([]);
  const [user, setUser] = useState([]);
  const [data, setData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [larkUser, setLarkUser] = useState({});
  const [seats, setSeats] = useState(new Set());

  const containerRef = useRef()
  useEffect(() => {
    // 直接通过组件传递过来的anchor获取大小等信息，移除storage
    const r = anchor.element.getBoundingClientRect().toJSON();
    // console.log(rect.current.width);
    setTimeout(() => {
      setStyle({ width: r?.width, height: r?.height });
    }, 400);
  });
  useEffect(() => {
    setState(anchor.element.dataset);
    addCssToDocument(semiCss + `.semi-modal-body .semi-modal-close {transform: translate3d(-12px, -10px, 10px)!important;}`, 'semiCss')
    sendToBackground({ name: "user-info", body: { domain } }).then(({ user }) => {
      console.log("userInfo", user);
      if (user) {
        setLarkUser(user);
      }
    }).catch(e => {
      console.log("userInfo", e);
    });
    getSeat()
  }, []);
  // console.log('style', style, state, rect)
  
  const { innerHeight, innerWidth } = window

  const handleBatchImport = useCallback(async (e) => {
    console.log('handleBatchImport', e)
    sendToBackground({ name: "get-people", body: { domain } }).then(({ response }) => {
      console.log('getPeopleList', response)
      const temp = response.reduce((s, i) => {
        (s[i.depName]=s[i.depName]||[]).push({value: i.name, label: i.name, key: i.name, disabled: seats.has(i.name)});
        return s 
      }, {})
      const data = Object.entries(temp).map(([k, v]) => {
        const value = k === 'undefined' || !k ? '-' : k
        return {children: v, label: value, value, key: value}
      })
      console.log('data', temp, data)
      setPeople(response)
      setData(data)
      setVisible(true)
    }).catch(e => {
      console.log('getPeopleList', e)
    })
  }, [seats])

  const getSeat = () => {
    let url = '/api/seats?size=99999';
    if (window.location.hostname.indexOf("localhost") > -1) {
      url = "/api" + url;
    }
    return fetch(url, {
      method: "GET",
      mode: "cors",
      credentials: "include"
    }).then(res => res.json()).then(res => {
      console.log("getSeat", res);
      if (res.code == 0) {
        setSeats(new Set(res.data.map(i => i.name)))
      }
    });
  }

  const batchImport = useCallback(async (e) => {
    console.log('batchImport', user)
    const seats = people.filter(i => user.indexOf(i.name) > -1).map(i => ({ name: i.name, department: i.depName, telephone: "" }))
    if (seats.length == 0) {
      return Toast.warning({
        content: "请选择员工",
        duration: 3,
        zIndex
      });
    }
    let url = '/api/seats/import';
    if (window.location.hostname.indexOf("localhost") > -1) {
      url = "/api" + url;
    }
    fetch(url, {
      method: "POST",
      body: JSON.stringify({seats}),
      mode: "cors",
      credentials: "include"
    }).then(res => res.json()).then(res => {
      console.log("BatchImport", res);
      if (res.code == 0) {
        setVisible(false)
        Toast.success({
          content: "导入成功",
          duration: 3,
          zIndex
        });
      } else {
        Toast.warning({
          content: res.msg || '导入失败',
          duration: 3,
          zIndex
        });
      }
    });
  }, [user, people])
  
  return (
    <div>
      <button
        style={style}
        className="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 min-w-130px dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-450 flex"
        onClick={handleBatchImport}
      >
        <svg
          className="w-5 h-5 mr-2"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
          width="32"
          height="32"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M11.5 19h1v-1.85l3.5-3.5V9H8v4.65l3.5 3.5V19Zm-2 2v-3L6 14.5V9q0-.825.588-1.413T8 7h1L8 8V3h2v4h4V3h2v5l-1-1h1q.825 0 1.413.588T18 9v5.5L14.5 18v3h-5Zm2.5-7Z"
          />
        </svg>
        {larkUser.tenant ? '使用插件，快捷添加成员' : '飞书授权，快捷添加成员'}
      </button>
      <div ref={containerRef} style={{display: visible? 'block': 'none', position: 'fixed', width: innerWidth, height: innerHeight, top: 0, left: 0}} />
      <Modal
        visible={visible}
        zIndex={zIndex-1}
        onCancel={e => setVisible(false)}
        onOk={batchImport}
        closable={false}
        okButtonProps={{style: {backgroundColor: 'var(--semi-color-primary)' }}}
        // getPopupContainer={() => containerRef.current}
        // bodyStyle={{ backgroundColor: 'rgba(24,24,28)', borderRadius: 6 }}
      >
        <Transfer
          dataSource={data}
          type="treeList"
          value={user}
          onChange={setUser}
          className="batch-import-seats"
         ></Transfer>
      </Modal>
    </div>
  );
};

export default BatchImport;

