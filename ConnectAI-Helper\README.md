
<h1 align="center">ConnectA<PERSON>-<PERSON> Helper</h1>

<div align="center">

### A Chrome Extension for one-click deployment ConnectAI app

[![author][author-image]][author-url]
[![license][license-image]][license-url]
[![last commit][last-commit-image]][last-commit-url]



### Install

<a href="https://chrome.google.com/webstore/detail/%E9%A3%9E%E4%B9%A6%E5%BA%94%E7%94%A8%E5%8A%A9%E6%89%8B/ooadpplaohllpppemnkmnpnjkfdimjjp?utm_source=github"><img src="https://user-images.githubusercontent.com/64502893/231991498-8df6dd63-727c-41d0-916f-c90c15127de3.png" width="200" alt="Get ChatHub for Chromium"></a>
<a href="https://microsoftedge.microsoft.com/addons/detail/%E9%A3%9E%E4%B9%A6%E5%BA%94%E7%94%A8%E5%8A%A9%E6%89%8B/dnaejfpmadobnekocbcnkhlpfgabbjnh?utm_source=github"><img src="https://user-images.githubusercontent.com/64502893/231991158-1b54f831-2fdc-43b6-bf9a-f894000e5aa8.png" width="160" alt="Get ChatHub for Microsoft Edge"></a>

##

[Screenshot](#-screenshot) &nbsp;&nbsp;|&nbsp;&nbsp; [Features](#-features) &nbsp;&nbsp;|&nbsp;&nbsp; [Supported Bots](#-supported-bots) &nbsp;&nbsp;|&nbsp;&nbsp; [Manual Installation](#-manual-installation) &nbsp;&nbsp;|&nbsp;&nbsp; [Build from Source](#-build-from-source) &nbsp;&nbsp;|&nbsp;&nbsp; [Changelog](#-changelog)

[author-image]: https://img.shields.io/badge/author-river-blue.svg
[author-url]: https://github.com/leizhenpeng
[license-image]: https://img.shields.io/github/license/connectai-e/FeishuAppHelper?color=blue
[license-url]: https://github.com/connectai-e/FeishuAppHelper/blob/main/LICENSE
[last-commit-image]: https://img.shields.io/github/last-commit/connectai-e/FeishuAppHelper?label=last%20commit
[last-commit-url]: https://github.com/connectai-e/FeishuAppHelper/commits

</div>

## 📷 Screenshot

https://github.com/ConnectAI-E/FeishuAppHelper/assets/50035229/287eeb13-bed8-458f-8cd0-0e5dda9502df


## 🥷 Feature

 - one-click installed app in feishu
 - one-click installed app in lark
 - one-click installed app in dingtalk
 - get people list in feishu



