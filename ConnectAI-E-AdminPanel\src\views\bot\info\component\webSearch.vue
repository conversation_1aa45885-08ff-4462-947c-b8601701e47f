<template>
  <div
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flex justify-between items-center">
      <h3 class="mb-4 text-xl font-semibold dark:text-white">{{ $t('message.bot.websearch') }}</h3>
      <n-switch v-model:value="data.web_search" checked-value="enable" unchecked-value="disable"></n-switch>
    </div>

    <div class="mb-4 font-normal text-gray-500 dark:text-gray-400">
      {{ $t('message.bot.websearchinfo') }}
    </div>

    <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
    <button
      v-else
      type="button"
      class="inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
      @click="handleSave"
    >
      <icon-akar-icons-save class="mr-2" />
      {{ $t('message.bot.save') }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { updateAppSetting } from '@/service/api/app';

const message = useMessage();

const route = useRoute();

const emit = defineEmits(['change', 'update:data']);

const props = defineProps<{
  data: ApiApp.APPSetting;
  loading: boolean;
}>();

const data = useVModel(props, 'data', emit);

async function handleSave() {
  await updateAppSetting({ id: route.query.id as string, data: data.value });
  message.success(t('message.msg.bccg'));
}
</script>
