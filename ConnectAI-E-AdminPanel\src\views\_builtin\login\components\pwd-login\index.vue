<template>
  <n-form ref="formRef" :model="model" :rules="rules" size="large" :show-label="false">
    <n-form-item path="userName">
      <n-input v-model:value="model.userName" placeholder="请输入用户名" />
    </n-form-item>
    <n-form-item path="password">
      <n-input v-model:value="model.password" type="password" show-password-on="click" placeholder="请输入密码" />
    </n-form-item>
    <n-space :vertical="true" :size="24">
      <div class="flex-y-center justify-end">
        <n-checkbox v-model:checked="rememberMe">记住我</n-checkbox>
      </div>
      <n-button
        type="primary"
        size="large"
        :block="true"
        :round="true"
        :loading="auth.loginLoading"
        @click="handleSubmit"
      >
        确定
      </n-button>
      <!--      <div class="flex-y-center justify-between">-->
      <!--        <n-button class="flex-1" :block="true" @click="toLoginModule('code-login')">-->
      <!--          {{ loginModuleLabels['code-login'] }}-->
      <!--        </n-button>-->
      <!--        <div class="w-12px"></div>-->
      <!--        <n-button class="flex-1" :block="true" @click="toLoginModule('register')">-->
      <!--          {{ loginModuleLabels.register }}-->
      <!--        </n-button>-->
      <!--      </div>-->
    </n-space>
    <!--    <other-account @login="handleLoginOtherAccount" />-->
  </n-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { FormInst, FormRules } from 'naive-ui';
import { useAuthStore } from '@/store';
import { formRules } from '@/utils';

const auth = useAuthStore();
const { login } = useAuthStore();
// const { toLoginModule } = useRouterPush();

const formRef = ref<HTMLElement & FormInst>();
const model = reactive({
  userName: '<EMAIL>',
  password: 'a123456'
});

const rules: FormRules = {
  password: formRules.pwd
};

const rememberMe = ref(false);

async function handleSubmit() {
  await formRef.value?.validate();

  const { userName, password } = model;

  login(userName, password);
}

// function handleLoginOtherAccount(param: { userName: string; password: string }) {
//   const { userName, password } = param;
//   login(userName, password);
// }
</script>

<style scoped></style>
