#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConnectAI项目一键设置和启动脚本
适用于本地开发环境，无需Docker
"""

import os
import sys
import subprocess
import threading
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    ConnectAI 项目启动器                        ║
║                                                              ║
║  🚀 企联AI项目本地开发环境                                      ║
║  📦 无需Docker，使用本地服务                                   ║
║  🔧 自动配置和启动所有服务                                      ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查虚拟环境
    manager_venv = Path("manager-server/venv")
    datachat_venv = Path("DataChat-API/venv")
    
    if not manager_venv.exists():
        print("❌ manager-server虚拟环境不存在")
        return False
    
    if not datachat_venv.exists():
        print("❌ DataChat-API虚拟环境不存在")
        return False
    
    print("✅ 虚拟环境检查通过")
    return True

def start_service_in_thread(script_name, service_name):
    """在线程中启动服务"""
    def run_service():
        try:
            print(f"🚀 启动 {service_name}...")
            subprocess.run([sys.executable, script_name], check=True)
        except Exception as e:
            print(f"❌ {service_name} 启动失败: {e}")
    
    thread = threading.Thread(target=run_service, daemon=True)
    thread.start()
    return thread

def wait_for_service(url, service_name, timeout=30):
    """等待服务启动"""
    import urllib.request
    import urllib.error
    
    print(f"⏳ 等待 {service_name} 启动...")
    
    for i in range(timeout):
        try:
            urllib.request.urlopen(url, timeout=1)
            print(f"✅ {service_name} 已启动")
            return True
        except:
            time.sleep(1)
    
    print(f"❌ {service_name} 启动超时")
    return False

def show_service_info():
    """显示服务信息"""
    info = """
🌐 服务访问地址:
┌─────────────────────────────────────────────────────────────┐
│  服务名称              │  访问地址                           │
├─────────────────────────────────────────────────────────────┤
│  Manager Server       │  http://localhost:3000             │
│  DataChat API         │  http://localhost:5000             │
│  健康检查             │  http://localhost:5000/health      │
└─────────────────────────────────────────────────────────────┘

📁 数据存储位置:
├── ./data/connectai.db          (SQLite数据库)
├── ./data/files/                (上传文件)
├── ./data/search_index/         (搜索索引)
└── ./data/                      (其他数据)

🔧 开发说明:
• 本地环境使用SQLite替代MySQL
• 使用文件存储替代Elasticsearch
• 使用内存存储替代Redis
• 所有服务运行在本地端口

⚠️  注意事项:
• 确保端口3000和5000未被占用
• 数据存储在./data目录中
• 按Ctrl+C停止所有服务
"""
    print(info)

def install_node_guide():
    """显示Node.js安装指南"""
    guide = """
📦 前端服务需要Node.js环境

🔧 Node.js安装指南:

Windows:
1. 访问 https://nodejs.org/
2. 下载LTS版本 (推荐18.x或20.x)
3. 运行安装程序，按默认设置安装
4. 重启命令行工具

验证安装:
  node --version
  npm --version

安装完成后，运行以下命令安装前端依赖:
  cd ConnectAI-E-AdminPanel
  npm install
  npm run dev

前端服务将在 http://localhost:3000 运行
"""
    print(guide)

def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，请先完成环境设置")
        return False
    
    print("\n🎯 准备启动服务...")
    
    try:
        # 启动DataChat API (后台服务)
        datachat_thread = start_service_in_thread("start_datachat_api.py", "DataChat API")
        time.sleep(3)  # 等待服务启动
        
        # 检查DataChat API是否启动成功
        if wait_for_service("http://localhost:5000/health", "DataChat API", 10):
            print("✅ DataChat API 启动成功")
        
        # 显示服务信息
        show_service_info()
        
        # 显示Node.js安装指南
        install_node_guide()
        
        print("\n🚀 启动Manager Server...")
        print("按 Ctrl+C 停止所有服务")
        
        # 启动Manager Server (前台运行)
        subprocess.run([sys.executable, "start_manager_server.py"])
        
    except KeyboardInterrupt:
        print("\n⏹️  正在停止所有服务...")
        print("✅ 所有服务已停止")
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
