'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M8.25 9a.75.75 0 0 0-.75.75v4.5c0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75v-4.5a.75.75 0 0 0-.75-.75h-4.5zM9 13.5v-3h3v3H9zM2 7.75A4.75 4.75 0 0 1 6.75 3h10.5a4.75 4.75 0 0 1 3.5 7.961v7.789A2.25 2.25 0 0 1 18.5 21h-13a2.25 2.25 0 0 1-2.25-2.25v-7.789A4.735 4.735 0 0 1 2 7.75zM6.75 4.5a3.25 3.25 0 0 0-2.234 5.61a.75.75 0 0 1 .234.545v8.095c0 .414.336.75.75.75h10a.75.75 0 0 0 .75-.75v-7.692a.75.75 0 0 1 .328-.62A3.25 3.25 0 0 0 14.75 4.5h-8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'FoodToast24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
