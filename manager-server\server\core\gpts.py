import re
import logging
from io import StringIO


LINK_REG = re.compile('\[(.*)\]\((.*)\)')
SOURCE_REG = re.compile('【([0-9]+)†source】')


class GPTSResult(object):

    def __init__(self, content=''):
        self.data = StringIO(content)

    def write(self, data):
        self.data.write(data)

    def append(self, data):
        self.data.write(data)

    def content(self):
        value = self.data.getvalue()
        data = value.split('\n')
        count = 0
        status = ''
        for i, v in enumerate(data):
            if len(v) > 0 and (v[0] == '>' or '\r' == v[-1]):
                count = i + 1
                status = v

        content = '\n'.join(data[count:])
        header = []
        for line in data[:count]:
            m = LINK_REG.findall(line)
            if len(m) == 1:
                header.append(m[0])

        self.index = 0
        def repl(m):
            # print(m, m.group())
            title, link = header[self.index]
            self.index += 1
            return f'[{m.group()}]({link})'
        content = re.sub('【([0-9]+)†source】', repl, content)

        return content if content or status else value, status if not content else '', header

    def __str__(self):
        return str(self.content())


if __name__ == "__main__":
    def chunks(l, n):
        for i in range(0, len(l), n):
            yield l[i:i + n]

    result = '\n> search("Janice 青屹创投") \n\n> click(1) \n\n> quote_lines(44, 48)\r\nquote_lines(50, 50)\r\nquote_lines(52, 52)\r\nquote_lines(54, 54)\r\nquote_lines(56, 58) \n\n> [🌐青屹创投入选「上海产业园区金融伙伴计划」-电子工程专辑](https://www.eet-china.com/mp/a184622.html) \n\n **end-searching**\n\n\n> [🌐青屹创投入选「上海产业园区金融伙伴计划」-电子工程专辑](https://www.eet-china.com/mp/a184622.html) \n\n **end-searching**\n\n\n> [🌐青屹创投入选「上海产业园区金融伙伴计划」-电子工程专辑](https://www.eet-china.com/mp/a184622.html) \n\n **end-searching**\n\n\n> [🌐青屹创投入选「上海产业园区金融伙伴计划」-电子工程专辑](https://www.eet-china.com/mp/a184622.html) \n\n **end-searching**\n\n\n> [🌐青屹创投入选「上海产业园区金融伙伴计划」-电子工程专辑](https://www.eet-china.com/mp/a184622.html) \n\n **end-searching**\n\n\n> search("Janice 青屹创投 news") \nJanice 青屹创投, also known as Qingyi Venture Capital, is a direct investment fund under Qingtong Capital. Established in 2020, it focuses on investment opportunities in consumer, technology, and medical sectors. The fund maintains an entrepreneurial mindset in its investment strategy, emphasizing long-term value and aiming to be a loyal partner in entrepreneurs\' ventures.\n\nRecently, Qingyi Venture Capital was selected as one of the first 20 members of the "Shanghai Industrial Park Financial Partner Program" launched by the Shanghai Development Zone Association. This program aims to bridge financial and industrial barriers, enhancing financial services within industrial parks and their internal industries. The selection into this program is both an acknowledgment of Qingyi Venture Capital\'s past achievements and a motivation for future development. The firm believes in "using the investor\'s perspective for investment banking and the entrepreneur\'s mindset for investment." It focuses on enhancing its coverage and accumulation in important tracks and directions, improving investment banking capabilities, and creating positive cycles between its investment and investment banking activities.\n\nQingyi Venture Capital\'s investment research director, Li Ming, emphasized the importance of adhering to a long-term strategy and research-driven approach, even amidst external changes. The firm plans to actively collaborate with the Shanghai Industrial Park, combining policy drivers, park deployments, and project needs to provide targeted financial support. Through project collaborations and leveraging industrial park resources, Qingyi Venture Capital continues to empower entrepreneurial companies【9†source】【10†source】【11†source】【12†source】【13†source】.\n\nFor more information, you can visit https://bibigpt.co/redirect?url=https://www.eet-china.com/news/************.html.'

    result = '郑ZY：嗨，你好呀！今天过得怎么样？有没有想我呢？😊🍷'
    result = '2. 以下哪种情况，不属于机动车驾驶证累积记分的情形？\nA. 驾驶与准驾车型不符的机动车\nB. 机动车在高速公路上倒车\nC. 车辆逾期未检验\nD. 违反禁止标线指示\n\n请作答。'
    r = GPTSResult()
    for c in chunks(result, 130):
        r.write(c)
        print('----------------\n', r)




