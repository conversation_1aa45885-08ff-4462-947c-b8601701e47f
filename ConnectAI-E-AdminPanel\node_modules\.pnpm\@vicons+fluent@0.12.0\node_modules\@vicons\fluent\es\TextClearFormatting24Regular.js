import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.212 13.82c.146.12.33.18.55.18c.36 0 .609-.188.744-.563l1.074-2.92h4.831l1.083 2.92c.135.375.384.563.745.563c.22 0 .4-.06.541-.18a.587.587 0 0 0 .22-.465c0-.108-.031-.244-.093-.407L7.973 2.702c-.18-.468-.5-.702-.956-.702c-.485 0-.818.237-.998.71L2.093 12.948c-.062.163-.093.299-.093.407c0 .185.07.34.212.465zM8.98 9.27H5.02L6.975 3.9h.05L8.98 9.269zM13 13.105l1.748-1.748c-.23-.482-.344-1.056-.344-1.722c0-.93.227-1.682.681-2.256c.46-.578 1.05-.868 1.769-.868c.746 0 1.336.281 1.769.844c.356.463.566 1.061.629 1.793c.424.14.823.378 1.161.715l.286.286a7.47 7.47 0 0 0 .017-.514c0-1.32-.327-2.377-.982-3.173c-.654-.795-1.514-1.192-2.58-1.192a3.13 3.13 0 0 0-1.598.414c-.47.275-.841.657-1.112 1.144h-.032V2.755a.752.752 0 0 0-.203-.544a.653.653 0 0 0-.503-.211a.68.68 0 0 0-.511.21a.77.77 0 0 0-.195.545v10.35zm3.982 8.395h3.02a.75.75 0 1 1 0 1.5H14.5l.002-.007a1.94 1.94 0 0 1-1.208-.563l-2.724-2.724a1.95 1.95 0 0 1 .002-2.76l6.374-6.374a1.95 1.95 0 0 1 2.759-.001l2.724 2.723a1.95 1.95 0 0 1-.002 2.76L16.981 21.5zm1.026-9.867L13.85 15.79l3.36 3.36l4.157-4.157a.45.45 0 0 0 .001-.638l-2.724-2.724a.45.45 0 0 0-.637.002zM12.79 16.85l-1.157 1.157a.45.45 0 0 0-.001.637l2.724 2.724a.45.45 0 0 0 .637-.002l1.157-1.157l-3.36-3.36z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextClearFormatting24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
