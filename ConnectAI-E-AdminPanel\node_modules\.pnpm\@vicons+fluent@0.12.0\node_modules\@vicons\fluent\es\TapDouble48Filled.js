import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.5 17.5c0-6.075 4.925-11 11-11s11 4.925 11 11c0 1.474-.29 2.88-.816 4.164l.191.041a7.752 7.752 0 0 1 2.161.814c.622-1.552.964-3.245.964-5.019C37 10.044 30.956 4 23.5 4S10 10.044 10 17.5c0 2.644.76 5.11 2.074 7.193a7.341 7.341 0 0 1 2.536-.714A10.95 10.95 0 0 1 12.5 17.5zm20 0c0 1.333-.29 2.598-.81 3.736L30 20.874V17.5a6.5 6.5 0 1 0-13 .085v6.14A9 9 0 1 1 32.5 17.5zm-9-4.5a4.5 4.5 0 0 0-4.5 4.5v9.468l-1.87-.688c-3.138-1.155-6.553.828-7.105 4.127a1.94 1.94 0 0 0 1.118 2.084c7.293 3.287 10.395 6.377 11.476 8.69c.489 1.047 1.623 1.963 3.033 1.798l6.242-.731a4.25 4.25 0 0 0 3.604-3.094l2.295-8.347a5.75 5.75 0 0 0-4.338-7.146L28 22.49V17.5a4.5 4.5 0 0 0-4.5-4.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapDouble48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
