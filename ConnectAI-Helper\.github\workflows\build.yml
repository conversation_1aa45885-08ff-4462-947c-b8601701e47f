name: Publish Extension
on:
  workflow_dispatch:
  # release:
  #   types: [published]

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup pnpm
        uses: pnpm/action-setup@v2.2.4
        with:
          version: latest
      - name: Install deps
        run: pnpm i --frozen-lockfile
      - name: Build extension
        run: pnpm run zip:chrome
      - name: Build extension
        run: pnpm run zip:firefox
      - name: Browser Platform Publisher
        uses: PlasmoHQ/bpp@v3.5.0
        with:
          keys: ${{ secrets.BPP_KEYS }}
          chrome-file: build/chrome-mv3-prod.zip
          firefox-file: build/firefox-mv2-prod.zip
          verbose: true


