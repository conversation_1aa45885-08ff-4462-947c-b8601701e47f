import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M23.5 7.5A9 9 0 0 0 17 22.726v.427a7.26 7.26 0 0 0-2.951-.1A11.448 11.448 0 0 1 12 16.5C12 10.149 17.149 5 23.5 5S35 10.149 35 16.5a11.47 11.47 0 0 1-.822 4.277c-.1-.026-.2-.05-.303-.072l-2.185-.469A9 9 0 0 0 23.5 7.5zm0 4.5a4.5 4.5 0 0 0-4.5 4.5v9.468l-1.87-.688c-3.138-1.155-6.553.828-7.105 4.127a1.94 1.94 0 0 0 1.118 2.084c7.293 3.287 10.395 6.377 11.476 8.69c.489 1.047 1.623 1.963 3.033 1.798l6.242-.731a4.25 4.25 0 0 0 3.604-3.094l2.295-8.347a5.75 5.75 0 0 0-4.338-7.146L28 21.49V16.5a4.5 4.5 0 0 0-4.5-4.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapSingle48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
