#!/usr/bin/env python
# coding=utf-8
import asyncio
import json
import base64
import qrcode
import logging
from time import time
from io import BytesIO
from tornado.web import Finish
from tornado.options import options
from core.base_handler import (
    BaseHandler,
    arguments,
    authenticated,
)
from core.utils import ObjIDStr, TimeStr, row2dict, _
from core.redisdb import delete
from core.web import Web
from core.feishu import (
    FeishuMessageDiv,
    FeishuMessageCard,
    FeishuMessageNote,
    FeishuMessagePlainText,
    FeishuMessageText,
    FeishuImageMessage,
    FeishuFileMessage,
)
from .model import (
    MessengerModel,
    MessengerSession as Session,
)
from core.exception import PermissionDenied, NotFound
from modules.callback.model import FeishuModel


class MessengerInfoHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, messenger_id: ObjIDStr = '', model: MessengerModel = None):
        messenger = await model.get_messenger(
            self.session.tenant_id, self.session.id,
            messenger_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': row2dict(messenger),
        })


class MessengerAppHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, model: MessengerModel = None):
        know = await model.get_messenger_app(lang=self._get_locale())
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': know,
        })


class MessengerListHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(self, page: int = 1, size: int = 10, model: MessengerModel = None):
        messengers, total = await model.get_list(
            self.session.tenant_id, self.session.id,
            page, size,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': messengers,
            'total': total,
        })

    @authenticated
    @arguments
    async def post(
        self, name: str = '',
        description: str = '',
        platform: str = '',
        model: MessengerModel = None,
    ):
        await model.create_messenger(
            self.session.tenant_id, self.session.id,
            name, description, platform,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def put(
        self, messenger_id: ObjIDStr = '',
        chat_id: str = '', ai: int = '', 
        model: MessengerModel = None,
    ):
        if chat_id:
            await model.update_chat_id(
                self.session.tenant_id, self.session.id,
                messenger_id, chat_id,
            )
        elif ai in [0, 1]:
            await model.update_ai(
                self.session.tenant_id, self.session.id,
                messenger_id, ai,
            )
        elif name or description:
            await model.update_name(
                self.session.tenant_id, self.session.id,
                messenger_id, name, description,
            )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class MessengerInstanceHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        messenger_id: ObjIDStr = '',
        model: MessengerModel = None,
    ):
        app = await model.get_app_info_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': app,
        })


class MessengerBotHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        messenger_id: ObjIDStr = '',
        model: MessengerModel = None,
    ):
        setting = await model.get_bot_setting_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
            lang=self._get_locale(),
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': setting,
        })

    @authenticated
    @arguments
    async def post(
        self,
        messenger_id: ObjIDStr = '',
        app_id: str = '', app_secret: str = '',
        encript_key: str = '', validation_token: str = '',
        model: MessengerModel = None,
    ):
        await model.save_bot_setting_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
            app_id, app_secret,
            encript_key, validation_token,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class MessengerClientHandler(BaseHandler):

    @arguments
    async def get(
        self,
        messenger_id: ObjIDStr = '',
        model: MessengerModel = None,
    ):
        auth_token = self.request.headers.get('Authorization')
        if not self.current_user:
            # 外部请求，使用Authorization校验权限
            model.init_by_bot_id(messenger_id)  # 这里的messenger_id实际是bot_instance_id
            await model.check_access_token(auth_token)
        setting = await model.get_client_setting_by_messenger_id(
            messenger_id,
            lang=self._get_locale(),
            # 内部请求有current_user并且不会传Authorization
            current_user=self.current_user and not auth_token,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': setting,
        })

    @authenticated
    @arguments
    async def post(
        self,
        messenger_id: ObjIDStr = '',
        form: int = '', links: dict = list(),
        lang: str = options.DEFAULT_LOCALE,
        theme: dict = dict(),
        logo: str = '',
        welcome: str = '',
        api: int = '',
        model: MessengerModel = None,
    ):
        if form == '' and api == '':
            raise PermissionDenied()
        await model.save_client_setting_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
            form, links,
            lang, theme, logo, welcome,
            api,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def put(self, messenger_id: ObjIDStr = '', action: str = '', model: MessengerModel = None):
        if action == 'refresh_access_token':
            await model.refresh_access_token_by_messenger_id(
                self.session.tenant_id, self.session.id,
                messenger_id,
            )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class MessengerChatListHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        messenger_id: ObjIDStr = '',
        model: MessengerModel = None,
    ):
        try:
            chat = await model.get_chat_list_by_messenger_id(
                self.session.tenant_id, self.session.id,
                messenger_id,
            )
        except Exception as e:
            chat = []
            logging.error(e)
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': chat,
        })


class MessengerChatMemberHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        messenger_id: ObjIDStr = '',
        model: MessengerModel = None,
    ):
        member = await model.get_member_list_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': member,
        })


class MessengerSeatHandler(BaseHandler):

    @authenticated
    @arguments
    async def get(
        self,
        messenger_id: ObjIDStr = '',
        page: int = 1, size: int = 20,
        model: MessengerModel = None,
    ):
        seats, total = await model.get_seat_list_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
            page, size
        )
        self.finish({
            'code': 0,
            'msg': 'success',
            'data': seats,
            'total': total,
        })

    @authenticated
    @arguments
    async def post(
        self,
        messenger_id: ObjIDStr = '',
        open_id: str = '', name: str = '', 
        start_time: TimeStr = '', end_time: TimeStr = '',
        model: MessengerModel = None,
    ):
        await model.add_seat_by_messenger_id(
            self.session.tenant_id, self.session.id,
            messenger_id,
            open_id, name, start_time, end_time,
        )
        self.finish({
            'code': 0,
            'msg': 'success',
        })

    @authenticated
    @arguments
    async def put(
        self,
        messenger_id: ObjIDStr = '',
        seat_id: ObjIDStr = '',
        action: str = '',  # enable/disable/delete
        open_id: str = '', name: str = '', 
        start_time: TimeStr = '', end_time: TimeStr = '',
        model: MessengerModel = None,
    ):
        if action:
            await model.seat_action_by_messenger_id(
                self.session.tenant_id, self.session.id,
                messenger_id,
                seat_id, action,
            )
        else:
            await model.update_seat_by_messenger_id(
                self.session.tenant_id, self.session.id,
                messenger_id, seat_id,
                open_id, name, start_time, end_time,
            )
        self.finish({
            'code': 0,
            'msg': 'success',
        })


class MessengerCallbackHandler(BaseHandler):

    client = None
    chat_id = ''

    @arguments
    async def get(self, action: str = '', bot_id: ObjIDStr = '', visitor_id: str = '', nickname: str = '', telephone: str = '', model: MessengerModel = None, fsmodel: FeishuModel = None):
        logging.info("debug GET %r", [action, bot_id, visitor_id, self.request.body, list(self.request.headers.items())])
        try:
            if not visitor_id:
                raise PermissionDenied()

            fsmodel.init_by_bot_id(bot_id)
            model.init_by_bot_id(bot_id)
            if action == 'auth':
                # check access_token
                access_token = self.request.headers.get('Authorization') or self.get_cookie('access_token')
                await model.check_access_token(access_token)
            if action == 'sub':
                # 开启收集信息，那就需要姓名或者手机号
                if model.form and not (nickname or telephone):
                    logging.error("need nickname %r %r", bot_id, visitor_id)
                    raise PermissionDenied()
        except Exception as e:
            # 这里必须手动设置状态码，否则都是200
            # 去掉了auth应该都不生效了
            return self.set_status(401)

        if action == 'sub':  # 用户订阅
            with Session(bot_id, visitor_id) as session:
                history = session.chat_history
                for message in history:
                    await model.client.pubsub('POST', bot_id, visitor_id, message)
                if session.root_message_id:
                    reply_text = _('用户重新进入', lang=model.lang)
                    result = await fsmodel.client.reply(
                        session.root_message_id,
                        FeishuMessageCard(FeishuMessageNote(FeishuMessagePlainText(reply_text))),
                        msg_type='interactive',
                    )
                    logging.debug("debug user enter %r", (session.root_message_id, result))
        if action == 'unsub':
            with Session(bot_id, visitor_id) as session:
                if session.root_message_id:
                    reply_text = _('用户离开', lang=model.lang)
                    result = await fsmodel.client.reply(
                        session.root_message_id,
                        FeishuMessageCard(FeishuMessageNote(FeishuMessagePlainText(reply_text))),
                        msg_type='interactive',
                    )
                    logging.debug("debug user enter %r", (session.root_message_id, result))

        self.finish()

    async def create_root_message(self, visitor_id, nickname, telephone, email):
        first_line = ' | '.join(filter(lambda i: i, [nickname, telephone or email, visitor_id[-8:]]))
        # first message
        result = await self.client.send_text(self.chat_id, first_line, receive_id_type='chat_id')
        return result.data['message_id']

    async def parse_message_content(self):
        content_type = self.request.headers.get('Content-Type', '')
        if 'multipart/form-data' in content_type:
            # 图片或者文件
            if 'image' in self.request.files:
                file_obj = self.request.files['image'][0]
                image_key = await self.client.upload_image_binary(file_obj['body'])
                return FeishuImageMessage(image_key), 'image'
            elif 'file' in self.request.files:
                file_obj = self.request.files['file'][0]
                fname = file_obj['filename']
                extension = fname.split('.').pop()
                if extension not in ['opus', 'mp4', 'pdf', 'doc', 'xls', 'ppt', 'docx', 'xlsx', 'pptx', 'txt']:
                    raise Exception('unkown type')
                file_key = await self.client.upload_file_binary(file_obj['body'], file_obj['filename'])
                return FeishuFileMessage(file_key), 'file'
            else:
                raise Exception('unkown message')
        else:
            # 文本消息，或者直接转发给前端的消息
            message = self.request.body.decode()
            try:
                message = json.loads(message)
            except Exception as e:
                logging.error(e)
                message = Web.create_text_message(text=message)
            finally:
                if type(message) == dict and 'type' in message:
                    # 回复给用户的消息
                    if message['type'] == 'message':
                        if message['payload'].get('sender', {}).get('sender_type', '') in ['user', 'app', 'sys']:
                            logging.info('ws reply: %r', message)
                            raise Finish(json.dumps(message, ensure_ascii=False).encode('utf-8'))
                    elif message['type'] == 'system':
                        raise Finish(json.dumps(message, ensure_ascii=False).encode('utf-8'))
                # 这里考虑组装成一个text消息
                reply_text = message['payload'].get('content', {}).get('text', '')
                if reply_text:
                    return FeishuMessageText(reply_text), 'text'

                raise Exception('unkown message')

    @arguments
    async def post(self, action: str = '', bot_id: ObjIDStr = '', visitor_id: str = '', nickname: str = '', telephone: str = '', email: str = '', model: MessengerModel = None, fsmodel: FeishuModel = None):
        logging.info("debug POST %r", [action, bot_id, visitor_id, self.request.body[:500], list(self.request.headers.items())])
        if not visitor_id:
            return self.set_status(401)

        """
        1. 判断是否是初次会话，是初次会话，就创建话题
        2. 判断是什么消息格式，如果是文件或者图片，就上传获取对应的key，并获取对应的消息内容，以及消息格式
        3. 将消息放到session里面，同时将消息返回给im server, im server会自动将消息推送到前端
        4. 检查人工客服开关是否开启，如果没有开启，就把消息转发给客服机器人，放入队列
        ~~5. 手动检查一下开启人工客服的开关（后面可能移除，交给AI处理）~~
        """
        try:
            fsmodel.init_by_bot_id(bot_id)
            self.client = fsmodel.client
            model.init_by_bot_id(bot_id)
            self.chat_id = model.chat_id
            msg_content, msg_type = await self.parse_message_content()
            with Session(bot_id, visitor_id) as session:
                if not session.root_message_id:
                    session.root_message_id = await self.create_root_message(
                        visitor_id, nickname, telephone, email
                    )

                result = await fsmodel.client.reply(
                    session.root_message_id,
                    msg_content, msg_type=msg_type,
                )
                logging.debug("debug user enter %r", (session.root_message_id, result))
                message = Web.create_message_from_feishu(result.data)
                # 如果没有开启人工，就发消息给对应的机器人
                if not session.is_manual():
                    try:
                        if msg_type == 'text':
                            # 客服机器人只处理文本消息
                            # 如果用户输入了“人工”或者“人工客服”，也主动切换
                            if msg_content['text'].strip() in ['人工', '人工客服', 'CS']:
                                raise NotFound('manual')
                            await model.send_message_to_message_queue(message, bot_id, visitor_id)
                        else:
                            raise NotFound('only support text message')
                    except NotFound as e:
                        # 前面抛出这个异常的时候转人工
                        logging.info("转人工 %r %r", message, e)
                        # 如果是其他格式消息，就转人工
                        session.switch_to_manual()
                        # 找到配置的座席人员
                        open_id, name = await model.choice_seat(model.messenger.id)
                        reply_text = f'''{_('转人工：', lang=model.lang)}<at id={open_id}></at>'''
                        # 之前计划使用卡片，尝试在卡片中发@消息失败，还是使用文本消息
                        result = await fsmodel.client.reply(
                            session.root_message_id,
                            FeishuMessageCard(FeishuMessageDiv(reply_text, tag="lark_md", quote=False)),
                            msg_type='interactive',
                        )
                    except Exception as e:
                        logging.exception(e)

                # 消息返回给im server
                session.add_message(message)
                return self.finish(json.dumps(message, ensure_ascii=False).encode('utf-8'))
        except Finish as e:
            self.set_status(304)
        except PermissionDenied as e:
            self.set_status(401)
        except Exception as e:
            logging.exception(e)
            logging.error('ws error: %r', e)
            self.set_status(204)



