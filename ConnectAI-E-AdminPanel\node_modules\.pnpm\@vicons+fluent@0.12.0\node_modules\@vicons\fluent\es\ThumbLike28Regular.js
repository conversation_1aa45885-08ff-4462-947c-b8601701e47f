import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.302 2.104c.24-.608.936-1.235 1.82-.988C17.9 1.61 18.574 3.5 18.759 5.27c.157 1.513-.002 3.227-.347 4.73h2.167a3.75 3.75 0 0 1 3.675 4.497l-1.228 6.046a6.75 6.75 0 0 1-8.47 5.146l-7.69-2.197a3.75 3.75 0 0 1-2.668-2.989l-.416-2.496c-.22-1.321.547-2.528 1.648-3.086c4.775-2.417 6.606-7.066 8.612-12.158l.26-.66zm1.432.487a.245.245 0 0 0-.037.064l-.316.802c-1.947 4.957-3.975 10.12-9.273 12.802c-.613.31-.942.927-.846 1.501l.416 2.496a2.25 2.25 0 0 0 1.6 1.794l7.69 2.197a5.25 5.25 0 0 0 6.588-4.003l1.228-6.046a2.25 2.25 0 0 0-2.205-2.698h-3.142a.75.75 0 0 1-.717-.968c.468-1.541.715-3.489.547-5.106c-.174-1.673-.737-2.616-1.513-2.855a.181.181 0 0 0-.02.02z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ThumbLike28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
