<template>
  <div>
    <section class="bg-white dark:bg-gray-900">
      <div class="grid-cols-2 gap-8 content-center py-2 px-4 mx-auto max-w-screen-xl md:grid">
        <div class="self-center">
          <h1 class="mb-4 text-2xl font-bold text-primary-600 dark:text-primary-500">Not Found Manual</h1>
          <p class="mb-4 text-3xl tracking-tight font-bold text-gray-900 lg:mb-10 md:text-4xl dark:text-white">
            The little buddy is writing...
          </p>
          <p class="mb-4 text-gray-500 dark:text-gray-400">Here are some helpful links:</p>
          <ul class="flex items-center space-x-4 text-gray-500 dark:text-gray-400">
            <li>
              <a href="/bot" class="underline hover:text-gray-900 dark:hover:text-white">Bot Market</a>
            </li>
          </ul>
        </div>
        <img
          class="hidden mx-auto mb-4 md:flex"
          src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/500/500.svg"
          alt="500 Server Error"
        />
      </div>
    </section>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
