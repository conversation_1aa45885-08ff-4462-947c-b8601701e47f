<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="x-ua-compatible" content="IE=edge">
  <title>{{ title }}</title>
  <link rel="icon" type="image/png" href="{{url_for('flasgger.static', filename='')}}images/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="{{url_for('flasgger.static', filename='')}}images/favicon-16x16.png" sizes="16x16" />
  <link href='{{url_for('flasgger.static', filename='')}}css/typography.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='{{url_for('flasgger.static', filename='')}}css/reset.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='{{url_for('flasgger.static', filename='')}}css/screen.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='{{url_for('flasgger.static', filename='')}}css/reset.css' media='print' rel='stylesheet' type='text/css'/>
  <link href='{{url_for('flasgger.static', filename='')}}css/print.css' media='print' rel='stylesheet' type='text/css'/>

  <script src='{{url_for('flasgger.static', filename='')}}lib/object-assign-pollyfill.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/jquery-1.8.0.min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/jquery.slideto.min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/jquery.wiggle.min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/jquery.ba-bbq.min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/handlebars-4.0.5.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/lodash.min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/backbone-min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}swagger-ui.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/highlight.9.1.0.pack.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/highlight.9.1.0.pack_extended.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/jsoneditor.min.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/marked.js' type='text/javascript'></script>
  <script src='{{url_for('flasgger.static', filename='')}}lib/swagger-oauth.js' type='text/javascript'></script>

  <!-- Some basic translations -->
  <!-- <script src='lang/translator.js' type='text/javascript'></script> -->
  <!-- <script src='lang/ru.js' type='text/javascript'></script> -->
  <!-- <script src='lang/en.js' type='text/javascript'></script> -->

  <script type="text/javascript">
    $(function () {
      var url = window.location.search.match(/url=([^&]+)/);
      if (url && url.length > 1) {
        url = decodeURIComponent(url[1]);
      } else {
        //url = "http://petstore.swagger.io/v2/swagger.json";
        url = "{{ specs[0]['url'] }}"
      }

      hljs.configure({
        highlightSizeThreshold: 5000
      });

      // Pre load translate...
      if(window.SwaggerTranslator) {
        window.SwaggerTranslator.translate();
      }
      window.swaggerUi = new SwaggerUi({
        url: url,
        validatorUrl: null,
        dom_id: "swagger-ui-container",
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        onComplete: function(swaggerApi, swaggerUi){
          if(typeof initOAuth == "function") {
              let oauth_config = {{ flasgger_config.get("auth") | safe }};
              initOAuth(oauth_config);
          }

          if(window.SwaggerTranslator) {
            window.SwaggerTranslator.translate();
          }

          $('pre code').each(function(i, e) {
            hljs.highlightBlock(e)
          });

          {% if config.JWT_AUTH_URL_RULE -%}
            $(document).find('div.response_headers').bind('DOMSubtreeModified', function(event) {
              var response = JSON.parse($(this).text());
              {% if config.JWT_AUTH_HEADER_TOKEN -%}
                var jwtAuthHeaderToken = "{{ config.JWT_AUTH_HEADER_TOKEN }}";
              {%- else %}
                var jwtAuthHeaderToken = "jwt-token";
              {%- endif %}
              var tokenField = jwtAuthHeaderToken;
              if (response.hasOwnProperty(tokenField)) {
                var jwt_token = response[tokenField];
                {% if config.JWT_AUTH_HEADER_NAME -%}
                  var jwtAuthHeaderName = "{{ config.JWT_AUTH_HEADER_NAME }}";
                {%- else %}
                  var jwtAuthHeaderName = "Authorization";
                {%- endif %}
                {% if config.JWT_AUTH_HEADER_TYPE -%}
                  var jwtAuthHeaderType = "{{ config.JWT_AUTH_HEADER_TYPE }}";
                {%- else %}
                  var jwtAuthHeaderType = "Bearer";
                {%- endif %}
                swaggerUi.api.clientAuthorizations.add("key", new SwaggerClient.ApiKeyAuthorization(jwtAuthHeaderName, jwtAuthHeaderType + " " + jwt_token, "header"));
              }
            });
          {%- endif %}
        },
        onFailure: function(data) {
          log("Unable to Load SwaggerUI");
        },
        {% if flasgger_config.doc_expansion -%}
            docExpansion: "{{flasgger_config.doc_expansion | safe }}",
        {%- else %}
           docExpansion: "none",
        {%- endif %}
        jsonEditor: false,
        defaultModelRendering: 'schema',
        showRequestHeaders: false,
        showOperationIds: false
      });

      window.swaggerUi.load();

      function log() {
        if ('console' in window) {
          console.log.apply(console, arguments);
        }
      }
  });
  </script>
</head>

<body class="swagger-section">
<div id='header'>
  <div class="swagger-ui-wrap">
    <a id="logo" href="{{ url_for('flasgger.apidocs') }}"><img class="logo__img" alt="swagger" height="30" width="30" src="{{url_for('flasgger.static', filename='')}}images/logo_small.png" /><span class="logo__title">{{title}}</span></a>
    <form id='api_selector'>
      {% if specs|length > 1 %}
          <div class='input'>
            <select id="input_baseUrl" name="baseUrl">
              {% for spec in specs %}
              <option value="{{spec['url']}}">{{spec['title']}}</option>
              {% endfor %}
            </select>
          </div>
      {% else %}
          <div class='input'><input placeholder="http://example.com/api" id="input_baseUrl" name="baseUrl" type="text"/></div>
      {% endif %}
      <div id='auth_container'></div>
      <div class='input'><a id="explore" class="header__btn" href="#" data-sw-translate>Explore</a></div>
    </form>
  </div>
</div>

<div id="message-bar" class="swagger-ui-wrap" data-sw-translate>&nbsp;</div>
<div id="swagger-ui-container" class="swagger-ui-wrap"></div>
<div class="swagger-ui-wrap footer">
<small style="font-size: 60%; color: #ccc;">
[Powered by <a href="https://github.com/rochacbruno/flasgger">Flasgger</a>]
</small>
</div>
</body>
</html>
