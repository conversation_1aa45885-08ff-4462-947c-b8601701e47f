<template>
  <div class="w-full">
    <div class="grid grid-cols-1 px-4 pt-6 xl:grid-cols-12 xl:gap-4 overflow-auto h-full">
      <div class="col-span-full xl:col-span-4 gap-10">
        <HeadInfo :messenger-info="messengerInfo" />
        <collect-message v-model:data="webConfig" :loading="webConfigLoading" />
        <help-links v-model:data="webConfig" :loading="webConfigLoading" />
        <Config v-model:data="webConfig" :loading="webConfigLoading" />
      </div>
      <!-- Right Content -->
      <div class="col-span-8">
        <basic-config
          v-model:data="webConfig"
          :messenger-info="messengerInfo"
          :loading="infoLoading"
          :ai-info="aiInfo"
          @change="() => getChatInfo()"
        />
        <seat-config :messenger-info="messengerInfo" />
        <ai-config
          v-model:data="setting"
          :ai-info="aiInfo"
          :messenger-info="messengerInfo"
          :client="client"
          :loading="settingLoading"
          :resource="resource"
          @change="() => getChatInfo()"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import {
  getMessengerChatInfo,
  getAppSetting,
  getMessengerAiInfo,
  getMessengerWebConfig,
  getAppClient
} from '@/service/api/messenger';
import HeadInfo from './component/headInfo.vue';
import HelpLinks from './component/helpLinks.vue';
import SeatConfig from './component/seatConfig.vue';
import BasicConfig from './component/basicConfig/basicConfig.vue';
import CollectMessage from './component/collectMessage.vue';
import AiConfig from './component/aiConfig/aiConfig.vue';
import Config from './component/config.vue';
import { getAppResource } from '@/service/api/app';

const route = useRoute();

const webConfig = ref<ApiMessenger.Resp.MessengerWebConfig>({
  theme: { primaryColor: 'rgba(172, 14, 235, 1)' }
} as ApiMessenger.Resp.MessengerWebConfig);
const webConfigLoading = ref(false);

const id = route.query.id as string;

const messengerInfo = ref<ApiMessenger.MessengerChatInfoDetails>({} as ApiMessenger.MessengerChatInfoDetails);

const infoLoading = ref(false);
const settingLoading = ref(false);

const setting = ref<ApiApp.APPSetting>({} as ApiApp.APPSetting);

const aiInfo = ref<ApiApp.AppInfo>({} as ApiApp.AppInfo);
const client = ref<ApiApp.AppClient>({} as ApiApp.AppClient);
const resource = ref<ApiApp.AppResource[]>([]);

async function getChatInfo() {
  try {
    infoLoading.value = true;
    const {
      data: { data }
    } = await getMessengerChatInfo({ id });
    messengerInfo.value = { ...data };
    infoLoading.value = false;
  } catch (err) {
    infoLoading.value = false;
  }
}

async function getAiInfo() {
  try {
    settingLoading.value = true;

    const {
      data: { data }
    } = await getMessengerAiInfo({ id });
    aiInfo.value = { ...data };
    getSetting();
    getClientBot();
    getResourceList();
    settingLoading.value = false;
  } catch (err) {
    settingLoading.value = true;
  }
}

async function getSetting() {
  const instance_id = aiInfo.value.id;
  try {
    const res = await getAppSetting({ id: instance_id });
    setting.value = res.data!.data;
  } catch (err) {}
}

async function getResourceList() {
  const application_id = aiInfo.value.application_id;
  try {
    const {
      data: { data: resourceData }
    } = await getAppResource({ id: application_id });
    resource.value = resourceData;
  } catch (err) {
    console.error(err);
  }
}

async function getClientBot() {
  const application_id = aiInfo.value.application_id;
  const {
    data: { data: clientData = [] }
  } = await getAppClient({ id: application_id });
  client.value = clientData.find((item) => item.platform === 'feishu');
}

async function getWebConfig() {
  webConfigLoading.value = true;
  try {
    const {
      data: { data }
    } = await getMessengerWebConfig({ id });
    webConfig.value = {
      ...data,
      theme: { primaryColor: 'rgba(172, 14, 235, 1)', ...data?.theme }
    };
    webConfigLoading.value = false;
  } catch (err) {
    webConfigLoading.value = false;
  }
}

onMounted(() => {
  getChatInfo();
  getAiInfo();
  getWebConfig();
});
</script>

<style scoped></style>
