class AVSummaryTool(CTool):
    name: str = 'AVSummary'
    description: str = 'av summary tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class AVSummaryCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️请稍等片刻......'))),
                            header=FeishuMessageCardHeader('⚡️正在解析视频中 ', template='wathet')

                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在解析，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️请稍等片刻......'))),
                            header=FeishuMessageCardHeader('⚡️正在解析视频中 ', template='wathet')
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                content = response.generations[0][0].text
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                source_url = additional_kwargs.get('sourceUrl', '')
                actions = {
                    'chat': [_('对话追问'), {'action': 'chat', 'url': source_url}],
                    'subtitle': [_('解析字幕'), {'action': 'subtitle', 'url': source_url}],
                }
                # 移除最后一行的宣传内容
                if content and 'https://bibigpt.co' in content.split('\n')[-1]:
                    content = '\n'.join(content.split('\n')[:-1])
                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md', quote=False),
                            FeishuMessageAction(*[FeishuMessageButton(v[0], value=v[1]) for k, v in actions.items()]),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader('Chat Video Bot 🎉', template='wathet')
                        )
                    )

                elif platform == 'dingding':
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title=_('Chat Video Bot🎉'),
                            text=content,
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error('av llm error: %r', error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        input_url = parse_urls(input)
        if not input_url:
            invalid_note = _('无效输入，请输入音视频网站的链接或分享链接')
            if platform == 'feishu':
                return send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(invalid_note)),
                        header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='wathet')
                    )
                )
            elif platform == 'dingding':
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🤖 机器人提醒'),
                        text=invalid_note,
                    )
                )
        input_url = input_url[0]
        lang_map = {'zh_CN': 'zh-CN', 'en_US': 'en-US'}  # TODO 其他对应语言
        params = {
            'url': input_url,
            'includeDetail': False,
            'promptConfig': {
                'showEmoji': session.extra.get('show_emoji', True),
                'showTimestamp': False,
                'outlineLevel': 1,
                'sentenceNumber': 5,
                'detailLevel': 1000,
                'outputLanguage': lang_map.get(data.lang, 'zh-CN'),
                'isRefresh': session.extra.get('refresh', True)
            }
        }
        model.callbacks = [AVSummaryCallbackHandler()]
        chat = BibiGPTChat(**model)
        messages = [HumanMessage(content=input, additional_kwargs=dict(params))]
        return chat.invoke(messages)


class AVChatTool(CTool):
    name: str = 'AVChat'
    description: str = 'av chat mode'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class AVChatCallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                pass

            def on_llm_end(self, response, *args, **kwargs):
                content = response.generations[0][0].text
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader('Chat Video Bot🎉', template='wathet')
                        )
                    )
                elif platform == 'dingding':
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title='Chat Video Bot🎉', template='wathet',
                            text=content,
                        )
                    )

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error('av llm error: %r', error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        if not ('root_id' in data.extra.extra and data.extra.extra.root_id):
            return
        root_message = syncify(client.get_messages_by_id)(data.extra.extra.root_id)
        logging.debug('chat message %r', root_message)
        if 'parent_id' in root_message and root_message['parent_id']:
            root_message = syncify(client.get_messages_by_id)(root_message['parent_id'])
        if root_message['deleted']:
            # 话题消息创建失败
            logging.error('av topic created error')
            return
        content = json.loads(root_message['body']['content'])
        # content = content['elements'][0][0]['text']
        url = parse_urls(content['text'])
        if not url:
            logging.error('av parse url error: %r', url)
        url = url[0]
        model.callbacks = [AVChatCallbackHandler()]
        chat = BibiGPTChat(**model)
        lang_map = {'zh_CN': 'zh-CN', 'en_US': 'en-US'}  # TODO 其他对应语言
        params = {'action': 'chat', 'url': url, 'question': input, 'language': lang_map.get(data.lang, 'zh-CN')}
        # TODO bibigpt chat history
        messages = [HumanMessage(content=input, additional_kwargs=dict(params))]
        return chat.invoke(messages)


class AVSubtitleTool(CTool):
    name: str = 'AVSubtitle'
    description: str = 'av subtitle'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class AVSubtitleCallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                pass

            def on_llm_end(self, response, *args, **kwargs):
                content = response.generations[0][0].text
                # 再判断一下
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader(_('字幕解析成功🎉'), template='wathet')
                        )
                    )
                elif platform == 'dingding':
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            title='字幕解析成功🎉', template='wathet',
                            text=content,
                        )
                    )

            def on_llm_error(
                    self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error('av llm error: %r', error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        if 'action' not in data.extra.extra:
            return None
        action_value = data.extra.extra.get('action', {}).get('value', {})
        if action_value.get('action', '') != 'subtitle':
            return None
        model.callbacks = [AVSubtitleCallbackHandler()]
        params = action_value
        chat = BibiGPTChat(**model)
        messages = [HumanMessage(content=input, additional_kwargs=dict(params))]
        return chat.invoke(messages)


class DingdingCommand(CommandTool):
    next_tool_name: str = 'AVSummary'
    name: str = 'AV_summary_dingding_command'
    description: str = 'AV_summary dingding command'

    def send_usage(self):
        # TODO 可使用ActionCardMessage替代，dtmdLink放到actionURL内即可
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_('💥 **我是ChatVideo，输入bilibili、抖音、youtube、小红书等网站链接帮你加速理解音视频内容**')
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        if data.extra.message_type in ['text'] and input and parse_urls(input):
            return None,
        return 'note',

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送视频链接，不支持自然语言（除帮助外）、文件、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送链接！')
        return send_note(text, title)


class FeishuCommand(CommandTool):
    next_tool_name: str = 'AVSummary'
    name: str = 'AV_summary_feishu_command'
    description: str = 'AV_summary feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<帮助> 或 /help 可选择是否显示emoji表情',
        '输入<帮助> 或 /help 可选择是否缓存',
        '选择忽略缓存，相关配置才会生效',
        '支持直接粘贴音视频的网站分享链接哦',
        '音视频解析完成后，可通过创建话题进行对话追问',
        '音视频解析完成后，可获取相应字幕'
    ]
    example_question: str = 'https://v.douyin.com/ieucY5hW/'
    example_anwser: str = """## 摘要
- 这个视频是关于可爱的柯基宠物和治愈系，在歌会变的过程中，哥却不会变。
- 视频中的柯基宠物很可爱，给人带来治愈的感觉。

## 亮点
- 视频中的柯基宠物展示了它的可爱和独特之处。
- 治愈系的元素使人们感到舒适和放松。
- 视频以柯基宠物不变的特点作为亮点，强调了它们的可靠性和稳定性。
- 视频发布者感谢观看，并欢迎订阅。"""

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def ai_example_btn(self):
        return FeishuMessageButton(
            _('查看示例'),
            type='default',
            value={'example': 1},
        )

    @property
    def ai_answer_btn(self):
        return FeishuMessageButton(
            _('解析示例视频'),
            type='primary',
            value={'answer': 1},
        )

    @property
    def show_emoji_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, value) for value in ['NO', 'YES']],
            placeholder=_('是否显示emoji表情，默认显示'),
            initial_option='YES' if session.extra.get('show_emoji', True) else 'NO',
            value={'command': 'emoji'},
            confirm=FeishuMessageConfirm(
                title=_('您确定显示表情吗？'),
                text=_('选择否后，总结内容中将不再显示emoji表情')
            )
        )

    @property
    def refresh_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, value) for value in ['NO', 'YES']],
            placeholder=_('是否忽略缓存，默认不缓存'),
            initial_option='YES' if session.extra.get('refresh', True) else 'NO',
            value={'command': 'refresh'},
            confirm=FeishuMessageConfirm(
                title=_('您确定修改缓存策略吗？'),
                text=_('缓存策略会影响视频解析速度')
            )
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是ChatVideo，输入bilibili、抖音、youtube、小红书等网站链接帮你加速理解音视频内容**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('😁 **是否显示表情**\n解析后的总结内容中显示emoji表情'),
                    tag='lark_md',
                    extra=self.show_emoji_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🖌️️ **是否忽略缓存**\n选择缓存后解析速度更快'),
                    tag='lark_md',
                    extra=self.refresh_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('📖 **查看示例**\n文本回复 *示例* 或 */example*'),
                    tag='lark_md',
                    extra=self.ai_example_btn
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='wathet'),
            )
        )

    def parse_command(self, input, action):
        self.next_tool_name = 'AVSummary'
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:8] == '/example' or input[:2] == '示例':
            return 'example',
        elif not input and action:
            if action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
            elif action['tag'] == 'button':
                a_value = action['value']
                if 'example' in a_value:
                    return 'example',
                elif 'answer' in a_value:
                    return 'answer',
                v_action = a_value.get('action', '')
                if v_action == 'chat':
                    return 'chat', a_value
                elif v_action == 'subtitle':
                    self.next_tool_name = 'AVSubtitle'
                    return None,
                elif v_action == 'exit':
                    return 'exit',
        if data.extra.extra.get('root_id'):
            # 话题内 仅接收文本
            if data.extra.message_type in ['text'] and input and not parse_urls(input):
                self.next_tool_name = 'AVChat'
                return None,
            return 'note', _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!'), _('不支持的消息，请更改发送文本内容！')
        else:
            # 话题外 仅接收链接
            if data.extra.message_type in ['text'] and input and parse_urls(input):
                return None,
            return 'note',

    def on_emoji(self, is_show):
        # 默认True
        session.set_extra('show_emoji', is_show == 'YES')
        if not is_show or is_show == 'YES':
            return
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('接下来的总结内容将不再显示emoji表情')),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('👺 不显示表情'), template='wathet'),
            )
        )

    def on_refresh(self, is_refresh):
        # 默认True
        session.set_extra('refresh', is_refresh == 'YES')
        if not is_refresh or is_refresh == 'YES':
            return
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('接下来的总结内容将会进行缓存')),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('👺 已选择缓存'), template='wathet'),
            )
        )

    def on_example(self):
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _(self.example_question),
                    tag='lark_md'
                ),
                FeishuMessageHr(),
                FeishuMessageAction(self.ai_answer_btn),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 示例输入'), template='wathet'),
            )
        )

    def on_answer(self):
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _(self.example_anwser),
                    tag='lark_md'
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 示例输出'), template='wathet'),
            )
        )

    def on_chat(self, value):
        if not value or 'url' not in value:
            return
        img_url = 'https://p1-hera.feishucdn.com/tos-cn-i-jbbdkfciu3/fc5a17626d494e6abe4cad32c991d035~tplv-jbbdkfciu3-image:0:0.image'
        img_key = syncify(client.upload_image)(img_url)
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('**请在上方「解析消息」或「链接消息」处点击「创建话题」后进行对话**'), tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(_('将鼠标悬浮至某条消息上，选择创建话题，在右侧话题详情页的输入框中即可回复该话题；发送回复后，针对该消息的话题即已创建完成。'), tag='lark_md'),
                FeishuMessageImage(img_key=img_key, alt='图片'),
                FeishuMessageAction(
                    FeishuMessageButton(
                        _('查看如何创建话题'),
                        url='https://www.feishu.cn/hc/zh-CN/articles/423295624296-%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E8%AF%9D%E9%A2%98%E5%9B%9E%E5%A4%8D',
                    )
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader('Chat Video Bot 🎉', template='wathet')
            ),
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送视频链接，不支持自然语言（除帮助外）、文件、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送链接！')
        return send_note(text, title)


class AVSummaryAgent(CAgent):
    class AppConfig(BaseAppConfig):
        category: str = '音视频'
        name: str = '音视频总结'
        title: str = 'ChatVideo'
        title_en: str = 'ChatVideo'
        description: str = ' 🍗 AI 音视频一键总结，轻松学习哔哩哔哩丨YouTube丨播客丨小红书丨抖音等内容'
        description_en: str = ' 🍗 One-click summary of AI audio and video, easy to learn content such as Bilibili丨You Tube丨Podcast丨Little Red Book丨TikTok'
        problem: str = 'AI帮你加速理解音视频内容'
        problem_en: str = 'How to quickly understand audio and video content'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/UmdRd2ADzoeDFMxzl02coeNsnzg'
        manual_en: str = 'https://q5o2cctqdb7.sg.larksuite.com/docx/Us9UdfLT9o6nCQxMseClKicrgAb'
        icon: str = 'https://pic1.forkway.cn/cdn/20230915192817.png'
        logo: str = 'https://pic1.forkway.cn/cdn/20230915192817.png?imageMogr2/thumbnail/720x'
        sorted: int = 7
        support_resource: List[object] = [dict(
            category=ModelCategory.Video.value,
            scene=ModelCategory.Video.value,
            title='视频处理',
            tip='',
            required=True,
            resource=['BibiGPT']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnfunxOSwbovxTRVlkwjJhhb'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/YOqDwzm8aisr66kxE0pcjL6Xnyg'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='AV_summary_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='AV_summary_dingding_command', tool_input=kwargs, log="")
        return AgentAction(tool='AVSummary', tool_input=kwargs, log="")
