import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.495 3.25a.75.75 0 0 1 .698.504l3.652 10.5a.75.75 0 1 1-1.417.492l-.781-2.246H11.06l-.86 2.266a.75.75 0 1 1-1.402-.532l3.984-10.5a.75.75 0 0 1 .712-.484zM11.63 11h3.495l-1.671-4.805L11.63 11zM4.904 8.937c-.458.018-.85.124-1.068.234a.75.75 0 0 1-.671-1.342c.447-.224 1.056-.367 1.681-.39c.63-.025 1.346.069 1.99.39c1.42.71 1.416 2.125 1.414 2.628V14.5a.75.75 0 0 1-1.498.056c-.871.538-1.89.85-2.945.57c-2.077-.555-2.801-3.365-.723-4.75c.774-.516 1.702-.652 2.526-.61c.379.02.753.077 1.105.162c-.055-.31-.196-.58-.55-.757c-.357-.179-.807-.251-1.261-.234zm1.846 2.562a4.512 4.512 0 0 0-1.216-.235c-.629-.032-1.201.082-1.619.36c-.923.616-.645 1.805.279 2.052c.75.2 1.668-.183 2.556-.996V11.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextChangeCase20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
