import time
import json
import httpx
from uuid import uuid4
from core.redisdb import stalecache, redis_cli
from core.utils import syncify
from tornado.options import options


class KnowClient(object):

    def __init__(self, user_id=options.CHATPDF_USER):
        self.user_id = user_id

    def __getattr__(self, name):
        try:
            return syncify(object.__getattribute__(self, 'async_' + name))
        except Exception as e:
            return object.__getattribute__(self, name)

    @stalecache(stale=0, expire=60)
    async def fetch_access_token(self, tenant_id, t):
        code = str(uuid4())
        key = 'code:{}'.format(code)
        user = {
            'openid': tenant_id,
            'permission': {
                'has_privilege': True,
                'expires': time.time() + 86400,
            }
        }
        redis_cli().pipeline().set(key, json.dumps(user)).expire(key, 1000).execute()
        response = await httpx.AsyncClient().get(
            '{}/api/access_token?code={}'.format(options.KNOW_SERVER, code),
            headers={
                'X-System-Url': f'{options.SCHEMA}://{options.DOMAIN}/api/code2session',
            }
        )
        return response.json().get('access_token')

    @property
    def access_token(self):
        return syncify(self.fetch_access_token)(self.user_id, int(time.time() / 100))

    async def post(self, path, **kwargs):
        return await self.request('POST', path, **kwargs)

    async def get(self, path, **kwargs):
        return await self.request('GET', path, **kwargs)

    async def delete(self, path, **kwargs):
        return await self.request('DELETE', path, **kwargs)

    async def request(self, method='GET', path='', **kwargs):
        response = await httpx.AsyncClient().request(method=method, url='{}{}'.format(
            options.KNOW_SERVER,
            path,
        ), headers={
            'Authorization': 'Bearer {}'.format(self.access_token),
        }, **kwargs)
        return response.json()

    async def async_upload(self, file_name, bytes, content_type='application/octet-stream'):
        files = {'file': (file_name, bytes, content_type)}
        result = await self.post('/api/upload', files=files, timeout=60)
        return result['url']

    async def async_add_document_to_collection(self, collection_id, file_name, url, file_type, uniqid=''):
        data = {
            'fileName': file_name,
            'fileUrl': url,
            'fileType': file_type,
            'uniqid': uniqid,
        }
        path = '/api/collection/{}/documents'.format(collection_id)
        result = await self.post(path, json=data, timeout=20)
        return result['data']

    async def async_fetch_task_result(self, collection_id, task_id):
        result = await self.get('/api/collection/{}/task/{}'.format(
            collection_id,
            task_id,
        ), timeout=20)
        return result['data']

    async def async_get_collection(self, collection_id):
        return await self.get('/api/collection/{}'.format(
            collection_id,
        ), timeout=20)

    async def async_query(self, collection_id, keyword, size=4):
        return await self.get('/api/collection/{}/query?q={}&size={}'.format(
            ','.join(collection_id) if isinstance(collection_id, list) else collection_id,
            keyword,
            size,
        ), timeout=20)

    async def async_query_document(self, document_id, keyword, size=4):
        return await self.get('/api/document/{}/query?q={}&size={}'.format(
            document_id,
            keyword,
            size,
        ), timeout=20)

    async def get_docs_by_document_id_and_page(self, document_id, page=1, size=20):
        return await self.get('/api/document/{}/query?page={}&size={}'.format(
            document_id,
            page,
            size,
        ), timeout=20)

    async def get_docs_by_document_id(self, document_id, size=20):
        page = 1
        while True:
            result = await self.get_docs_by_document_id_and_page(document_id, page, size)
            docs = result.get('data', [])
            if len(docs) == 0:
                break
            for doc in docs:
                yield doc
            page += 1

    async def async_get_document(self, document_id):
        result = []
        async for doc in self.get_docs_by_document_id(document_id):
            result.append(doc)
        return result

    async def async_save_summary(self, document_id, summary):
        return await self.post('/api/document/{}'.format(
            document_id,
        ), json=dict(summary=summary), timeout=20)

    async def async_get_document_info(self, document_id):
        return await self.get('/api/document/{}'.format(
            document_id,
        ), timeout=20)

    async def async_get_client_info(self):
        return await self.get('/api/collection/client', timeout=20)

