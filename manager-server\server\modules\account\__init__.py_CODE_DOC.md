# 代码文档 - server/modules/account/__init__.py

## 文件作用
账户管理模块的路由配置文件，定义账户相关的所有API接口路由和处理器映射。

## 逐行代码解释

### 导入处理器 (2-20行)
```python
from .handler import (
    AccountHandler,                      # 账户登录/注册/信息管理
    AccountCodeHandler,                  # 验证码发送
    PromptCategoryHandler,               # 提示词分类管理
    PromptHandler,                       # 提示词管理
    PromptImportHandler,                 # 提示词导入
    SensitiveHandler,                    # 敏感词管理
    ChatLogHandler,                      # 聊天日志管理
    TenantHandlerHandler,                # 租户权限控制
    ProductHandler,                      # 套餐产品管理
    OrderHandler,                        # 订单管理
    WXPayHandler,                        # 微信支付
    WXPayCallbackHandler,                # 微信支付回调
    TenantSeatHandler,                   # 租户座席管理
    TenantSeatParseHandler,              # 座席文件解析
    TenantSeatImportHandler,             # 座席批量导入
    TenantSeatDepartmentHandler,         # 座席部门管理
    TenantSeatConfigHandler,             # 座席配置管理
)
```

### 路由配置 (22-61行)
```python
urls = [
    # 登录相关接口 (27-31行)
    # 支持三种登录方式：
    # 1. 手机号/邮箱+密码 --> 登录 (默认)
    # 2. 手机号+验证码 --> 注册或登录 (快捷登录)
    # 3. 邮箱+验证码+密码 --> 注册 (邮箱注册)
    (r"/api/account/login", AccountHandler),     # POST: 用户登录/注册
    (r"/api/account/logout", AccountHandler),    # DELETE: 用户登出
    (r"/api/account/code", AccountCodeHandler),  # POST: 发送验证码
    (r"/api/account/update", AccountHandler),    # PUT: 修改租户信息
    (r"/api/account/info", AccountHandler),      # GET: 获取租户信息

    # 提示词接口 (33-37行)
    (r"/api/prompt/category", PromptCategoryHandler),  # GET: 获取提示词分类
    (r"/api/prompt", PromptHandler),                   # GET/POST: 提示词列表/创建
    (r"/api/prompt/([0-9a-z]{24})", PromptHandler),    # PUT/DELETE: 修改/删除提示词
    (r"/api/prompt/export", PromptHandler, dict(export=True)),  # GET: 导出提示词
    (r"/api/prompt/import", PromptImportHandler),      # POST: 导入提示词

    # 敏感词接口 (39-42行)
    (r"/api/sensitive", SensitiveHandler),             # GET/POST: 敏感词列表/创建
    (r"/api/sensitive/([0-9a-z]{24})", SensitiveHandler),  # PUT/DELETE: 修改/删除敏感词
    (r"/api/sensitive/([0-9a-z]{24})/(start|stop)", SensitiveHandler),  # PUT: 启用/禁用敏感词
    (r"/api/sensitive/export", SensitiveHandler, dict(export=True)),  # GET: 导出敏感词

    # 对话日志 (44-45行)
    (r"/api/chatlog", ChatLogHandler),                 # GET: 获取聊天日志
    (r"/api/chatlog/export", ChatLogHandler, dict(export=True)),  # GET: 导出聊天日志

    # 账号权限控制 (47行)
    (r"/api/tenant/handler", TenantHandlerHandler),    # 针对租户的权限管理

    # 套餐接口 (49-52行)
    (r"/api/product", ProductHandler),                 # GET: 获取套餐产品列表
    (r"/api/order", OrderHandler),                     # POST: 创建订单
    (r"/api/wepay", WXPayHandler),                     # POST: 微信支付
    (r"/api/wepay/callback", WXPayCallbackHandler),    # POST: 微信支付回调

    # 座席相关接口 (54-60行)
    (r"/api/seats", TenantSeatHandler),                # GET/POST/PUT/DELETE: 座席CRUD
    (r"/api/seats/parse", TenantSeatParseHandler),     # POST: 解析导入文件，返回用户列表
    (r"/api/seats/import", TenantSeatImportHandler),   # POST: 批量导入用户
    (r"/api/seats/department", TenantSeatDepartmentHandler),  # 座席部门管理
    (r"/api/seats/config", TenantSeatConfigHandler),   # 座席配置管理
]
```

## 路由分类说明

### 1. 用户认证模块
- **登录注册**: 支持多种登录方式的统一接口
- **验证码**: 短信和邮件验证码发送
- **用户信息**: 租户信息的查询和更新

### 2. 提示词管理模块
- **分类管理**: 提示词的分类组织
- **CRUD操作**: 提示词的增删改查
- **导入导出**: 支持批量导入和导出功能

### 3. 敏感词管理模块
- **内容过滤**: 敏感词的管理和配置
- **状态控制**: 敏感词的启用和禁用
- **批量操作**: 支持导出功能

### 4. 日志管理模块
- **聊天记录**: 用户对话日志的查询
- **数据导出**: 支持日志数据的导出

### 5. 商业化模块
- **套餐管理**: 产品套餐的展示
- **订单系统**: 订单创建和管理
- **支付集成**: 微信支付的完整流程

### 6. 座席管理模块
- **用户管理**: 租户下用户的管理
- **批量导入**: 支持Excel等文件的批量导入
- **组织架构**: 部门和配置管理

## 技术特点

### URL模式设计
- **RESTful风格**: 遵循REST API设计规范
- **资源标识**: 使用24位ObjectID标识资源
- **操作区分**: 通过HTTP方法区分不同操作

### 参数传递
- **路径参数**: 使用正则表达式捕获路径中的参数
- **查询参数**: 支持URL查询参数
- **请求体**: 支持JSON格式的请求体

### 功能扩展
- **导出功能**: 通过dict(export=True)参数启用导出
- **状态控制**: 支持start/stop等状态操作
- **文件处理**: 支持文件上传和解析

## 使用场景
- **管理后台**: 为管理员提供完整的账户管理功能
- **用户中心**: 为用户提供个人信息和设置管理
- **内容管理**: 提示词和敏感词的内容管理
- **商业运营**: 套餐销售和用户付费管理
- **企业集成**: 座席管理和组织架构集成
