# 📖 ConnectAI 项目开发流程指南

## 🎯 项目概述

ConnectAI是一个企业级AI应用管理平台，包含多个子项目和不同的部署方式。

### 📁 项目结构

```
project-manager/                    # 主项目根目录
├── manager-server/                 # 后端服务 (Tornado + SQLAlchemy)
│   ├── server/                     # 真实的业务代码
│   │   ├── server.py              # Tornado主服务器
│   │   ├── api.py                 # API路由配置
│   │   ├── modules/               # 业务模块
│   │   │   ├── account/           # 账户管理
│   │   │   ├── application/       # 应用管理
│   │   │   ├── callback/          # 回调处理
│   │   │   └── messenger/         # 消息处理
│   │   ├── core/                  # 核心组件
│   │   └── settings/              # 配置文件
│   ├── simple_flask_server.py     # 简化版Flask服务器 (本地开发)
│   ├── requirements.txt           # Python依赖
│   └── docker-compose.yml         # Docker部署配置
├── DataChat-API/                   # 知识库API服务
├── ConnectAI-E-AdminPanel/         # Vue3管理面板
├── ConnectAI-Helper/               # React浏览器扩展
├── Lark-Messenger-Web/             # 飞书消息组件
└── data/                          # 本地数据存储
    ├── connectai.db              # SQLite数据库 (本地开发)
    ├── files/                    # 文件存储
    └── search_index/             # 搜索索引
```

## 🔄 开发环境类型

### 1. 生产环境 (Docker)
```yaml
# docker-compose.yml
services:
  manager:     # Tornado服务器
  mysql:       # MySQL数据库
  redis:       # Redis缓存
  rabbitmq:    # 消息队列
  elasticsearch: # 搜索引擎
```

### 2. 本地开发环境 (简化版)
```
manager-server/simple_flask_server.py  # Flask替代Tornado
data/connectai.db                      # SQLite替代MySQL
内存存储                                # 替代Redis
文件存储                                # 替代Elasticsearch
内存队列                                # 替代RabbitMQ
```

## 🚀 正确的开发流程

### 阶段一：本地开发环境搭建

#### 1. 环境准备
```bash
# 检查Python环境
python --version  # 需要3.8+

# 检查Node.js环境 (前端开发)
node --version    # 需要16+
pnpm --version    # 包管理器
```

#### 2. 后端服务启动

**方式A: 使用真实的Tornado服务器 (推荐)**
```bash
cd manager-server

# 安装依赖
pip install -r requirements.txt

# 配置数据库 (修改settings/config.py)
# 启动Tornado服务器
python server/server.py
```

**方式B: 使用简化的Flask服务器 (快速开发)**
```bash
# 使用我们创建的简化版本
python simple_flask_server.py
```

#### 3. 前端服务启动
```bash
cd ConnectAI-E-AdminPanel

# 安装依赖
pnpm install

# 配置API地址 (.env-config.ts)
# 启动开发服务器
pnpm run dev
```

#### 4. 知识库API启动
```bash
cd DataChat-API

# 安装依赖并启动
python simple_app.py
```

### 阶段二：真实业务代码开发

#### 1. 后端API开发
```bash
# 在manager-server/server/modules/下开发业务模块
manager-server/server/modules/
├── account/handler.py     # 账户相关API
├── application/handler.py # 应用管理API
└── messenger/handler.py   # 消息处理API
```

#### 2. 数据库模型
```bash
# 在各模块的model.py中定义数据模型
manager-server/server/modules/account/model.py
manager-server/server/modules/application/model.py
```

#### 3. 前端页面开发
```bash
# 在ConnectAI-E-AdminPanel/src/下开发前端页面
ConnectAI-E-AdminPanel/src/
├── views/           # 页面组件
├── api/            # API调用
├── stores/         # 状态管理
└── components/     # 通用组件
```

### 阶段三：集成测试

#### 1. 本地集成测试
```bash
# 启动所有服务
python start_connectai.py  # 后端服务
cd ConnectAI-E-AdminPanel && pnpm run dev  # 前端服务

# 测试完整流程
1. 前端登录
2. API调用
3. 数据库操作
4. 文件上传下载
```

#### 2. API接口测试
```bash
# 使用测试脚本验证API
python test_login_api.py
python test_tenant_api.py
```

### 阶段四：生产环境部署

#### 1. Docker构建
```bash
cd manager-server
make build  # 构建Docker镜像
```

#### 2. 生产环境配置
```bash
# 修改配置文件
manager-server/etc/web_config.conf

# 配置数据库连接
# 配置Redis连接
# 配置消息队列
```

#### 3. Docker部署
```bash
# 使用docker-compose部署
docker-compose up -d
```

## 🔧 开发工具和脚本

### 本地开发脚本
```bash
# 环境检查
python check_environment.py

# 一键启动 (简化版)
python start_connectai.py

# 数据库初始化
python init_database_with_admin.py

# 服务状态测试
python test_services_running.py
```

### 前端开发脚本
```bash
# 前端启动
python start_frontend.py

# 或直接使用
cd ConnectAI-E-AdminPanel
pnpm run dev
```

## 📊 数据流和架构

### 请求流程
```
前端 (Vue3) 
    ↓ HTTP请求
Vite代理 (/api/*)
    ↓ 转发
Manager Server (Tornado/Flask)
    ↓ 数据库查询
SQLite/MySQL
    ↓ 返回数据
前端渲染
```

### 模块依赖
```
account模块 → 用户认证、权限管理
application模块 → AI应用管理、配置
messenger模块 → 消息处理、回调
callback模块 → 第三方集成回调
```

## 🎯 开发建议

### 1. 新功能开发流程
```
1. 在manager-server/server/modules/下创建新模块
2. 定义数据模型 (model.py)
3. 实现API处理器 (handler.py)
4. 在api.py中注册路由
5. 前端调用API并实现UI
6. 编写测试用例
7. 集成测试
```

### 2. 调试技巧
```bash
# 后端调试
- 使用Tornado的debug模式
- 查看日志输出
- 使用断点调试

# 前端调试
- 浏览器开发者工具
- Vue DevTools
- 网络请求监控
```

### 3. 常见问题解决
```bash
# CORS问题
- 检查Flask-CORS配置
- 确认前端代理设置

# 数据库连接问题
- 检查数据库路径
- 验证数据库文件权限

# 依赖问题
- 重新安装requirements.txt
- 检查Python版本兼容性
```

## 📝 代码规范

### 后端代码规范
```python
# 使用Tornado的RequestHandler基类
class LoginHandler(BaseHandler):
    async def post(self):
        # 业务逻辑
        pass

# 数据模型使用SQLAlchemy
class Account(BaseModel):
    __tablename__ = 'account'
    # 字段定义
```

### 前端代码规范
```typescript
// 使用Vue3 Composition API
import { defineComponent } from 'vue'

export default defineComponent({
  setup() {
    // 组件逻辑
  }
})
```

## 🔄 版本管理

### 分支策略
```
main                    # 主分支
privatization          # 私有化标品分支
privatization-channel1 # 定制版本分支
```

### 版本号规则
```
v1.0.0                 # 主版本
v1.0.0-privatization   # 私有化标品版本
v1.0.0-channel1        # 定制版本
```

## 📞 技术支持

### 相关文档
- `README.md` - 项目总体介绍
- `manager-server/server/*/CODE_DOC.md` - 模块详细文档
- `📚 文档索引.md` - 文档导航

### 调试工具
- `test_*.py` - 各种测试脚本
- `check_environment.py` - 环境诊断
- `start_*.py` - 启动脚本

---

## 🎉 总结

ConnectAI项目采用**微服务架构**，支持**本地开发**和**Docker部署**两种模式：

- **本地开发**: 使用简化版Flask服务器和SQLite数据库
- **生产环境**: 使用Tornado服务器和MySQL/Redis等完整技术栈

**推荐开发流程**: 本地开发 → 集成测试 → Docker部署 → 生产发布
