class ChatTool(CTool):
    name: str = 'openai_chat'
    description: str = 'openai chat'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                if platform == 'feishu':
                    if data.extra.extra.get('action', {}).get('value', {}).get('index'):
                        return
                    send_message(
                        AppResult.ReplyCard if 'open_message_id' not in data.extra.extra and 'function_flag' not in data.extra.extra else AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                        ),
                        **{'reply_message_id': data.extra.extra.get('open_message_id')}
                    )
                else:
                    send_message(AppResult.ReplyText, _('正在思考，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        ),
                        **{'reply_message_id': data.extra.extra.get('open_message_id')}
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                logging.info('response: %r', response)
                content = response.generations[0][0].text
                if 'action' not in data.extra.extra:
                    # vision模型或上下文未开启时不启用重新生成
                    history_count = -1 if 'vision_content' in data.extra.extra or instance.extra.get('chat_history', '') != 'enable' else 0
                    user_message_id = data.extra.message_id
                    input_content = data.extra.extra.get('vision_content', input.strip())
                    action_index = 0
                    session.add_message({'role': 'human', 'content': input_content, 'additional_kwargs': {'id': user_message_id}})
                    session.add_message({'role': 'ai', 'content': content, 'additional_kwargs': {'choices': [content]}})
                else:
                    action_value = data.extra.extra.action['value']
                    user_message_id, input_content, action_index, history_index = action_value['id'], action_value['input'], action_value['index'], action_value['history_index']
                    choices = chat_history[history_index].additional_kwargs['choices']
                    if action_index:
                        content = choices[action_index - 1]
                    else:
                        choices.append(content)
                    history_count = len(choices)
                    session.update_message(history_index, {'role': 'ai', 'content': content, 'additional_kwargs': {'choices': choices}})
                actions = []
                for i in range(1, min(history_count, 4) + 1):
                    selected = (i == action_index) or (i == history_count and action_index == 0)
                    actions.append((_('回答 %(i)s', i=i), {'id': user_message_id, 'index': i, 'input': input_content}, selected))
                if 0 <= history_count < 4:
                    actions.append((_('重新生成'), {'id': user_message_id, 'index': 0, 'input': input_content}, False))

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageMarkdown(content),
                            *([FeishuMessageAction(*[FeishuMessageButton(content=content, value=value, type='primary' if selected else 'default') for content, value, selected in actions])] if actions else []),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                        **{'reply_message_id': data.extra.extra.get('open_message_id')}
                    )
                else:
                    send_message(AppResult.ReplyText, content)

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))

        model.callbacks = [OpenAICallbackHandler()]
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        temperature = session.temperature
        api_type = model.openai_api_type
        del model['openai_api_type']
        if 'action' in data.extra.extra and 'index' in data.extra.extra.action['value']:
            action_value = data.extra.extra.action['value']
            history_index = -1
            for i in range(len(chat_history)):
                if chat_history[i].additional_kwargs.get('id', '') == action_value['id']:
                    history_index = i
                    break
            if history_index < 0 or not chat_history[history_index + 1].additional_kwargs.get('choices'):
                if platform == 'feishu':
                    return send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageNote(FeishuMessagePlainText(_('操作无效，该内容已被清除')))
                        ),
                        **{'reply_message_id': action_value['id']}
                    )
            action_value['history_index'] = history_index + 1
            if action_value['index']:
                return ForwardChat(callbacks=model.callbacks).invoke([])
            else:
                input = action_value['input']
                app_model.update_content(input)

        content = input.strip()
        if model_name in ['gpt-4-vision-preview']:
            # 图文消息和纯文本消息都要转换为特定格式
            input_kwargs = data.extra.extra.get('input_kwargs', {'type': 'text', 'text': content})
            if 'input_kwargs' in data.extra.extra:
                del data['extra']['extra']['input_kwargs']
            content = []
            if 'text' in input_kwargs:
                content.append({
                    'type': 'text',
                    'text': input_kwargs['text']
                })
            if 'image_urls' in input_kwargs:
                for image_url in input_kwargs['image_urls']:
                    content.append({
                        'type': 'image_url',
                        'image_url': {'url': image_url}
                    })
            data['extra']['extra']['vision_content'] = content
            model['max_retries'] = 10
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                max_tokens=4096,
                **model,
            )
        elif model_name in ['tts-1', 'tts-1-hd']:
            # TODO 这里临时处理，只处理tts-1
            # 后面会考虑单应用使用多资源，由llm去判断是否调用画图模型
            send_note(_('正在思考，请稍等...'))
            voice = session.get_extra('voice', 'alloy')
            body = httpx.post(
                f"{model.openai_api_base}/audio/speech",
                headers={
                    'Authorization': f'Bearer {model.openai_api_key}',
                    'Content-Type': 'application/json',
                },
                json=dict(
                    model=model_name,
                    input=input,
                    voice=voice,
                ),
                timeout=60,
            ).content
            logging.info("debug tts response %r", len(body))
            # 语音拿到的直接就是一个下载的mp3格式文件
            # 1. mp3文件放到cdn或者本地文件，获取一个url，给用户
            url = syncify(upload_file)({'body': body, 'filename': 'speech.mp3'})
            if platform == 'feishu':
                send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageAction(FeishuMessageButton(_('下载音频'), type='primary', url=url)),
                        FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功') + '\n' + _(FeishuCommand.choice_tip()))),
                        header=FeishuMessageCardHeader(content=_('OpenAI TTS 🎉'), template='blue')
                    )
                )
            else:
                send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        DingDingActionCardButton(url, _('下载音频')),
                        title=_('OpenAI TTS 🎉'),
                    )
                )
            # 2. mp3文件转换opus上传飞书语音消息
            try:
                if platform == 'feishu':
                    content, duration = mp3_to_opus(body)
                    file_key = syncify(client.upload_file_binary)(content, 'speech.opus', duration=duration)
                    logging.info("debug upload audio %r", file_key)
                    send_message(
                        AppResult.ReplyAudio,
                        FeishuAudioMessage(file_key)
                    )
            except Exception as e:
                logging.exception(e)
            return AIMessage(content=url)
        elif model_name == 'dall-e-3':
            send_note(_('正在思考，请稍等...'))
            response = httpx.post(
                f"{model.openai_api_base}/images/generations",
                headers={
                    'Authorization': f'Bearer {model.openai_api_key}',
                    'Content-Type': 'application/json',
                },
                json=dict(
                    model="dall-e-3",
                    prompt=input,
                    n=1,  # 这里暂时只支持一张
                    size="1024x1024"
                ),
                timeout=60,
            ).json()
            logging.info("debug dall3 response %r", response)
            _data = response.get('data', response.get('Data', []))
            if len(_data) > 0:
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('生成成功，正在上传...')))
                        )
                    )
            else:
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('生成失败，请重试...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('生成失败，请重试...'))

            image_url = ''
            count = 0
            for item in _data:
                try:
                    count += 1
                    item_url = item['url']
                    if not item_url.startswith('http'):
                        if item_url[9:].startswith('http'):
                            item_url = item_url[9:-1]
                        else:
                            item_url = parse_urls(item_url)[0]
                    image_url = item_url.replace('oaidalleapiprodscus.blob.core.windows.net', 'mpic.forkway.cn')
                    if platform == 'feishu':
                        img_bytes = syncify(download_bytes)(item_url, True)
                        img_key = syncify(client.upload_image_binary)(img_bytes)
                        if img_key:
                            send_message(
                                AppResult.UpdateCard if count == 1 else AppResult.ReplyNewCard,
                                FeishuMessageCard(
                                    FeishuMessageImage(img_key=img_key, alt="图片"),
                                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                                )
                            )
                    elif platform == 'dingding':
                        send_message(
                            AppResult.ReplyActionCard,
                            DingDingActionCardMessage(
                                title='OpenAI Dall 3 🎉'.capitalize(),
                                text=_('![图片]({})  \n{}').format(image_url, _('🤖️：生成成功')),
                            )
                        )
                except Exception as e:
                    logging.exception(e)
                    if platform == 'feishu':
                        time.sleep(1)
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                FeishuMessageDiv('', tag='lark_md'),
                                FeishuMessageNote(FeishuMessagePlainText(_('上传失败，请重试...')))
                            )
                        )
                    else:
                        send_message(AppResult.ReplyText, _('上传失败，请重试...'))
            message = AIMessage(content=image_url, additional_kwargs=response)
            return message
        elif model_name == 'whisper-1':
            return send_note(_('该模型暂不支持'))
        elif api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )

        if session.system_role:
            system_message = [SystemMessage(content=session.system_role)]
        else:
            system_message = []
        messages = system_message + chat_history + [HumanMessage(content=content)]
        if isinstance(content, str) and instance.extra.get('web_search', '') != 'disable' and session.get_extra('web_search_disable', True) == False:
            data['extra']['extra']['function_flag'] = True
            send_note(_('正在思考，请稍等...'))
            if api_type == 'azure':
                web_search_res, content = web_search(content, region=data.lang, max_results=5)
                messages[-1].content = content
            else:
                web_search_func = {
                    'type': 'function',
                    'function': {
                        "name": "web_search",
                        "description": "A custom search engine. Useful for when you need to answer questions about current events. Input should be a search query. Outputs a JSON array of results.",
                        "parameters": {
                            "type": "object",
                            "properties": {"keyword": {"type": "string"}},
                            "required": ["keyword"],
                        },
                    }
                }
                func_chat = type(chat)(temperature=0, model_name=model_name, **model)
                func_chat.callbacks = []
                func_res = func_chat.invoke(messages, tools=[web_search_func], tool_choice='auto')
                for func in func_res.additional_kwargs.get('tool_calls', []):
                    try:
                        func_args = json.loads(func['function']['arguments'])
                    except Exception as e:
                        func_args = {}
                    func_name = func['function']['name']
                    func_content = ''
                    if func_name == 'web_search':
                        web_search_res = web_search(func_args.get('keyword', '').strip() or content, region=data.lang, max_results=5)[0]
                        if not web_search_res:
                            continue
                        func_content = ", ".join([f"[{y}]" for y in [", ".join([f"{k}: {v}" for k, v in x.items()]) for x in web_search_res]])
                        func_content += '\n' + 'Instructions: Using the provided web search results, write a comprehensive reply to the given query. Make sure to cite results using [[title](url)] notation after the reference. If the provided search results refer to multiple subjects with the same name, write separate answers for each subject.  The title and url in notation is from web search results, and DONOT use number as notation.'
                    messages.append(FunctionMessage(
                        name=func_name,
                        content=func_content,
                        additional_kwargs={'tool_call_id': func['id']}
                    ))
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class DingdingCommand(CommandTool):
    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_dingding_command'
    description: str = 'openai chat dingding command'

    def send_usage(self):
        tech = '大语言模型'
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    DingDingActionURL('/web'),
                    _('🌏 联网搜索')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/clear',
                    _('🆑 清除话题上下文')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/model',
                    _('🚀 AI模型切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/ai_mode',
                    _('🤖 发散模式选择')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/roles',
                    _('🏠 内置角色列表')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                    _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_("👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！", tech=tech)
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:4] == '/web' or input[:2] == '联网':
            return 'web_search',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            mode_name = input.replace('/ai_mode', '').replace('发散模式', '').strip()
            return 'ai_mode', mode_name
        elif input[:6] == '/roles' or input[:2] == '角色':
            role_name = input.replace('/roles', '').replace('角色列表', '').replace('角色', '').strip()
            return 'roles', role_name
        elif input[:7] == '/system' or input[:4] == '角色扮演':
            system_role = input.replace('/system', '').replace('角色扮演', '').strip()
            return 'system', system_role
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        elif data.extra.message_type in ['richText']:
            return data.extra.message_type, data.extra.extra.platform_content
        return 'note',

    def on_web_search(self, flag=None):
        if flag is None:
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    DingDingActionCardButton(DingDingActionURL(quote('/web_search 1')), _('开启')),
                    DingDingActionCardButton(DingDingActionURL(quote('/web_search 0')), _('关闭')),
                    title=_('您确定更改联网搜索吗？'),
                    text=_('开启联网搜索可以让AI查询最新的信息，并提供更多最新和准确的答复'),
                ),
            )
        flag = bool(int(flag))
        session.set_extra('web_search_disable', not flag)
        if instance.extra.get('web_search', '') == 'disable' and flag:
            return self.on_note(_('无法开启联网搜索，已在管理后台关闭'), _('👺 机器人提醒'))
        return self.on_note(_('已开启联网搜索') if flag else _('已关闭联网搜索'), _('👺 机器人提醒'))

    def on_clear(self):
        session['chat_history'] = []
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_("🆑 机器人提醒"),
                text=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧"),
            )
        )

    def on_model(self, model_name=None):
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            last_model_name = m.get(session.model_id, ai_model[0][1])
            action_value = m[model_name]
            session['model_id'] = action_value
            if last_model_name in ['gpt-4-vision-preview'] or model_name in ['gpt-4-vision-preview']:
                # gpt4v模型切换后要主动清除上下文，否则会报错
                session['chat_history'] = []
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择模型：%(model)s", model=model_name),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🚀 机器人提醒'),
                        text=_('无可用模型'),
                    )
                )
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/model ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下模型：'),
                    title=_('🚀 AI模型切换'),
                ),
            )

    def on_ai_mode(self, mode_name=None):
        m = {content: value for value, content in ai_mode}
        if mode_name and mode_name in m:
            action_value = m[mode_name]
            session['temperature'] = float(action_value)
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🤖 机器人提醒'),
                    text=_("已选择模式：%(mode)s", mode=mode_name),
                ),
            )
        else:
            if len(ai_mode) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🤖 机器人提醒'),
                        text=_('无可用模式'),
                    )
                )
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/ai_mode ' + content)),
                        content
                    ) for _, content in ai_mode],
                    text=_('选择以下模式：'),
                    title=_('🤖 发散模式切换'),
                ),
            )

    def on_roles(self, role_name=None):
        m = {content: value for value, content in prompts}
        if role_name and role_name in m:
            action_value = m[role_name]
            prompt = get_prompt_by_id(action_value)
            session['prompt_id'] = action_value
            session['system_role'] = prompt.content
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('👺 已进入角色扮演模式'),
                    text=prompt.content,
                )
            )
        else:
            if len(prompts) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('👺 机器人提醒'),
                        text=_('无可用角色'),
                    )
                )
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/roles ' + content)),
                        content
                    ) for _, content in prompts],
                    text=_('选择以下内置角色：'),
                    title=_('🏠 内置角色选择'),
                )
            )

    def on_system(self, system_role=None):
        if not(system_role):
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('👺 机器人提醒'),
                    text=_('文本回复*角色扮演* 或 */system*+空格+角色信息'),
                )
            )
        session['prompt_id'] = ''
        session['system_role'] = system_role
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                text=system_role,
                title=_('👺 已进入角色扮演模式'),
            )
        )

    def on_richText(self, platform_content):
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        if model_name not in ['gpt-4-vision-preview']:
            return self.on_note(text=_('该模型不支持图文消息，请切换模型后使用!'), title=_('🚀 机器人提醒'))
        robot_code = bot.app_id
        input_text, input_image_keys = '', []
        for msg in platform_content['richText']:
            if 'text' in msg and '@' not in msg and not input_text:
                input_text = msg['text'].strip()
            elif msg.get('type', '') == 'picture':
                input_image_keys.append(msg['downloadCode'])
        input_text = input_text.strip()
        if not input_image_keys:
            return self.on_note(text=_('请直接向我发送图文消息。'))
        image_urls = get_event_loop().run_until_complete(asyncio.gather(*[functools.partial(client.get_message_resource, robot_code, download_code)() for download_code in input_image_keys]))
        for image_url in image_urls:
            if not self.check_image_format(image_url):
                return self.on_note(text=_('请直接向我发送图文消息。'))
        data['extra']['extra']['input_kwargs'] = {'text': input_text, 'image_urls': image_urls}
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或图片加文字一起发送（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/图文！')
        return send_note(text, title)

    def check_image_format(self, image_url):
        image_type = image_url.split('?')[0].split('.')[-1]
        if not image_type:
            return True
        return image_type.lower() in ['png', 'jpeg', 'jpg', 'webp', 'gif']


class WEWorkCommand(CommandTool):
    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_wework_command'
    description: str = 'openai chat wework command'

    def send_usage(self):
        tech = '大语言模型'
        return send_message(
            AppResult.ReplyTemplateCard,
            ButtonInteractionCardMessage(
                WXCardButton(_('🌏 联网搜索'), '/web_search'),
                WXCardButton(_('🆑 清除话题上下文'), '/clear'),
                WXCardButton(_("🚀 AI模型切换"), '/model'),
                WXCardButton(_('🤖 发散模式选择'), '/ai_mode'),
                WXCardButton(_('🏠 内置角色列表'), '/roles'),
                main_title=WXCardMainTitle(
                    _("🎒 需要帮助吗？"),
                    desc=_("👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！", tech=tech)
                ),
                source=WXCardSource(instance.name, instance.icon or app.icon)
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:4] == '/web' or input[:2] == '联网':
            return 'web_search',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            mode_name = input.replace('/ai_mode', '').replace('发散模式', '').strip()
            return 'ai_mode', mode_name
        elif input[:6] == '/roles' or input[:2] == '角色':
            role_name = input.replace('/roles', '').replace('角色列表', '').replace('角色', '').strip()
            return 'roles', role_name
        elif input[:7] == '/system' or input[:4] == '角色扮演':
            system_role = input.replace('/system', '').replace('角色扮演', '').strip()
            return 'system', system_role
        elif input[0] == '/' and not input.replace('/', ''):
            return 'help',
        elif data.extra.extra.get('SelectedItems', {}):
            selected_items = data.extra.extra.get('SelectedItems', {}).get('SelectedItem', {})
            question_key = selected_items.get('QuestionKey')
            option_id = selected_items.get('OptionIds', {}).get('OptionId')
            if question_key and option_id:
                return question_key, option_id

        return None,

    def on_web_search(self, flag=None):
        if flag is None:
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        WXCardSelectOption('/web_search 1', _('开启')),
                        WXCardSelectOption('/web_search 0', _('关闭')),
                        question_key='web_search',
                        title=_('🌏 联网搜索'),
                    ),
                    main_title=_('🌏 联网搜索'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )
        flag = bool(int(flag))
        session.set_extra('web_search_disable', not flag)
        if instance.extra.get('web_search', '') == 'disable' and flag:
            return self.on_note(_('无法开启联网搜索，已在管理后台关闭'), _('👺 机器人提醒'))
        return self.on_note(_('已开启联网搜索') if flag else _('已关闭联网搜索'), _('👺 机器人提醒'))

    def on_clear(self):
        session['chat_history'] = []
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.ReplyCard,
            WXTextCard(
                title=_("🆑 机器人提醒"),
                description=_("已删除此话题的上下文信息\n\n我们可以开始一个全新的话题，继续找我聊天吧"),
                url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                btntxt=_('详情'),
            )
        )

    def on_model(self, action_value=None):
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        if action_value:
            session['model_id'] = action_value
            m = {value: content for value, content in ai_model_options}
            model_name = m[action_value]
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('🚀 机器人提醒'),
                    description=_("已选择模型：%(model)s", model=model_name),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('🚀 机器人提醒'),
                        description=_('无可用模型'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, content) for value, content in ai_model_options],
                        question_key='model',
                        title=_('选择以下模型：'),
                    ),
                    main_title=_('🚀 AI模型切换'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )

    def on_ai_mode(self, action_value=None):
        if action_value:
            session['temperature'] = float(action_value)
            m = {str(value): content for value, content in ai_mode}
            mode_name = m.get(action_value, action_value)
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('🤖 机器人提醒'),
                    description=_("已选择模式：%(mode)s", mode=mode_name),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                ),
            )
        else:
            if len(ai_mode) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('🤖 机器人提醒'),
                        description=_('无可用模式'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, text=content) for value, content in ai_mode],
                        question_key='ai_mode',
                        title=_('选择以下模式：'),
                    ),
                    main_title=_('🤖 发散模式切换'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                ),
            )

    def on_roles(self, action_value=None):
        if action_value:
            prompt = get_prompt_by_id(action_value)
            session['prompt_id'] = action_value
            session['system_role'] = prompt.content
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('👺 已进入角色扮演模式'),
                    description=prompt.content,
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        else:
            if len(prompts) == 0:
                return send_message(
                    AppResult.ReplyCard,
                    WXTextCard(
                        title=_('👺 机器人提醒'),
                        description=_('无可用角色'),
                        url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                        btntxt=_('详情'),
                    )
                )
            return send_message(
                AppResult.ReplyTemplateCard,
                MultipleInteractionCardMessage(
                    WXCardButtonSelection(
                        *[WXCardSelectOption(value, content) for value, content in prompts[:10]],
                        question_key='roles',
                        title=_('选择以下内置角色：'),
                    ),
                    main_title=_('🏠 内置角色选择'),
                    submit_button=_('确定'),
                    source=WXCardSource(instance.name, instance.icon or app.icon)
                )
            )

    def on_system(self, system_role=None):
        if not system_role:
            return send_message(
                AppResult.ReplyCard,
                WXTextCard(
                    title=_('👺 机器人提醒'),
                    description=_('文本回复*角色扮演* 或 */system*+空格+角色信息'),
                    url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                    btntxt=_('详情'),
                )
            )
        session['prompt_id'] = ''
        session['system_role'] = system_role
        return send_message(
            AppResult.ReplyCard,
            WXTextCard(
                description=system_role,
                title=_('👺 已进入角色扮演模式'),
                url=app.manual if data.lang == 'zh_CN' else app.manual_en,
                btntxt=_('详情'),
            )
        )


class FeishuCommand(CommandTool):
    next_tool_name: str = 'openai_chat'
    name: str = 'openai_chat_feishu_command'
    description: str = 'openai chat feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<清除> 或 /clear 即可清除上下文',
        '输入<发散模式> 或 /ai_mode 即可选择发散模式',
        '输入<角色列表> 或 /roles 即可选择内置角色',
        '输入<角色扮演> 或 /system+空格+角色信息 即可进入角色扮演模式',
        '输入<联网> 或 /web 即可开启或关闭联网搜索功能',
    ]

    @property
    def web_search_select(self):
        return FeishuMessageSelect(
            FeishuMessageOption('1', _('开启')),
            FeishuMessageOption('0', _('关闭')),
            # initial_option=str(int(instance.extra.get('web_search', '') != 'disable' and not session.extra.get('web_search_disable'))),
            initial_option=str(int(instance.extra.get('web_search', '') != 'disable' and session.get_extra('web_search_disable', True) == False)),
            value={'command': 'web_search'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改联网搜索吗？'),
                text=_('开启联网搜索可以让AI查询最新的信息，并提供更多最新和准确的答复'),
            )
        )

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def voice_options(self):
        return ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer']

    @property
    def voice_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(voice, voice) for voice in self.voice_options],
            placeholder=_('选择声音'),
            initial_option=session.get_extra('voice', self.voice_options[0]),
            value={'command': 'voice'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改声音吗？'),
                text=_('选择声音，找到一种与你想要的音调和听众相匹配的声音。'),
            )
        )

    @property
    def ai_mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in ai_mode],
            placeholder=_('选择模式'),
            initial_option=str(float(session.temperature)),
            value={'command': 'ai_mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改发散模式吗？'),
                text=_('选择内置模式，可以让AI更好的理解您的需求。'),
            )
        )

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def prompt_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in prompts],
            placeholder=_('选择内置角色'),
            initial_option=session.prompt_id or None,
            value={'command': 'prompt'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改角色吗？'),
                text=_('选择内置场景，快速进入角色扮演模式。'),
            )
        ) if len(prompts) > 0 else None

    def send_usage(self):
        tech = '大语言模型'
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**👋 你好呀，我是一款基于%(tech)s技术的智能聊天机器人！**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉', tech=tech),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                *([FeishuMessageHr(),
                FeishuMessageDiv(
                    _('**🌏 联网搜索**\n文本回复 *联网* 或 */web*'),
                    tag='lark_md',
                    extra=self.web_search_select,
                )] if instance.extra.get('web_search', '') != 'disable' else []),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **发散模式选择**\n文本回复 *发散模式* 或 */ai_mode*'),
                    tag='lark_md',
                    extra=self.ai_mode_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🏠 **内置角色列表**\n文本回复 *角色列表* 或 */roles*'),
                    tag='lark_md',
                    extra=self.prompt_select
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('👺 **角色扮演模式**\n文本回复*角色扮演* 或 */system*+空格+角色信息'),
                    tag='lark_md',
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:4] == '/web' or input[:2] == '联网':
            return 'web_search',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/voice' or input[:2] == '声音':
            return 'voice',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            return 'ai_mode',
        elif input[:6] == '/roles' or input[:4] == '角色列表':
            return 'roles',
        elif input[:7] == '/system' or input[:4] == '角色扮演':
            system_role = input.replace('/system', '').replace('角色扮演', '').strip()
            return 'system', system_role
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
                elif 'index' in action['value']:
                    return None,
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        elif data.extra.message_type in ['post']:
            return data.extra.message_type, data.extra.extra.platform_content
        return 'note',

    def on_web_search(self, flag=None):
        if flag is None:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.web_search_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('开启后，AI可以查询最新的信息，并提高更多最新和准确的答复'))
                    ),
                    header=FeishuMessageCardHeader(_('🌏 联网搜索'), template='blue'),
                )
            )
        flag = bool(int(flag))
        session.set_extra('web_search_disable', not flag)
        if instance.extra.get('web_search', '') == 'disable' and flag:
            return self.on_note(_('无法开启联网搜索，已在管理后台关闭'), _('🤖 机器人提醒'))
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv((_('已开启联网搜索') if flag else _('已关闭联网搜索')) + '。' + _('仅在纯语言模型中生效，不支持和生图/多模态模型混用')),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            ),
        )

    def on_clear(self, flag=None, reply_log_id=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')),
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        session['chat_history'] = []
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.SendCard if not reply_log_id else AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            ),
            **({'reply_log_id': reply_log_id} if reply_log_id else {})
        )

    def on_voice(self, voice_name=None):
        if voice_name in self.voice_options:
            session.set_extra('voice', voice_name)
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择声音：**%(voice)s**', voice=voice_name),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('无法选择声音'), tag="lark_md"),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
            )
        )

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            last_model_name = m.get(session.model_id, ai_model[0][1])
            session['model_id'] = model_name
            real_model_name = m.get(model_name, model_name)
            if real_model_name in ['tts-1', 'tts-1-hd']:
                return send_message(
                    AppResult.ReplyCard,
                    FeishuMessageCard(
                        FeishuMessageAction(self.voice_select),
                        FeishuMessageNote(
                            FeishuMessagePlainText(_('提醒：选择不同的声音，找到一种与你想要的音调和听众相匹配的声音。'))
                        ),
                        header=FeishuMessageCardHeader(_('🚀 声音切换'), template='blue'),
                    )
                )
            elif last_model_name in ['gpt-4-vision-preview'] or real_model_name in ['gpt-4-vision-preview']:
                # gpt4v模型切换后要主动清除上下文，否则会报错
                session['chat_history'] = []
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择模型：**%(model)s**', model=real_model_name),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_ai_mode(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模式，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 发散模式选择'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_mode}
        session['temperature'] = float(mode_name)
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('已选择发散模式：**%(mode)s**', mode=m.get(mode_name, mode_name)),
                    tag="lark_md",
                ),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_prompt(self, *args):
        return self.on_roles(*args)

    def on_roles(self, role_name=None):
        if not role_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.prompt_select) if len(prompts) else FeishuMessageDiv(_("无可用角色选择")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置场景，快速进入角色扮演模式。'))
                    ),
                    header=FeishuMessageCardHeader(_('🏠 请选择角色'), template='blue'),
                )
            )
        prompt = get_prompt_by_id(role_name)
        session['prompt_id'] = role_name
        session['system_role'] = prompt.content
        return send_message(
            AppResult.SendCard,  # 这里还使用replycard会引用一大短文字
            FeishuMessageCard(
                FeishuMessageDiv(prompt.content),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'))
                ),
                header=FeishuMessageCardHeader(_('👺 已进入角色扮演模式'), template='blue'),
            )
        )

    def on_system(self, system_role=None):
        if not(system_role):
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('文本回复*角色扮演* 或 */system*+空格+角色信息', tag='lark_md')),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_("👺 **角色扮演模式**"), template='blue'),
                )
            )
        session['prompt_id'] = ''
        session['system_role'] = system_role
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(system_role),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'))
                ),
                header=FeishuMessageCardHeader(_('👺 已进入角色扮演模式'), template='blue'),
            )
        )

    def on_post(self, platform_content):
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        if model_name not in ['gpt-4-vision-preview']:
            return self.on_note(text=_('该模型不支持图文消息，请切换模型后使用!'), title=_('🚀 机器人提醒'))
        message_id = data.extra.message_id
        input_text, input_image_keys = '', []
        for msg_line in platform_content['content']:
            for msg in msg_line:
                if msg['tag'] == 'text' and not input_text:
                    input_text = msg['text'].strip()
                elif msg['tag'] == 'img':
                    input_image_keys.append(msg['image_key'])
        input_text = input_text.strip()
        if not input_image_keys:
            return self.on_note(text=_('请直接向我发送图文消息。'))
        image_urls = [f'{options.SCHEMA}://{options.DOMAIN}/api/feishu/image/message?message_id={message_id}&image_key={image_key}&auto_download=' for image_key in input_image_keys]
        data['extra']['extra']['input_kwargs'] = {'text': input_text, 'image_urls': image_urls}
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或图片加文字一起发送（图片仅支持png、gif、webp、jpg、jpeg），不支持文件、链接等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/图文！')
        return send_note(text, title)


class OpenaiAgent(CAgent):
    class OpenAIAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'openai'
        title = 'OpenAI聊天机器人'
        title_en = 'OpenAI Chatbot'
        description = '🚀 下一代IM：融合了GPT-4、DALL·E和Whisper，开创非凡工作体验！💼'
        description_en = '🚀 Next-generation IM: Integrating GPT-4, DALL·E and Whisper to create an extraordinary work experience! 💼'
        problem = '如何让 OpenAI 优化 IM 平台的协同体验'
        problem_en = 'let Open AI optimize the collaborative experience of the IM platform'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/UE28dJnpxo5KlmxbfITcpGmLnhg?from=from_copylink'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/IeoKdfvXto2nbmxx5BNlGHW0gNg'
        icon = 'https://pic1.forkway.cn/cdn/202308230934893.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308230934893.png?imageMogr2/thumbnail/720x'
        sorted = 1
        support_resource = [dict(
            category=ModelCategory.All.value,
            title='大模型',
            resource=['OpenAI', 'Azure']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnbNTu98ASACpGaHgA4kXOqd'
        deploy = 'https://connect-ai.feishu.cn/docx/W5gXd0KhqomvsGxM7PGcle30nwc'


    class AzureAppConfig(BaseAppConfig):
        category = 'LLM'
        name = 'azure'
        title = 'Azure聊天机器人'
        title_en = 'Azure Chatbot'
        description = '🎯 企业级 Azure OpenAI GPT 加速你的办公效率'
        description_en = '🎯 Accelerate your office efficiency with enterprise-level Azure OpenAI GPT.'
        problem = '可以在飞书中使用自己的Azure OpenAI吗？'
        problem_en = 'Can I use my own Azure Open AI in Lark?'
        video = ''
        video_en = ''
        manual = 'https://connect-ai.feishu.cn/docx/G0WWdCm0eo1yBzxF5CacXXjunjg'
        manual_en = 'https://q5o2cctqdb7.sg.larksuite.com/docx/Y2nZdU0isov1avxalDTlgLe6gJf'
        icon = 'https://pic1.forkway.cn/cdn/202308211421653.png?imageMogr2/thumbnail/720x'
        logo = 'https://pic1.forkway.cn/cdn/202308211421653.png?imageMogr2/thumbnail/720x'
        sorted = 10000
        support_resource = [dict(
            category=ModelCategory.All.value,
            title='大模型',
            resource=['OpenAI', 'Azure']
        )]
        support_bots = ['feishu', 'dingding', 'wework']
        feedback_url = 'https://connect-ai.feishu.cn/share/base/form/shrcnsIqL5k5TT8ryhXl3xLmkGd'
        deploy = 'https://connect-ai.feishu.cn/wiki/RwK1wyjdJi2FFuk9S3mcrO0znkb'


    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        if platform == 'feishu':
            return AgentAction(tool='openai_chat_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='openai_chat_dingding_command', tool_input=kwargs, log="")
        elif platform in ['wework', 'wxwork']:
            return AgentAction(tool='openai_chat_wework_command', tool_input=kwargs, log="")
        return AgentAction(tool='openai_chat', tool_input=kwargs, log="")


