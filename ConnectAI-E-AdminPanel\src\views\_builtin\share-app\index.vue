<template>
  <detail
    :id="appId"
    ref="detailPanel"
    :show="true"
    :share="true"
    :is-full-screen="true"
    @close="false"
    @full-screen="true"
    @min-screen="false"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useTitle } from '@vueuse/core';
import detail from '@/views/bot/market/components/detail.vue';
const { query, fullPath } = useRoute();
const appId = ref('');
onMounted(() => {
  // 获取路由
  const idMatch = /id=([^&]+)/.exec(fullPath);
  const id = idMatch?.[1];
  if (!id) return;
  appId.value = id as any;
  console.log(appId.value);
});
</script>

<style scoped></style>
