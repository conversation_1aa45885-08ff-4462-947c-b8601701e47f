import base64
from Crypto.Cipher import AES
import json
import time
import logging
import hashlib
import uuid
from tornado.options import options
from datetime import datetime
from html.entities import codepoint2name, html5
from urllib.parse import quote
from tornado.httpclient import AsyncHTTPClient, HTTPRequest, HTTPError

from core.redisdb import stalecache
from core.exception import InternalError, InvalidEventException
from core.decrypt import AES<PERSON>ipher
from core.utils import ObjectDict, _
from core.downloader import ChunkedDownloader
from typing import Dict
from urllib3.filepost import encode_multipart_formdata


LARK_HOST = 'https://open.feishu.cn'


class Event(object):

    # event base
    def __init__(self, request, token, encrypt_key):
        # event check and init
        self.request = request
        body = json.loads(request.body.decode())
        if body.get('encrypt'):
            self.data = ObjectDict(self._decrypt_data(encrypt_key, body['encrypt']))
        else:
            # card url
            self.data = ObjectDict(body)

        if self.data.get('header'):
            self._validate(token, encrypt_key)

    def _validate(self, token, encrypt_key):
        if token and self.data.header.token != token:
            raise InvalidEventException("invalid token")
        if not encrypt_key:  # 没有传encrypt_key不校验signature
            return
        timestamp = self.request.headers.get("X-Lark-Request-Timestamp")
        nonce = self.request.headers.get("X-Lark-Request-Nonce")
        signature = self.request.headers.get("X-Lark-Signature")
        body = self.request.body
        bytes_b1 = (timestamp + nonce + encrypt_key).encode("utf-8")
        bytes_b = bytes_b1 + body
        h = hashlib.sha256(bytes_b)
        if signature != h.hexdigest():
            raise InvalidEventException("invalid signature in event")

    def _decrypt_data(self, encrypt_key, encrypt_data):
        cipher = AESCipher(encrypt_key)
        return json.loads(cipher.decrypt_string(encrypt_data))

    @property
    def event_type(self):
        return self.data.header.event_type

    @property
    def event_handler_name(self):
        if 'header' in self.data:
            return self.data.header.event_type.replace('.', '_')
        if 'event' in self.data and 'type' in self.data.event:
            return self.data.event.type
        return 'action' if 'action' in self.data else 'unkown'

    @property
    def app_id(self):
        if 'header' in self.data:
            return self.data.header.app_id
        if 'event' in self.data and 'app_id' in self.data.event:
            return self.data.event.app_id
        return 'unkown'

    @property
    def message(self):
        message = self.data.event.message
        try:
            message['content'] = json.loads(message['content'])
            # 移除@机器人
            for mention in message.get('mentions', []):
                message['content']['text'] = message['content']['text'].replace(mention['key'] + ' ', '')
            # 移除@_all
            message['content']['text'] = message['content']['text'].replace('@_all ', '')
        except Exception as e:
            logging.warn(e)
        return message

    @property
    def at_all(self):
        return '@_all' == self.data.event.message.content[9:14]

    @property
    def sender(self):
        return self.data.event.sender

    @property
    def operator(self):
        return self.data.event.operator


class Lark(object):

    def __init__(self, app_id=None, app_secret=None):
        self.app_id = app_id
        self.app_secret = app_secret

    @property
    def access_token_cache_key(self):
        return "connectai:access_token:{}".format(self.app_id)

    @stalecache(expire=600, stale=1800, attr_key="access_token_cache_key", namespace='connectai')
    async def get_tenant_access_token(self):
        """获取 tenant_access_token"""
        # https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/tenant_access_token_internal
        url = '{api}/open-apis/auth/v3/tenant_access_token/internal'.format(
            api=LARK_HOST,
        )
        request = HTTPRequest(url, method='POST', body=json.dumps({
            'app_id': self.app_id,
            'app_secret': self.app_secret,
        }), headers={'Content-Type': 'application/json'})
        try:
            response = await AsyncHTTPClient().fetch(request)
        except Exception as e:
            raise InternalError("获取 tenant_access_token 失败")
        result = json.loads(response.body.decode())
        logging.info(result)
        if "tenant_access_token" not in result:
            raise InternalError("获取 tenant_access_token 失败")
        return result.get("tenant_access_token")

    async def get_messages_by_chat(self, chat_id, page_token='', page_size=20):
        # https://open.feishu.cn/open-apis/im/v1/messages
        # https://open.feishu.cn/open-apis/im/v1/messages?container_id=oc_65a816f249cea5d4e4e0cef7d5c565b3&container_id_type=chat&page_size=20&sort_type=ByCreateTimeDesc
        response = await self.request('{}/open-apis/im/v1/messages?container_id={}&container_id_type=chat&page_token={}&page_size={}&sort_type=ByCreateTimeDesc'.format(
            LARK_HOST, chat_id,
            page_token, page_size,
        ))
        return response.data

    async def gen_recent_messages_by_chat(self, chat_id, size=1000):
        count = 1
        page_token = ''
        while True:
            result = await self.get_messages_by_chat(chat_id, page_token=page_token, page_size=20)
            for item in result.get('items', []):
                count += 1
                yield item
                if count > size:
                    break
            if not result.has_more:
                break
            page_token = result.page_token

    async def get_recent_messages_by_chat(self, chat_id, size=1000):
        result = []
        async for item in self.gen_recent_messages_by_chat(chat_id, size=size):
            result.append(item)
        return result

    async def get_messages_by_id(self, message_id):
        # https://open.feishu.cn/open-apis/im/v1/messages/:message_id
        response = await self.request('{}/open-apis/im/v1/messages/{}'.format(
            LARK_HOST, message_id,
        ))
        if len(response.data.get('items', [])) > 0:
            return response.data['items'][0]
        return None

    async def send_text(self, receive_id, text, receive_id_type='open_id'):
        return await self.send(
            receive_id,
            {'text': text},
            receive_id_type=receive_id_type,
            msg_type='text'
        )

    async def send_card(self, receive_id, title, *elements, actions=None, config=None, receive_id_type='open_id'):
        if isinstance(actions, list) and len(actions) > 0:
            elements = list(elements) + [{
                "tag": "action",
                "layout": "bisected",
                "actions": actions,
            }]
        content = {
            'elements': elements,
            'config': config if config else {'wide_screen_mode': True},
        }
        if title:
            content['header'] = {'title': {'tag': 'plain_text', 'content': title}}
        return await self.send(
            receive_id, content,
            receive_id_type=receive_id_type,
            msg_type='interactive'
        )

    async def send(self, receive_id, content, msg_type='text', receive_id_type='open_id'):
        # https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create
        url = "{}/open-apis/im/v1/messages?receive_id_type={}".format(
            LARK_HOST,
            receive_id_type,
        )
        body = {
            'receive_id': receive_id,
            'content': json.dumps(content),
            'msg_type': msg_type,
        }
        return await self.request(url, body)

    async def reply_text(self, message_id, text):
        return await self.reply(
            message_id,
            {'text': _(text)},
            msg_type='text'
        )

    async def reply_card(self, message_id, title, *elements, actions=None, config=None):
        if isinstance(actions, list) and len(actions) > 0:
            elements = list(elements) + [{
                "tag": "action",
                "layout": "bisected",
                "actions": actions,
            }]
        content = {
            'elements': elements,
            'config': config if config else {'wide_screen_mode': True},
        }
        if title:
            content['header'] = {'title': {'tag': 'plain_text', 'content': _(title)}}
        return await self.reply(message_id, content, msg_type='interactive')

    async def update_card(self, message_id, title, *elements, actions=None, config=None):
        if isinstance(actions, list) and len(actions) > 0:
            elements = list(elements) + [{
                "tag": "action",
                "layout": "bisected",
                "actions": actions,
            }]
        content = {
            'elements': elements,
            'config': config if config else {'wide_screen_mode': True},
        }
        if title:
            content['header'] = {'title': {'tag': 'plain_text', 'content': _(title)}}

        return await self.update(message_id, content)

    async def update(self, message_id, content):
        # https://open.feishu.cn/open-apis/im/v1/messages/:message_id
        url = "{}/open-apis/im/v1/messages/{}".format(
            LARK_HOST,
            message_id
        )
        body = {
            'content': json.dumps(content),
        }
        return await self.request(url, body, method='PATCH')

    async def reply(self, message_id, content, msg_type='text'):
        # https://open.feishu.cn/document/server-docs/im-v1/message/reply
        url = "{}/open-apis/im/v1/messages/{}/reply".format(
            LARK_HOST,
            message_id,
        )
        body = {
            'uuid': str(uuid.uuid4()),
            'content': json.dumps(content),
            'msg_type': msg_type,
        }
        return await self.request(url, body)

    # 获取用户信息
    # 缓存60s就算过期，但是会存一天，保持后台异步刷新缓存
    @stalecache(expire=60, stale=86400)
    async def get_user_info(self, open_id):
        # https://open.feishu.cn/document/server-docs/contact-v3/user/get
        response = await self.request('{}/open-apis/contact/v3/users/{}'.format(
            LARK_HOST, open_id,
        ))
        return response.data.user

    async def get_user_name(self, open_id):
        try:
            user_info = await self.get_user_info(open_id)
            return user_info.name
        except Exception as e:
            logging.error(e)
            return ''

    async def get_user_lang(self, open_id):
        try:
            user_info = await self.get_user_info(open_id)
            if user_info.country in ['CN', '']:
                return 'zh_CN'
            if user_info.country in ['US']:
                return 'en_US'
            return options.DEFAULT_LOCALE
        except Exception as e:
            logging.error(e)
            return options.DEFAULT_LOCALE

    @stalecache()
    async def bot_info(self, app_id=''):
        try:
            response = await self.request('{}/open-apis/bot/v3/info'.format(
                LARK_HOST,
            ))
            return response.bot
        except Exception as e:
            logging.error(e)
            if hasattr(e, 'response'):
                logging.error("response: %r", e.response.body)
            return {}

    @stalecache()  # 获取群信息使用缓存
    async def get_chat_info(self, chat_id):
        # https://open.feishu.cn/document/server-docs/group/chat/get-2
        try:
            response = await self.request('{}/open-apis/im/v1/chats/{}'.format(
                LARK_HOST, chat_id,
            ))
            return response.data
        except Exception as e:
            logging.error(e)
            if hasattr(e, 'response'):
                logging.error("response: %r", e.response.body)
            return {}

    async def get_chat_name(self, chat_id):
        chat_info = await self.get_chat_info(chat_id)
        return chat_info.get('name')

    async def get_chat_mode(self, chat_id):
        # 获取群聊类型 p2p/group/topic
        chat_info = await self.get_chat_info(chat_id)
        return chat_info.get('chat_mode')

    async def request(self, url, body=None, method='', content_type='application/json'):
        tenant_access_token = await self.get_tenant_access_token()
        headers = {
            'Content-Type': content_type,
            'Authorization': 'Bearer {}'.format(tenant_access_token),
        }
        if not method:
            method = 'POST' if body else 'GET'
        request = HTTPRequest(
            url,
            method=method,
            body=json.dumps(body) if body and content_type == 'application/json' else body or None,
            request_timeout=120,
            headers=headers,
            allow_nonstandard_methods=True,
        )
        if content_type == 'application/json':
            logging.info("Request: %r %r %r", request.url, request.headers, request.body)
        response = await AsyncHTTPClient().fetch(request, raise_error=False)
        logging.info("Response: %r %r", response.headers['Content-Type'], response.body[:200] if len(response.body) >= 1000 else response.body)
        if 'application/json' not in response.headers['Content-Type']:
            return response.body
        return ObjectDict(json.loads(response.body.decode()))

    async def upload_image(self, img_url):
        # https://open.feishu.cn/document/server-docs/im-v1/image/create
        try:
            # file_response = await AsyncHTTPClient().fetch(img_url, request_timeout=60)
            file_content = await ChunkedDownloader().download(img_url)
        except Exception as e:
            logging.exception(e)
            file_content = await ChunkedDownloader().download(img_url)
        logging.info("download file %r %r", img_url, len(file_content))
        return await self.upload_image_binary(file_content)

    async def download_image(self, image_key):
        # https://open.feishu.cn/document/server-docs/im-v1/image/get
        url = f'{LARK_HOST}/open-apis/im/v1/images/{image_key}'
        return await self.request(url)
    
    async def upload_image_binary(self, img_bin):
        url = f"{LARK_HOST}/open-apis/im/v1/images"
        encoded, content_type = encode_multipart_formdata([
            ('image', ('image', img_bin)),
            ('image_type', 'message'),
        ])
        response = await self.request(url, encoded, method='POST', content_type=content_type)
        return response['data']['image_key']

    async def upload_file(self, file_url, filename=""):
        # https://open.feishu.cn/document/server-docs/im-v1/file/create
        try:
            file_content = await ChunkedDownloader().download(file_url)
        except Exception as e:
            logging.exception(e)
            file_content = await ChunkedDownloader().download(file_url)
        logging.info("download file %r %r", file_url, len(file_content))
        return await self.upload_file_binary(file_content, filename)

    async def upload_file_binary(self, body, filename, duration=None):
        url = f"{LARK_HOST}/open-apis/im/v1/files"
        file_type = filename.split('.').pop()
        if file_type in ['xlsx', 'docx', 'pptx']:
            file_type = file_type[:3]
        if file_type not in ['opus', 'mp4', 'pdf', 'doc', 'xls', 'ppt']:
            file_type = 'stream'

        data = [
            ('file', ('file', body)),
            ('file_type', file_type),
            ('file_name', filename),
        ]
        try:
            if duration:
                data.append(('duration', str(int(duration))))
        except Exception as e:
            logging.error(e)
        encoded, content_type = encode_multipart_formdata(data)
        response = await self.request(url, encoded, method='POST', content_type=content_type)
        return response['data']['file_key']

    async def get_message_resource(self, message_id, file_key, type='image'):
        # https://open.feishu.cn/document/server-docs/im-v1/message/get-2
        try:
            # Body must be None for method GET (unless allow_nonstandard_methods is true)
            # # image | file
            # action block拿到的使用下面的格式下载
            # 下载视频/文件： https://open.feishu.cn/open-apis/im/v1/files/file_key ; 下载图片： https://open.feishu.cn/open-apis/image/v4/get?image_key=xxx&operator=app
            if message_id:
                url = f"{LARK_HOST}/open-apis/im/v1/messages/{message_id}/resources/{file_key}?type={type}"
            else:
                if type == 'image':
                    url = f"{LARK_HOST}//open-apis/image/v4/get?image_key={file_key}&operator=app"
                else:
                    url = f"{LARK_HOST}//open-apis/im/v1/files/{file_key}"
            response = await self.request(url, method='GET')
            logging.info('logging feishu res: {} {}'.format(message_id, file_key, response[:200]))
            return response
        except Exception as e:
            logging.error(e)
            if hasattr(e, 'response'):
                logging.error("response: %r", e.response.body)
            return ''

    async def chat_list(self):
        response = await self.request(f"{LARK_HOST}/open-apis/im/v1/chats")
        return response['data']['items']

    async def member_list(self, chat_id):
        # https://open.feishu.cn/document/server-docs/group/chat-member/get
        response = await self.request(f"{LARK_HOST}/open-apis/im/v1/chats/{chat_id}/members?member_id_type=open_id&page_size=100")
        # TODO 这里暂时不处理分页逻辑，先取100个
        return response['data']['items']

    async def get_ticket(self):
        # 获取jsapi_ticket，具体参考文档：https://open.feishu.cn/document/ukTMukTMukTM/uYTM5UjL2ETO14iNxkTN/h5_js_sdk/authorization
        url = f"{LARK_HOST}/open-apis/jssdk/ticket/get"
        response = await self.request(url, method='POST')
        code = response.get('code', -1)
        if code != 0:
            raise InternalError('获取 ticket 失败')
        return response['data']['ticket']

    async def create_bitable_record(self, app_token, table_id, **fields):
        url = f"{LARK_HOST}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        response = await self.request(
            url, method='POST',
            body=dict(fields=fields)
        )
        return response

    async def update_bitable_record(self, app_token, table_id, record_id, **fields):
        url = f"{LARK_HOST}/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}"
        response = await self.request(
            url, method='PUT',
            body=dict(fields=fields)
        )
        return response


def decrypt_with_aes(session_key, iv, data):
    session_key = bytes.fromhex(session_key)
    iv = bytes.fromhex(iv)
    decipher = AES.new(session_key, AES.MODE_CBC, iv)
    decoded_data = decipher.decrypt(base64.b64decode(data))
    unpad = lambda s: s[0:-s[-1]]
    decoded_data = json.loads(unpad(decoded_data))
    return decoded_data


html5_code2name = dict(zip(html5.values(), html5.keys()))


def char2name(c):
    if c in ['*', '_', '~']:
        return c
    if c in html5_code2name:
        return '&{}'.format(html5_code2name[c])
    if ord(c) in codepoint2name:
        return '&{};'.format(codepoint2name[ord(c)])
    return c


def quote_markdown(content):
    return ''.join([char2name(c) for c in content])


# 以下为飞书消息
class FeishuImageMessage(Dict):

    def __init__(self, image_key=''):
        super().__init__(image_key=image_key)


class FeishuFileMessage(Dict):

    def __init__(self, file_key=''):
        super().__init__(file_key=file_key)


class FeishuAudioMessage(FeishuFileMessage): pass


class FeishuMessageHr(Dict):

    def __init__(self):
        super().__init__(tag='hr')


class FeishuMessageText(Dict):

    def __init__(self, text=''):
        super().__init__(text=text)


class FeishuMessageDiv(Dict):

    def __init__(self, content='', tag='plain_text', quote=True, **kwargs):
        super().__init__(tag='div', text=dict(
            tag=tag,
            content=quote_markdown(content) if tag == 'lark_md' and quote == True else content,
        ), **kwargs)


# https://open.feishu.cn/document/common-capabilities/message-card/add-card-interaction/interactive-components/button
class FeishuMessageButton(Dict):

    def __init__(self, content='', tag='plain_text', value=dict(), type='default', **kwargs):
        super().__init__(
            tag='button',
            text=dict(tag=tag, content=content),
            value=value,
            type=type,
            **kwargs
        )


class FeishuMessageAction(Dict):

    def __init__(self, *actions, layout="flow"):
        super().__init__(tag="action", layout=layout, actions=actions)


class FeishuMessageOption(Dict):

    def __init__(self, value='', content='', tag='plain_text'):
        super().__init__(value=value, text=dict(tag=tag, content=content or value))


class FeishuMessageSelect(Dict):

    def __init__(self, *options, placeholder='', tag='plain_text', **kwargs):
        super().__init__(
            tag='select_static',
            placeholder=dict(tag=tag, content=placeholder),
            options=options,
            **kwargs,
        )


class FeishuMessageOverflow(Dict):

    def __init__(self, *options, placeholder='', tag='plain_text', **kwargs):
        super().__init__(
            tag='overflow',
            placeholder=dict(tag=tag, content=placeholder),
            options=options,
            **kwargs,
        )


class FeishuMessageSelectPerson(Dict):

    def __init__(self, *persons, placeholder='', tag='plain_text', **kwargs):
        super().__init__(
            tag='select_person',
            placeholder=dict(tag=tag, content=placeholder),
            options=[{'value': v} if isinstance(v, str) else v for v in persons],
            **kwargs,
        )


class FeishuMessageDatePicker(Dict):
    def __init__(self, content='Please select date', tag='plain_text'):
        super().__init__(tag='date_picker', placeholder=dict(tag=tag, content=content))


class FeishuMessageCardConfig(Dict):
    def __init__(self, update_multi=True, enable_forward=True):
        super().__init__(update_multi=update_multi, enable_forward=enable_forward)


class FeishuMessageCardHeader(Dict):
    def __init__(self, content='', tag='plain_text', template='default'):
        super().__init__(title=dict(tag=tag, content=content), template=template)


class FeishuMessageCard(Dict):

    def __init__(self, *elements, header=None, config=None):
        if isinstance(header, str):
            header = FeishuMessageCardHeader(header)
        elif not header:
            header = FeishuMessageCardHeader()

        if not config:
            config = FeishuMessageCardConfig()

        super().__init__(header=header, elements=elements, config=config)


class FeishuMessagePlainText(Dict):
    def __init__(self, content=''):
        super().__init__(tag='plain_text', content=content)


class FeishuMessageMDText(Dict):
    def __init__(self, content=''):
        super().__init__(tag='lark_md', content=quote_markdown(content))


class FeishuMessageLarkMD(Dict):
    def __init__(self, content='', is_short=False):
        super().__init__(is_short=is_short, text=dict(
            tag='lark_md', content=quote_markdown(content),
        ))


class FeishuMessageImage(Dict):
    def __init__(self, img_key='', alt='', tag='', mode='fit_horizontal', preview=True):
        super().__init__(
            tag='img',
            img_key=img_key,
            alt=dict(tag=tag, content=alt),
            mode=mode,
            preview=preview,
        )

class FeishuMessageMarkdown(Dict):
    def __init__(self, content=''):
        super().__init__(tag='markdown', content=content)


class FeishuMessageNote(Dict):
    def __init__(self, *elements):
        super().__init__(tag='note', elements=elements)


class FeishuMessageConfirm(Dict):
    def __init__(self, title='', text=''):
        super().__init__(
            title=dict(tag='plain_text', content=title),
            text=dict(tag='plain_text', content=text),
        )


# https://bytedance.feishu.cn/docx/Iy8EdadF9ohXepxGInHcdgFLn5g
class FeishuMessageInput(Dict):
    def __init__(self, placeholder: dict or str = '', label: dict or str = '', default_value='', name='', **kwargs):
        super().__init__(
            tag='input',
            placeholder=placeholder if isinstance(placeholder, dict) else FeishuMessagePlainText(content=placeholder),
            label=label if isinstance(label, dict) else FeishuMessagePlainText(content=label),
            default_value=default_value,
            name=name,  # 在form中给input作标记
            **kwargs
        )
'''
{'header': {'title': {'tag': 'plain_text', 'content': ''}, 'template': 'default'}, 'elements': ({'tag': 'action', 'layout': 'flow', 'actions': ({'tag': 'input', 'name': 'input', 'placeholder': {'tag': 'plain_text', 'content': 'placeholder'}, 'label': {'tag': 'plain_text', 'content': 'label'}},)},), 'config': {'update_multi': True, 'enable_forward': True}}
FeishuMessageCard(
    FeishuMessageAction(
        FeishuMessageInput(
            placeholder='placeholder',
            label='label',
            name='input',
        )
    )
)
callback:
'action': {'tag': 'input', 'input_value': 'test', 'name': 'input'}}
'''


# https://bytedance.feishu.cn/wiki/O7oxw4i6hify1mkp1DncJNBknth
class FeishuMessageForm(Dict):
    def __init__(self, name='', *elements):
        super().__init__(tag='form', name=name, elements=elements)


class FeishuCallbackError(Exception):
    """飞书机器人回调异常"""
    pass


class FeishuTextMessage(Dict):
    def __init__(self, text=''):
        super().__init__(msg_type='text', content=dict(text=text))


if __name__ == "__main__":
    import httpx
    from tornado.ioloop import IOLoop

    async def main():
        # client = Lark(app_id='cli_a42cea111d3e9013', app_secret='')
        # def func():
        #     client.upload_image('https://cdn.aigcfun.com/attachments/1092632390930808946/1127840952468381806/551a3df7-2791-4e7e-9243-1e6883d1ff4b.png')
        # img_key = asyncio.get_event_loop().run_until_complete(func())
        # print('img_key', img_key)
        # await client.send('')
        response = httpx.post(
            'https://open.feishu.cn/open-apis/bot/v2/hook/ddd70e58-93f7-478d-9fdd-2473b09caa0f',
            json=FeishuTextMessage('test message'),
        )
        print(response)

    IOLoop.current().run_sync(main)

