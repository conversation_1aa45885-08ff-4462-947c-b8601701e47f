import base
import logging
import time
import json
import httpx
from uuid import uuid4
from pprint import pprint
from tornado.ioloop import IOLoop
from tornado.options import options
from core.base_model import MysqlModel
from core.redisdb import stalecache, redis_cli
from core.know import KnowClient


class PurgeChatPDFDocumentModel(MysqlModel):

    client = KnowClient(options.CHATPDF_USER)

    async def get_documents(self):
        page = 1
        while True:
            result = await self.client.get('/api/collection/{}/documents?page={}&size=20'.format(
                options.CHATPDF_COLLECTION_ID,
                page,
            ), timeout=20)
            pprint(('page: ', page, 'total: ', result['total']))
            if len(result['data']) == 0:
                break
            for item in result['data']:
                yield item
            page += 1

    async def run(self):
        now = time.time()
        # 获取知识库文件
        # 这里倒序获取文件列表，每一个检查一遍是否过期
        async for item in self.get_documents():
            pprint(item)
            # 如果过期了就
            if item['created'] + options.CHATPDF_EXPITED < now:
                logging.warning('PurgeChatPDFDocument %r', item)
                result = await self.client.delete('/api/document/{}'.format(
                    item['id']
                ), timeout=20)
                logging.warning('PurgeChatPDFDocument %r', result)


if __name__ == '__main__':
    with PurgeChatPDFDocumentModel() as task:
        IOLoop.current().run_sync(task.run)

