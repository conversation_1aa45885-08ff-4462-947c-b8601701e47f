import asyncio
import logging
import json
import pandas as pd
from tornado.options import options

from settings.config import load_config
load_config()
from modules.account.model import AdminModel
import gradio as gr


def refresh():
    with AdminModel() as model:
        tenant, total = model.get_tenant_list('', 1, 200)
        value = pd.DataFrame(tenant)
        choices = [i["email"] for i in tenant]
        logging.info("refresh %r", choices)
        return {
            tenant_list: gr.update(value=value),
            tenant_email: gr.update(choices=choices),
        }


def upgrade_by_name(tenant_email, product_name, product_time, seats):
    logging.info("upgrade_by_name %r", (tenant_email, product_name, product_time, seats))
    with AdminModel() as model:
        model.upgrade(tenant_email, product_name, product_time, seats)
    return refresh()


def register(email, passwd, tenant_name, api_key):
    with AdminModel() as model:
        model.register(email, passwd, tenant_name=tenant_name, api_key=api_key)
    return refresh()


with gr.<PERSON>s(theme="soft") as admin:
    with gr.Tab("帐号"):
        with gr.Row():
            with gr.Column(scale=1):
                tenant_name = gr.Textbox(label='公司信息(ai-feishu)')
                email = gr.Textbox(label='邮箱')
                passwd = gr.Textbox(label='密码')
                api_key = gr.Textbox(label='API KEY')
                register_button = gr.Button("注册")
            with gr.Column(scale=1):
                tenant_email = gr.Dropdown(
                    multiselect=False,
                    label="选择帐号",
                )
                seats = gr.Number(label="座席", value=10)
                product_name = gr.Dropdown(
                    multiselect=False,
                    label="选择套餐",
                    choices=['体验版', '个人版', '企业版'],
                )
                product_time = gr.Dropdown(
                    multiselect=False,
                    label="选择时间",
                    choices=['7 day', '15 day', '1 month', '3 month', '1 year']
                )
                upgrade_button = gr.Button("升级")

        refresh_button = gr.Button("刷新")
        tenant_list = gr.DataFrame(
            headers=["id", "name", "email", "resource_count", "app_count", "bot_count", "product_count", "product_expired", "status", "created"],
            # value=refresh(),
        )

    register_button.click(fn=register, inputs=[email, passwd, tenant_name, api_key], outputs=[tenant_list, tenant_email])
    upgrade_button.click(fn=upgrade_by_name, inputs=[tenant_email, product_name, product_time, seats], outputs=[tenant_list, tenant_email])
    refresh_button.click(fn=refresh, outputs=[tenant_list, tenant_email])
    admin.load(refresh, outputs=[tenant_list, tenant_email])


if __name__ == "__main__":
    admin.queue().launch(
        auth=("connectai", "connectai@2023"),
        server_name='0.0.0.0',
        server_port=options.SERVER_PORT,
        show_error=True,
    )

