import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M21.179 18.5c-1.48 0-2.679 1.2-2.679 2.679c0 .177.144.321.321.321H29.18a.321.321 0 0 0 .321-.321c0-1.48-1.2-2.679-2.679-2.679H21.18zM24 4a8.001 8.001 0 0 0-7.951 7.113C11.239 13.872 8 19.058 8 25v3h32v-3c0-5.942-3.24-11.128-8.049-13.887A8.001 8.001 0 0 0 24 4zm0 5c-1.764 0-3.461.285-5.048.813a5.501 5.501 0 0 1 10.096 0A15.984 15.984 0 0 0 24 9zm-8 12.179A5.179 5.179 0 0 1 21.179 16h5.642A5.179 5.179 0 0 1 32 21.179A2.821 2.821 0 0 1 29.179 24H18.82A2.821 2.821 0 0 1 16 21.179zm0 9.321v3.25a1.25 1.25 0 1 0 2.5 0V30.5H40v8.25c0 2.9-2.35 5.25-5.25 5.25h-21.5A5.25 5.25 0 0 1 8 38.75V30.5h8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Backpack48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
