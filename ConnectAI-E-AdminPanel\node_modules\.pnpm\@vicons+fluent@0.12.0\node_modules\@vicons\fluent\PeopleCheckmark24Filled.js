'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M12.81 13L4 13a2 2 0 0 0-2 2V16.5l.005.23C2.165 20.044 5.778 21 8.5 21c1.03 0 2.189-.137 3.239-.487A6.472 6.472 0 0 1 11 17.5c0-1.746.689-3.332 1.81-4.5zM13 6.5a4.5 4.5 0 1 0-9 0a4.5 4.5 0 0 0 9 0zm8 1a3.5 3.5 0 1 0-7 0a3.5 3.5 0 0 0 7 0zm2 10a5.5 5.5 0 1 1-11 0a5.5 5.5 0 0 1 11 0zm-2.146-2.354a.5.5 0 0 0-.708 0L16.5 18.793l-1.646-1.647a.5.5 0 0 0-.708.708l2 2a.5.5 0 0 0 .708 0l4-4a.5.5 0 0 0 0-.708z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'PeopleCheckmark24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
