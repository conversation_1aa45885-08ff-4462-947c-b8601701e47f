# 文档

https://connect-ai.feishu.cn/wiki/H4I6wKgSii6gR3kIDTWcNDsVnUf


# 消息格式
```
// 第一层，尽量只暴露要使用的基本字段，其他的放到extra内部
{
  "log_id": "xxx", // 使用一个log_id，方便记录日志，以便后续查找
  "extra": {
    // 把之前的chat_log数据放这个字段里面
  },
  "platform": "feishu/dingding/...", // 这个字段从Platform.name读取，方便放下一级队列
  "action": AppAction.Chat.value,
  "data": "user input text", //
}

//往机器人队列放的消息格式
{
  "log_id": "xxx", // 使用一个log_id，方便记录日志，以便后续查找
  "extra": {
    // 把之前的chat_log数据放这个字段里面
  }
  "result_type": AppResult.ReplyText.value,
  "data": "这里是回复的结果，可能是一段文字，也可能是一个图片url", //
}
```


## 2023-07-20
```
alter table tenant add column level int(11) not null default 0;
alter table application add column sorted int(11) not null default 0;
```

