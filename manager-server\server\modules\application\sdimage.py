class SDImageTool(CTool):
    name: str = 'sdimage_chat'
    # https://connect-ai.feishu.cn/wiki/Ufv1wjXZoiFWBPkD7ZYcuQ7dnug
    description: str = 'sdimage chat'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        class OpenAICallbackHandler(BaseCallbackHandler):
            result: str = ''

            def on_llm_start(self, *args, **kwargs):
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('', tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs):
                if model.streaming and platform == 'feishu':
                    # 这里的token实际上是进度，可能为None或者是Done
                    if token:
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，%(progress)s%% ...', progress=token)))
                            )
                        )

            def on_llm_end(self, response, *args, **kwargs):
                img_url = response.generations[0][0].text
                # additional_kwargs = response.generations[0][0].message.addtional_kwargs
                # params = {
                #     'jobId': additional_kwargs['jobId'],
                #     'messageId': additional_kwargs['messageId']
                # }
                # is_reroll = True if 'action' in data.extra.extra and data.extra.extra.action.value.action == 'reroll' else False
                if platform == 'feishu':
                    with FeishuModel() as fmodel:
                        fmodel.init_by_bot_id(data.extra.bot_instance_id)
                        try:
                            loop = asyncio.get_event_loop()
                        except Exception as e:
                            logging.error(e)
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                        img_key = loop.run_until_complete(functools.partial(fmodel.client.upload_image, img_url)())
                        contents = [
                            FeishuMessageImage(img_key=img_key, alt='图片'),
                            FeishuMessageAction(FeishuMessageButton(_('重新生成'), value={'action': 'reroll', 'logId': data.extra.id}))
                        ]
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                *contents,
                                FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功') + '\n' + _(FeishuCommand.choice_tip()))),
                                header=FeishuMessageCardHeader('Stable Diffusion Bot🎉', template='blue')
                            )
                        )
                else:
                    # dingding图片重新生成按钮
                    send_message(
                        AppResult.ReplyActionCard,
                        DingDingActionCardMessage(
                            *[DingDingActionCardButton(
                                'dtmd://dingtalkclient/sendMessage?content={}'.format(
                                    quote('{}'.format(':'.join(['reroll', data.extra.id, '', ''])))
                                ),
                                _('重新生成'),
                            )],
                            text=_('![图片]({})  \n🤖️：生成成功').format(img_url),
                            title='Stable Diffusion Bot🎉',
                        )
                    )


            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any):
                logging.error(error)
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md')
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [OpenAICallbackHandler()]
        m = {value: content for value, content in ai_model}
        model_name = m.get(session.model_id, ai_model[0][1])
        if 'action' in data.extra.extra:
            if data.extra.extra.action.value.action == 'reroll' and input:
                # 重新生成
                messages = [HumanMessage(content=input, additional_kwargs=dict(
                    prompt=input,
                ))]
            else:
                messages = [HumanMessage(content='', additional_kwargs=data.extra.extra.action.value)]
        else:
            messages = [HumanMessage(content=input, additional_kwargs=dict(prompt=input))]
        # 图片尺寸
        # 注意目前只能用 . 的方式获取
        img_size = session.extra.get('img_size') or (512, 512)
        params = {
            # 模型参数统一为 model_name
            'model_name': model_name
        }
        if img_size:
            params.update({
                'width': img_size[0],
                'height': img_size[1]
            })
        # 图片质量
        img_quality = session.extra.get('img_quality')
        if img_quality: # 0也不用传入
            params.update({
                'enable_hr': True,
                'hr_scale': img_quality + 1
            })
        chat = SDImageChat(**model)
        return chat.invoke(
            messages,
            **params
        )


class DingdingCommand(CommandTool):
    next_tool_name: str = 'sdimage_chat'
    name: str = 'sdimage_dingding_command'
    description: str = ''
    sizes = {(512, 512): '小', (1024, 1024): '中', (2048, 2048): '大'}
    qualities = ['清晰', '高清']

    def send_usage(self):
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/model',
                    _('🚀 AI模型切换')
                ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/size',
                    _('🤖 图片尺寸选择')
                ),
                # DingDingActionCardButton(
                #     'dtmd://dingtalkclient/sendMessage?content=/quality',
                #     _('🤖 图片质量选择')
                # ),
                DingDingActionCardButton(
                    'dtmd://dingtalkclient/sendMessage?content=/help',
                   _('🎒 需要更多帮助')
                ),
                title=_('🎒 需要帮助吗？'),
                text=_('我是Stable Diffusion AI生图小助手')
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/model' or input[:2] == '模型':
            # 如果"/model {model_name}"，就发送成功消息，否则发送选项
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:5] == '/size' or input[:2] == '尺寸':
            size_input = input.replace('/size', '').replace('尺寸', '').strip()
            return 'size', size_input
        # elif input[:8] == '/quality' or input[:2] == '质量' or input[:3] == '清晰度':
        #     quality_input = input.replace('/quality', '').replace('质量', '').replace('清晰度', '').strip()
        #     return 'quality', quality_input
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',


    def on_model(self, model_name=None):
        # 如果"/model {model_name}"，就发送成功消息，否则发送选项
        ai_model_options = [(value, content) for value, content in ai_model if value in data.models]
        m = {content: value for value, content in ai_model_options}
        if model_name and model_name in m:
            action_value = m[model_name]
            session['model_id'] = action_value
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('🚀 机器人提醒'),
                    text=_("已选择模型：%(model)s", model=model_name),
                )
            )
        else:
            if len(ai_model_options) == 0:
                return send_message(
                    AppResult.ReplyActionCard,
                    DingDingActionCardMessage(
                        title=_('🚀 机器人提醒'),
                        text=_('无可用模型'),
                    )
                )
            # 这里给出可选择的模型列表
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote('/model ' + content)),
                        content
                    ) for _, content in ai_model_options],
                    text=_('选择以下模型：'),
                    title=_('🚀 AI模型切换'),
                ),
            )

    def on_size(self, size_input=None):
        # 是否展示note消息
        note_flag = False
        width, height = '', ''
        if not size_input:
            note_flag = True
        else:
            sizes = size_input.split()
            if len(sizes) >= 2:
                width, height = sizes
            else:
                width = sizes[0]
                height = width
        if not (width.isdigit() and height.isdigit() and int(width) and int(height)):
            note_flag = True
        if note_flag:
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote(f'/size {k[0]} {k[1]}')),
                        "{} {}*{}".format(_(v), k[0], k[1])
                    ) for k, v in self.sizes.items()],
                    text=_('选择以下图片尺寸：'),
                    title=_('🤖 内置图片尺寸选择'),
                )
            )

        else:
            width, height = int(width), int(height)
            session.set_extra('img_size', [width, height])
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    title=_('👺 机器人提醒'),
                    text=_('👺 已选择图片尺寸：%(width)s*%(height)s', width=width, height=height),
                )
            )

    def on_quality(self, quality):
        if not quality or quality not in self.qualities:
            return send_message(
                AppResult.ReplyActionCard,
                DingDingActionCardMessage(
                    *[DingDingActionCardButton(
                        'dtmd://dingtalkclient/sendMessage?content={}'.format(quote(f'/quality {v}')),
                        v
                    ) for v in self.qualities],
                    text=_('选择以下图片质量：'),
                    title=_('🏠 内置图片质量选择'),
                )
            )
        session.set_extra('img_quality', self.qualities.index(quality))
        return send_message(
            AppResult.ReplyActionCard,
            DingDingActionCardMessage(
                title=_('👺 机器人提醒'),
                text=_('👺 已选择图片质量：%(quality)s', quality=quality),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class FeishuCommand(CommandTool):
    next_tool_name: str = 'sdimage_chat'
    name: str = 'sdimage_feishu_command'
    description: str = ''
    sizes = {(512, 512): '小', (1024, 1024): '中', (2048, 2048): '大'}
    qualities = ['清晰', '高清']
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
    ]

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def size_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(f'{k[0]} {k[1]}', "{} {}*{}".format(_(v), k[0], k[1])) for k, v in self.sizes.items()],
            placeholder=_('选择图片尺寸'),
            initial_option=' '.join(list(map(str, session.extra.get('img_size', list(self.sizes.keys())[0])))),
            value={'command': 'size'},
            confirm=FeishuMessageConfirm(
                title=_('您确定选择该图片尺寸吗?'),
                text=_('AI将生成该尺寸的图片')
            )
        )

    @property
    def quality_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(v, v) for v in self.qualities],
            placeholder=_('选择图片质量'),
            initial_option=session.extra.get('img_quality', self.qualities[0]),
            value={'command': 'quality'},
            confirm=FeishuMessageConfirm(
                title=_('您确定选择该图片质量吗?'),
                text=_('AI将生成该质量的图片')
            )
        )

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是Stable Diffusion AI生图小助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **图片尺寸选择**\n文本回复 *尺寸* 或 */size*'),
                    tag='lark_md',
                    extra=self.size_select,
                ),
                # FeishuMessageHr(),
                # FeishuMessageDiv(
                #     '**图片质量选择**',
                #     tag='lark_md',
                #     extra=self.quality_select,
                # ),
                # FeishuMessageHr(),
                # 尽量不让用户输入图片尺寸
                # FeishuMessageDiv('👺 **输入图片尺寸**\n文本回复*尺寸* 或 *大小* 或 */size*+空格+宽度+空格+高度，示例：/size 300 300', tag='lark_md'),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？', template='blue'))
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/model' or input[:2] == '模型':
            # 如果"/model {model_name}"，就发送成功消息，否则发送选项
            model_name = input.replace('/model', '').replace('模型', '').strip()
            return 'model', model_name
        elif input[:5] == '/size' or input[:2] == '尺寸':
            return 'size',
        # elif input[:8] == '/quality' or input[:2] == '质量' or input[:3] == '清晰度':
        #     quality_input = input.replace('/quality', '').replace('质量', '').replace('清晰度', '').strip()
        #     return 'quality', quality_input
        elif not input and action:
            if action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
            elif action['tag'] == 'button':
                return None,
        elif input and parse_urls(input):
            return 'note',
        if data.extra.message_type in ['text']:
            return None,
        return 'note',

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv("无可用模型切换"),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('已选择模型：**%(model)s**', model=m.get(model_name, model_name)), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_size(self, size_input=None):
        # 是否展示note消息
        note_flag = False
        width, height = '', ''
        if not size_input:
            note_flag = True
        else:
            sizes = size_input.split()
            if len(sizes) >= 2:
                width, height = sizes
            else:
                width = sizes[0]
                height = width
        if not (width.isdigit() and height.isdigit() and int(width) and int(height)):
            note_flag = True
        if note_flag:
            # 没有输入或者输入不合法
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.size_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择图片尺寸。'))
                    ),
                    header=FeishuMessageCardHeader(_('🏠 请选择图片尺寸'), template='blue'),
                )
            )
        width, height = int(width), int(height)
        session.set_extra('img_size', [width, height])
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(f'{width}*{height}'),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('接下来模型生成图片的尺寸为：宽度%(width)s 高度%(height)s', width=width, height=height))
                ),
                header=FeishuMessageCardHeader(_('👺 已选择图片尺寸：%(width)s*%(height)s', width=width, height=height), template='blue'),
            )
        )

    def on_quality(self, quality=None):
        if not quality or quality not in self.qualities:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.quality_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择图片质量。'))
                    ),
                    header=FeishuMessageCardHeader(_('🏠 请选择图片质量'), template='blue'),
                )
            )
        session.set_extra('img_quality', self.qualities.index(quality))
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(quality),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('接下来模型生成图片的质量为：%(quality)s', quality=quality))
                ),
                header=FeishuMessageCardHeader(_('👺 已选择图片质量：%(quality)s', quality=quality), template='blue'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class SDImageAgent(CAgent):
    class AppConfig(BaseAppConfig):
        category: str = 'AI绘画'
        name: str = 'Stable Diffusion'
        title: str = 'Stable Diffusion文本生图'
        title_en: str = 'Stable Diffusion Bot'
        description: str = '🕹 在IM上利用sd进行图片创作，C站模型全部兼容，可随心选择你需要的LoRa，甚至支持ControlNet。'
        description_en: str = '🕹  You can use SD card to create images on IM, and all C-site models are compatible. You can freely choose the LoRa you need, and even support ControlNet.'
        problem: str = 'SD WebUI 复杂的劝退怎么办？'
        problem_en: str = 'What to do with the complex discouragement of SD WebUI?'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/OaKidJrZioHSedxew6ecNMPJnge?from=from_copylink'
        manual_en: str = 'https://q5o2cctqdb7.sg.larksuite.com/docx/EXetd8DODoNQanxjzwMlDgHGgfd'
        icon: str = 'https://pic1.forkway.cn/cdn/202308211523813.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/202308211523813.png?imageMogr2/thumbnail/720x'
        sorted: int = 5
        support_resource: List[object] = [dict(
            category=ModelCategory.Draw.value,
            scene=ModelCategory.Draw.value,
            title='AI绘画',
            tip='',
            required=True,
            resource=['Stable Diffusion']
        )]
        support_bots: List[str] = ['feishu', 'dingding']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnR0ieZ5WJNrCsWazIAo5QS5'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/TbvmwoxTwiBiMRkrHi8cA9m4nHb'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                return AgentAction(tool=last_result, tool_input=kwargs, log='')
            return AgentFinish(
                {'output': last_result},
                'result for action {}: {}'.format(last_action.tool, str(last_result)))

        if platform == 'feishu':
            return AgentAction(tool='sdimage_feishu_command', tool_input=kwargs, log='')
        elif platform == 'dingding':
            return AgentAction(tool='sdimage_dingding_command', tool_input=kwargs, log='')
        return AgentAction(tool='sdimage', tool_input=kwargs, log='')
