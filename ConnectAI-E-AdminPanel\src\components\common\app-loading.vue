<template>
  <div class="loading hidden md:block">
    <div>
      <div class="box">
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
      </div>
    </div>
  </div>
  <div class="h5-loading flex justify-center items-center flex-col md:hidden">
    <div class="spinner">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getRgbOfColor, localStg } from '@/utils';
import themeSettings from '@/settings/theme.json';

function addThemeColorCssVars() {
  const defaultColor = themeSettings.themeColor;
  const themeColor = localStg.get('themeColor') || defaultColor;

  const { r, g, b } = getRgbOfColor(themeColor);

  const cssVars = `--primary-color: ${r},${g},${b}`;
  document.documentElement.style.cssText = cssVars;
}

addThemeColorCssVars();
</script>

<style lang="scss" scoped>
$c-white: #fff;
$c-black: #000;
$ani-dur: 3s;
$size: 50px;

@mixin fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.loading {
  height: 100vh;
  transform-style: preserve-3d;
  perspective: 1000px;
  background: linear-gradient(to bottom right, #141414, #2d3f48);
}
.h5-loading {
  height: 100vh;
  transform-style: preserve-3d;
  background: linear-gradient(to bottom right, #141414, #2d3f48);
}
.cube {
  position: absolute;
  width: 1em;
  height: 1em;
  background: #fed74c;
  animation: move $ani-dur ease-in-out infinite;
  // animation-play-state: paused;
  transform-style: preserve-3d;
  box-shadow: 5em 5em 0.3em 0.1em #dbdbdb;

  &::before,
  &::after {
    content: '';
    @include fill;
  }

  &::before {
    background-color: #c97431;
    transform-origin: 100% 100%;
    transform: rotateY(-90deg);
  }

  &::after {
    background-color: #e7a22b;
    transform-origin: 0% 100%;
    transform: rotateX(90deg);
  }

  @for $i from 1 through 4 {
    &:nth-of-type(#{$i}) {
      animation-delay: $ani-dur * calc($i / 4 - 4);
    }
  }

  @keyframes move {
    0%,
    87.5%,
    100% {
      transform: translate(1em, 0em);
    }
    12.5% {
      transform: translate(2em, 0em);
    }
    25% {
      transform: translate(2em, 1em);
    }
    37.5%,
    50% {
      transform: translate(1em, 1em);
    }
    62.5% {
      transform: translate(0em, 1em);
    }
    75% {
      transform: translate(0em, 0em);
    }
  }
}

.box {
  @include fill;
  width: 6em;
  height: 4em;
  margin: 40vmin auto;
  font-size: $size;
  transform-style: preserve-3d;
  transform: rotateX(60deg) rotateZ(45deg);

  &:hover * {
    animation-play-state: paused;
  }
  &:active * {
    animation-play-state: running;
  }
}

/// reset
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  margin: 0;
  line-height: 1.4;
}

.intro {
  width: 90%;
  max-width: 50rem;
  padding-top: 0.5em;
  padding-bottom: 1rem;
  margin: 0 auto 1em;
  font-size: calc(1rem + 2vmin);
  text-transform: capitalize;
  text-align: center;
  font-family: serif;
  border-bottom: 1px dashed rgba(#000, 0.5);
}

.info {
  margin: auto 0 0;
  padding: 1em;
  font-size: 0.9em;
  font-style: italic;
  font-family: serif;
  text-align: right;
  opacity: 0.5;

  a {
    color: inherit;
  }
}
.spinner {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-left: -75px;
}

.spinner span {
  position: absolute;
  top: 50%;
  left: var(--left);
  width: 35px;
  height: 7px;
  background: #ffff;
  animation: dominos 1s ease infinite;
  box-shadow: 2px 2px 3px 0px black;
}

.spinner span:nth-child(1) {
  --left: 80px;
  animation-delay: 0.125s;
}

.spinner span:nth-child(2) {
  --left: 70px;
  animation-delay: 0.3s;
}

.spinner span:nth-child(3) {
  left: 60px;
  animation-delay: 0.425s;
}

.spinner span:nth-child(4) {
  animation-delay: 0.54s;
  left: 50px;
}

.spinner span:nth-child(5) {
  animation-delay: 0.665s;
  left: 40px;
}

.spinner span:nth-child(6) {
  animation-delay: 0.79s;
  left: 30px;
}

.spinner span:nth-child(7) {
  animation-delay: 0.915s;
  left: 20px;
}

.spinner span:nth-child(8) {
  left: 10px;
}

@keyframes dominos {
  50% {
    opacity: 0.7;
  }

  75% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }

  80% {
    opacity: 1;
  }
}
</style>
