import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.404 4.723l2.718 10.142a2.75 2.75 0 0 1-1.945 3.368L11.9 19.915a2.75 2.75 0 0 1-3.368-1.944L5.813 7.828A2.75 2.75 0 0 1 7.758 4.46l6.278-1.682a2.75 2.75 0 0 1 3.368 1.945zm-6.438 3.019a1 1 0 1 0-1.932.517a1 1 0 0 0 1.932-.517zm-5.163 3.917l1.762 6.57a3.732 3.732 0 0 0 1.002 1.713l-.443-.023a2.75 2.75 0 0 1-2.602-2.89l.281-5.37zm-.925-1.479l-.355 6.796c-.037.699.12 1.363.424 1.94l-.414-.16a2.75 2.75 0 0 1-1.582-3.553l1.927-5.023z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'StyleGuide24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
