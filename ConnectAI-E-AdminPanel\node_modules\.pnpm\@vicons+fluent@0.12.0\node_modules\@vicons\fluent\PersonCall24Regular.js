'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M5.253 14l10.989.001c.104.172.23.331.38.473l.719.682c-.04.16-.094.319-.16.478a.75.75 0 0 0-.428-.134h-11.5a.75.75 0 0 0-.75.75v.907c0 .656.286 1.28.784 1.707C6.545 19.945 8.44 20.5 11 20.5c.291 0 .575-.007.85-.021c.08.446.294.87.63 1.209l.228.228c-.542.056-1.11.084-1.708.084c-2.89 0-5.128-.656-6.691-2a3.75 3.75 0 0 1-1.306-2.844v-.907A2.25 2.25 0 0 1 5.253 14zm11.757-1.755l.504-1.187c.236-.556.801-.859 1.356-.743l.118.03l.63.202c.625.2 1.104.735 1.259 1.407c.367 1.598-.074 3.543-1.322 5.836c-1.247 2.29-2.614 3.666-4.1 4.13a1.76 1.76 0 0 1-1.664-.343l-.123-.113l-.479-.48a1.36 1.36 0 0 1-.222-1.592l.071-.116l.721-1.06c.284-.417.77-.614 1.238-.515l.127.035l1.332.444a5.08 5.08 0 0 0 1.33-1.519a4.798 4.798 0 0 0 .596-1.59l.038-.27l-1.109-1.052a1.354 1.354 0 0 1-.348-1.373l.047-.13l.504-1.188l-.504 1.187zM11 2.005a5 5 0 1 1 0 10a5 5 0 0 1 0-10zm0 1.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'PersonCall24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
