// import { islark } from '@/utils/common/typeof'
const isLark = !!JSON.parse(import.meta.env.VITE_IS_LARK);  // 判断是否为海外版

const dashboard: AuthRoute.Route = {
  name: 'dashboard',
  path: '/dashboard',
  component: 'basic',
  children: [
    // {
    //   name: 'dashboard_pricing',
    //   path: '/dashboard/pricing',
    //   component: 'self',
    //   meta: {
    //     title: '账户权益',
    //     icon: 'akar-icons:pointing-up',
    //     i18nTitle: 'message.routes.dashboard.zhqy',
    //     requiresAuth: true,
    //   }
    // },
    {
      name: 'dashboard_ai',
      path: '/dashboard/ai',
      component: 'self',
      meta: {
        title: 'AI 资源管理',
        icon: 'akar-icons:shipping-box-v1',
        i18nTitle: 'message.routes.dashboard.aizygl',
        requiresAuth: true
      },
      children: []
    },
    {
      name: 'dashboard_seats',
      path: '/dashboard/seats',
      component: 'self',
      meta: {
        title: 'dashboard_seats',
        icon: 'akar-icons:people-group',
        i18nTitle: 'message.routes.dashboard.seats',
        requiresAuth: true
      }
    }
  ],
  meta: {
    title: '我的账户',
    icon: 'akar-icons:command',
    order: 1,
    i18nTitle: 'message.routes.dashboard._value',
    hide: false
  }
};

export default dashboard;
