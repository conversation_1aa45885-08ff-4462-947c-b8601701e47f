#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时Manager Server - 使用Flask
解决Windows兼容性问题
"""

import os
import sys
import json
import sqlite3
import hashlib
from flask import Flask, request, jsonify, session
from flask_cors import CORS

app = Flask(__name__)
CORS(app)
app.secret_key = 'connectai-manager-secret-key'

def hash_password(password):
    """密码哈希函数"""
    return hashlib.md5(password.encode('utf-8')).hexdigest()

def get_db():
    """获取数据库连接"""
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 数据库在项目根目录的data文件夹中
    db_path = os.path.join(current_dir, "..", "data", "connectai.db")
    db_path = os.path.normpath(db_path)

    if not os.path.exists(db_path):
        raise FileNotFoundError(f"数据库文件不存在: {db_path}")

    return sqlite3.connect(db_path)

@app.route('/')
def index():
    """主页"""
    return jsonify({
        "message": "ConnectAI Manager Server",
        "version": "1.0.0",
        "status": "running",
        "framework": "Flask"
    })

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({"status": "healthy"})

@app.route('/api/account/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')

        if not email or not password:
            return jsonify({
                "code": -1,
                "msg": "邮箱和密码不能为空"
            }), 400

        # 查询用户
        conn = get_db()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name, t.apikey
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.email = ? AND a.password = ?
        """, (email, hash_password(password)))

        user = cursor.fetchone()
        conn.close()

        if user:
            # 设置session
            session['user_id'] = user[0]
            session['user_email'] = user[2]
            session['tenant_id'] = user[3]

            return jsonify({
                "code": 0,
                "msg": "登录成功",
                "type": "login",
                "data": {
                    "id": user[0],
                    "name": user[1],
                    "email": user[2],
                    "tenant_id": user[3],
                    "tenant_name": user[4],
                    "tenant_apikey": user[5]
                }
            })
        else:
            return jsonify({
                "code": -1,
                "msg": "邮箱或密码错误"
            }), 401

    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"登录失败: {str(e)}"
        }), 500

@app.route('/api/account/info', methods=['GET'])
def account_info():
    """获取账户信息"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "code": -1,
                "msg": "未登录"
            }), 401

        conn = get_db()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT a.id, a.name, a.email, a.tenant_id, t.name as tenant_name
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.id = ?
        """, (session['user_id'],))

        user = cursor.fetchone()
        conn.close()

        if user:
            return jsonify({
                "code": 0,
                "msg": "success",
                "data": {
                    "id": user[0],
                    "display_name": user[1],
                    "email": user[2],
                    "tenant_id": user[3],
                    "tenant_name": user[4]
                }
            })
        else:
            return jsonify({
                "code": -1,
                "msg": "用户不存在"
            }), 404

    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"获取账户信息失败: {str(e)}"
        }), 500

@app.route('/api/account/logout', methods=['DELETE'])
def logout():
    """用户登出"""
    try:
        session.clear()
        return jsonify({
            "code": 0,
            "msg": "success"
        })
    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"登出失败: {str(e)}"
        }), 500

@app.route('/api/tenant/handler', methods=['GET'])
def tenant_handler():
    """获取租户权限信息"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "code": -1,
                "msg": "未登录"
            }), 401

        # 模拟租户权限数据
        return jsonify({
            "code": 0,
            "msg": "success",
            "data": [
                "app_management",
                "user_management",
                "log_management",
                "sensitive_management",
                "prompt_management"
            ],
            "product": "enterprise",
            "expired": "2025-12-31 23:59:59"
        })
    except Exception as e:
        return jsonify({
            "code": -1,
            "msg": f"获取权限失败: {str(e)}"
        }), 500

if __name__ == '__main__':
    # 设置UTF-8编码，避免Windows下的编码问题
    import sys
    import io
    if sys.platform.startswith('win'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

    print("启动ConnectAI Manager Server (临时Flask版本)...")
    print("服务将在 http://localhost:3000 运行")
    print("按 Ctrl+C 停止服务器")

    try:
        app.run(host='0.0.0.0', port=3000, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
