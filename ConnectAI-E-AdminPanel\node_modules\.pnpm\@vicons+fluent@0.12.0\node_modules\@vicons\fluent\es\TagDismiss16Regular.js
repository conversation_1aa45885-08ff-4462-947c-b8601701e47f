import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.5 15a4.5 4.5 0 1 0 0-9a4.5 4.5 0 0 0 0 9zm1.854-6.354a.5.5 0 0 1 0 .708L11.207 10.5l1.147 1.146a.5.5 0 0 1-.708.708L10.5 11.207l-1.146 1.147a.5.5 0 0 1-.708-.708L9.793 10.5L8.646 9.354a.5.5 0 1 1 .708-.708L10.5 9.793l1.146-1.147a.5.5 0 0 1 .708 0zM10.356 4.66a.774.774 0 0 1-1.09 0a.76.76 0 0 1 0-1.081a.774.774 0 0 1 1.09 0c.3.299.3.783 0 1.081zm-8.821 4.5a1.992 1.992 0 0 1 0-2.83l4.76-4.73a2.02 2.02 0 0 1 1.415-.586L10.974 1a2.007 2.007 0 0 1 2.021 2.015l-.018 2.573a5.462 5.462 0 0 0-1.004-.389l.016-2.19A1.004 1.004 0 0 0 10.978 2l-3.264.014a1.01 1.01 0 0 0-.708.293l-4.76 4.73a.996.996 0 0 0 0 1.415l2.803 2.785c.079.59.252 1.151.503 1.667c-.258-.097-.5-.249-.707-.455l-3.31-3.29z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagDismiss16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
