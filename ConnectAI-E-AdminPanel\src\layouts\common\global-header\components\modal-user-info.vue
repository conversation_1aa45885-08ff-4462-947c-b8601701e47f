<template>
  <n-modal
    :show="ifShow"
    :auto-focus="false"
    :mask-closable="true"
    @mask-click="setOff"
    @positive-click="setInfoShow"
    @negative-click="setOff"
  >
    <div class="relative w-full max-w-md max-h-full">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <div class="p-4">
          <h1 class="text-lg font-500 pb-2">{{ t('message.system.xgxx') }}</h1>
          <n-alert type="info"><span class="font-500">{{ t('message.system.xgxxinfo') }}</span></n-alert>
          <div class="my-6">
            <span class="font-400">{{ t('message.system.teamname') }}</span>
            <n-input class="my-2" maxlength="30" show-count clearable :placeholder="t('message.system.qsrmc')" v-model:value="teamname" />
          </div>
          <div class="text-right mt-16">
            <button
              @click="saveInfo"
              data-modal-hide="popup-modal"
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm inline-flex items-center px-3 py-2 text-center mr-3"
            >
              {{ $t('message.system.save') }}
            </button>
            <button
              data-modal-hide="popup-modal"
              type="button"
              class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-3 py-2 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"
              @click="setOff"
            >
              {{ $t('message.system.qx') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import useShowInfoModal from '@/hooks/common/use-show-info';
import { changeCompanyname } from '@/service';
import { t } from '@/locales';

const { ifShow, setInfoShow, setOff } = useShowInfoModal();

const teamname = ref('');

async function saveInfo(){
  console.log(teamname.value);
  const res = await changeCompanyname(teamname.value);
  if(res.data.code === 0){
    teamname.value = '';
    setOff();
  }else{
    console.log('error');
    teamname.value = '';
    setOff();
  }
  location.reload();
}



</script>

<style lang="less" scoped></style>
