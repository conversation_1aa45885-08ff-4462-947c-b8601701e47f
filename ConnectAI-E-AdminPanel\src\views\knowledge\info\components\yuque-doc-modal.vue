<template>
  <n-modal v-model:show="showYuQue" @after-leave="handleAfterLeave">
    <div>
      <n-card
        style="width: 600px"
        :title="t('添加语雀在线文档')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        v-if="step === 0"
      >
        <n-p depth="3" style="margin: 8px 0 8px 0">{{
          $t('首次添加「语雀」在线文档需要输入团队Token，请确保您已经创建了团队Token')
        }}</n-p>
        <a
          href="https://connect-ai.feishu.cn/docx/LzNZdP9PuoQid1xbQMocoPUTnrg"
          target="_blank"
          class="font-medium text-primary-600 hover:underline"
          >{{ $t('如何获取团队Token') }}</a
        >
        <template #footer>
          <div class="text-right">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('取消') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="step++"
            >
              {{ t('下一步') }}
            </button>
          </div>
        </template>
      </n-card>
      <n-card
        style="width: 600px"
        :title="t('输入团队Token')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        v-else-if="step === 1"
      >
        <n-form ref="formRef" :model="data" :rules="rules" :label-width="80">
          <n-form-item label="团队Token" path="token">
            <n-input v-model:value="data.token" placeholder="请输入团队Token" />
          </n-form-item>
        </n-form>
        <a
          href="https://connect-ai.feishu.cn/docx/LzNZdP9PuoQid1xbQMocoPUTnrg"
          target="_blank"
          class="font-medium text-primary-600 hover:underline"
          >{{ $t('如何获取团队Token') }}</a
        >

        <template #footer>
          <div class="text-right">
            <button
              type="button"
              class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
              @click="handleClose"
            >
              {{ t('取消') }}
            </button>
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="validate"
            >
              {{ t('下一步') }}
            </button>
          </div>
        </template>
      </n-card>
      <n-card
        style="width: 600px"
        :title="t('添加语雀在线文档')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        v-else-if="step === 2"
      >
        <n-form ref="docFormRef" :model="model" :rules="docRules" :label-width="80">
          <n-form-item label="语雀云文档链接" path="fileUrl">
            <n-input v-model:value="model.fileUrl" placeholder="请输入语雀云文档链接" />
          </n-form-item>
        </n-form>
        <a
          href="https://connect-ai.feishu.cn/docx/LzNZdP9PuoQid1xbQMocoPUTnrg"
          target="_blank"
          class="font-medium text-primary-600 hover:underline"
          >{{ $t('如何获取团队Token') }}</a
        >

        <template #footer>
          <div class="flex justify-between items-center">
            <div>
              <button
                type="button"
                class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
                @click="step = 1"
              >
                {{ t('修改团队Token配置') }}
              </button>
            </div>
            <div>
              <button
                type="button"
                class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
                @click="handleClose"
              >
                {{ t('message.ai.qx') }}
              </button>
              <button
                type="button"
                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
                @click="handleUploadDoc"
                :disabled="uploadButtonLoading"
              >
                {{ t('确定') }}
              </button>
            </div>
          </div>
        </template>
      </n-card>
    </div>
  </n-modal>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import { t } from '@/locales';
import { useMessage } from 'naive-ui';
import { updateDatasetYuQueConfig, uploadDocument } from '@/service/api/knowledge';
import { useRoute } from 'vue-router';

const route = useRoute();

const datasetId = route.query.id as string;

const emit = defineEmits(['update:showYuQue', 'update:data', 'after-upload']);

const props = defineProps<{
  showYuQue: boolean;
  data: any;
}>();

const showYuQue = useVModel(props, 'showYuQue', emit);
const data = useVModel(props, 'data', emit);

const step = ref(0);
const formRef = ref();
const message = useMessage();

const rules = {
  token: {
    required: true,
    message: t('请输入Token'),
    trigger: ['input']
  }
};
const docFormRef = ref();
const model = ref({
  fileUrl: ''
});
const docRules = {
  fileUrl: {
    type: 'url',
    required: true,
    message: t('请输入正确的语雀云文档链接'),
    trigger: ['input']
  }
};
const uploadButtonLoading = ref(false);
watch(
  () => showYuQue.value,
  (val) => {
    if (val) {
      if (data.value.token) {
        step.value = 2;
      }
    }
  }
);

function handleAfterLeave() {
  model.value.fileUrl = '';
}

function handleClose() {
  showYuQue.value = false;
}

async function validate() {
  await formRef.value?.validate();
  try {
    await updateDatasetYuQueConfig({
      data: { token: data.value.token }
    });
    message.success(t('添加成功'));
    step.value = 2;
  } catch (error) {}
}

async function handleUploadDoc() {
  await docFormRef.value?.validate();
  uploadButtonLoading.value = true;
  try {
    const res = await uploadDocument({
      id: datasetId,
      data: {
        fileUrl: model.value.fileUrl,
        fileType: 'yuque'
      }
    });
    if (res.error) {
      throw Error(t(res.error.msg || 'Token错误，请检查以后重新配置'));
    }
    emit('after-upload', { name: '语雀云文档', taskId: res.data.data?.task_id });
    message.success(t('提交成功，内容同步解析中，解析完成之后即可使用'));
    showYuQue.value = false;
    uploadButtonLoading.value = false;
  } catch (error) {
    console.log('error', error);
    message.error(error.message);
    uploadButtonLoading.value = false;
  }
}
</script>
