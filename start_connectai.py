#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConnectAI 本地启动脚本
一键启动所有服务
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path

class ConnectAILauncher:
    def __init__(self, debug=False):
        self.processes = []
        self.running = True
        self.debug = debug

    def print_banner(self):
        """打印启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    ConnectAI 本地启动器                        ║
║                                                              ║
║  一键启动所有服务                                              ║
║  Manager Server + DataChat API                              ║
║  本地开发环境                                                 ║
╚══════════════════════════════════════════════════════════════╝
"""
        print(banner)

    def check_environment(self):
        """检查环境"""
        print("检查环境...")

        # 检查虚拟环境
        manager_venv = Path("manager-server/venv")
        datachat_venv = Path("DataChat-API/venv")

        if not manager_venv.exists():
            print("× manager-server虚拟环境不存在")
            print("请先运行: python init_database_with_admin.py")
            return False

        if not datachat_venv.exists():
            print("× DataChat-API虚拟环境不存在")
            print("请先运行: python init_database_with_admin.py")
            return False

        # 检查数据库
        if not Path("data/connectai.db").exists():
            print("× 数据库不存在")
            print("请先运行: python init_database_with_admin.py")
            return False

        print("√ 环境检查通过")
        return True

    def start_service(self, name, command, cwd=None):
        """启动服务"""
        print(f"启动 {name}...")

        try:
            if self.debug:
                print(f"   调试: 命令 = {command}")
                print(f"   调试: 工作目录 = {cwd}")

            process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False
            )
            self.processes.append((name, process))

            if self.debug:
                print(f"   调试: 进程ID = {process.pid}")

            return process
        except Exception as e:
            print(f"× {name} 启动失败: {e}")
            return None

    def wait_for_service(self, name, url, timeout=30):
        """等待服务启动"""
        import urllib.request
        import urllib.error

        print(f"等待 {name} 启动...")

        for i in range(timeout):
            try:
                urllib.request.urlopen(url, timeout=1)
                print(f"√ {name} 启动成功")
                return True
            except Exception as e:
                if i % 5 == 0:  # 每5秒打印一次状态
                    print(f"   等待中... ({i+1}/{timeout})")
                time.sleep(1)

        print(f"× {name} 启动超时")
        # 检查进程是否还在运行
        for proc_name, process in self.processes:
            if proc_name == name:
                if process.poll() is None:
                    print(f"   进程仍在运行，可能是端口或配置问题")
                else:
                    print(f"   进程已退出，返回码: {process.returncode}")
                    # 读取错误输出
                    try:
                        stdout, stderr = process.communicate(timeout=1)
                        if stderr:
                            print(f"   错误信息: {stderr.decode('utf-8', errors='ignore')}")
                    except:
                        pass
        return False

    def show_service_info(self):
        """显示服务信息"""
        # 加载管理员信息
        try:
            import json
            with open("data/admin_info.json", 'r', encoding='utf-8') as f:
                admin_info = json.load(f)
        except:
            admin_info = None

        info = """
服务访问地址:
┌─────────────────────────────────────────────────────────────┐
│  Manager Server      │  http://localhost:3000             │
│  DataChat API        │  http://localhost:5000             │
│  健康检查            │  http://localhost:5000/health      │
└─────────────────────────────────────────────────────────────┘
"""
        print(info)

        if admin_info:
            print("预置管理员账号:")
            print(f"   邮箱: {admin_info['admin_email']}")
            print(f"   密码: {admin_info['admin_password']}")
            print(f"   租户: {admin_info['tenant_name']}")

        print("\nAPI测试示例:")
        if admin_info:
            print("   # 登录测试")
            print("   curl -X POST http://localhost:3000/api/login \\")
            print("     -H 'Content-Type: application/json' \\")
            print(f"     -d '{{\"email\": \"{admin_info['admin_email']}\", \"password\": \"{admin_info['admin_password']}\"}}'")

        print("\n   # 健康检查")
        print("   curl http://localhost:5000/health")

        print("\n注意: 按 Ctrl+C 停止所有服务")

    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n正在停止所有服务...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)

    def stop_all_services(self):
        """停止所有服务"""
        for name, process in self.processes:
            try:
                print(f"停止 {name}...")
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass

    def run(self):
        """主运行函数"""
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        self.print_banner()

        # 检查环境
        if not self.check_environment():
            return False

        print("\n启动服务...")

        # 启动DataChat API
        datachat_python = str(Path("DataChat-API/venv/Scripts/python.exe").absolute())
        datachat_process = self.start_service(
            "DataChat API",
            [datachat_python, "simple_app.py"],
            "DataChat-API"
        )

        if not datachat_process:
            return False

        # 等待DataChat API启动
        time.sleep(3)
        if not self.wait_for_service("DataChat API", "http://localhost:5000/", 15):
            self.stop_all_services()
            return False

        # 启动Manager Server
        manager_python = str(Path("manager-server/venv/Scripts/python.exe").absolute())
        manager_process = self.start_service(
            "Manager Server",
            [manager_python, "simple_flask_server.py"],
            "manager-server"
        )

        if not manager_process:
            self.stop_all_services()
            return False

        # 等待Manager Server启动
        time.sleep(3)
        if not self.wait_for_service("Manager Server", "http://localhost:3000/", 15):
            self.stop_all_services()
            return False

        # 显示服务信息
        self.show_service_info()

        # 保持运行
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)

        return True

def main():
    """主函数"""
    # 设置UTF-8编码，避免Windows下的编码问题
    import io
    if sys.platform.startswith('win'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

    # 检查是否有调试参数
    debug = "--debug" in sys.argv or "-d" in sys.argv

    if debug:
        print("调试模式已启用")

    launcher = ConnectAILauncher(debug=debug)
    success = launcher.run()

    if not success:
        print("\n启动失败")
        if not debug:
            print("提示: 使用 'python start_connectai.py --debug' 查看详细信息")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
