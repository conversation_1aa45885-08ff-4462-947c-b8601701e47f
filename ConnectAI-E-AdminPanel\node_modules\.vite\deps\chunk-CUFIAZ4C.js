import {
  initCustomFormatter,
  init_runtime_dom_esm_bundler,
  warn
} from "./chunk-MYYYCDVO.js";

// node_modules/.pnpm/vue@3.3.0/node_modules/vue/dist/vue.runtime.esm-bundler.js
init_runtime_dom_esm_bundler();
init_runtime_dom_esm_bundler();
function initDev() {
  {
    initCustomFormatter();
  }
}
if (true) {
  initDev();
}
var compile = () => {
  if (true) {
    warn(
      `Runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".`
      /* should not happen */
    );
  }
};

export {
  compile
};
//# sourceMappingURL=chunk-CUFIAZ4C.js.map
