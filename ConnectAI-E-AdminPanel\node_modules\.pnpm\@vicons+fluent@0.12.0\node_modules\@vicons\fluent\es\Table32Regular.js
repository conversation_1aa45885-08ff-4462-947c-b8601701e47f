import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4 9a5 5 0 0 1 5-5h14a5 5 0 0 1 5 5v14a5 5 0 0 1-5 5H9a5 5 0 0 1-5-5V9zm5-3a3 3 0 0 0-3 3v2h5V6H9zm4 0v5h6V6h-6zm0 7v6h6v-6h-6zm-2 6v-6H6v6h5zm-5 2v2a3 3 0 0 0 3 3h2v-5H6zm7 0v5h6v-5h-6zm8 0v5h2a3 3 0 0 0 3-3v-2h-5zm5-2v-6h-5v6h5zM21 6v5h5V9a3 3 0 0 0-3-3h-2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Table32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
