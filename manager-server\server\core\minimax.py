import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)   

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)
from tornado.options import options
from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage
from langchain.schema import AIMessage, SystemMessage


logger = logging.getLogger(__name__)


class MiniMaxClient(object):

    def build_query(
        self,
        api_base='https://api.minimax.chat/v1',
        api_key='',
        group_id='',
        **kwargs
    ):
        headers = {
            'Authorization': 'Bearer {}'.format(api_key),
            'Content-Type': 'application/json',
        }
        if 'https://minimax' != api_base[:15]:
            headers['group-id'] = group_id
            headers['api-base'] = api_base
            headers['api-key'] = api_key
            from core.api_base import NewApiBase
            api_base = NewApiBase('MiniMax').url
        body = json.dumps(kwargs)
        return '{}/text/chatcompletion'.format(api_base), body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    def create(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class MiniMaxChat(SimpleChatModel):
    """
    minimax https://api.minimax.chat/document/guides/chat?id=6433f37294878d408fc82953
    """
    client: Any  #: :meta private:
    # 当前支持2个模型
    model_name: str = "abab5.5-chat"  # abab5.5-chat/abab5-chat
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    group_id: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 3
    prefix_messages: List = Field(default_factory=list)
    streaming: bool = False

    temperature: float = 0.9       # 默认0.9，范围 (0, 1.0]，不能为0
    top_p: float = 0.8             # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    skip_info_mask: bool = False   # 对输出中易涉及隐私问题的文本信息进行脱敏，目前包括但不限于邮箱、域名、链接、证件号、家庭住址等，默认false，即开启脱敏
    tokens_to_generate: int = 2048 # 最大生成token数，默认256
    use_standard_sse: bool = True  # 是否使用标准SSE格式
    user_name: str = '我'          # 用户代称
    bot_name: str = '你'           # ai代称
    # 给定一个默认的prompt
    prompt: str = "你是MiniMax旗下一个专家型的大模型，总是会简洁全面的回答我的问题"

    def _llm_type(self) -> str:
        return "minimax_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'stream': self.streaming,
            'api_key': self.api_key,
            'group_id': self.group_id,
            'api_base': self.api_base,
            'model': self.model_name,
            'temperature': self.temperature,
            'top_p': self.top_p,
            'skip_info_mask': self.skip_info_mask,
            'tokens_to_generate': self.tokens_to_generate,
            'use_standard_sse': self.use_standard_sse,
            'role_meta': {
                'user_name': self.user_name,
                'bot_name': self.bot_name,
            }
        }

        prompt, message_dicts = self.prompt, []
        for message in messages:
            if isinstance(message, SystemMessage):
                prompt = message.content
            elif isinstance(message, AIMessage):
                message_dicts.append({'sender_type': 'BOT', 'text': message.content})
            else:
                message_dicts.append({'sender_type': 'USER', 'text': message.content})

        self.client = MiniMaxClient()

        if self.streaming:
            response = ""
            for stream_resp in completion_with_retry(self, prompt=prompt, messages=message_dicts, **params):
                # minimax stream模式和openai类似
                token = stream_resp['choices'][0].get('delta', '')
                response += token
                if run_manager:
                    run_manager.on_llm_new_token(token)
            return response
        else:
            full_response = completion_with_retry(self, prompt=prompt, messages=message_dicts, **params)
            return full_response["reply"]


if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    async def main():
        from langchain.schema import HumanMessage
        from core.api_base import NewApiBase

        api_key = ''
        api_base = NewApiBase('MiniMax').url

        chat = MiniMaxChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        # messages = [HumanMessage(content='你是谁')]
        messages = [HumanMessage(content='推荐成都旅游线路')]
        result = chat(messages)
        print(result)

    asyncio.run(main())


