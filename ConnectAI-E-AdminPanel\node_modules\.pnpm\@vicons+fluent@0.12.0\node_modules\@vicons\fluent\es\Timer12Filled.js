import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 12 12'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.5 0a.5.5 0 0 0 0 1h4a.5.5 0 0 0 0-1h-4zM1 6.5a4.5 4.5 0 1 1 9 0a4.5 4.5 0 0 1-9 0zm4 1a.5.5 0 0 0 1 0v-3a.5.5 0 0 0-1 0v3zm5.854-3.647a.5.5 0 0 1-.707.001l-1.002-.998a.5.5 0 1 1 .706-.708l1.002.998a.5.5 0 0 1 .001.707z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Timer12Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
