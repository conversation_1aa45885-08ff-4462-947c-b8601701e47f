# 本地开发环境配置文件
import os

# 数据库配置 - 使用SQLite替代MySQL
DATABASE_CONFIG = {
    'MYSQL_HOST': 'localhost',
    'MYSQL_PORT': 3306,
    'MYSQL_USER': 'root',
    'MYSQL_PASSWORD': 'connectai2023',
    'MYSQL_DATABASE': 'connectai-manager',
    # SQLite作为备用
    'SQLITE_PATH': './data/connectai.db'
}

# Redis配置 - 使用内存存储替代
REDIS_CONFIG = {
    'REDIS_HOST': 'localhost',
    'REDIS_PORT': 6379,
    'REDIS_DB': 0,
    'USE_MEMORY_STORE': True  # 使用内存存储替代Redis
}

# Elasticsearch配置 - 使用文件存储替代
ELASTICSEARCH_CONFIG = {
    'ES_HOST': 'localhost',
    'ES_PORT': 9200,
    'USE_FILE_STORE': True,  # 使用文件存储替代Elasticsearch
    'FILE_STORE_PATH': './data/search_index'
}

# RabbitMQ配置 - 使用内存队列替代
RABBITMQ_CONFIG = {
    'RABBITMQ_HOST': 'localhost',
    'RABBITMQ_PORT': 5672,
    'RABBITMQ_USER': 'rabbitmq',
    'RABBITMQ_PASSWORD': 'rabbitmq',
    'USE_MEMORY_QUEUE': True  # 使用内存队列替代RabbitMQ
}

# 服务端口配置
PORTS = {
    'MANAGER_SERVER': 3000,
    'DATACHAT_API': 5000,
    'ADMIN_PANEL': 8080,
    'PROXY': 8081
}

# 创建数据目录
def create_data_directories():
    """创建必要的数据目录"""
    directories = [
        './data',
        './data/mysql',
        './data/redis',
        './data/elasticsearch',
        './data/rabbitmq',
        './data/files',
        './data/search_index'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

if __name__ == "__main__":
    create_data_directories()
