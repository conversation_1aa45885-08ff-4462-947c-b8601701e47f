<template>
  <dark-mode-container class="flex-center h-full" :inverted="theme.footer.inverted">
    <span
      >Copyright &nbsp; ©2023 &nbsp; {{ $t('message.system.title')
      }}<a v-if="!isLark" class="text-md ml-3 z-9" href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2023011946号</a></span
    >
  </dark-mode-container>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/store';
import { isLark } from '@/utils';

defineOptions({ name: 'GlobalFooter' });

const theme = useThemeStore();


</script>

<style scoped></style>
