'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M3 7a4 4 0 0 1 4-4h3.343a1 1 0 0 1 0 2H7a2 2 0 0 0-2 2v3.341a1 1 0 0 1-2 0V7zm13.659-3a1 1 0 0 1 1-1H21a4 4 0 0 1 4 4v3.341a1 1 0 0 1-2 0V7a2 2 0 0 0-2-2h-3.341a1 1 0 0 1-1-1zM4 16.659a1 1 0 0 1 1 1V21a2 2 0 0 0 2 2h3.341a1 1 0 0 1 0 2H7a4 4 0 0 1-4-4v-3.341a1 1 0 0 1 1-1zm20 0a1 1 0 0 1 1 1V21a4 4 0 0 1-4 4h-3.341a1 1 0 0 1 0-2H21a2 2 0 0 0 2-2v-3.341a1 1 0 0 1 1-1zM14 16a2 2 0 1 0 0-4a2 2 0 0 0 0 4zm-1.382-8a1.5 1.5 0 0 0-1.342.83L10.691 10H10a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2h-.691l-.585-1.17A1.5 1.5 0 0 0 15.382 8h-2.764zM14 11a3 3 0 1 1 0 6a3 3 0 0 1 0-6z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ScanCamera28Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
