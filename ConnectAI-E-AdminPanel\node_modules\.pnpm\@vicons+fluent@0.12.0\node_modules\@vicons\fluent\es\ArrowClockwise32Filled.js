import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M5 16C5 9.925 9.925 5 16 5c2.923 0 5.58 1.14 7.55 3h-2.3a1.25 1.25 0 1 0 0 2.5h4.5c.69 0 1.25-.56 1.25-1.25v-4.5a1.25 1.25 0 1 0-2.5 0v.761A13.444 13.444 0 0 0 16 2.5C8.544 2.5 2.5 8.544 2.5 16S8.544 29.5 16 29.5S29.5 23.456 29.5 16c0-.28-.009-.558-.025-.833c-.041-.673-.621-1.167-1.296-1.167c-.708 0-1.239.643-1.198 1.35c.013.215.019.432.019.65c0 6.075-4.925 11-11 11S5 22.075 5 16z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ArrowClockwise32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
