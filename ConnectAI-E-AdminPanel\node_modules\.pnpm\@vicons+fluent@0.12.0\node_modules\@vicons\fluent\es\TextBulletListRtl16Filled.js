import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.75 5a1.25 1.25 0 1 1 0-2.5a1.25 1.25 0 0 1 0 2.5zm0 4.25a1.25 1.25 0 1 1 0-2.5a1.25 1.25 0 0 1 0 2.5zm-1.25 3a1.25 1.25 0 1 0 2.5 0a1.25 1.25 0 0 0-2.5 0zM10.25 3a.75.75 0 0 1 0 1.5h-8.5a.75.75 0 0 1 0-1.5h8.5zM11 8a.75.75 0 0 0-.75-.75h-8.5a.75.75 0 0 0 0 1.5h8.5A.75.75 0 0 0 11 8zm-.75 3.5a.75.75 0 0 1 0 1.5h-8.5a.75.75 0 0 1 0-1.5h8.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListRtl16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
