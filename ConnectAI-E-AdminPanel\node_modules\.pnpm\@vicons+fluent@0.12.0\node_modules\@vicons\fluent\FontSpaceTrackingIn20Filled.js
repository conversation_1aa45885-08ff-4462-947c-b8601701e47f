'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M7.7 2.482a.75.75 0 0 0-1.4 0l-3.25 8.495a.75.75 0 0 0 1.4.536l.773-2.018h3.555l.772 2.018a.75.75 0 0 0 1.4-.536L7.7 2.482zm.504 5.513H5.797L7 4.849l1.204 3.146zm-1.463 5.19a.75.75 0 1 0-.988 1.13l.498.435h-2.5a.75.75 0 0 0 0 1.5h2.5l-.498.436a.75.75 0 1 0 .988 1.128l2-1.751a.75.75 0 0 0 0-1.13l-2-1.748zM13 12a.75.75 0 0 0 .7-.482l3.25-8.495a.75.75 0 1 0-1.401-.536L13 9.15l-2.55-6.664a.75.75 0 0 0-1.4.536l3.25 8.495A.75.75 0 0 0 13 12zm1.312 5.744a.75.75 0 0 1-1.058.07l-2-1.748a.75.75 0 0 1 0-1.129l2-1.751a.75.75 0 1 1 .988 1.128l-.498.436h2.505a.75.75 0 0 1 0 1.5h-2.506l.498.435a.75.75 0 0 1 .071 1.059z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'FontSpaceTrackingIn20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
