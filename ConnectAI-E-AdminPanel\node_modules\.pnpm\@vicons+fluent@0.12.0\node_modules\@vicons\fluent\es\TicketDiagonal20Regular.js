import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12.03 3.237a.75.75 0 0 0-1.06 0L3.237 10.97a.75.75 0 0 0 0 1.061l.775.775a.083.083 0 0 0 .027.005a.266.266 0 0 0 .132-.035a2.25 2.25 0 0 1 3.054 3.054a.266.266 0 0 0-.035.131c0 .016.004.024.005.027l.774.775a.75.75 0 0 0 1.061 0l7.732-7.732a.75.75 0 0 0 0-1.06l-.774-.775a.083.083 0 0 0-.027-.005a.266.266 0 0 0-.133.035a2.25 2.25 0 0 1-3.053-3.054a.266.266 0 0 0 .035-.132a.083.083 0 0 0-.005-.027l-.775-.775zm-1.768-.707a1.75 1.75 0 0 1 2.475 0l.775.775c.407.407.337.986.14 1.346a1.25 1.25 0 0 0 1.696 1.696c.36-.197.94-.266 1.346.14l.776.775a1.75 1.75 0 0 1 0 2.475L9.736 17.47a1.75 1.75 0 0 1-2.475 0l-.775-.774c-.406-.407-.337-.986-.14-1.346a1.25 1.25 0 0 0-1.696-1.696c-.36.196-.94.266-1.346-.14l-.775-.776a1.75 1.75 0 0 1 0-2.475l7.732-7.732z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TicketDiagonal20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
