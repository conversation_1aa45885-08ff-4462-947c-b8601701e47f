<template>
  <div class="">
    <n-skeleton v-if="loading" class="skeleton" height="40px" :sharp="false" />
    <n-select v-else-if="isEmpty(resources)" :placeholder="`${t('请选择')}`" :options="[]" />
    <n-form v-else ref="formRef" :label-width="80" :model="data" size="large">
      <n-form-item
        v-for="(item, i) in resources"
        :key="i"
        :label="item.title"
        :path="item.scene"
        :rule="{
          required: item.required,
          trigger: ['input', 'blur'],
          message: `${t('请选择')}`
        }"
      >
        <n-select
          v-model:value="data[item.scene]"
          :placeholder="`${t('请选择')}`"
          :options="item.resource.map((item) => ({ value: item.id, label: item.name, models: item.models }))"
          @update-value="(value) => handleChange(value, item.scene)"
        />
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { FormInst } from 'naive-ui';
import { useVModel } from '@vueuse/core';
import { t } from '@/locales';
import { isEmpty } from 'lodash-es';

const emit = defineEmits(['update:value']);

const formRef = ref<FormInst | null>(null);
const props = defineProps<{
  data: Record<string, string>;
  resources: ApiApp.AppResources[];
  loading?: boolean;
}>();

const data = useVModel(props, 'data', emit);

const validate = async () => {
  return formRef?.value?.validate() || false;
};

const handleChange = (value: string, scene: string) => {
  emit('update:value', { [scene]: value });
};

defineExpose({ validate });
</script>
