import json
import re
import logging
from html import unescape
from random import choice
from typing import De<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Optional, <PERSON>, Tuple
from urllib.parse import urlparse, parse_qs
from urllib.parse import unquote
from curl_cffi.requests import BrowserType
from curl_cffi import requests


BROWSERS = [x.value for x in BrowserType]
REGEX_500_IN_URL = re.compile(r"(?:\d{3}-\d{2}\.js)")
REGEX_STRIP_TAGS = re.compile("<.*?>")
logger = logging.getLogger("duckduckgo_search.DDGS")
HOST = 'duckduckgo.aionekey.shop'


def _extract_vqd(html_bytes: bytes, keywords: str) -> Optional[str]:
    for c1, c2 in (
        (b'vqd="', b'"'),
        (b"vqd=", b"&"),
        (b"vqd='", b"'"),
    ):
        try:
            start = html_bytes.index(c1) + len(c1)
            end = html_bytes.index(c2, start)
            return html_bytes[start:end].decode()
        except Exception:
            pass
    raise Exception(f"_extract_vqd() {keywords=} Could not extract vqd.")


def _extract_query_url(html_bytes: bytes, keywords: str) -> Optional[str]:
    for c1, c2 in (
            (b'href="', b'">'),
            (b'src="', b'" '),
    ):
        try:
            start = html_bytes.index(c1) + len(c1)
            end = html_bytes.index(c2, start)
            return html_bytes[start:end].decode()
        except Exception:
            pass
    raise Exception(f"_extract_vqd() {keywords=} Could not extract vqd.")


def _text_extract_json(html_bytes: bytes, keywords: str) -> Optional[str]:
    """text(backend="api") -> extract json from html"""
    try:
        start = html_bytes.index(b"DDG.pageLayout.load('d',") + 24
        end = html_bytes.index(b");DDG.duckbar.load(", start)
        data = html_bytes[start:end]
        return json.loads(data)
    except Exception as ex:
        raise Exception(f"_text_extract_json() {keywords=} {type(ex).__name__}: {ex}")


def _is_500_in_url(url: str) -> bool:
    """something like '506-00.js' inside the url"""
    return bool(REGEX_500_IN_URL.search(url))


def _normalize(raw_html: str) -> str:
    """Strip HTML tags from the raw_html string."""
    return unescape(re.sub(REGEX_STRIP_TAGS, "", raw_html)) if raw_html else ""


def _normalize_url(url: str) -> str:
    """Unquote URL and replace spaces with '+'"""
    return unquote(url.replace(" ", "+")) if url else ""


def _random_browser() -> BrowserType:
    """Return a random browser from the curl-cffi"""
    return choice(BROWSERS)


class DDGS:
    """DuckDuckgo_search class to get search results from duckduckgo.com

    Args:
        headers (dict, optional): Dictionary of headers for the HTTP client. Defaults to None.
        proxies (Union[dict, str], optional): Proxies for the HTTP client (can be dict or str). Defaults to None.
        timeout (int, optional): Timeout value for the HTTP client. Defaults to 10.
    """

    def __init__(self, headers=None, proxies=None, timeout=10) -> None:
        self.proxies = proxies if proxies and isinstance(proxies, dict) else {"http": proxies, "https": proxies}
        self._session = requests.Session(
            headers=headers, proxies=self.proxies, timeout=timeout, impersonate=_random_browser()
        )
        self._session.headers["Referer"] = self._get_url_from_uri() + '/'

    def __enter__(self) -> "DDGS":
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self._session.close()

    def _get_url_from_uri(self, uri='', prefix='', schema='https'):
        url = schema + '://' + prefix + HOST + uri
        return url

    def _get_url(self, method: str, url: str, **kwargs) -> Optional[requests.Response]:
        try:
            resp = self._session.request(method, url, **kwargs)
            logger.debug(f"_get_url() {url} {resp.status_code} {resp.http_version} {resp.elapsed} {len(resp.content)}")
            resp.raise_for_status()
            if _is_500_in_url(str(resp.url)) or resp.status_code == 202:
                raise Exception("Ratelimit")
            if resp.status_code == 200:
                return resp
        except Exception as ex:
            raise Exception(f"_get_url() {url} {type(ex).__name__}: {ex}")

    def _update_query_params(self, keywords: str, query_url: str, payload: Dict):
        resp = self._get_url("POST", self._get_url_from_uri(), data={"q": keywords})
        if not resp:
            return None
        html_bytes = resp.content
        start = html_bytes.index(query_url.encode())
        end = html_bytes.index(b'"', start)
        url = html_bytes[start:end].decode()
        # print(url)
        query_params = parse_qs(urlparse(url).query)
        params = {'q': keywords}
        for key in query_params:
            if key in payload:
                params[key] = payload[key]
            else:
                params[key] = query_params[key][0]
        return params

    def text(
            self,
            keywords: str,
            region: str = "wt-wt",
            safesearch: str = "moderate",
            timelimit: Optional[str] = None,
            backend: str = "api",
            max_results: Optional[int] = None,
    ) -> Iterator[Dict[str, Optional[str]]]:
        """DuckDuckGo text search generator. Query params: https://duckduckgo.com/params

        Args:
            keywords: keywords for query.
            region: wt-wt, us-en, uk-en, ru-ru, etc. Defaults to "wt-wt".
            safesearch: on, moderate, off. Defaults to "moderate".
            timelimit: d, w, m, y. Defaults to None.
            backend: api, html, lite. Defaults to api.
                api - collect data from https://duckduckgo.com,
                html - collect data from https://html.duckduckgo.com,
                lite - collect data from https://lite.duckduckgo.com.
            max_results: max number of results. If None, returns results only from the first response. Defaults to None.
        Yields:
            dict with search results.

        """
        if backend == "api":
            results = self._text_api(keywords, region, safesearch, timelimit, max_results)
        # elif backend == "html":
        #     results = self._text_html(keywords, region, safesearch, timelimit, max_results)
        # elif backend == "lite":
        #     results = self._text_lite(keywords, region, timelimit, max_results)

        if results:
            for i, result in enumerate(results, start=1):
                yield result
                if max_results and i >= max_results:
                    break

    def _text_api(
            self,
            keywords: str,
            region: str = "wt-wt",
            safesearch: str = "moderate",
            timelimit: Optional[str] = None,
            max_results: Optional[int] = None,
    ) -> Iterator[Dict[str, Optional[str]]]:
        assert keywords, "keywords is mandatory"

        query_url = self._get_url_from_uri(prefix='links-', uri='/d.js')
        lang, region = region.split('-')
        payload = {'l': region.lower() + '-' + lang.lower(), 'ct': region.upper(), 'bing_market': lang.lower() + '-' + region.upper()}
        payload = self._update_query_params(keywords=keywords, query_url=query_url, payload=payload)
        safesearch = safesearch.lower()
        if safesearch == "moderate":
            payload["ex"] = "-1"
        elif safesearch == "off":
            payload["ex"] = "-2"
        elif safesearch == "on":  # strict
            payload["p"] = "1"
        if timelimit:
            payload['df'] = timelimit
        # print(payload)
        cache = set()
        for _ in range(11):
            resp = self._get_url("GET", url=query_url, params=payload)
            if resp is None:
                return

            page_data = _text_extract_json(resp.content, keywords)
            if page_data is None:
                return

            result_exists, next_page_url = False, None
            for row in page_data:
                href = row.get("u", None)
                if href and href not in cache and href != f"http://www.google.com/search?q={keywords}":
                    cache.add(href)
                    body = _normalize(row["a"])
                    if body:
                        result_exists = True
                        yield {
                            "title": _normalize(row["t"]),
                            "href": _normalize_url(href),
                            "body": body,
                        }
                else:
                    next_page_url = row.get("n", None)
            if max_results is None or result_exists is False or next_page_url is None:
                return
            payload["s"] = next_page_url.split("s=")[1].split("&")[0]


if __name__ == '__main__':
    with DDGS() as ddgs:
        results = [x for x in ddgs.text("日本地震", region='zh-CN', max_results=10)]
        print(len(results), results)