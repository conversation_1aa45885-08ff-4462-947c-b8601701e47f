import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M11.749 6c1.358 0 1.952.898 2 2.326l.003.189V11l2.871.422a2.75 2.75 0 0 1 2.292 3.284l-.04.17l-1.228 4.442a1.75 1.75 0 0 1-1.292 1.239l-.146.027l-3.061.44a1.75 1.75 0 0 1-1.825-.973l-.06-.14l-.217-.572a4.13 4.13 0 0 0-1.176-1.674l-.203-.163l-1.596-1.2a1.748 1.748 0 0 0-.266-.166l-.143-.064l-2.195-.868a.75.75 0 0 1-.474-.66c-.036-.722.49-1.246 1.422-1.712c.719-.36 1.727-.33 3.066.043l.272.08v-4.45c0-1.538.578-2.506 1.996-2.506zm0-3.5a5.75 5.75 0 0 1 5.3 7.987l.173.042a3.763 3.763 0 0 0-.453-.095l-1.267-.186a4.25 4.25 0 1 0-6.748 1.018l-.001.415c-.614-.109-1.168-.136-1.672-.072A5.75 5.75 0 0 1 11.75 2.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapSingle24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
