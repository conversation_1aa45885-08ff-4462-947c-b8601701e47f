
from .handler import (
    CompanyHandler,
    ProxyListHandler,
    TenantProxyHandler,
    UpdateProxyHandler,
    RechargeProxyHandler,
    AppListHandler,
    AppUpdateHandler,
    AdminLoginHandler,
    AdminLogoutHandler,
    NewAccountHandler
)

urls = [
    #company
    (r"/admin/company", CompanyHandler),    # GET/POST
    (r"/admin/company/benifits", CompanyHandler, dict(benifits=True)), # GET
    (r"/admin/company/([0-9a-z]{24})", CompanyHandler), # PUT TODO: 长度可能会改，先放在这
    (r"/admin/company/([0-9a-z]{24})/benifits", CompanyHandler, dict(benifits=True)), # POST
    #proxy
    (r"/admin/proxy", ProxyListHandler),    # GET
    (r"/admin/proxy/([0-9]{1})", TenantProxyHandler), # GET
    (r"/admin/proxy/([0-9]{1})/(on|off)", UpdateProxyHandler), # PUT
    (r"/admin/proxy/([0-9]{1})", TenantProxyHandler), # POST
    (r"/admin/proxy/([0-9]{1})/recharge", RechargeProxyHandler, dict(recharge=True)), # GET
    (r"/admin/proxy/([0-9]{1})/recharge/([\w-]+)", RechargeProxyHandler, dict(recharge=True)), # POST
    #applicaiton
    (r"/admin/application", AppListHandler),    # GET
    (r"/admin/application/([0-9a-z]{24})", AppUpdateHandler), # POST
    #admin
    (r"/admin/account/login", AdminLoginHandler),    # POST
    (r"/admin/account/logout", AdminLogoutHandler), # POST
    (r"/admin/account", NewAccountHandler),    # POST
    (r"/admin/account", NewAccountHandler), # GET
    (r"/admin/account/([0-9a-z]{24})", NewAccountHandler), # PUT

]
