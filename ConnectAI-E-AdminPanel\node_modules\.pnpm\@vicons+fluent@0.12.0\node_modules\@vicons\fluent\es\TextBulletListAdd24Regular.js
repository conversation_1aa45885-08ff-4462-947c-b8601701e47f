import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.25 15.5a1.25 1.25 0 1 1 0 2.499a1.25 1.25 0 0 1 0-2.499zm3.5.5h4.424A6.52 6.52 0 0 0 11 17.5H6.75a.75.75 0 0 1-.102-1.493L6.75 16zm0-5h14.5l.102-.007A.75.75 0 0 0 21.25 9.5H6.75l-.102.007A.75.75 0 0 0 6.75 11zm-3.5-2a1.25 1.25 0 1 1 0 2.499A1.25 1.25 0 0 1 3.25 9zm0-6.5a1.25 1.25 0 1 1 0 2.499a1.25 1.25 0 0 1 0-2.499zm3.5.5h14.5a.75.75 0 0 1 .102 1.493l-.102.007H6.75a.75.75 0 0 1-.102-1.493L6.75 3zM23 17.5a5.5 5.5 0 1 0-11 0a5.5 5.5 0 0 0 11 0zm-5 .5l.001 2.503a.5.5 0 1 1-1 0V18h-2.505a.5.5 0 0 1 0-1H17v-2.5a.5.5 0 1 1 1 0V17h2.497a.5.5 0 0 1 0 1H18z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextBulletListAdd24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
