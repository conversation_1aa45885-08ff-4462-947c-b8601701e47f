'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M11.75 15a2.25 2.25 0 0 1 .154 4.495l-.154.005H11v1.75a.75.75 0 0 1-.648.743L10.25 22a.75.75 0 0 1-.743-.648L9.5 21.25v-5.5a.75.75 0 0 1 .648-.743L10.25 15h1.5zM18 15a2 2 0 0 1 2 2a.75.75 0 0 1-1.493.102L18.5 17a.5.5 0 0 0-.41-.492L18 16.5h-.625a.625.625 0 0 0-.092 1.243l.092.007h.5a2.125 2.125 0 0 1 .152 4.245l-.152.005h-.625a2 2 0 0 1-2-2a.75.75 0 0 1 1.493-.102l.007.102a.5.5 0 0 0 .41.492l.09.008h.625a.625.625 0 0 0 .092-1.243l-.092-.007h-.5a2.125 2.125 0 0 1-.152-4.245l.152-.005H18zM7.75 15a.75.75 0 0 1 .102 1.493l-.102.007H5.5v1.502h1.75a.75.75 0 0 1 .102 1.494l-.102.007H5.5v1.728a.75.75 0 0 1-.648.743l-.102.007a.75.75 0 0 1-.743-.648L4 21.231V15.75a.75.75 0 0 1 .648-.743L4.75 15h3zm4 1.5H11V18h.75a.75.75 0 0 0 .102-1.493l-.102-.007zM5 3a3 3 0 0 1 3 3l-.002.105l.002.05V10a3 3 0 0 1-2.824 2.995L5 13h-.249A2.751 2.751 0 0 1 2 10.249a1 1 0 0 1 1.993-.117l.007.117c0 .38.283.694.65.744L4.75 11H5a1 1 0 0 0 .993-.883L6 10V8.83A3 3 0 1 1 5 3zm7.251 0a2.751 2.751 0 0 1 2.752 2.751a1 1 0 0 1-1.994.117l-.006-.117a.752.752 0 0 0-.65-.744L12.251 5h-.248a1 1 0 0 0-.994.883L11.003 6v1.17a3 3 0 1 1-2 2.83l.001-.105l-.001-.05V6a3 3 0 0 1 2.823-2.995L12.003 3h.248zM19 3a3 3 0 0 1 2.995 2.824L22 6v4a3 3 0 0 1-5.995.176L16 10V6a3 3 0 0 1 3-3zm-6.997 6a1 1 0 1 0 0 2a1 1 0 0 0 0-2zM19 5a1 1 0 0 0-.993.883L18 6v4a1 1 0 0 0 1.993.117L20 10V6a1 1 0 0 0-1-1zM5 5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Fps96024Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
