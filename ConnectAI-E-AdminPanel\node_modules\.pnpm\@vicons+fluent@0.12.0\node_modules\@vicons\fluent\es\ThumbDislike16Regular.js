import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.578 10c.016.133.034.285.05.45c.074.74.131 1.793-.013 2.677c-.072.44-.201.887-.442 1.237c-.257.372-.647.635-1.173.635c-.516 0-.83-.37-1.024-.69c-.192-.321-.36-.756-.533-1.201l-.013-.034c-.547-1.41-1.295-3.317-3.307-4.658a4.979 4.979 0 0 0-.853-.458c-.697-.289-1.322-1.057-1.157-1.936l.224-1.196a2 2 0 0 1 1.43-1.558l4.95-1.375a3.5 3.5 0 0 1 4.377 2.727l.454 2.419a2.5 2.5 0 0 1-2.458 2.96h-.512zm1.533-5.196a2.5 2.5 0 0 0-3.126-1.948l-4.95 1.375a1 1 0 0 0-.716.78l-.224 1.195c-.053.283.162.664.558.828c.31.129.659.306 1.025.55c2.287 1.524 3.128 3.696 3.676 5.108l.008.02c.19.49.329.844.472 1.082c.069.115.12.17.151.195a.11.11 0 0 0 .016.01c.142 0 .245-.054.349-.203c.118-.173.216-.451.278-.83c.122-.75.077-1.697.005-2.417a16.374 16.374 0 0 0-.114-.897l-.009-.05l-.002-.013v-.003A.5.5 0 0 1 10 9h1.09a1.5 1.5 0 0 0 1.475-1.777l-.454-2.419z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ThumbDislike16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
