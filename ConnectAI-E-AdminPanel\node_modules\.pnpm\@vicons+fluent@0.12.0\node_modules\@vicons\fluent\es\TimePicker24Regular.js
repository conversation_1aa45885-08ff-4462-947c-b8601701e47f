import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.583 17.527a.75.75 0 0 1-.11 1.055l-4 3.25a.75.75 0 0 1-.946 0l-4-3.25a.75.75 0 1 1 .946-1.164L12 20.284l3.527-2.866a.75.75 0 0 1 1.056.11zM4.926 9.485c.543 0 .957.178 1.244.535c.288.356.434.869.441 1.536v.916c0 .688-.142 1.215-.427 1.58c-.285.364-.702.546-1.25.546c-.543 0-.958-.179-1.245-.537c-.287-.357-.434-.87-.44-1.538v-.916c-.001-.695.143-1.222.431-1.582c.289-.36.704-.54 1.246-.54zm4.016 0c.543 0 .957.178 1.245.535c.287.356.434.869.44 1.536v.916c0 .688-.142 1.215-.427 1.58c-.285.364-.702.546-1.25.546c-.543 0-.958-.179-1.245-.537c-.287-.357-.434-.87-.44-1.538v-.916c0-.695.143-1.222.432-1.582c.288-.36.703-.54 1.245-.54zm5.992 0c.542 0 .957.178 1.244.535c.287.356.434.869.441 1.536v.916c0 .688-.142 1.215-.427 1.58c-.285.364-.702.546-1.251.546c-.542 0-.957-.179-1.244-.537c-.287-.357-.434-.87-.441-1.538v-.916c0-.695.144-1.222.432-1.582c.288-.36.704-.54 1.246-.54zm4.016 0c.543 0 .957.178 1.244.535c.287.356.434.869.441 1.536v.916c0 .688-.142 1.215-.427 1.58c-.285.364-.702.546-1.251.546c-.542 0-.957-.179-1.244-.537c-.287-.357-.434-.87-.441-1.538v-.916c0-.695.144-1.222.432-1.582c.289-.36.704-.54 1.246-.54zM12 13c.162 0 .296.049.402.147a.497.497 0 0 1 .159.383a.49.49 0 0 1-.157.378a.571.571 0 0 1-.403.145a.566.566 0 0 1-.4-.146a.49.49 0 0 1-.158-.377c0-.157.053-.285.16-.383A.564.564 0 0 1 12 13zm-7.074-2.715c-.232 0-.404.09-.514.268c-.11.179-.17.459-.176.84v1.21c0 .405.055.706.166.903c.11.197.287.296.531.296c.242 0 .416-.095.523-.284c.107-.19.163-.478.168-.868v-1.183c0-.412-.057-.712-.17-.9c-.112-.188-.288-.282-.528-.282zm4.016 0c-.232 0-.404.09-.514.268c-.11.179-.17.459-.176.84v1.21c0 .405.055.706.166.903c.11.197.287.296.531.296c.242 0 .416-.095.523-.284c.107-.19.163-.478.168-.868v-1.183c0-.412-.057-.712-.17-.9c-.112-.188-.288-.282-.528-.282zm5.992 0c-.232 0-.404.09-.514.268c-.11.179-.17.459-.176.84v1.21c0 .405.055.706.165.903c.11.197.288.296.532.296c.241 0 .416-.095.523-.284c.107-.19.163-.478.167-.868v-1.183c0-.412-.056-.712-.169-.9c-.113-.188-.289-.282-.528-.282zm4.016 0c-.232 0-.404.09-.514.268c-.092.149-.148.368-.169.657l-.007.182v1.21c0 .406.055.707.165.904c.111.197.288.296.532.296c.242 0 .416-.095.523-.284c.09-.158.143-.385.16-.682l.008-.186v-1.183c0-.412-.057-.712-.17-.9c-.113-.188-.289-.282-.528-.282zM12 10.2c.162 0 .296.049.402.147a.497.497 0 0 1 .159.382a.49.49 0 0 1-.157.378a.571.571 0 0 1-.403.145a.566.566 0 0 1-.4-.145a.49.49 0 0 1-.158-.378c0-.157.053-.284.16-.382A.564.564 0 0 1 12 10.2zM7.529 5.418l4-3.25a.75.75 0 0 1 .85-.066l.095.066l4 3.25a.75.75 0 0 1-.854 1.228l-.091-.064L12 3.716L8.473 6.582a.75.75 0 0 1-1.026-1.088l.08-.076l4-3.25l-4 3.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TimePicker24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
