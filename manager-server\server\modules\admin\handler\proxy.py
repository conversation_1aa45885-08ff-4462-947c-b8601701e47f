from core.base_handler import (
    arguments,
    authenticated
)

from settings.constant import PROXY_LIST
from core.utils import ObjIDStr
from modules.account.model import NewAdminModel, ProxyModel

from .base_handler import BaseHandler

class ProxyListHandler(BaseHandler):

    # get /admin/proxy
    @authenticated
    @arguments
    def get(self, model: NewAdminModel = None):
        self.finish({
            "code": 0,
            "msg": "success",
            "data": [
                {
                    "proxy_id": proxy["id"],
                    "name": proxy["name"]
                }
                for proxy in PROXY_LIST
            ]
        })


class TenantProxyHandler(BaseHandler):

    # get /admin/proxy/{proxy_id}
    @authenticated
    @arguments
    async def get(
        self,
        proxy_id: ObjIDStr = "",
        page: int = 1,
        page_size: int = 10,
        query: str = '',
        model: ProxyModel = None
    ):
        tenant_list, total = await model.get_proxy_list(query, proxy_id, page, page_size)

        # 当提供的page和page_size不合法时，返回空列表（get_tenant_list处理）
        self.finish({
            "code": 0,
            "msg": "success",
            "page": page,
            "page_size": page_size,
            "total": total,
            "data": [
                {
                    "id": tenant["id"],
                    "company_name": tenant["name"],
                    "key": tenant["token"],  # token -> apikey
                    "request_count": tenant["request_count"],
                    "used": tenant["used"],
                    "available_balance": tenant["available_amount"],
                    "is_on": tenant["status"],
                    "creation_time": tenant["created"],

                } for tenant in tenant_list
            ] if tenant_list else []
        })

    @authenticated
    @arguments
    async def post(
        self,
        proxy_id: ObjIDStr = "",
        name: str = "",
        model: ProxyModel = None
    ):
        code = await model.add_tenant(proxy_id, name)
        if code == 0:
            self.finish({
                "code": 0,
                "msg": "success",
            })
        else:
            self.finish({
                'code': 1,
                'msg': 'error',
            })


class UpdateProxyHandler(BaseHandler):
    # put /admin/proxy/{proxy_id}/{proxy_action}
    @authenticated
    @arguments
    async def put(
        self,
        proxy_id: ObjIDStr = "",
        proxy_action: ObjIDStr = "",
        tenant_id: str = "",
        model: ProxyModel = None
    ):
        code = await model.update(proxy_id, tenant_id, proxy_action)
        if code == 0:
            self.finish({
                "code": 0,
                "msg": "success",
            })
        else:
            self.finish({
                'code': 1,
                'msg': 'error',
            })

class RechargeProxyHandler(BaseHandler):

    recharge = False

    def initialize(self, recharge=False):
        self.recharge = recharge

    # GET /admin/proxy/([0-9a-z]{24})/recharge
    @authenticated
    @arguments
    async def get(
        self,
        proxy_id: ObjIDStr = "",
        page: int = 1,
        page_size: int = 10,
        query: str = '',
        model: ProxyModel = None
    ):
        order_list, total = await model.get_order_list(query, query, proxy_id, page, page_size)

        self.finish({
            "code": 0,
            "msg": "success",
            "page": page,
            "page_size": page_size,
            "total": total,
            "data": [
                {
                    "company_name": order["name"],
                    "key": order['key'],  # token -> apikey
                    "recharge_amount": order["amount"],
                    "token": order["token"],
                    "recharge_date": order["created"],
                } for order in order_list
            ]
        })

    # POST /admin/proxy/([0-9a-z]{24})/recharge/([0-9a-z]{24})
    @authenticated
    @arguments
    async def post(
        self,
        proxy_id: ObjIDStr = "",
        key: ObjIDStr = "",
        amount: int = 0,
        token: int = 0,
        model: ProxyModel = None
    ):
        code = await model.recharge(proxy_id, key, amount, token)
        if code == 0:
            self.finish({
                "code": 0,
                "msg": "success",
            })
        else:
            self.finish({
                'code': 1,
                'msg': 'error',
            }
    )