import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  const cookies = await chrome.cookies.getAll({ domain:"feishu.cn" })
  res.send({
    cookies:filtersCookies(cookies)
  })
}

export default handler


function filtersCookies(cookies){
  let result;
  console.log(cookies);
  result = cookies.filter((item)=>{
    return item.name === 'lark_oapi_csrf_token' || item.name === 'session';
  })
  return result;
}


