# 代码文档 - package.json

## 项目信息
- **名称**: connectai-helper
- **显示名**: ConnectA<PERSON> Helper
- **版本**: 0.0.22
- **描述**: 🤞One-click deployment for connect-ai app
- **作者**: <EMAIL>
- **贡献者**: leizhenpeng

## 技术栈
- **Plasmo**: 浏览器扩展开发框架
- **React 18.2.0**: 前端框架
- **TypeScript 5.1.3**: 类型系统
- **Tailwind CSS**: 样式框架
- **Semi UI**: 字节跳动UI组件库

## 核心脚本
- **dev**: 开发模式 (`plasmo dev`)
- **build**: 构建扩展 (`plasmo build`)
- **zip:firefox**: 构建Firefox版本并打包
- **zip:chrome**: 构建Chrome版本并打包
- **zip:edge**: 构建Edge版本并打包
- **package**: 打包扩展

## 主要依赖

### UI组件
- **@douyinfe/semi-ui**: Semi Design组件库
- **@douyinfe/semi-icons**: Semi图标库

### 扩展开发
- **@plasmohq/messaging**: Plasmo消息传递
- **@plasmohq/storage**: Plasmo存储管理
- **plasmo**: 扩展开发框架

### 代码编辑
- **ace-builds**: Ace代码编辑器
- **react-ace**: React版Ace编辑器

### 工具库
- **axios**: HTTP客户端
- **ahooks**: React Hooks工具库
- **node-fetch**: 网络请求

## 浏览器权限
- **host_permissions**: 所有URL访问权限
- **permissions**:
  - scripting: 脚本注入权限
  - activeTab: 活动标签页权限
  - cookies: Cookie访问权限

## 开发工具
- **TypeScript**: 类型检查
- **Prettier**: 代码格式化
- **Vitest**: 单元测试
- **PostCSS**: CSS处理

## 浏览器兼容性
- **Chrome**: Manifest V3
- **Firefox**: Manifest V2
- **Edge**: Manifest V3

## 技术特点
- 跨浏览器扩展支持
- 现代React开发体验
- 代码编辑器集成
- 一键部署功能
- 多平台构建支持
