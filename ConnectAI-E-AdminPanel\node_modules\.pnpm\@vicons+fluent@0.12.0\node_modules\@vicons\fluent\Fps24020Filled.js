'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M2.988 4.382a.75.75 0 0 1-1.478-.255v-.004l.002-.004l.001-.01l.006-.027l.018-.078a2.745 2.745 0 0 1 .457-.954C2.405 2.502 3.12 2 4.25 2c.959 0 1.731.38 2.222 1.003c.48.608.643 1.39.517 2.124c-.14.816-.604 1.312-1.147 1.628c-.364.212-.816.366-1.183.491a14.7 14.7 0 0 0-.294.103c-.453.165-.767.327-.982.57a1.384 1.384 0 0 0-.298.581H6.25a.75.75 0 0 1 0 1.5h-4a.75.75 0 0 1-.75-.75c0-1.012.266-1.769.76-2.326c.475-.536 1.09-.802 1.59-.984c.166-.061.314-.112.448-.157c.322-.11.566-.194.789-.324c.242-.14.374-.293.424-.586a1.196 1.196 0 0 0-.217-.942C5.113 3.7 4.792 3.5 4.25 3.5c-.62 0-.905.248-1.056.45a1.245 1.245 0 0 0-.206.432zm0-.001l.002-.007v.002l-.001.003v.002zm0 .003zM16.5 2A2.5 2.5 0 0 0 14 4.5v3a2.5 2.5 0 0 0 5 0v-3A2.5 2.5 0 0 0 16.5 2zm1 5.5a1 1 0 1 1-2 0v-3a1 1 0 1 1 2 0v3zM3.5 12a.5.5 0 0 0-.5.5v5a.5.5 0 0 0 1 0V16h1.5a.5.5 0 0 0 0-1H4v-2h2.5a.5.5 0 0 0 0-1h-3zm5 0a.5.5 0 0 0-.5.5v5a.5.5 0 0 0 1 0V16h1a2 2 0 1 0 0-4H8.5zm.5 3v-2h1a1 1 0 1 1 0 2H9zm4-1.25c0-.966.784-1.75 1.75-1.75h.764c.82 0 1.486.665 1.486 1.486v.014a.5.5 0 0 1-1 0v-.014a.486.486 0 0 0-.486-.486h-.764a.75.75 0 0 0 0 1.5h.5a1.75 1.75 0 1 1 0 3.5h-.764c-.82 0-1.486-.665-1.486-1.486V16.5a.5.5 0 0 1 1 0v.014c0 .268.217.486.486.486h.764a.75.75 0 0 0 0-1.5h-.5A1.75 1.75 0 0 1 13 13.75zM8.75 2a.75.75 0 0 1 .75.75V5.5H11V2.75a.75.75 0 0 1 1.5 0V9.5a.75.75 0 0 1-1.5 0V7H8.75A.75.75 0 0 1 8 6.25v-3.5A.75.75 0 0 1 8.75 2zM2.989 4.38v.002z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Fps24020Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
