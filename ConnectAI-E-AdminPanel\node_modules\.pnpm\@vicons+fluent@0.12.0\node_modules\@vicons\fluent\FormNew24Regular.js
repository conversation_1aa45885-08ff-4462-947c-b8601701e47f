'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M5.75 3A2.75 2.75 0 0 0 3 5.75v12.5A2.75 2.75 0 0 0 5.75 21h6.272a6.471 6.471 0 0 1-.709-1.5H5.75c-.69 0-1.25-.56-1.25-1.25V5.75c0-.69.56-1.25 1.25-1.25h12.5c.69 0 1.25.56 1.25 1.25v5.563a6.471 6.471 0 0 1 1.5.709V5.75A2.75 2.75 0 0 0 18.25 3H5.75zm12.207 8.016a.749.749 0 0 0-.703-1.014H12.25a.75.75 0 0 0 0 1.5h2.742a6.48 6.48 0 0 1 2.965-.486zM8.25 8.502a2.25 2.25 0 1 0 0 4.5a2.25 2.25 0 0 0 0-4.5zm-.75 2.25a.75.75 0 1 1 1.5 0a.75.75 0 0 1-1.5 0zM6 16.25a2.25 2.25 0 1 1 4.5 0a2.25 2.25 0 0 1-4.5 0zm2.25-.75a.75.75 0 1 0 0 1.5a.75.75 0 0 0 0-1.5zm9.004-8h-10.5a.75.75 0 1 1 0-1.5h10.5a.75.75 0 0 1 0 1.5zM23 17.5a5.5 5.5 0 1 0-11 0a5.5 5.5 0 0 0 11 0zm-4.999 3.003a.5.5 0 1 1-1 0V18h-2.505a.5.5 0 1 1 0-1H17v-2.5a.5.5 0 1 1 1 0V17h2.503a.5.5 0 1 1 0 1h-2.502v2.503z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'FormNew24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
