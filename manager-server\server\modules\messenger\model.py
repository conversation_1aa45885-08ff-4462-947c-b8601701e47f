import logging
import random
import json
from uuid import uuid4
from datetime import datetime
from functools import cached_property
from tornado.options import options
from urllib.parse import quote
from sqlalchemy import alias, and_, or_, select, func, distinct, text
from sqlalchemy.orm import (
    relationship,
    joinedload,
    foreign,
    column_property,
    aliased,
)
from core.base_model import MysqlModel, RedisModel
from core.web import Web
from core.utils import row2dict, _
from core.exception import NotFound, PermissionDenied, InternalError, Duplicate
from core.schema import (
    ObjID,
    Tenant,
    Application, AppInstance, AppInstanceBotInstance,
    Bot, BotInstance,
    Messenger, MessengerWithConfig, MessengerSeat,
)
from core.feishu import Lark
from settings.constant import (
    VALIDATION_TOKEN, ENCRIPT_KEY,
    WEBSOCKET_SESSION_CACHE_EXPIRE,
)


class MessengerWithSeatCount(Messenger):

    seat_count = column_property(
        select(func.count(MessengerSeat.id)).where(and_(
            MessengerSeat.messenger_id == Messenger.id,
            MessengerSeat.status == 1,
        ))
    )


class MessengerModel(MysqlModel):
    async def get_messenger_app(self, lang='zh_CN'):
        app = self.session.query(Application).filter(
            Application.name == 'Messenger',
            Application.status == 0,
        ).order_by(
            Application.modified.desc(),
        ).first()
        return row2dict(app)

    async def get_list(self, tenant_id, user_id, page, size, lang='zh_CN'):
        query = self.session.query(MessengerWithSeatCount).filter(
            Messenger.tenant_id == tenant_id,
            Messenger.status == 0
        ).order_by(
            Messenger.created.desc(),
        )
        total = await self.query_total(query)
        if total == 0:
            return [], 0
        return [row2dict(i, lang=lang) for i in await self.query_one_page(query, page, size)], total

    async def create_messenger(self, tenant_id, user_id, name, description, platform):
        messenger_id = ObjID.new_id()
        self.session.begin_nested()
        self.session.add(Messenger(
            id=messenger_id,
            tenant_id=tenant_id,
            user_id=user_id,
            name=name,
            description=description,
            platform=platform,
        ))
        self.session.commit()
        return messenger_id

    async def update_chat_id(self, tenant_id, user_id, messenger_id, chat_id):
        self.session.begin_nested()
        self.session.query(Messenger).filter(
            Messenger.id == messenger_id,
            Messenger.tenant_id == tenant_id,
        ).update(dict(chat_id=chat_id), synchronize_session=False)
        self.session.commit()

    async def update_ai(self, tenant_id, user_id, messenger_id, ai):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        self.session.begin_nested()
        self.session.query(Messenger).filter(
            Messenger.id == messenger_id,
            Messenger.tenant_id == tenant_id,
        ).update(dict(ai=ai), synchronize_session=False)
        self.session.commit()

    async def update_name(self, tenant_id, user_id, messenger_id, name, description):
        self.session.begin_nested()
        self.session.query(Messenger).filter(
            Messenger.id == messenger_id,
            Messenger.tenant_id == tenant_id,
        ).update(dict(
            name=name,
            description=description,
        ), synchronize_session=False)
        self.session.commit()

    async def get_messenger(self, tenant_id, user_id, messenger_id):
        messenger = self.session.query(MessengerWithConfig).filter(
            Messenger.id == messenger_id,
            Messenger.tenant_id == tenant_id,
            Messenger.status == 0,
        ).first()
        if not messenger:
            raise NotFound()
        return messenger

    async def get_app_info_by_messenger_id(self, tenant_id, user_id, messenger_id, lang='zh_CN'):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        instance_id = messenger.instance_id
        if not messenger.instance_id:
            # 自动帮忙创建一个app_instance记录
            from modules.application.resource_model import AppModel
            application_id = self.session.query(Application.id).filter(
                Application.name == 'Messenger',
                Application.status == 0,
            ).limit(1).scalar()
            with AppModel() as model:
                instance_id = await model.action(tenant_id, application_id, 'buy', multi=True, lang=lang)
            self.session.begin_nested()
            self.session.query(Messenger).filter(
                Messenger.id == messenger_id,
                Messenger.tenant_id == tenant_id,
            ).update(dict(
                instance_id=instance_id,
            ), synchronize_session=False)
            self.session.commit()
        instance = self.session.query(AppInstance).filter(AppInstance.id == instance_id).first()
        if not instance:
            raise NotFound()
        return row2dict(instance, lang=lang)

    async def get_bot_setting_by_messenger_id(self, tenant_id, user_id, messenger_id, lang='zh_CN'):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        bot_instance_id = messenger.bot_instance_id
        if not bot_instance_id:
            bot_instance_id = ObjID.new_id()
            bot_id = self.session.query(Bot.id).filter(Bot.platform == 'messenger').limit(1).scalar()
            self.session.begin_nested()
            self.session.add(BotInstance(
                id=bot_instance_id,
                tenant_id=tenant_id,
                user_id=user_id,
                bot_id=bot_id,
                # 这里直接使用平台的名字作为机器人的名字
                # 使用客服的名称
                name=_(messenger.name, lang=lang),
                description=_('你的飞书 AI 助手，助你提升工作效率', lang=lang),
                validation_token='',
                encript_key='',
            ))
            self.session.flush()
            self.session.query(Messenger).filter(
                Messenger.id == messenger_id,
            ).update(dict(bot_instance_id=bot_instance_id), synchronize_session=False)
            self.session.commit()

        from modules.application.resource_model import BotInstanceWithPlatform

        bot_instance = self.session.query(BotInstanceWithPlatform).filter(
            BotInstance.id == bot_instance_id,
        ).first()
        if not bot_instance:
            raise NotFound('找不到机器人')
        result = row2dict(bot_instance, lang=lang)
        # 这里不再使用子域名
        # 当前这个bot固定是feishu
        callback_url = f'{options.SCHEMA}://{options.DOMAIN}/feishu/{bot_instance_id}'
        result['callback_url'] = {
            'card': callback_url + '/card',
            'event': callback_url + '/event',
        }
        # TODO 这里要给一个logo
        result['logo'] = 'https://mpic.forkway.cn/cdn/logo/feishuapp.png'
        return result

    async def save_bot_setting_by_messenger_id(
        self, tenant_id, user_id, messenger_id,
        app_id, app_secret,
        encript_key, validation_token,
    ):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        bot_instance_id = messenger.bot_instance_id
        if not bot_instance_id:
            raise NotFound('找不到机器人')

        # 如果是飞书，尝试获取机器人信息，包含头像以及open_id
        info = {}
        try:
            if app_id[:4] == 'cli_':  # 飞书机器人
                info = await Lark(app_id, app_secret).bot_info(app_id)
        except Exception as e:
            logging.error(e)

        self.session.begin_nested()
        self.session.query(BotInstance).filter(
            BotInstance.id == bot_instance_id,
        ).update(dict(
            app_id=app_id or '',
            app_secret=app_secret or '',
            encript_key=encript_key or ENCRIPT_KEY,
            validation_token=validation_token or VALIDATION_TOKEN,
            info=info,
        ), synchronize_session=False)
        self.session.commit()

    async def get_client_setting_by_messenger_id(self, messenger_id, lang='zh_CN', current_user=None):
        messenger = self.session.query(MessengerWithConfig).filter(
            or_(
                Messenger.id == messenger_id,
                # chat box那边会使用bot_instance_id查询
                Messenger.bot_instance_id == messenger_id,
            ),
            Messenger.status == 0,
        ).first()
        if not messenger:
            raise NotFound()
        config = messenger.config if isinstance(messenger.config, dict) else {}
        res = dict(
            form=config.get('form', 0),
            links=[dict(
                title=_(i.get('title', ''), lang=lang),
                href=i.get('href', ''),
            ) for i in config.get('links', [])],
            lang=config.get('lang', options.DEFAULT_LOCALE),
            theme=config.get('theme', {'primaryColor': "rgba(102, 36, 176, 1)"}),
            logo=config.get('logo', ''),
            welcome=config.get('welcome', ''),
            api=config.get('api', 0),
        )
        if current_user:
            res['access_token'] = config.get('access_token', '')
        return res
    
    async def save_client_setting_by_messenger_id(
        self, tenant_id, user_id, messenger_id, form, links,
        lang='', theme=dict(), logo='', welcome='', api=0,
    ):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        config = messenger.config if isinstance(messenger.config, dict) else {}
        config.update(
            form=form, links=links,
            lang=lang, theme=theme,
            logo=logo, welcome=welcome,
            api=api,
        )
        self.session.begin_nested()
        self.session.query(MessengerWithConfig).filter(
            Messenger.id == messenger_id,
        ).update(dict(config=config), synchronize_session=False)
        self.session.commit()

    async def refresh_access_token_by_messenger_id(self, tenant_id, user_id, messenger_id):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        config = messenger.config if isinstance(messenger.config, dict) else {}
        if not config.get('api', False):
            raise PermissionDenied('api disable')
        access_token = f"m-{str(uuid4()).replace('-', '')}"
        config.update(
            access_token=access_token,
        )
        self.session.begin_nested()
        self.session.query(MessengerWithConfig).filter(
            Messenger.id == messenger_id,
        ).update(dict(config=config), synchronize_session=False)
        self.session.commit()

    async def get_chat_list_by_messenger_id(self, tenant_id, user_id, messenger_id):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        bot_instance = self.session.query(BotInstance).filter(
            BotInstance.id == messenger.bot_instance_id,
        ).first()
        if not bot_instance:
            return []
            # raise NotFound()
        return await Lark(bot_instance.app_id, bot_instance.app_secret).chat_list()

    async def get_member_list_by_messenger_id(self, tenant_id, user_id, messenger_id):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        if not messenger.chat_id:
            raise NotFound()
        bot_instance = self.session.query(BotInstance).filter(
            BotInstance.id == messenger.bot_instance_id,
        ).first()
        if not bot_instance:
            raise NotFound()
        return await Lark(bot_instance.app_id, bot_instance.app_secret).member_list(messenger.chat_id)

    async def get_seat_list_by_messenger_id(self, tenant_id, user_id, messenger_id, page, size):
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        query = self.session.query(MessengerSeat).filter(
            MessengerSeat.messenger_id == messenger_id,
            MessengerSeat.status.in_([0, 1]),
        ).order_by(
            MessengerSeat.created.desc(),
        )
        total = await self.query_total(query)
        if total == 0:
            return [], 0
        return [row2dict(i) for i in await self.query_one_page(query, page, size)], total

    async def add_seat_by_messenger_id(
        self, tenant_id, user_id, messenger_id,
        open_id, name, start_time, end_time
    ):
        # 使用这个检查一下，是不是自己的
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        self.session.begin_nested()
        self.session.add(MessengerSeat(
            id=ObjID.new_id(),
            messenger_id=messenger_id,
            open_id=open_id,
            name=name,
            start_time=start_time,
            end_time=end_time,
            status=1,  # 默认启用
        ))
        self.session.commit()

    async def seat_action_by_messenger_id(self, tenant_id, user_id, messenger_id, seat_id, action):
        status = {'enable': 1, 'disable': 0, 'delete': -1}
        if action not in status:
            raise PermissionDenied()
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        self.session.begin_nested()
        self.session.query(MessengerSeat).filter(
            MessengerSeat.id == seat_id,
            MessengerSeat.messenger_id == messenger_id,
        ).update(dict(
            status=status[action],
        ), synchronize_session=False)
        self.session.commit()

    async def update_seat_by_messenger_id(
        self, tenant_id, user_id, messenger_id, seat_id,
        open_id, name, start_time, end_time
    ):
        # 使用这个检查一下，是不是自己的
        messenger = await self.get_messenger(tenant_id, user_id, messenger_id)
        self.session.begin_nested()
        self.session.query(MessengerSeat).filter(
            MessengerSeat.id == seat_id,
            MessengerSeat.messenger_id == messenger_id,
        ).update(dict(
            open_id=open_id,
            name=name,
            start_time=start_time,
            end_time=end_time,
        ), synchronize_session=False)
        self.session.commit()

    async def choice_seat(self, messenger_id):
        now = datetime.now()
        t = now.time()
        def valid(m):
            # 这里判断的时候，如果start_time==end_time说明一整天都可以
            if m.start_time == m.end_time:
                return True
            # 这里包含两种情况：start_time > end_time和start_time < end_time
            # 但是不管是哪一种t总是大于start_time或者t总是小于end_time
            if m.start_time < t < m.end_time:
                return True
            elif m.start_time > m.end_time:
                if m.start_time < t or t < m.end_time:
                    return True
            return False

        seats = [m for m in self.session.query(MessengerSeat).filter(
            MessengerSeat.messenger_id == messenger_id,
            MessengerSeat.status == 1,
        ).order_by(
            MessengerSeat.created.desc(),
        ) if valid(m)]
        if len(seats) == 0:
            raise NotFound('当前时间没有座席')
        m = random.choice(seats)
        return m.open_id, m.name

    # 以下部分是从callback.model移过来的
    def init_by_bot_id(self, bot_id):
        # TODO 检查bot_id
        self.bot_id = bot_id
        self.bot = self.session.query(BotInstance).filter(
            BotInstance.id == bot_id,
            BotInstance.status == 0,
        ).first()
        if not self.bot:
            raise NotFound()
            logging.error('找不到飞书机器人{}'.format(bot_id))

        self.messenger = self.session.query(MessengerWithConfig).filter(
            Messenger.bot_instance_id == bot_id,
            Messenger.status == 0,
        ).order_by(Messenger.modified.desc()).first()
        if not self.chat_id:
            raise NotFound()
            logging.error('找不到飞书机器人{}对应的话题群ID'.format(bot_id))
        # 检查messenger.platform
        if self.platform not in ['官网']:
            raise PermissionDenied()
            logging.error('渠道不对应')
        self.client = Web(bot_id, self.access_token)

    @property
    def chat_id(self):
        return self.messenger.chat_id

    @property
    def platform(self):
        return self.messenger.platform

    @property
    def form(self):
        return self.messenger.config.get('form') == True

    @property
    def access_token(self):
        return self.messenger.config.get('access_token', '') or ''

    @cached_property
    def app_instance(self):
        return self.session.query(AppInstance).filter(
            AppInstance.id == self.messenger.instance_id,
            # AppInstance.status == 0,
        ).first() if self.messenger.instance_id else None

    @cached_property
    def lang(self):
        return self.messenger.config.get('lang', options.DEFAULT_LOCALE) or options.DEFAULT_LOCALE

    @cached_property
    def app_bot(self):
        return self.session.query(BotInstance).join(
            AppInstanceBotInstance,
            AppInstanceBotInstance.bot_instance_id == BotInstance.id,
        ).filter(
            AppInstanceBotInstance.instance_id == self.app_instance.id,
            AppInstanceBotInstance.status == 0,
        ).first()

    async def send_message_to_message_queue(self, message, bot_id, visitor_id):
        try:
            from modules.callback.model import UserAccessModel, MqSession
            if self.messenger.ai != 1:
                raise NotFound('AppInstance off')
            if not self.app_instance:
                raise NotFound('AppInstance')
            if not self.app_bot:
                raise NotFound('app bot')
            # 这里客服机器人是不配置访问控制数据的，所以应该总是能拿到数据的
            model_id, models = await UserAccessModel(self.app_bot, self.app_instance).check_policy('p2p', 'messenger', 'messenger')
            if not model_id:
                raise NotFound('model_id')
            input = message.get('payload', {}).get('content', {}).get('text', '')
            message['payload'].update(dict(
                # 这里应该使用哪一个作为sender_id？现在的sender_id实际上是转发客服消息机器人的ID
                # 使用这个ID，那么切换模型等信息的时候，对于AI能力来说就是全局的
                # 也可以考虑使用root_message_id或者visitor_id？使用不同id的时候，表现不一样
                sender_id=message.get('payload', {}).get('sender', {}).get('id'),
                chat_id=message.get('payload', {}).get('chat_id', ''),
                instance_id=self.app_instance.id,
                bot_instance_id=self.app_bot.id,
            ))
            mq_message = {
                # 'log_id': log.id,  # 客服消息不记录chatlog
                'platform': 'messenger',  # 客服消息对应的platform
                'extra': message['payload'],
                'input': input,
                # 优先使用配置的语言
                'lang': self.lang,
                'model_id': model_id,  # 这里需要查询出模型以及版本
                'models': models,
                'bot_id': bot_id,
                'visitor_id': visitor_id,
            }
            pikachu = MqSession()
            pikachu.put(options.QUEUE_APPLICATION, json.dumps(mq_message))
            pikachu.close()
        except NotFound as e:
            raise e
        except Exception as e:
            logging.exception(e)

    async def check_access_token(self, access_token):
        if self.messenger.config.get('api') == 1:
            if self.messenger.config.get('access_token', '') != access_token:
                raise PermissionDenied('PermissionDenied')


class MessengerSession(RedisModel):

    key_prefix = 'ws:session'

    def __init__(self, bot_id, visitor_id='', root_message_id='', **kwargs):
        super().__init__(**kwargs)
        self.bot_id = bot_id
        self.visitor_id = visitor_id
        if visitor_id:
            self.root_message_id = self.redis_client.get(f'{self.key_prefix}:{self.bot_id}:{self.visitor_id}') or ''
        elif root_message_id:
            self.root_message_id = root_message_id

        self.load_data()
        # 先load 缓存中的数据，如果kwargs传了新的值，再更新
        self.data.update(kwargs)
        if not self.visitor_id and 'visitor_id' in self.data:
            self.visitor_id = self.data['visitor_id']
        self.chat_history = self.data.get('chat_history', [])

    def load_data(self):
        self.data = {}
        res = self.redis_client.get(f'{self.key_prefix}:{self.root_message_id}')
        if res:
            data = json.loads(res)
            # 如果在消费者里面，可能没有bot_id，只使用root_message_id初始化
            if not self.bot_id:
                self.bot_id = data['bot_id']
            if self.bot_id == data['bot_id']:
                self.data.update(data)
            else:
                self.root_message_id = ''
                logging.error("invalid bot_id %r %r", self.bot_id, data['bot_id'])
        else:
            logging.error("can not get session by root_message_id")

    def add_message(self, message):
        self.chat_history.append(message)

    def switch_to_manual(self):
        # TODO 这里应该是给一个开关，如果触发了用户的什么关键词之类的，就转换人工
        # 或者，机器人判断是否需要转换人工服务
        self.data['manual'] = True

    def is_manual(self):
        # 返回是否是人工处理
        return self.data.get('manual', False) == True

    def clear(self):
        # 每次clear的时候，保存一下数据
        if not self.root_message_id:
            return
        self.data.update(dict(
            bot_id=self.bot_id,
            visitor_id=self.visitor_id,
            root_message_id=self.root_message_id,
            chat_history=self.chat_history,
        ))
        key = f'{self.key_prefix}:{self.root_message_id}'
        client_key = f'{self.key_prefix}:{self.bot_id}:{self.visitor_id}'
        self.redis_client.pipeline().set(
            client_key, self.root_message_id
        ).expire(
            client_key, WEBSOCKET_SESSION_CACHE_EXPIRE
        ).set(
            key, json.dumps(self.data)
        ).expire(
            key, WEBSOCKET_SESSION_CACHE_EXPIRE
        ).execute()



