import { Tab<PERSON><PERSON>, Tabs, Typography } from "@douyinfe/semi-ui";
import type { FC } from "react";

import type { AppCardProps } from "~contents/mainContent/appCard";
import { AppCard } from "~contents/mainContent/appCard";
import { CodeEditor } from "~components/configEditor/editor";

const demoData: AppCardProps[] = [
  {
    id: "1",
    title: "Chat OpenAI",
    description: "享受飞一般的工作体验",
    avatar:
      "https://s1-imfile.feishucdn.com/static-resource/v1/v2_2514eb9a-de2f-41cd-89b4-274940456f3g"
  },
  {
    id: "2",
    title: "Chat MidJour<PERSON>",
    description: "一键创意生图",
    avatar:
      "https://s3-imfile.feishucdn.com/static-resource/v1/v2_75f18b2c-677b-48b7-be44-33e4d1f97bcg"
  },
  {
    id: "3",
    title: "Chat 文心一言",
    description: "最懂中文的AI专家",
    avatar:
      "https://s1-imfile.feishucdn.com/static-resource/v1/v2_7bd484e6-f065-4576-863e-f3d26d3ac9dg"
  },
  {
    id: "4",
    title: "Chat Claude",
    description: "100k上下文玩家",
    avatar:
      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTFAEW03ObhlJmQKg4VNUEE5dFEEnjgycGkpSR9z72To6kkx-V3JCIFzbH6ZupuNGact2Q&usqp=CAU"
  },
  {
    id: "5",
    title: "Chat StableDiffusion",
    description: "商业生图最佳大模型",
    avatar:
      "https://s3-imfile.feishucdn.com/static-resource/v1/v2_dd750344-5344-4e41-94a2-b0908ba4c75g"
  }
]
export const ContentPanel: FC = () => {
  const { Text } = Typography;

  return (
    <div>
      <Tabs type="button" className="py-4 px-2" size="small">
         <TabPane tab="一键部署" itemKey="2" className="py-2">
              <CodeEditor/>
        </TabPane>
        <TabPane tab="应用市场" itemKey="1" className="py-2">
          <div className="flex justify-center flex-col items-center gap-2">
            { demoData.map((item) => (
              <AppCard key={ item.id } { ...item } />
            )) }
          </div>
        </TabPane>
        <TabPane tab="配置解析" itemKey="3">
          帮助
        </TabPane>
      </Tabs>
    </div>
  );
}
