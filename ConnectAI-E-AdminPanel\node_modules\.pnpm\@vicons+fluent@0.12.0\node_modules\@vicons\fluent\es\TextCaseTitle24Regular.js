import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M15.75 3a.75.75 0 0 0-.75.75v15.5a.75.75 0 0 0 1.5 0v-.237c.685.618 1.554.987 2.5.987c2.21 0 4-2.015 4-4.5S21.21 11 19 11c-.946 0-1.815.37-2.5.987V3.75a.75.75 0 0 0-.75-.75zm.75 12.5c0-1.828 1.28-3 2.5-3s2.5 1.172 2.5 3s-1.28 3-2.5 3s-2.5-1.172-2.5-3zM7.76 3a.75.75 0 0 1 .697.5l5.5 15.5a.75.75 0 1 1-1.414.5l-1.42-4H4.007L2.45 19.52a.75.75 0 1 1-1.4-.54l6-15.5A.75.75 0 0 1 7.76 3zm-.04 2.907L4.587 14h6.005L7.72 5.907z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseTitle24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
