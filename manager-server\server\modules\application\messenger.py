class ChatTool(CTool):
    name: str = 'chat'
    description: str = 'messenger chat tool'

    def _run(self, *args, run_manager=None, input='', question='', context='', **kwargs):
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class KnowledgeCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在思考，请稍等...')))
                        )
                    )
                elif platform == 'messenger':
                    send_message(AppResult.MessengerSystem, _("输入中..."))
                else:
                    pass
                    # send_message(AppResult.ReplyText, _('正在思考，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                self.result += token
                if model.streaming and platform == 'feishu':
                    if len(self.result) - self.send_length < 25:
                        logging.debug("skip send new token %r %r", len(self.result), self.send_length)
                        return
                    # 至少有新的25个字符开始发送，发送之后，重新赋值
                    self.send_length = len(self.result)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(self.result, tag='lark_md'),
                            FeishuMessageNote(FeishuMessagePlainText(_('正在生成，请稍等...')))
                        )
                    )

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # 同时添加当前用户输入的问题，以及ai回复问题
                # 客服消息实际上放到MessengerSession里面了，不需要放这个ChatSession里面
                content = response.generations[0][0].text
                # session.add_message({'role': 'human', 'content': input.strip()})
                # session.add_message({'role': 'ai', 'content': content})

                if platform == 'feishu':
                    time.sleep(1)
                    # 延时1s，防止触发飞书更新卡片5qps导致不能更新最终结果的问题
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(content, tag='lark_md'),
                            FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                        ),
                    )
                else:
                    # 尝试检查结果，转人工客服（暂时移除这个逻辑）
                    """I'm sorry  +  assist you/很抱歉，我无法回答"""
                    if False and ("I'm sorry" in content and 'assist you' in content) or '很抱歉，我无法回答' in content:
                        send_message(
                            AppResult.ForwardMessengerSeat,
                            True,
                        )
                    else:
                        contents = content.split('\n\n')
                        endline = contents.pop()
                        if len(contents) > 0 and '人工客服' in endline:
                            # 这种情况是找到了答案，同时还在建议走人工客服，将最后的建议移除
                            send_message(AppResult.ReplyText, '\n\n'.join(contents))
                        else:
                            send_message(AppResult.ReplyText, content)

            def on_llm_error(
                self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any
            ) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv(str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, str(error))

        model.callbacks = [KnowledgeCallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = 0.01  # 这里需要严格一些
        # AI应用配置尝试直接在后台选择了使用的模型是哪一个
        model_name = m.get(session.model_id or instance.extra.get('model_id', ''), ai_model[0][1])

        # 支持文心一言+openai+azure
        if model.openai_api_type == '文心一言':
            chat = WenXinChat(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        elif model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                streaming=model.streaming,
                openai_api_key=model.openai_api_key,
                openai_api_base=model.openai_api_base,
                callbacks=model.callbacks,
            )

        # 这里需要从后台配置的system_role获取。不是session.system_role
        # 另外，这里需要通过API查询知识库中内容，组合之后再进行查询，这里参考know-server中的chat逻辑
        """
        1. 如果配置了prompt，将这一条写到system_message
        2. 这部分是chat_history
        3. 使用模板将context以及question组合成最后一条message
        """

        system_role = instance.extra.get('prompt', KNOWLEDGE_DEFAULT_PROMPT) or KNOWLEDGE_DEFAULT_PROMPT
        if system_role:
            if 'openai_api_type' in model and model.openai_api_type == '文心一言':
                # 文心一言必须奇数条消息，不支持SystemMessage，消息不能为空
                system_message = [HumanMessage(content=system_role), AIMessage(content='好的')]
            else:
                system_message = [SystemMessage(content=system_role)]
        else:
            system_message = []
        human_message = HumanMessage(
            content=textwrap.dedent(
                """
                Important Notice:
                You are a good concise summary assistant who can know the language to reply to users based on their question or context. If the user only inputs numbers or you do not know the language, then using default language '{data.lang}'.
                You are a rigorous customer service and do not use content outside of context to answer user.
                You are a smart customer service representative who can determine whether your answer is what the user wants. If not, sugesst user enter '人工客服' or 'CS' for further assistance. In other cases, do not sugesst user enter anything.
                Use the following context to answer the user's question.

                ```
                {context}
                ```

                Question: {question}

                Helpful Answer:
                """
            ).strip().format(context=context or _("内容为空，请直接告诉用户你不知道答案"), question=question, data=data)
        )
        logging.debug('chat messages %r %r', question, kwargs)
        # 这里的chat_history应该使用MessengerSession里面的
        messenger_chat_history = []
        root_id = data.extra.extra.root_id if 'extra' in data.extra else data.extra.root_id
        with MessengerSession('', root_message_id=root_id) as msession:
            logging.info("chat_history %r", msession.chat_history)
            for m in msession.chat_history[-6:]:
                if 'text' in m['payload']['content']:
                    text = m['payload']['content']['text']
                    # 这里需要反着来，app转发的消息实际上是用户输入的
                    if m['payload']['sender']['id_type'] == 'user':
                        messenger_chat_history.append(AIMessage(content=text))
                    else:
                        messenger_chat_history.append(HumanMessage(content=text))

        messages = system_message + messenger_chat_history + [human_message]
        logging.debug('chat messages %r', messages)
        return chat.invoke(messages)


class KnowledgeSummaryTool(CTool):
    name: str = 'knowledge_summary'
    description: str = 'knowledge summary tool'

    def _run(self, *args, run_manager=None, **kwargs):
        send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv('', tag='lark_md'),
                FeishuMessageNote(FeishuMessagePlainText(_('查询知识库信息...')))
            )
        )
        try:
            # 现在存的是数组，但是也只能支持单个知识库查询
            collection_id = instance.extra.get('collection_id', [])
            # 兼容现在前端配置单个知识库
            if isinstance(collection_id, str):
                collection_id = [collection_id]
            name = ''
            description = ''
            if len(collection_id) > 0:
                know_client = KnowClient(instance.tenant_id)
                # 查询知识库
                result = know_client.get_collection(collection_id[0])
                if result['data'].get('name'):
                    name = result['data'].get('name')
                    # 尝试从summary以及description获取字段
                    description = result['data'].get('summary', result['data'].get('description', ''))
        except Exception as e:
            logging.error(e)
        return dict(next_tool_name='knowledge_analysis', name=name, description=description)


class KnowledgeAnalysisTool(CTool):
    name: str = 'knowledge_analysis'
    description: str = 'knowledge analysis tool'

    prompt = """This is a knowledge base.
The name of the knowledge base is:
{name}


The description of the knowledge base is:
{description}


I would like to know the answer for this question:
{input}


Please tell me what keywords are most suitable for use?
And help me optimize the question!

Please use the JSON format to return the results according to the following example
{{
  "keywords": ["keyword1", "keyword2"],
  "question": "real question"
}}"""

    def _run(self, *args, run_manager=None, input='', name='', description='', **kwargs):
        # 暂时屏蔽优化关键词逻辑
        return dict(next_tool_name='knowledge_query', keywords=[input], question=input)
        send_message(
            AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv('', tag='lark_md'),
                FeishuMessageNote(FeishuMessagePlainText(_('优化关键词...')))
            )
        )
        m = {value: content for value, content in ai_model}
        # temperature = session.temperature
        # 这里需要输出json格式的结果，所以尽量不使用发散的模式
        temperature = 0.1
        model_name = m.get(session.model_id, ai_model[0][1])

        # 支持文心一言+openai+azure
        if model.openai_api_type == '文心一言':
            chat = WenXinChat(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        elif model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                streaming=model.streaming,
                openai_api_key=model.openai_api_key,
                openai_api_base=model.openai_api_base,
            )

        human_message = HumanMessage(content=self.prompt.format(
            name=name, description=description, input=input
        ))
        m = chat.invoke([human_message])
        logging.debug('chat messages %r', m)
        # TODO mock
        keywords = [input]
        question = input
        try:
            # 尝试从消息提取字段
            result = json.loads(re.findall(r'(\{.*\})', m.content.replace('\n', '')).pop())
            keywords = result.get('keywords', [input])
            question = result.get('question', input)
        except Exception as e:
            logging.error(e)
        return dict(next_tool_name='knowledge_query', keywords=keywords, question=question)


class KnowledgeQueryTool(CTool):
    name: str = 'knowledge_query'
    description: str = 'knowledge query tool'

    def _run(self, *args, run_manager=None, input='', keywords=[], question='', **kwargs):
        send_message(
            AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv('', tag='lark_md'),
                FeishuMessageNote(FeishuMessagePlainText(_('查询相关文档信息...')))
            )
        )
        try:
            # 现在存的是数组，但是也只能支持单个知识库查询
            collection_id = instance.extra.get('collection_id', [])
            # 兼容现在前端配置单个知识库
            if isinstance(collection_id, str):
                collection_id = [collection_id]
            context = ''
            if len(collection_id) > 0:
                know_client = KnowClient(instance.tenant_id)
                # 1. 查询知识库，支持多数据库
                # 2. 如果传了question就使用question，否则直接使用input
                keyword = ' '.join(keywords) if len(keywords) > 1 else question
                result = know_client.query(collection_id, keyword or input)
                if len(result['data']) > 0:
                    context = '\n\n'.join(['Context:\n{}'.format(document['document']) for document in result['data']])
        except Exception as e:
            logging.error(e)
        return dict(next_tool_name='chat', context=context, question=question or input)
        # 暂时屏蔽
        if not context and platform == 'messenger':
            return send_message(
                AppResult.ForwardMessengerSeat,
                True,
            )
        else:
            return dict(next_tool_name='chat', context=context, question=question)


class FeishuCommand(CommandTool):

    next_tool_name: str = 'knowledge_query'
    name: str = 'messenger_feishu_command'
    description: str = 'messenger feishu command'
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<发散模式> 或 /ai_mode 即可选择发散模式',
        '输入<清除> 或 /clear 即可清除上下文'
    ]

    @property
    def ai_clear_btn(self):
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')
            )
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def ai_mode_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in ai_mode],
            placeholder=_('选择模式'),
            initial_option=str(float(session.temperature)),
            value={'command': 'ai_mode'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改发散模式吗？'),
                text=_('选择内置模式，可以让AI更好的理解您的需求。'),
            )
        )

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('**我是知识库小助手**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🤖 **发散模式选择**\n文本回复 *发散模式* 或 */ai_mode*'),
                    tag='lark_md',
                    extra=self.ai_mode_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:8] == '/ai_mode' or input[:4] == '发散模式':
            return 'ai_mode',
        elif not input and action:
            if action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', True
            elif action['tag'] == 'select_static':
                return action["value"]["command"], action['option']
        elif not input:
            return 'help',
        return None,

    def on_clear(self, flag=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'))
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        # session['chat_history'] = []  # 清除历史
        '''
        root_id = data.extra.extra.root_id if 'extra' in data.extra else data.extra.root_id
        with MessengerSession('', root_message_id=root_id) as msession:
            msession.chat_history = []
        '''
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            )
        )

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('已选择模型：**%(model)s**', model=m.get(model_name, model_name)), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_ai_mode(self, mode_name=None):
        if not mode_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_mode_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('提醒：选择内置模式，让AI更好的理解您的需求。'))
                    ),
                    header=FeishuMessageCardHeader(_('🤖 发散模式选择'), template='blue'),
                )
            )
        '''已选择发散模式:平衡'''
        m = {str(value): content for value, content in ai_mode}
        session['temperature'] = float(mode_name)
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已选择发散模式：**%(mode)s**', mode=m.get(mode_name, mode_name)), tag="lark_md"),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖 机器人提醒'), template='blue'),
            )
        )

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本内容！')
        return send_note(text, title)


class KnowledgeAgent(CAgent):

    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'Messenger'
        title: str = '客服应用'
        title_en: str = 'Messenger'
        description: str = '📚 支持将各种文档内容投喂给大模型，让 AI 回答更贴近业务，可广泛用于客服问答等场景'
        description_en: str = '📚 Support feeding various document contents to the large model, enabling AI to provide answers that are more closely related to the business. This can be widely used in customer service Q&A and other scenarios.'
        problem: str = '如何拥有公司专属的知识库机器人？'
        problem_en: str = 'How to have a company-specific knowledge base robot?'
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/docx/FG5OdXEEroHZuGxddp9cY56pnJe'
        manual_en: str = 'https://q5o2cctqdb7.sg.larksuite.com/docx/OGOmdmvWzoCq53xt3Kcll0Qggvb'
        icon: str = 'https://pic1.forkway.cn/cdn/202308202004050.png?imageMogr2/thumbnail/720x'
        logo: str = 'https://pic1.forkway.cn/cdn/202308202004050.png?imageMogr2/thumbnail/720x'
        sorted: int = 2
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['OpenAI', 'Azure', '文心一言']
        )]
        support_bots: List[str] = ['feishu']
        feedback_url: str = 'https://connect-ai.feishu.cn/share/base/form/shrcnukcwA0Mmoqt6hzZlkTCsmc'
        deploy: str = 'https://connect-ai.feishu.cn/wiki/IKBgwoxjVief1akl32ZcvTW8nQg'
        show: int = 0

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result:
                # 以链式传递到下一个Tool
                if isinstance(last_result, str) and last_result in self.allowed_tools:
                    return AgentAction(tool=last_result, tool_input=kwargs, log="")
                elif isinstance(last_result, dict):
                    # 如果是AIMessage
                    # 并且从additional_kwargs拿到next_tool_name，就继续到下一个Tool
                    next_tool_name = last_result.get('next_tool_name')
                    if next_tool_name:
                        return AgentAction(tool=next_tool_name, tool_input={**kwargs, **last_result}, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='messenger_feishu_command', tool_input=kwargs, log="")
        elif platform == 'messenger':
            return AgentAction(tool='messenger_feishu_command', tool_input=kwargs, log="")
        return AgentAction(tool='chat', tool_input=kwargs, log="")


