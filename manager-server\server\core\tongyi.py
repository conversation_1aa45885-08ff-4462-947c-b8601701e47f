from http import HTTPStatus
import json
import httpx
import logging
import sys
import warnings
from typing import (
    Any,
    Dict,
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)

from pydantic import Field, root_validator
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)
from tornado.options import options
from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage
from langchain.schema import AIMessage, SystemMessage, HumanMessage


logger = logging.getLogger(__name__)


class TongYiClient(object):

    def build_query(
        self,
        api_base='https://dashscope.aliyuncs.com',  # api基础地址
        api_key='',
        stream=False,
        **kwargs
    ):
        headers = {
            'Authorization': 'Bearer {}'.format(api_key),
            'api-key': api_key,
            'Content-Type': 'application/json',
            'X-DashScope-SSE': 'enable' if stream else 'disable'
        }
        if 'https://qwen' != api_base[:12]:
            headers['api-base'] = api_base
            headers['api-key'] = api_key
            from core.api_base import NewApiBase
            api_base = NewApiBase('通义千问').url
        body = json.dumps(kwargs)
        return '{}/api/v1/services/aigc/text-generation/generation'.format(api_base), body, headers

    def stream(self, url, data, headers=dict(), timeout=120):
        with httpx.stream("POST", url, data=data, headers=headers, timeout=120) as r:
            for line in r.iter_lines():
                # 去掉头 'data:' 提取后面需要的信息
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    def create(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        if stream:
            return self.stream(url, data, headers=headers, timeout=timeout)
        else:
            response = httpx.post(url, data=data, headers=headers, timeout=timeout)
            # print('response', response, url, data, response.text)
            return response.json()

    async def astream(self, url, data, headers=dict(), timeout=120):
        async with httpx.AsyncClient().stream("POST", url, data=data, headers=headers, timeout=timeout) as r:
            async for line in r.aiter_lines():
                if 'data:' == line[:5]:
                    yield json.loads(line[5:])

    async def acreate(self, stream=False, timeout=120, **kwargs):
        url, data, headers = self.build_query(stream=stream, **kwargs)
        # 使用异步客户端
        if stream:
            return self.astream(url, data, headers=headers, timeout=timeout)
        else:
            response = await httpx.AsyncClient().post(url, data=data, headers=headers, timeout=timeout)
            return response.json()


def _create_retry_decorator(llm: BaseChatModel) -> Callable[[Any], Any]:
    min_seconds = 1
    max_seconds = 60
    # Wait 2^x * 1 second between each retry starting with
    # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
    return retry(
        reraise=True,
        stop=stop_after_attempt(llm.max_retries),
        wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
        retry=retry_if_exception_type(httpx.HTTPError),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


def completion_with_retry(llm: BaseChatModel, **kwargs: Any) -> Any:
    """Use tenacity to retry the completion call."""
    retry_decorator = _create_retry_decorator(llm)

    @retry_decorator
    def _completion_with_retry(**kwargs: Any) -> Any:
        return llm.client.create(**kwargs)

    return _completion_with_retry(**kwargs)


class TongYiChat(SimpleChatModel):

    client: Any  #: :meta private:
    model_name: str = "qwen-turbo"
    openai_api_type: Optional[str] = None  # 这个字段是为了好进行判断，其实在ChatModel内部不使用
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 3
    prefix_messages: List = Field(default_factory=list)

    streaming: bool = False

    result_format: str = "text"
    top_p: float = 0.8             # 影响输出文本的多样性，取值越大，生成文本的多样性越强 默认0.8，取值范围 [0, 1.0]
    top_k: int = 50                # 采样候选集的大小，取值越大，生成的随机性越高，取值越小，生成的确定性越高
    seed: int = 1234
    max_tokens: int = 1500
    repetition_penalty: float = 1.1
    temperature: float = 1.0
    # stop:
    enable_search: bool = False
    incremental_output: bool = True

    def _llm_type(self) -> str:
        return "tongyi_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        params = {
            'api_key': self.api_key,
            'api_base': self.api_base,
            'model': self.model_name,
            'stream': self.streaming
        }
        parameters = {
            'result_format': self.result_format,
            'top_p': self.top_p,
            'top_k': self.top_k,
            'seed': self.seed,
            'max_tokens': self.max_tokens,
            'repetition_penalty': self.repetition_penalty,
            'temperature': self.temperature,
            # 'stop': self.stop,
            'enable_search': self.enable_search,
            'incremental_output': self.incremental_output
        }

        input = {'messages': []}
        for message in messages:
            if isinstance(message, AIMessage):
                input['messages'].append({'role': 'assistant', 'content': message.content})
            elif isinstance(message, HumanMessage):
                input['messages'].append({'role': 'user', 'content': message.content})
            else:
                input['messages'].append({'role': message.type, 'content': message.content})


        self.client = TongYiClient()

        if self.streaming:  # 是否流式传输
            response = ""
            for stream_resp in completion_with_retry(self, input=input, parameters=parameters, **params):
                # 获取流式响应中的关键信息
                print(stream_resp)
                finish_reason = stream_resp.get('finish_reason')
                output = stream_resp.get('output', {})
                token = output.get('text', '')
                response += token
                if finish_reason == 'stop':  # 根据finish_reason进行相应处理，例如判断是否为停止或长度结束
                    print(f"Finish Reason: {finish_reason}")
                if run_manager:
                    run_manager.on_llm_new_token(token)
                # print(response)
            return response
        else:
            full_response = completion_with_retry(self, input=input, parameters=parameters, **params)
            print(full_response)
            return full_response['output']['text']

if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    async def main():
        from langchain.schema import HumanMessage
        from core.api_base import NewApiBase

        api_key = ''
        api_base = 'https://dashscope.aliyuncs.com'

        chat = TongYiChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )

        # messages = [HumanMessage(content='你是谁')]
        messages = [HumanMessage(content='推荐成都旅游线路')]

        result = chat(messages)
        print(result)

    asyncio.run(main())
