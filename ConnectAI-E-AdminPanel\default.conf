server {
    listen 80;
    server_name _;

    root /app;

    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control 'no-store';
    }

    location /api/ {
        proxy_set_header Host $http_host;
        proxy_set_header  X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://connectai-manager-server:8080;
    }
}