<template>
  <div class="mb-6px px-4px cursor-pointer relative" @mouseenter="setTrue" @mouseleave="setFalse">
    <div
      class="flex-center flex-col py-12px rounded-2px bg-transparent transition-colors duration-300 ease-in-out"
      :class="{ 'text-primary !bg-primary_active': isActive, 'text-primary': isHover }"
    >
      <component :is="icon" :class="[isMini ? 'text-16px' : 'text-20px']" />
      <p
        class="text-12px overflow-hidden transition-height duration-300 ease-in-out"
        :class="[isMini ? 'h-0 pt-0 w-0' : 'h-24px pt-4px']"
      >
        {{ label }}
      </p>
    </div>
    <span
      v-if="tag"
      class="bg-yellow-100 text-yellow-800 text-xs font-medium px-1 py-0.5 rounded-full dark:bg-yellow-900 dark:text-yellow-300 absolute top-0 right-0"
      >{{ tag }}</span
    >
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { VNodeChild } from 'vue';
import { useBoolean } from '@/hooks';

defineOptions({ name: 'MixMenuDetail' });

interface Props {
  /** 路由名称 */
  routeName: string;
  /** 路由名称文本 */
  label: string;
  /** 当前激活状态的理由名称 */
  activeRouteName: string;
  /** 路由图标 */
  icon?: () => VNodeChild;
  /** mini尺寸的路由 */
  isMini?: boolean;
  /** 图标右上角的角标 */
  tag?: string;
}

const props = withDefaults(defineProps<Props>(), {
  icon: undefined,
  isMini: false,
  tag: ''
});

const { bool: isHover, setTrue, setFalse } = useBoolean();

const isActive = computed(() => props.routeName === props.activeRouteName);
</script>

<style scoped></style>
