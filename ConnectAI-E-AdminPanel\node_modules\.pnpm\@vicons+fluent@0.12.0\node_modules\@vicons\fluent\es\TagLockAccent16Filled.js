import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M6.648 1.954l-4.76 4.728a1.49 1.49 0 0 0 0 2.12l3.31 3.287c.488.485 1.226.57 1.802.255V10a2 2 0 0 1 1.5-1.937V8a3 3 0 0 1 3.98-2.836l.016-2.153a1.507 1.507 0 0 0-1.52-1.511l-3.263.014c-.4.001-.783.16-1.065.44z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagLockAccent16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
