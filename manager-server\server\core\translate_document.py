import logging
import time
from typing import Any, List, Optional
import os
import sys
from tornado.options import options
from typing import List
import urllib.request
from alibabacloud_alimt20181012.client import Client as alimt20181012Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_alimt20181012 import models as alimt_20181012_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import AIMessage, BaseMessage, ChatGeneration, ChatResult
import httpx

logger = logging.getLogger(__name__)


class ALYMTClient(object):
    def __init__(
        self, api_key='',
        api_base='',
        region_id='',
        access_key_secret="random",
        access_key_id="",
        client_class=alimt20181012Client,
    ):
        self.api_key = api_key
        self.api_base = api_base
        self.region_id = region_id
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.client_class = client_class

    def create_client(self):
        using_proxy = self.region_id == 'connectai'
        config = open_api_models.Config(
            access_key_id=self.api_key if using_proxy else self.access_key_id,
            access_key_secret=self.access_key_secret,
            region_id=self.region_id if not using_proxy else None,
        )
        # Endpoint 请参考 https://api.aliyun.com/product/alimt
        # 只有走代理的时候才需要换成api_base，默认情况，前面传了region_id就不用设置endpoint
        if using_proxy:
            config.endpoint = self.api_base.replace('https://', '')
        return self.client_class(config)

    def create_translate_task(self, timeout=600, **kwargs):
        client = self.create_client()
        create_doc_translate_task_request = alimt_20181012_models.CreateDocTranslateTaskRequest(**kwargs)
        res = client.create_doc_translate_task(create_doc_translate_task_request)
        logging.info("result %r", res.to_map())
        #taskId = res.body.task_id
        get_doc_translate_task_request = alimt_20181012_models.GetDocTranslateTaskRequest(
            task_id=res.body.task_id,
        )
        time.sleep(5)  # 第一次的时候，先等待5s
        while timeout > 0:
            try:
                # 注意大文档的首次查询响应较慢，且可能失败，失败后先等待再重试
                try:
                    res = client.get_doc_translate_task(get_doc_translate_task_request)
                except Exception as e:
                    logging.error(e)
                    time.sleep(10)
                    res = client.get_doc_translate_task(get_doc_translate_task_request)
                logging.debug("result %r %r", res.to_map(), timeout)
                if res.body.status == "translated":
                    return res.body.to_map()
                elif res.body.status == "error":
                    logging.error("result %r", res.to_map())
                    raise Exception(res.body.translate_error_message)
            except Exception as error:
                # 如有需要，请打印 error
                # UtilClient.assert_as_string(error.message)
                logger.error("Error: {}".format(error))
                raise error
            finally:
                time.sleep(5)
                timeout -= 5
        raise Exception('timeout')


class AliyunChat(SimpleChatModel):

    client: Any
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    region_id: Optional[str] = 'connectai'
    access_key_id: Optional[str] = None
    access_key_secret: Optional[str] = None


    def _llm_type(self) -> str:
        return "translate_document_chat"

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> ChatResult:
        # 只获取一条 message
        message = messages.pop()
        # 将其他参数通过 additional_kwargs 传入
        self.client = ALYMTClient(
            api_key=self.api_key,
            api_base=self.api_base,
            region_id=self.region_id,
            access_key_id=self.access_key_id,
            access_key_secret=self.access_key_secret,
        )
        # 在这一步调用了 create 方法
        response = self.client.create_translate_task(**message.additional_kwargs)

        if response["TranslateFileUrl"] is None :
            raise Exception("Error: url is None")

        # log 的格式是什么？
        logger.info(f"chat res: succeeded reciving from translate_docunment")
        message = AIMessage(content=response["TranslateFileUrl"], additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])

def message_test():
    import asyncio
    import os

    async def main():
        pwd = os.getcwd()

        doc_path = os.path.join(
            pwd,
            "result.docx",
        )

        from langchain.schema import HumanMessage

        params = {
            "file_url":"http://workbench-file-transfer.oss-cn-shanghai.aliyuncs.com/user-files/d108b04c-1273-454a-8879-11fedff46aeb-om_0bf92835924619e9556b82d309edd111.pdf?OSSAccessKeyId=LTAI5tRvL6vYdjKSfTFZ156m&Expires=1700107004&Signature=nrRc9hRGvxVGVVGAgewdVkJ9Jg4%3D&response-content-disposition=attachment",
            "source_language":"auto",
            "target_language":"zh"
        }

        chat = AliyunChat(
            api_base="https://aliyun.forkway.cn",
            region_id="connectai",
            access_key_id="connectai-647f3ef47033660001217897",
            access_key_secret="test",
        )

        messages = [HumanMessage(content="", additional_kwargs=params)]
        response = chat(messages=messages)

        # 注意这里调用结果的方式和外部调用不同
        # 外部调用会再包一层
        additional_kwargs = response.additional_kwargs

        data = additional_kwargs["TranslateFileUrl"]
        # 下载文件
        async with httpx.AsyncClient() as client:
            res = await client.get(data)
            with open(doc_path, "wb") as f:
                f.write(res.content)

        print("Test complete")

    asyncio.run(main())


if __name__ == "__main__":
    message_test()

