import type { PlasmoMessaging } from "@plasmohq/messaging"
import { DeployTool } from "~utils/open-feishu-api/deploy";
import { Configuration, FeishuLoginCookies } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";
import { defaultCode } from "~hooks/useDeploy";

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  const message = await querySomeApi()
  res.send({
    message:22
  })
}

export default handler


const querySomeApi = async () => {
  const testConfig  = {
    lark_oapi_csrf_token:'bjDCGfwT/oYUc5cjnnGmkBtkUFW0Y8XKixpk3ktqGIc=',
    session:'XN0YXJ0-97ek73e0-3db0-4761-9012-07de52d18634-WVuZA'
  }

  const configCookie = new Configuration(testConfig);
  await configCookie.processCsrfToken()
  const userInfo = await configCookie.getUserInfo();
  console.log(userInfo);
  // const app = new OpenApp(configCookie);
  // const deploy = new DeployTool(defaultCode as any);
  // await deploy.loadOpenApi(app)
  // await deploy.createAndDeploy()
}
