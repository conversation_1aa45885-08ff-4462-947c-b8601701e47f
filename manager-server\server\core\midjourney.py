import json
import httpx
import logging      
import sys
import warnings
from typing import (
    Any,
    Dict,   
    List,
    Mapping,
    Tuple,
    Optional,
    Callable,
)
from tornado.options import options
from time import time, sleep
from core.utils import translate_to, syncify

from langchain.callbacks.manager import (
    AsyncCallbackManagerForLLMRun,
    CallbackManagerForLLMRun,
)

from langchain.chat_models.base import BaseChatModel, SimpleChatModel
from langchain.schema import ChatGeneration, ChatResult, BaseMessage, HumanMessage, AIMessage


logger = logging.getLogger(__name__)


class MJClient(object):
    def __init__(self, api_base, api_key=''):
        self.api_base = api_base
        self.api_key = api_key

    @property
    def headers(self):
        return {
            'api-key': self.api_key,
            'X-API-KEY': self.api_key,
        }

    # https://docs.goapi.ai/docs/midjourney-api/midjourney-api-v2
    def build_query(
        self,
        # imagine|reroll|upscale|variation|inpaint|outpaint|pan|describe|blend|fetch
        action='imagine',
        **kwargs
    ):
        action = action.lower()
        url = '{}/mj/v2/{}'.format(self.api_base, action)
        if 'task_id' in kwargs and action != 'fetch':
            kwargs['origin_task_id'] = kwargs['task_id']
            del kwargs['task_id']
        params = kwargs
        if action not in ['imagine', 'describe', 'blend'] and 'process_mode' in kwargs:
            del params['process_mode']
        if action == 'imagine':
            if 'prompt' not in kwargs:
                return None
        elif action == 'reroll':
            if 'origin_task_id' not in kwargs:
                return None
        elif action == 'upscale':
            if 'origin_task_id' not in kwargs or 'index' not in kwargs:
                return None
        elif action == 'variation':
            if 'origin_task_id' not in kwargs or 'index' not in kwargs:
                return None
        elif action == 'inpaint':
            if 'origin_task_id' not in kwargs or 'mask' not in kwargs:
                return None
        elif action == 'outpaint':
            if 'origin_task_id' not in kwargs:
                return None
            if not '1' < kwargs.get('zoom_ratio', '0') <= '2':
                return None
        elif action == 'pan':
            if 'origin_task_id' not in kwargs:
                return None
            if kwargs.get('direction', '') not in ['up', 'down', 'left', 'right']:
                return None
        elif action == 'describe':
            if 'image_url' not in kwargs:
                return None
        elif action == 'blend':
            if 'image_urls' not in kwargs:
                return None
        elif action == 'fetch':
            if 'task_id' not in kwargs:
                return None
        else:
            return None
        return {
            'url': url,
            'headers': self.headers,
            'data': json.dumps(params),
        }

    def stream(self, action, timeout=600, **kwargs):
        sleep_interval = 5
        # 响应久
        if action in ['describe', 'blend']:
            timeout = 1000
            sleep_interval = 8
        started = time()
        ended = started + timeout
        error_result = {'status': 'ERROR', 'reason': ''}
        try:
            # 开启翻译
            if 'enable_translate' in kwargs:
                if kwargs['enable_translate'] and 'prompt' in kwargs:
                    try:
                        kwargs['prompt'] = syncify(translate_to)(kwargs['prompt'], self.api_key)
                        logging.info('translate prompt to en: %r', kwargs['prompt'])
                    except Exception as e:
                        logging.error('translate error: %r', e)
                del kwargs['enable_translate']
            query = self.build_query(action=action, **kwargs)
            if not query:
                logging.error("build query error: %r", {action: action, **kwargs})
                yield error_result
                return
            create_result = httpx.post(**query, timeout=60)
            #print(create_result.url, create_result.status_code, create_result.content.decode())
            if create_result.status_code != 200:
                logging.error('create error: %r %r %r', create_result.url, create_result.status_code, create_result.content.decode())
                try:
                    error_message = json.loads(create_result.content.decode()).get('message', '')
                    error_result['reason'] = error_message
                except Exception as e:
                    code_reasons = {401: 'unauthorized'}
                    error_result['reason'] = code_reasons.get(create_result.status_code, '')
                yield error_result
                return
            create_result = create_result.json()
            #print('1', create_result)
            if ('status' in create_result and create_result['status'] != 'success') or ('success' in create_result and not create_result['success']):
                error_result['reason'] = create_result.get('message', '')
                logging.error('create error: %r %r', create_result, error_result)
                yield error_result
                return
            if 'task_id' not in create_result:
                logging.error('create error: %r', create_result)
                yield error_result
                return
            task_id = create_result['task_id']
            yield create_result
            while time() < ended:
                query = self.build_query(action='fetch', task_id=task_id)
                result = httpx.post(**query, timeout=60).json()
                #print('4', result)
                if 'status' not in result or 'task_result' not in result:
                    logging.error('create task error: %r', result)
                    yield error_result
                    return
                status = result.get('status', '')
                if status == 'finished':
                    yield result
                    break
                elif status == 'failed':
                    logging.error('query task error: %r', result)
                    # 交给后面处理
                    yield result
                    break
                elif status in ['pending', 'processing', 'retry']:
                    # 正常状态
                    yield result
                    sleep(sleep_interval)
                else:
                    # 其他异常情况
                    logging.error('query task error: %r', result)
                    yield error_result
                if time() >= ended:
                    # yield create_result
                    logging.error('query task timeout: %r', create_result)
                    error_result['reason'] = 'timeout'
                    yield error_result
        except Exception as e:
            logging.error('mj error: %r', e)
            yield error_result

    def create(self, action="imagine", stream=False, timeout=600, **kwargs):
        result = self.stream(action=action, timeout=timeout, **kwargs)
        if stream:
            return result
        else:
            return list(result).pop()


class MJChat(SimpleChatModel):
    """
    midjourney
    """
    client: Any  #: :meta private:
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_retries: int = 6
    streaming: bool = False

    def _llm_type(self) -> str:
        return "midjourney_chat"

    def _call(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager: Optional[CallbackManagerForLLMRun] = None,
            **kwargs,
    ) -> str:
        # 虽然主逻辑在_generate里面，但是_call需要继承
        pass

    # 其他的继承_call，只需要返回一个字符串就好了
    # mj的需要直接继承_generate，返回一个AIMessage对象
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        # MOCK data
        # response = {'task_id': 'd0e7f76e-9ec3-405d-a81f-13f457fe0453', 'status': 'finished', 'process_time': 40.*********, 'retry_count': 0, 'meta': {'account_id': 'ca5ad48a-0461-4a9e-9698-a9244c0cb20e', 'task_type': 'imagine', 'origin_task_id': '', 'bot_hash': 'ea34e786c966588e90fa1452b4f6b7dbd29fc0dea4b10177bb62819ce8f06d35', 'created_at': **********, 'started_at': **********, 'ended_at': **********, 'process_mode': 'mixed', 'frozen_credit': 15, 'task_request': {'aspect_ratio': '', 'process_mode': '', 'prompt': 'a green color Rattan Dsw Chair with beech leg, chair with no stitches line, Front View picture, Long Shot, picture with morandi light grey color background', 'skip_prompt_check': False}, 'task_param': {'prompt': 'a green color Rattan Dsw Chair with beech leg, chair with no stitches line, Front View picture, Long Shot, picture with morandi light grey color background', 'index': '', 'zoom_ratio': '2', 'aspect_ratio': '1:1', 'direction': ''}}, 'task_result': {'discord_image_url': 'https://img.midjourneyapi.xyz/mj/d21d0242-14f9-4d6b-b22f-41a41e6f3811.png', 'image_url': 'https://img.midjourneyapi.xyz/mj/d21d0242-14f9-4d6b-b22f-41a41e6f3811.png', 'image_id': 'd1a61293-913d-4738-bd76-d745e3b5936c', 'seed': '', 'result_message_id': '1165207196359995482', 'credit': 7, 'message': '', 'error_messages': [], 'need_retry': True, 'actions': ['upscale4', 'variation1', 'variation2', 'variation3', 'variation4', 'upscale1', 'upscale2', 'upscale3', 'reroll']}}
        # image_url = response.get('task_result', {}).get('image_url', '')
        # message = AIMessage(content=image_url, additional_kwargs=response)
        # return ChatResult(generations=[ChatGeneration(message=message)])

        params = {'stream': self.streaming, **kwargs}
        message = messages.pop()
        params.update({**message.additional_kwargs})
        self.client = MJClient(
            api_base=self.api_base,
            api_key=self.api_key,
        )

        def get_token_from_resp(result):
            token = ''
            status = result.get('status', '')
            if not result or status == 'failed':
                token = 'created:failure'
                raise Exception(token)
            elif status == 'ERROR':
                # 新增内部status: ERROR
                token = '{} {}'.format('Error', result.get('reason'))
                raise Exception(token)
            elif status == 'pending':
                # token = 'submit:success'
                token = None
            elif status == 'retry':
                token = 'retry'
            elif status == 'finished':
                token = '100%'
            elif status == 'processing':
                elapse = int(result.get('process_time', 0))
                token = f'{elapse}s' if elapse else None
            else:
                # 不做任何操作 因为状态为空可能是正常的
                pass
            return token
        if self.streaming:
            response = ""
            for resp in self.client.create(**params):
                #print(resp)
                if run_manager:
                    token = get_token_from_resp(resp)
                    run_manager.on_llm_new_token(token, **{
                        'action': resp.get('meta', {}).get('task_type', ''),
                        'image_url': resp.get('task_result', {}).get('discord_image_url', '')
                    })
                if resp.get('status', '') == 'finished':
                    response = resp
                    break
        else:
            response = self.client.create(**params)
            get_token_from_resp(response)
        image_url = response.get('task_result', {}).get('discord_image_url', '')
        # replace cdn
        if options.DEFAULT_LOCALE == 'zh_CN':
            image_url = image_url.replace('cdn.discordapp.com', 'mpic.forkway.cn')
        message = AIMessage(content=image_url, additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])


if __name__ == "__main__":
    import asyncio
    from tornado.options import options
    # from core.api_base import NewApiBase
    async def main():
        api_key = ''
        # api_base = NewApiBase('Midjourney').url
        api_base = 'https://api.midjourneyapi.xyz/mj'

        prompt = 'a green color Rattan Dsw Chair with beech leg, chair with no stitches line, Front View picture, Long Shot, picture with morandi light grey color background'
        # prompt = 'The girl lay on the beach watching us with her bare shoulders, extremely detailed eyes and face, medium chest, a delicate organza dress that was very close to the audience, messy long hair, veil, focused on the face, golden bracelet, long veil, lens shining, leaking light, medium wind, and a delicate and beautiful sky'

        """
<generator object MJClient.stream at 0x7f5a885dbdf0>
result {'code': 0, 'msg': 'success', 'success': True, 'used': '', 'data': {'resultId': '441048'}, 'result': 'created:success:result_id:441048'}
result {'code': 0, 'msg': 'success', 'success': True, 'used': '', 'state': '93%', 'data': None, 'result': '93%'}
result {'code': 0, 'msg': 'success', 'success': True, 'used': '', 'state': 'Done', 'data': {'jobId': '441048', 'messageId': '1127840953005264896', 'imageURL': 'https://cdn.aigcfun.com/attachments/1092632390930808946/1127840952468381806/551a3df7-2791-4e7e-9243-1e6883d1ff4b.png', 'status': 'completed'}, 'result': 'Done'}
        """

        from langchain.schema import HumanMessage
        chat = MJChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        params = {
            'action': 'imagine',
            'prompt': prompt
        }
        params_describe = {
            'action': 'describe',
            # 'image_url': 'https://ai-zhoulin.forkway.cn/api/feishu/image/message?image_key=img_v2_85355152-b787-4b66-9adb-3880f7d8530g&message_id=om_f6af9e5ea4c881f57c676751eac67024&compress=true&quality=90&max_size=1024&auto_download='
            # 'image_url': 'https://img.midjourneyapi.xyz/mj/4880fa04-93cc-48d6-ba7e-efce3e3d2a80.png',
            'image_url': 'https://i.ibb.co/ng6fzMx/td1.jpg'
        }
        params_fetch = {
            'action': 'fetch',
            'task_id': '3a7ccc29-9ef1-44b2-a17a-3a609626e53e'
        }

        messages = [HumanMessage(content=prompt, additional_kwargs=params_describe)]
        result = chat(messages)
        print(result)
        # {'task_id': '70f78e81-db29-40d8-91dc-7dab3a9a6445', 'status': 'finished', 'process_time': 50.*********, 'meta': {'account_id': 'ca5ad48a-0461-4a9e-9698-a9244c0cb20e', 'task_type': 'imagine', 'origin_task_id': '', 'bot_hash': '76fbb338fc3d5889958881be24d2ea984ad25c31b56823e33f2561d9a29902ae', 'created_at': **********, 'started_at': **********, 'ended_at': **********, 'process_mode': 'mixed', 'frozen_credit': 15, 'task_request': {'aspect_ratio': '', 'process_mode': '', 'prompt': 'dog', 'skip_prompt_check': False}, 'task_param': {'prompt': 'dog', 'index': '', 'zoom_ratio': '2', 'aspect_ratio': '1:1', 'direction': ''}}, 'task_result': {'discord_image_url': 'https://cdn.discordapp.com/attachments/1160412359572078725/1160477441547051028/alberas._dog_4b6817db-a0c0-4c63-b4d0-f6171ce03aa6.png?ex=6534cdd6&is=652258d6&hm=017b41d403603abebf155843385e190e2ede1798530033c32c91ff4ba8892e53&', 'image_url': 'https://img.midjourneyapi.xyz/mj/alberas._dog_4b6817db-a0c0-4c63-b4d0-f6171ce03aa6.png', 'image_id': '4b6817db-a0c0-4c63-b4d0-f6171ce03aa6', 'result_message_id': '1160477442209763328', 'credit': 7, 'message': '', 'error_messages': [], 'need_retry': False, 'actions': ['upscale4', 'variation1', 'variation2', 'variation3', 'variation4', 'upscale1', 'upscale2', 'upscale3', 'reroll']}}
        """
        content='https://cdn.aigcfun.com/attachments/1092632390930808946/1127879667181944882/62660168-8cc6-430d-98f3-c5f84cf77104.png' additional_kwargs={'jobId': '443676', 'messageId': '1127879667970478102', 'imageURL': 'https://cdn.aigcfun.com/attachments/1092632390930808946/1127879667181944882/62660168-8cc6-430d-98f3-c5f84cf77104.png', 'status': 'completed'} example=False
        """

    asyncio.run(main())


