import os
from tornado.options import define, parse_config_file


def load_config():
    # 服务器配置
    define("DEBUG", default=True)
    define("ENV", default="test")
    define("SERVER_PORT", default=3000)
    define("SCHEMA", default='https')
    define("DOMAIN", default='forkway.cn')
    define("KNOW_SERVER", default='https://know-test.forkway.cn')
    define("PROXY_BASE_SERVER", default='forkway.cn')
    define("PROXY_BASE_SERVER_NEW", default='ai2e.cn')
    define("OPENAI_PROXY", default='')
    define("CLAUDE_PROXY", default='')

    define("DEFAULT_LOCALE", default='zh_CN')
    define("OVERSEAS", default=False)

    define("GZIP", default=True)
    define("XHEADERS", default=True)
    define("COOKIE_SECRET", default="__REDSKULL__: REDSKULL_2021_05_16")
    define("MODULES", default=['account', 'application', 'callback', 'messenger'])

    # MySQL 配置
    define("MYSQL", default={
        "master": "mysql+pymysql://root:connectai2023@mysql:3306/connectai-manager?charset=utf8mb4&binary_prefix=true",
    })

    # Billing Server 配置
    define("BILLING_SERVER", default="https://billing.ai2e.cn")
    define("BILLING_ACCESS_TOKEN", default="")
    define("BILLING_HOST", default="billing.ai2e.cn")
    define("RECHARGE_LINK", default='https://www.qiniai.com/contact')

    # Redis 配置
    define("REDIS_HOST", default="redis")
    define("REDIS_PORT", default=6379)
    define("REDIS_DB", default=0)
    define("REDIS_NAMESPACE", default="connectai")

    # RabbitMQ 配置
    define("RABBIT_MQ_SERVER", default="rabbitmq")
    define("RABBIT_MQ_PORT", default=5672)
    define("RABBIT_MQ_USER", default="rabbitmq")
    define("RABBIT_MQ_PASSWORD", default="rabbitmq")
    define("RABBIT_MQ_EXCHANGE", default="connectai")
    define("RABBIT_MQ_URI", default="amqp://rabbitmq:rabbitmq@rabbitmq:5672/%2F")

    define("QUEUE_APPLICATION", default="queue-application")
    define("QUEUE_FEISHU", default="queue-feishu")
    define("QUEUE_DINGDING", default="queue-dingding")
    define("QUEUE_WEWORK", default="queue-wework")
    define("QUEUE_WXWORK", default="queue-wxwork")
    define("QUEUE_MESSENGER", default="queue-messenger")

    # 腾讯云
    define("SMS_APP_ID", default="")
    define("SMS_CAPTURE_TEMPLATE_ID", default="")
    define("SMS_SIGN", default="")
    define("SMS_SECRET_ID", default="")
    define("SMS_SECRET_KEY", default="")
    define("EMAIL_CAPTURE_TEMPLATE_ID", default=0)

    # 微信支付
    define("WXPAY_MCHID", default='')
    define("WXPAY_APPID", default='')
    define("WXPAY_APIV3_KEY", default='')
    define("WXPAY_PRIVATE_KEY", default='')
    define("WXPAY_CERT_SERIAL_NO", default='')
    define("WXPAY_CERT_DIR", default='')
    define("WXPAY_BOT_HOOK", default='')

    # 企业微信
    define("WXWORK_CROP_ID", default='')
    define("WXWORK_TOKEN", default='')
    define("WXWORK_AES_KEY", default='')
    define("WXWORK_PROVIDER_SECRET", default='')
    define("WEWORK_PROXY_HOST", default='')
    define("WEWORK_PROXY_PORT", default='4567')

    # chatpdf
    define("CHATPDF_USER", default='')
    define("CHATPDF_COLLECTION_ID", default='')
    define("CHATPDF_EXPITED", default=86400 * 7)

    # 对象存储地址，用来上传文件
    define("OSS_CDN_HOST", default='https://pic1.forkway.cn')
    define("OSS_CDN_PATH", default='upload')

    # 代理服务器
    define("PROXY_WEB", default='')
    define('PROXY_HOST', default='')
    define('PROXY_PORT', default=7777)
    define('PROXY_USERNAME', default='')
    define('PROXY_PASSWORD', default='')

    # 同步机器人
    define('SYSTEM_BOT_ID', default='')
    define('SYSTEM_BOT_SECRET', default='')
    define('SYSTEM_USER_BITABLE_TOKEN', default='')
    define('SYSTEM_USER_BITABLE_ID', default='')

    # loading config from *.conf
    root_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    config_file = os.path.join(root_path, "etc", "web_config.conf")
    if os.path.isfile(config_file):
        parse_config_file(config_file)
