import type { PlasmoMessaging } from "@plasmohq/messaging"
import { getCookieAndToken } from '~utils/browser'
import { Configuration, FeishuLoginCookies } from "~utils/open-feishu-api/configuration";
import { OpenApp } from "~utils/open-feishu-api/app";

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  // TODO
  const { domain } = req.body
  const params = await getCookieAndToken(domain)
  console.log('create app', params, req)
  const configCookie = new Configuration({ domain });
  // 这里手动设置csrftoken
  configCookie.csrfToken = params.csrf_token
  const app = new OpenApp(configCookie);
  // create app
  const appId = await app.newApp(req.body)

  const secret = await app.getAppSecret(appId)
  // const eventInfo = await app.eventManager.getEventInfo(appId)
  // const { encryptKey, verificationUrl, verificationToken } = eventInfo
   // 这里反了，应该是企联AI的encryptKey和verificationToken去决定飞书那边的配置
  res.send({ appId, secret})
}

export default handler

