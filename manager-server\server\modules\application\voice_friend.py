class VoiceFriendTool(CTool):
    name: str = 'VoiceFriend'
    description: str = 'voice friend tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class VoiceFriendCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.ReplyCard if data.extra.message_type not in ['audio', 'file'] else AppResult.UpdateCard,
                        FeishuMessageCard(
                            # FeishuMessageDiv('', tag='lark_md'),  # 一个占位置的空字符串
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在生成，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                pass

            def on_llm_end(self, response, *args, **kwargs):
                logging.info("debug %r", response.generations)
                # 同时添加当前用户输入的问题，以及ai回复问题
                content = response.generations[0][0].text
                session.add_message({'role': 'human', 'content': input.strip()})
                session.add_message({'role': 'ai', 'content': content})
                # 不展示生成的文本

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""

                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        model.callbacks = [VoiceFriendCallbackHandler()]
        m = {value: content for value, content in ai_model}
        temperature = session.temperature
        model_name = m.get(session.model_id, ai_model[0][1])
        if model.openai_api_type == '文心一言':
            chat = WenXinChat(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        elif model.openai_api_type == 'azure':
            chat = AzureChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        else:
            del model['openai_api_type']
            chat = ChatOpenAI(
                temperature=temperature,
                model_name=model_name,
                **model,
            )
        if session.system_role:
            if 'openai_api_type' in model and model.openai_api_type == '文心一言':
                # 文心一言必须奇数条消息，不支持SystemMessage，消息不能为空
                system_message = [HumanMessage(content=session.system_role), AIMessage(content='好的')]
            else:
                system_message = [SystemMessage(content=session.system_role)]
        else:
            system_message = []
        messages = system_message + chat_history + [HumanMessage(content=input)]
        return chat.invoke(messages)


class VoiceFriendSpeechTool(CTool):
    name: str = 'VoiceFriend_speech'
    description: str = 'voice friend speech tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        # 这里使用一个内部类，避免后面实例化之后调用的时候不能使用model以及platform等变量
        class VoiceFriendCallbackHandler(BaseCallbackHandler):
            result: str = ''
            send_length: int = 0

            def on_llm_start(self, *args, **kwargs):
                # 开始的时候先发一个消息
                if platform == 'feishu':
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在合成语音，请稍等...')))
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, _('🤖️：正在生成，请稍等...'))

            def on_llm_new_token(self, token, **kwargs) -> None:
                if model.streaming and platform == 'feishu':
                    pass

            def on_llm_end(self, response, *args, **kwargs):
                additional_kwargs = response.generations[0][0].message.additional_kwargs
                audio_bin = additional_kwargs.get('audio_bin')
                del additional_kwargs['audio_bin']
                if not audio_bin:
                    return
                error_msg = ''
                try:
                    audio_str = audio_bin.decode()
                    try:
                        audio_json = json.loads(audio_str)
                        logging.error('语音合成失败: %r', audio_json)
                        error_msg = audio_json.get('status', 'error')
                    except Exception as e:
                        error_msg = audio_str
                except Exception as e:
                    pass
                if error_msg:
                    if platform == 'feishu':
                        send_message(
                            AppResult.UpdateCard,
                            FeishuMessageCard(
                                FeishuMessageNote(FeishuMessagePlainText(f'error: {error_msg}'))
                            )
                        )
                    else:
                        send_message(AppResult.ReplyText, f'error: {error_msg}')
                    return
                if platform == 'feishu':
                    audio_bin, duration = mp3_to_opus(audio_bin)
                    audio_name = data.extra.message_id
                    file_key = syncify(client.upload_file_binary)(audio_bin, audio_name + '.opus', duration=duration)
                    logging.info('voice file: %r %r', file_key, duration)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            # 移除查看原文按钮
                            # FeishuMessageAction(FeishuMessageButton(_('查看原文'), type='primary', value={'text': input})),
                            FeishuMessageNote(FeishuMessagePlainText(_('🤖️：生成成功') + '\n' + _(FeishuCommand.choice_tip()))),
                            header=FeishuMessageCardHeader(content='Voice Friend 🎉', template='blue'),
                        )
                    )
                    send_message(
                        AppResult.ReplyAudio,
                        FeishuAudioMessage(file_key=file_key)
                    )

            def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs: Any) -> Any:
                """Run when LLM errors."""
                logging.error(error)
                if model.streaming and platform == 'feishu':
                    # 主动延迟等待更新卡片消息
                    time.sleep(1)
                    send_message(
                        AppResult.UpdateCard,
                        FeishuMessageCard(
                            FeishuMessageDiv('🤖️：' + str(error), tag='lark_md'),
                            header=FeishuMessageCardHeader(content='Voice Friend 🎉', template='blue'),
                        )
                    )
                else:
                    send_message(AppResult.ReplyText, '🤖️：' + str(error))

        models[1].callbacks = [VoiceFriendCallbackHandler()]
        chat = ElevenLabsChat(**models[1])
        params = dict(text=input)
        if 'voiceid' in session.extra:
            # 废弃旧的 voice_id，避免旧数据报错
            params.update(voice_id=session.extra['voiceid'])
        else:
            params.update(voice_name=session.extra.get('voice_name', 'Myra'))
        messages = [HumanMessage(content=input, additional_kwargs=params)]
        return chat.invoke(messages)


class VoiceFriendTranscriptTool(CTool):
    name: str = 'VoiceFriend_transcript'
    description: str = 'voice friend transcription tool'

    def _run(self, *args, run_manager=None, input='', **kwargs):
        model_name = ai_models[2][0][1]
        file_bin = data.extra.extra.input_kwargs['file_bin']
        file_name = data.extra.extra.input_kwargs['file_name']
        del data['extra']['extra']['input_kwargs']
        if platform == 'feishu':
            send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageDiv('', tag='lark_md'),
                    FeishuMessageNote(FeishuMessagePlainText(_('🤖️：正在转写语音，请稍等...')))
                )
            )
        try:
            form_data = {
                'file': (file_name, file_bin),
                'model': model_name
            }
            encoded_data, content_type = urllib3.filepost.encode_multipart_formdata(form_data)
            result = httpx.post(
                f"{models[2].openai_api_base}/audio/transcriptions",
                headers={
                    'Authorization': f'Bearer {models[2].openai_api_key}',
                    'Content-Type': content_type,
                },
                data=encoded_data,
                timeout=100,
            )
            logging.info("debug STT response %r", (result, result.content.decode()))
            return result.json()['text']
        except Exception as e:
            logging.error('STT error: %r', e)
            if platform == 'feishu':
                time.sleep(1)
                return send_message(
                    AppResult.UpdateCard,
                    FeishuMessageCard(
                        FeishuMessageDiv('', tag='lark_md'),
                        FeishuMessageNote(FeishuMessagePlainText(_('🤖️：语音转写失败，请尝试文本输入')))
                    )
                )


class FeishuCommand(CommandTool):
    next_tool_name: str = 'VoiceFriend'
    name: str = 'VoiceFriend_feishu_command'
    description: str = 'VoiceFriend feishu command'
    voice_options = {
        'Myra': 'Myra - female | young',
        'Hal': 'Hal - male | middle aged',
        'Joanne': 'Joanne - female | young'
    }
    tips: List[str] = [
        '输入<帮助> 或 /help 即可获取帮助菜单',
        '输入<模型> 或 /model 即可切换AI模型',
        '输入<清除> 或 /clear 即可清除上下文',
    ]

    def check_origin(self):
        return 'https://elevenlabs' != models[1].api_base[:18]

    @property
    def manual_btn(self):
        return FeishuMessageButton(
            _('使用说明'),
            type='primary',
            url=app.manual if data.lang == 'zh_CN' else app.manual_en,
        )

    @property
    def feedback_btn(self):
        return FeishuMessageButton(
            _('意见反馈'),
            url=app.feedback_url,
        )

    @property
    def ai_clear_btn(self):
        from_flag = data.input.startswith('/clear') or data.input.startswith('清除')
        return FeishuMessageButton(
            _('立刻清除'),
            type='danger',
            value={'clear': 1, 'reply_log_id': data.log_id if from_flag else None},
            confirm=FeishuMessageConfirm(
                title=_('您确定要清除对话上下文吗？'),
                text=_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息'),
            ) if not from_flag else None
        )

    @property
    def ai_model_options(self):
        return {str(value): content for value, content in ai_model if value in data.models}

    @property
    def ai_model_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value, content) for value, content in self.ai_model_options.items()],
            placeholder=_('选择模型'),
            initial_option=session.model_id or ai_model[0][0],
            value={'command': 'model'},
            confirm=FeishuMessageConfirm(
                title=_('您确定更改模型吗？'),
                text=_('选择模型，可以让AI更好的理解您的需求。'),
            )
        ) if len(self.ai_model_options) > 0 else None

    @property
    def voice_select(self):
        return FeishuMessageSelect(
            *[FeishuMessageOption(value=k, content=v) for k, v in self.voice_options.items()],
            placeholder=_('选择内置语音角色'),
            initial_option=session.extra.get('voice_name', list(self.voice_options.keys())[0]),
            value={'command': 'voice'},
        ) if not self.check_origin() else None

    def send_usage(self):
        return send_message(
            AppResult.ReplyCard if data.extra.message_id else AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(
                    _('💥 **我是智能语音AI助手 VoiceFriend**\n了解更多玩法技巧，请点击右侧「使用说明」查看👉'),
                    tag='lark_md',
                    extra=self.manual_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('** 🆑 清除话题上下文**\n文本回复 *清除* 或 */clear*'),
                    tag='lark_md',
                    extra=self.ai_clear_btn,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🚀 **AI模型切换**\n文本回复 *模型* 或 */model*'),
                    tag='lark_md',
                    extra=self.ai_model_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('👺 **选择语音角色**'),
                    tag='lark_md',
                    extra=self.voice_select,
                ),
                FeishuMessageHr(),
                FeishuMessageDiv(
                    _('🎒 **需要更多帮助**\n文本回复 *帮助* 或 */help*'),
                    tag='lark_md',
                    extra=self.feedback_btn,
                ),
                header=FeishuMessageCardHeader(_('🎒 需要帮助吗？'), template='blue'),
            )
        )

    def parse_command(self, input, action):
        if input[:5] == '/help' or input[:2] == '帮助':
            return 'help',
        elif input[:6] == '/clear' or input[:2] == '清除':
            return 'clear',
        elif input[:6] == '/model' or input[:2] == '模型':
            return 'model',
        elif input[:6] == '/voice' or input[:2] == '语音':
            return 'origin',
        elif not input and action:
            if action['tag'] == 'select_static':
                return action['value']['command'], action['option']
            elif action['tag'] == 'input':
                return action['name'], input, action
            elif action['tag'] == 'button':
                if 'clear' in action['value']:
                    return 'clear', action['value']['clear'], action['value'].get('reply_log_id')
                elif 'text' in action['value']:
                    return 'show_text', action['value']
        elif input and parse_urls(input):
            return 'note',
        if self.check_origin():
            return 'origin', input
        if data.extra.message_type in ['text']:
            return None,
        elif data.extra.message_type in ['audio', 'file']:
            self.next_tool_name = 'VoiceFriend_transcript'
            return 'audio', data.extra.extra.platform_content
        return 'note',

    def on_clear(self, flag=None, reply_log_id=None):
        if not flag:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_clear_btn),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('请注意，这将开始一个全新的对话，您将无法利用之前话题的历史信息')),
                    ),
                    header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
                )
            )

        session['chat_history'] = []  # 清除历史
        # 清除上下文的时候，把角色一起清除
        session['prompt_id'] = ''
        session['system_role'] = ''
        return send_message(
            AppResult.SendCard if not reply_log_id else AppResult.UpdateCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已删除此话题的上下文信息')),
                FeishuMessageNote(
                    FeishuMessagePlainText(_('我们可以开始一个全新的话题，继续找我聊天吧'))
                ),
                header=FeishuMessageCardHeader(_('🆑 机器人提醒'), template='blue'),
            ),
            **({'reply_log_id': reply_log_id} if reply_log_id else {})
        )

    def on_model(self, model_name=None):
        if not model_name:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.ai_model_select) if len(self.ai_model_options) > 0 else FeishuMessageDiv(_("无可用模型切换")),
                    FeishuMessageNote(FeishuMessagePlainText(_('提醒：选择内置模型，让AI更好的理解您的需求。'))),
                    header=FeishuMessageCardHeader(_('🚀 AI模型切换'), template='blue'),
                )
            )
        m = {str(value): content for value, content in ai_model if value in data.models}
        if model_name in m:
            session['model_id'] = model_name
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(
                        _('已选择模型：**%(model)s**', model=m.get(model_name, model_name)),
                        tag="lark_md"
                    ),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )
        else:
            return send_message(
                AppResult.SendCard,
                FeishuMessageCard(
                    FeishuMessageDiv(_('无法选择模型'), tag="lark_md"),
                    FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                    header=FeishuMessageCardHeader(_('🚀 机器人提醒'), template='blue'),
                )
            )

    def on_voice(self, voice=None):
        if not voice or voice not in self.voice_options.keys():
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(self.voice_select),
                    FeishuMessageNote(
                        FeishuMessagePlainText(_('选择内置语音角色')),
                    ),
                    header=FeishuMessageCardHeader(_('🤖：机器人提醒'), template='blue'),
                )
            )
        session.set_extra('voice_name', voice)
        return send_message(
            AppResult.SendCard,
            FeishuMessageCard(
                FeishuMessageDiv(_('已选择语音角色')),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(_('🤖：机器人提醒'), template='blue'),
            )
        )

    def on_audio(self, platform_content):
        file_key = platform_content.file_key
        if 'file_name' in platform_content:
            file_type = platform_content.file_name.split('.')[-1]
            if file_type not in ['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm']:
                return self.on_invalid()
            file_name = platform_content.file_name
        else:
            file_name = file_key + '.mp3'
        file_bin = syncify(client.get_message_resource)(data.extra.message_id, file_key, 'file')
        logging.info('audio data: %r', (file_key, file_name, len(file_bin)))
        data['extra']['extra']['input_kwargs'] = {'file_bin': file_bin, 'file_name': file_name}
        return self.next_tool_name

    def on_show_text(self, action_value):
        if 'text' not in action_value:
            return
        return send_message(
            AppResult.ReplyCard,
            FeishuMessageCard(
                FeishuMessageDiv(action_value['text'], tag='lark_md'),
                FeishuMessageNote(FeishuMessageMDText(_(FeishuCommand.choice_tip()))),
                header=FeishuMessageCardHeader(content='Voice Friend 🎉', template='blue'),
            )
        )

    def on_origin(self, content=''):
        if 'voiceid' not in session.extra or not content:
            return send_message(
                AppResult.ReplyCard,
                FeishuMessageCard(
                    FeishuMessageAction(
                        FeishuMessageInput(placeholder='Voice ID', label=_('输入您在官网的VoiceLab创建的Voice ID'), name='voice_input', value=dict(input=content))
                    ),
                    header=FeishuMessageCardHeader(_('🤖：机器人提醒'), template='blue'),
                )
            )
        return self.next_tool_name

    def on_voice_input(self, input, action):
        voice_id = action['input_value']
        if not re.match('[a-zA-Z0-9]{20}', voice_id):
            return self.on_invalid(text=_('无效的Voice ID'), header=_('🤖：机器人提醒'))
        session.set_extra('voiceid', voice_id)
        input = action['value']['input']
        if not input:
            return
        return self.next_tool_name

    def on_note(self, text='', title=''):
        text = text or _('请直接向我发送自然语言或语音，不支持文件、链接、图片等格式的消息哦!')
        title = title or _('不支持的消息，请更改发送文本/语音！')
        return send_note(text, title)


class VoiceFriendAgent(CAgent):
    class AppConfig(BaseAppConfig):
        category: str = 'LLM'
        name: str = 'VoiceFriend'
        title: str = 'VoiceFriend'
        title_en: str = 'VoiceFriend'
        description: str = '🎧 和GPT像朋友一样真人语音聊天对话，多种音色随心选择'
        description_en: str = '🎧 Live voice chat with GPT like a friend, with a variety of tones to choose from'
        problem: str = ''
        problem_en: str = ''
        video: str = ''
        video_en: str = ''
        manual: str = 'https://connect-ai.feishu.cn/wiki/XpWlwtG2Di74vRkNupzcZBJtnhe'
        manual_en: str = 'https://connect-ai.feishu.cn/wiki/D7O7wOQ31iURcJkwrl7cNORnnob'
        icon: str = 'https://pic1.forkway.cn/cdn/20231120190758.png'
        logo: str = 'https://pic1.forkway.cn/cdn/20231120190758.png?imageMogr2/thumbnail/720x'
        sorted: int = 105
        support_resource: List[object] = [dict(
            category=ModelCategory.LLM.value,
            scene=ModelCategory.LLM.value,
            title='大语言模型',
            tip='',
            required=True,
            resource=['OpenAI', 'Azure', '文心一言']
        ), dict(
            category=ModelCategory.TTS.value,
            scene=ModelCategory.TTS.value,
            title='语音合成',
            tip='',
            required=True,
            resource=['ElevenLabs']
        ), dict(
            category=ModelCategory.STT.value,
            scene=ModelCategory.STT.value,
            title='语音识别',
            tip='',
            required=False,
            resource=['OpenAI']
        ),]
        support_bots: List[str] = ['feishu']
        feedback_url: str = ''
        deploy: str = 'https://connect-ai.feishu.cn/wiki/L4e7wdsNUikOUbkMaIacBqH4nve'

    def next(self, last_action, last_result, intermediate_steps, **kwargs):
        if last_action:
            if last_result and last_result in self.allowed_tools:
                # 以链式传递到下一个Tool
                return AgentAction(tool=last_result, tool_input=kwargs, log="")
            if last_result and last_action.tool == 'VoiceFriend_transcript' and isinstance(last_result, str):
                return AgentAction(tool='VoiceFriend', tool_input={**kwargs, 'input': last_result}, log="")
            elif last_result and last_action.tool == 'VoiceFriend':
                return AgentAction(tool='VoiceFriend_speech', tool_input={**kwargs, 'input': last_result.content}, log="")
            return AgentFinish(
                {"output": last_result},
                "result for action {}: {}".format(last_action.tool, str(last_result))
            )
        # TODO 这里从input解析指令或者对话
        # 这里也可以直接调全局的send_message(message_type, data)，再返回一个AgentFinish
        if platform == 'feishu':
            return AgentAction(tool='VoiceFriend_feishu_command', tool_input=kwargs, log="")
        elif platform == 'dingding':
            return AgentAction(tool='VoiceFriend_dingding_command', tool_input=kwargs, log="")
        return AgentAction(tool='VoiceFriend', tool_input=kwargs, log="")
