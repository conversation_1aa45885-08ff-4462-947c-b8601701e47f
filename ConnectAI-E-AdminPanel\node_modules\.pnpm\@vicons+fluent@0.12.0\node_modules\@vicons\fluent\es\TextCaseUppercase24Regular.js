import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.707 3.5a.75.75 0 0 0-1.406-.02l-6 15.5a.75.75 0 1 0 1.399.54l1.556-4.02h7.118l1.42 4a.75.75 0 1 0 1.413-.5l-5.5-15.5zM3.837 14L6.97 5.907L9.84 14H3.837zM14.75 3.25A.75.75 0 0 0 14 4v15.25c0 .414.336.75.75.75h4.125a4.625 4.625 0 0 0 2.006-8.793A4.5 4.5 0 0 0 18 3.25h-3.25zM21 7.75a3 3 0 0 1-3 3h-2.5v-6H18a3 3 0 0 1 3 3zM18.875 18.5H15.5v-6.25h3.375a3.125 3.125 0 1 1 0 6.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseUppercase24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
