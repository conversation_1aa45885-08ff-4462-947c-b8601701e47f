import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8 12a8 8 0 1 1 15.582 2.56a5.458 5.458 0 0 1 1.795.922A9.982 9.982 0 0 0 26 12c0-5.523-4.477-10-10-10S6 6.477 6 12c0 1.792.471 3.473 1.296 4.928a5.537 5.537 0 0 1 1.701-1.057A7.964 7.964 0 0 1 8 12zm14.5 0c0 .79-.141 1.545-.399 2.245l-1.598-.309v-1.937a4.5 4.5 0 0 0-4.712-4.494a4.5 4.5 0 0 0-4.288 4.673v3.335a5.45 5.45 0 0 0-.974-.002A6.5 6.5 0 1 1 22.499 12zm-6.498-3a3 3 0 0 0-3 3v5.495l-.449-.189a4 4 0 0 0-5.41 2.642l-.09.332a1.51 1.51 0 0 0 .963 1.817c4.74 1.653 6.227 3.924 6.801 5.503c.3.824 1.116 1.48 2.113 1.398l4.76-.388a3 3 0 0 0 2.649-2.194l1.52-5.526a4 4 0 0 0-3.097-4.988l-3.76-.727V12a3 3 0 0 0-3-3z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TapDouble32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
