import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.776 17.37a1 1 0 0 1-.146 1.406l-4 3.25a1 1 0 0 1-1.261 0l-4-3.25a1 1 0 1 1 1.261-1.552L12 19.962l3.369-2.738a1 1 0 0 1 1.407.145zM4.709 9.488c.552 0 .98.183 1.282.55c.303.367.455.885.455 1.555v.906c0 .672-.151 1.19-.453 1.555c-.302.365-.728.547-1.277.547c-.553 0-.982-.183-1.285-.55c-.303-.367-.454-.884-.454-1.552v-.91c0-.671.15-1.19.452-1.554c.302-.365.729-.547 1.28-.547zm4.054 0c.552 0 .979.183 1.282.55c.303.367.454.885.454 1.555v.906c0 .672-.15 1.19-.452 1.555c-.302.365-.728.547-1.277.547c-.554 0-.982-.183-1.285-.55c-.303-.367-.455-.884-.455-1.552v-.91c0-.671.151-1.19.453-1.554c.302-.365.729-.547 1.28-.547zm6.47 0c.55 0 .978.183 1.281.55c.303.367.455.885.455 1.555v.906c0 .672-.151 1.19-.453 1.555c-.302.365-.728.547-1.277.547c-.554 0-.982-.183-1.285-.55c-.303-.367-.455-.884-.455-1.552v-.91c0-.671.151-1.19.453-1.554c.302-.365.729-.547 1.28-.547zm4.053 0c.552 0 .979.183 1.282.55c.303.367.454.885.454 1.555v.906c0 .672-.15 1.19-.452 1.555c-.302.365-.728.547-1.277.547c-.554 0-.982-.183-1.285-.55c-.303-.367-.455-.884-.455-1.552v-.91c0-.671.151-1.19.453-1.554c.302-.365.729-.547 1.28-.547zM12 13c.183 0 .334.054.455.162c.12.109.181.247.181.416c0 .168-.06.307-.18.415a.656.656 0 0 1-.456.162a.65.65 0 0 1-.456-.164a.536.536 0 0 1-.18-.413c0-.167.06-.305.18-.414A.65.65 0 0 1 12 13zm-7.29-2.623a.457.457 0 0 0-.432.246c-.09.165-.138.413-.145.746v1.271c0 .372.047.643.14.815a.47.47 0 0 0 .444.258c.196 0 .341-.083.434-.25c.094-.168.142-.43.144-.789v-1.23c0-.358-.047-.625-.14-.802a.468.468 0 0 0-.445-.265zm4.054 0a.457.457 0 0 0-.432.246c-.09.165-.139.413-.145.746v1.271c0 .372.046.643.14.815a.47.47 0 0 0 .444.258c.196 0 .34-.083.434-.25c.094-.168.141-.43.144-.789v-1.23c0-.358-.047-.625-.14-.802a.468.468 0 0 0-.445-.265zm6.47 0a.458.458 0 0 0-.433.246c-.09.165-.138.413-.145.746v1.271c0 .372.046.643.14.815a.47.47 0 0 0 .444.258c.196 0 .34-.083.434-.25c.094-.168.142-.43.144-.789v-1.23c0-.358-.047-.625-.14-.802a.469.469 0 0 0-.445-.265zm4.053 0a.457.457 0 0 0-.432.246c-.075.137-.121.333-.139.586l-.006.16v1.271c0 .372.046.643.14.815a.47.47 0 0 0 .444.258c.196 0 .34-.083.434-.25c.078-.14.124-.346.139-.618l.005-.17v-1.231c0-.358-.047-.625-.14-.802a.469.469 0 0 0-.445-.265zM12 10.273c.183 0 .334.054.455.162c.12.108.181.246.181.415c0 .169-.06.307-.18.415a.656.656 0 0 1-.456.163a.65.65 0 0 1-.456-.164a.537.537 0 0 1-.18-.414c0-.166.06-.304.18-.413a.65.65 0 0 1 .457-.164zm-4.63-5.05l4-3.25a1 1 0 0 1 1.157-.073l.104.074l4 3.25a1 1 0 0 1-1.159 1.625l-.102-.073l-3.37-2.738L8.63 6.776A1 1 0 0 1 7.276 5.31l.093-.085l4-3.25l-4 3.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TimePicker24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
