import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9 11a7.5 7.5 0 0 1 7.5-7.5h.095a.75.75 0 0 0 0-1.5H16.5a9 9 0 0 0-9 9v1.25c0 .69-.56 1.25-1.25 1.25h-3.5a.75.75 0 0 0 0 1.5h3.5A2.75 2.75 0 0 0 9 12.25V11zm7.75-4a4.25 4.25 0 0 0-4.25 4.25v1.5a5.75 5.75 0 0 1-5.75 5.75h-4a.75.75 0 0 1 0-1.5h4A4.25 4.25 0 0 0 11 12.75v-1.5a5.75 5.75 0 0 1 5.75-5.75h4.5a.75.75 0 0 1 0 1.5h-4.5zm.5 3.5c-.69 0-1.25.56-1.25 1.25V13a9 9 0 0 1-9 9h-.25a.75.75 0 0 1 0-1.5H7a7.5 7.5 0 0 0 7.5-7.5v-1.25A2.75 2.75 0 0 1 17.25 9h4a.75.75 0 0 1 0 1.5h-4z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Stream24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
