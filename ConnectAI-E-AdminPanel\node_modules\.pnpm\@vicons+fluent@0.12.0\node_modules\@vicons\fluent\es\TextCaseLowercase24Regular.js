import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.75 3a.75.75 0 0 1 .75.75v8.237c.685-.618 1.554-.987 2.5-.987c2.21 0 4 2.015 4 4.5S19.21 20 17 20c-.946 0-1.815-.37-2.5-.987v.237a.75.75 0 0 1-1.5 0V3.75a.75.75 0 0 1 .75-.75zm.75 12.5c0 1.828 1.28 3 2.5 3s2.5-1.172 2.5-3s-1.28-3-2.5-3s-2.5 1.172-2.5 3zm-7-4.999l.287.01c1.96.098 3.128 1.226 3.208 3.045l.005.204v5.5a.75.75 0 0 1-.648.743l-.102.007a.75.75 0 0 1-.743-.648L9.5 19.26l-.001-.104c-.988.565-1.901.854-2.749.854c-1.838 0-3.25-1.294-3.25-3.25c0-1.724 1.188-3.005 3.16-3.244a7.529 7.529 0 0 1 2.839.21c-.01-1.108-.556-1.655-1.786-1.717c-.962-.047-1.639.088-2.035.365a.75.75 0 1 1-.861-1.228c.658-.461 1.554-.665 2.683-.645zM9.5 15.324l-.303-.09a6.058 6.058 0 0 0-2.356-.193c-1.232.15-1.84.805-1.84 1.756c0 1.096.712 1.75 1.75 1.75c.68 0 1.518-.315 2.501-.964l.248-.169v-2.09z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseLowercase24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
