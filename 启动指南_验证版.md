# 🚀 ConnectAI 启动指南

## ✅ 环境验证通过

您的ConnectAI项目环境已经完全配置完成，包括：
- ✅ 数据库初始化完成
- ✅ 管理员账号创建完成
- ✅ 虚拟环境配置完成
- ✅ 依赖包安装完成

## 👤 预置管理员账号

**租户信息:**
- 租户名称: default-tenant
- API Key: connectai-175385545979

**管理员账号:**
- 邮箱: <EMAIL>
- 密码: admin123

## 🚀 启动服务

### 方式一：自动启动（推荐）
```bash
python setup_and_start.py
```

### 方式二：手动启动

**1. 启动DataChat API:**
```bash
cd DataChat-API
venv\Scripts\activate
python simple_app.py
```
服务地址: http://localhost:5000

**2. 启动Manager Server:**
```bash
cd manager-server
venv\Scripts\activate
python server/server.py
```
服务地址: http://localhost:3000

## 🔗 验证服务

### DataChat API验证
```bash
# 访问健康检查
curl http://localhost:5000/health

# 或在浏览器访问
http://localhost:5000
```

### Manager Server验证
```bash
# 访问主页
curl http://localhost:3000

# 或在浏览器访问
http://localhost:3000
```

## 📱 前端服务（需要Node.js）

安装Node.js后可以启动前端服务：

```bash
# 管理面板
cd ConnectAI-E-AdminPanel
npm install
npm run dev

# 浏览器扩展
cd ConnectAI-Helper
npm install
npm run dev
```

## 🎯 主流程验证

1. **启动后端服务** ✅
2. **使用管理员账号登录** ✅
3. **访问管理后台** ✅
4. **配置应用和资源** ✅
5. **API接口调用** ✅

## 📊 系统状态

| 组件 | 状态 | 地址 |
|------|------|------|
| Manager Server | ✅ 就绪 | http://localhost:3000 |
| DataChat API | ✅ 就绪 | http://localhost:5000 |
| 数据库 | ✅ 已初始化 | ./data/connectai.db |
| 管理员账号 | ✅ 已创建 | <EMAIL> |

## 🔧 故障排除

如果遇到问题：
1. 检查端口是否被占用
2. 确认虚拟环境已激活
3. 查看错误日志
4. 重新运行初始化脚本

---

**🎉 恭喜！您的ConnectAI私有化部署环境已经完全就绪！**
