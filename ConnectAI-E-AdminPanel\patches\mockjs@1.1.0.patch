diff --git a/dist/mock.js b/dist/mock.js
index 35d5b9af3eff34324656879705dcb81470fc9697..3e6a52e0fbfdd39d3aaf1592ffd19ecde33320f3 100644
--- a/dist/mock.js
+++ b/dist/mock.js
@@ -126,17 +126,17 @@ return /******/ (function(modules) { // webpackBootstrap
 /* 1 */
 /***/ (function(module, exports, __webpack_require__) {
 
-	/* 
+	/*
 	    ## Handler
 
 	    处理数据模板。
-	    
+
 	    * Handler.gen( template, name?, context? )
 
 	        入口方法。
 
 	    * Data Template Definition, DTD
-	        
+
 	        处理数据模板定义。
 
 	        * Handler.array( options )
@@ -146,7 +146,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	        * Handler.string( options )
 	        * Handler.function( options )
 	        * Handler.regexp( options )
-	        
+
 	        处理路径（相对和绝对）。
 
 	        * Handler.getValueByKeyPath( key, options )
@@ -177,7 +177,7 @@ return /******/ (function(modules) { // webpackBootstrap
 
 	    Handle.gen(template, name, options)
 	    context
-	        currentContext, templateCurrentContext, 
+	        currentContext, templateCurrentContext,
 	        path, templatePath
 	        root, templateRoot
 	*/
@@ -456,7 +456,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	                phed = Handler.placeholder(ph, options.context.currentContext, options.context.templateCurrentContext, options)
 
 	                // 只有一个占位符，并且没有其他字符
-	                if (placeholders.length === 1 && ph === result && typeof phed !== typeof result) { // 
+	                if (placeholders.length === 1 && ph === result && typeof phed !== typeof result) { //
 	                    result = phed
 	                    break
 
@@ -627,7 +627,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	            }
 	            // 引用的值已经计算好
 	            if (currentContext && (key in currentContext)) return currentContext[key]
-	    
+
 	            // 尚未计算，递归引用数据模板中的属性
 	            if (templateCurrentContext &&
 	                (typeof templateCurrentContext === 'object') &&
@@ -816,13 +816,13 @@ return /******/ (function(modules) { // webpackBootstrap
 	        var tpl = Mock.heredoc(function() {
 	            /*!
 	        {{email}}{{age}}
-	        <!-- Mock { 
+	        <!-- Mock {
 	            email: '@EMAIL',
 	            age: '@INT(1,100)'
 	        } -->
 	            *\/
 	        })
-	    
+
 	    **相关阅读**
 	    * [Creating multiline strings in JavaScript](http://stackoverflow.com/questions/805107/creating-multiline-strings-in-javascript)、
 	*/
@@ -850,7 +850,7 @@ return /******/ (function(modules) { // webpackBootstrap
 		解析数据模板（属性名部分）。
 
 		* Parser.parse( name )
-			
+
 			```json
 			{
 				parameters: [ name, inc, range, decimal ],
@@ -922,7 +922,7 @@ return /******/ (function(modules) { // webpackBootstrap
 
 	/*
 	    ## Mock.Random
-	    
+
 	    工具类，用于生成各种随机数据。
 	*/
 
@@ -1251,7 +1251,7 @@ return /******/ (function(modules) { // webpackBootstrap
 
 	        替代图片源
 	            http://fpoimg.com/
-	        参考自 
+	        参考自
 	            http://rensanning.iteye.com/blog/1933310
 	            http://code.tutsplus.com/articles/the-top-8-placeholders-for-web-designers--net-19485
 	    */
@@ -1541,7 +1541,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	        var bg_colour = Math.floor(Math.random() * 16777215).toString(16);
 	        bg_colour = "#" + ("000000" + bg_colour).slice(-6);
 	        document.bgColor = bg_colour;
-	    
+
 	    http://martin.ankerl.com/2009/12/09/how-to-create-random-colors-programmatically/
 	        Creating random colors is actually more difficult than it seems. The randomness itself is easy, but aesthetically pleasing randomness is more difficult.
 	        https://github.com/devongovett/color-generator
@@ -1561,7 +1561,7 @@ return /******/ (function(modules) { // webpackBootstrap
 
 	    http://tool.c7sky.com/webcolor
 	        网页设计常用色彩搭配表
-	    
+
 	    https://github.com/One-com/one-color
 	        An OO-based JavaScript color parser/computation toolkit with support for RGB, HSV, HSL, CMYK, and alpha channels.
 	        API 很赞
@@ -1593,7 +1593,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	            color += letters[Math.floor(Math.random() * 16)]
 	        }
 	        return color
-	    
+
 	        // 随机生成一个无脑的颜色，格式为 '#RRGGBB'。
 	        // _brainlessColor()
 	        var color = Math.floor(
@@ -1959,7 +1959,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	        }
 	        return result.join(' ')
 	    },
-	    // 
+	    //
 	    cparagraph: function(min, max) {
 	        var len = range(3, 7, min, max)
 	        var result = []
@@ -2282,17 +2282,17 @@ return /******/ (function(modules) { // webpackBootstrap
 	        随机生成一个 URL。
 
 	        [URL 规范](http://www.w3.org/Addressing/URL/url-spec.txt)
-	            http                    Hypertext Transfer Protocol 
-	            ftp                     File Transfer protocol 
-	            gopher                  The Gopher protocol 
-	            mailto                  Electronic mail address 
-	            mid                     Message identifiers for electronic mail 
-	            cid                     Content identifiers for MIME body part 
-	            news                    Usenet news 
-	            nntp                    Usenet news for local NNTP access only 
-	            prospero                Access using the prospero protocols 
+	            http                    Hypertext Transfer Protocol
+	            ftp                     File Transfer protocol
+	            gopher                  The Gopher protocol
+	            mailto                  Electronic mail address
+	            mid                     Message identifiers for electronic mail
+	            cid                     Content identifiers for MIME body part
+	            news                    Usenet news
+	            nntp                    Usenet news for local NNTP access only
+	            prospero                Access using the prospero protocols
 	            telnet rlogin tn3270    Reference to interactive sessions
-	            wais                    Wide Area Information Servers 
+	            wais                    Wide Area Information Servers
 	    */
 	    url: function(protocol, host) {
 	        return (protocol || this.protocol()) + '://' + // protocol?
@@ -2422,9 +2422,9 @@ return /******/ (function(modules) { // webpackBootstrap
 	    西南   重庆市 四川省 贵州省 云南省 西藏自治区
 	    西北   陕西省 甘肃省 青海省 宁夏回族自治区 新疆维吾尔自治区
 	    港澳台 香港特别行政区 澳门特别行政区 台湾省
-	    
+
 	    **排序**
-	    
+
 	    ```js
 	    var map = {}
 	    _.each(_.keys(REGIONS),function(id){
@@ -6527,7 +6527,7 @@ return /******/ (function(modules) { // webpackBootstrap
 		                "0" / "1" / "2" / "3" / "4" / "5" / "6" / "7" / "8" / "9" /
 		                "a" / "b" / "c" / "d" / "e" / "f" /
 		                "A" / "B" / "C" / "D" / "E" / "F"
-		    
+
 		    https://github.com/victorquinn/chancejs/blob/develop/chance.js#L1349
 		*/
 		guid: function() {
@@ -6629,7 +6629,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	}
 
 	function CaptureGroup(n) {
-	    Group.call(this, "capture-group"), this.index = cgs[this.offset] || (cgs[this.offset] = index++), 
+	    Group.call(this, "capture-group"), this.index = cgs[this.offset] || (cgs[this.offset] = index++),
 	    this.body = n;
 	}
 
@@ -6711,7 +6711,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	            }
 	            return r = l ? '"' + u(l) + '"' : "end of input", "Expected " + t + " but " + r + " found.";
 	        }
-	        this.expected = n, this.found = l, this.offset = u, this.line = t, this.column = r, 
+	        this.expected = n, this.found = l, this.offset = u, this.line = t, this.column = r,
 	        this.name = "SyntaxError", this.message = e(n, l);
 	    }
 	    function u(n) {
@@ -6724,8 +6724,8 @@ return /******/ (function(modules) { // webpackBootstrap
 	        function r(l) {
 	            function u(l, u, t) {
 	                var r, e;
-	                for (r = u; t > r; r++) e = n.charAt(r), "\n" === e ? (l.seenCR || l.line++, l.column = 1, 
-	                l.seenCR = !1) : "\r" === e || "\u2028" === e || "\u2029" === e ? (l.line++, l.column = 1, 
+	                for (r = u; t > r; r++) e = n.charAt(r), "\n" === e ? (l.seenCR || l.line++, l.column = 1,
+	                l.seenCR = !1) : "\r" === e || "\u2028" === e || "\u2029" === e ? (l.line++, l.column = 1,
 	                l.seenCR = !0) : (l.column++, l.seenCR = !1);
 	            }
 	            return Mt !== l && (Mt > l && (Mt = 0, Dt = {
@@ -6743,19 +6743,19 @@ return /******/ (function(modules) { // webpackBootstrap
 	        }
 	        function c() {
 	            var l, u, t, r, o;
-	            return l = qt, u = i(), null !== u ? (t = qt, 124 === n.charCodeAt(qt) ? (r = fl, 
-	            qt++) : (r = null, 0 === Wt && e(sl)), null !== r ? (o = c(), null !== o ? (r = [ r, o ], 
-	            t = r) : (qt = t, t = il)) : (qt = t, t = il), null === t && (t = al), null !== t ? (Lt = l, 
-	            u = hl(u, t), null === u ? (qt = l, l = u) : l = u) : (qt = l, l = il)) : (qt = l, 
+	            return l = qt, u = i(), null !== u ? (t = qt, 124 === n.charCodeAt(qt) ? (r = fl,
+	            qt++) : (r = null, 0 === Wt && e(sl)), null !== r ? (o = c(), null !== o ? (r = [ r, o ],
+	            t = r) : (qt = t, t = il)) : (qt = t, t = il), null === t && (t = al), null !== t ? (Lt = l,
+	            u = hl(u, t), null === u ? (qt = l, l = u) : l = u) : (qt = l, l = il)) : (qt = l,
 	            l = il), l;
 	        }
 	        function i() {
 	            var n, l, u, t, r;
-	            if (n = qt, l = f(), null === l && (l = al), null !== l) if (u = qt, Wt++, t = d(), 
+	            if (n = qt, l = f(), null === l && (l = al), null !== l) if (u = qt, Wt++, t = d(),
 	            Wt--, null === t ? u = al : (qt = u, u = il), null !== u) {
-	                for (t = [], r = h(), null === r && (r = a()); null !== r; ) t.push(r), r = h(), 
+	                for (t = [], r = h(), null === r && (r = a()); null !== r; ) t.push(r), r = h(),
 	                null === r && (r = a());
-	                null !== t ? (r = s(), null === r && (r = al), null !== r ? (Lt = n, l = dl(l, t, r), 
+	                null !== t ? (r = s(), null === r && (r = al), null !== r ? (Lt = n, l = dl(l, t, r),
 	                null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n, n = il);
 	            } else qt = n, n = il; else qt = n, n = il;
 	            return n;
@@ -6766,148 +6766,148 @@ return /******/ (function(modules) { // webpackBootstrap
 	        }
 	        function f() {
 	            var l, u;
-	            return l = qt, 94 === n.charCodeAt(qt) ? (u = pl, qt++) : (u = null, 0 === Wt && e(vl)), 
+	            return l = qt, 94 === n.charCodeAt(qt) ? (u = pl, qt++) : (u = null, 0 === Wt && e(vl)),
 	            null !== u && (Lt = l, u = wl()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function s() {
 	            var l, u;
-	            return l = qt, 36 === n.charCodeAt(qt) ? (u = Al, qt++) : (u = null, 0 === Wt && e(Cl)), 
+	            return l = qt, 36 === n.charCodeAt(qt) ? (u = Al, qt++) : (u = null, 0 === Wt && e(Cl)),
 	            null !== u && (Lt = l, u = gl()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function h() {
 	            var n, l, u;
-	            return n = qt, l = a(), null !== l ? (u = d(), null !== u ? (Lt = n, l = bl(l, u), 
+	            return n = qt, l = a(), null !== l ? (u = d(), null !== u ? (Lt = n, l = bl(l, u),
 	            null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n, n = il), n;
 	        }
 	        function d() {
 	            var n, l, u;
-	            return Wt++, n = qt, l = p(), null !== l ? (u = k(), null === u && (u = al), null !== u ? (Lt = n, 
-	            l = Tl(l, u), null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n, 
+	            return Wt++, n = qt, l = p(), null !== l ? (u = k(), null === u && (u = al), null !== u ? (Lt = n,
+	            l = Tl(l, u), null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n,
 	            n = il), Wt--, null === n && (l = null, 0 === Wt && e(kl)), n;
 	        }
 	        function p() {
 	            var n;
-	            return n = v(), null === n && (n = w(), null === n && (n = A(), null === n && (n = C(), 
+	            return n = v(), null === n && (n = w(), null === n && (n = A(), null === n && (n = C(),
 	            null === n && (n = g(), null === n && (n = b()))))), n;
 	        }
 	        function v() {
 	            var l, u, t, r, o, c;
-	            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)), 
-	            null !== u ? (t = T(), null !== t ? (44 === n.charCodeAt(qt) ? (r = ml, qt++) : (r = null, 
-	            0 === Wt && e(Rl)), null !== r ? (o = T(), null !== o ? (125 === n.charCodeAt(qt) ? (c = Fl, 
-	            qt++) : (c = null, 0 === Wt && e(Ql)), null !== c ? (Lt = l, u = Sl(t, o), null === u ? (qt = l, 
-	            l = u) : l = u) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, 
+	            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)),
+	            null !== u ? (t = T(), null !== t ? (44 === n.charCodeAt(qt) ? (r = ml, qt++) : (r = null,
+	            0 === Wt && e(Rl)), null !== r ? (o = T(), null !== o ? (125 === n.charCodeAt(qt) ? (c = Fl,
+	            qt++) : (c = null, 0 === Wt && e(Ql)), null !== c ? (Lt = l, u = Sl(t, o), null === u ? (qt = l,
+	            l = u) : l = u) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        function w() {
 	            var l, u, t, r;
-	            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)), 
-	            null !== u ? (t = T(), null !== t ? (n.substr(qt, 2) === Ul ? (r = Ul, qt += 2) : (r = null, 
-	            0 === Wt && e(El)), null !== r ? (Lt = l, u = Gl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)),
+	            null !== u ? (t = T(), null !== t ? (n.substr(qt, 2) === Ul ? (r = Ul, qt += 2) : (r = null,
+	            0 === Wt && e(El)), null !== r ? (Lt = l, u = Gl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il)) : (qt = l, l = il), l;
 	        }
 	        function A() {
 	            var l, u, t, r;
-	            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)), 
-	            null !== u ? (t = T(), null !== t ? (125 === n.charCodeAt(qt) ? (r = Fl, qt++) : (r = null, 
-	            0 === Wt && e(Ql)), null !== r ? (Lt = l, u = Bl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)),
+	            null !== u ? (t = T(), null !== t ? (125 === n.charCodeAt(qt) ? (r = Fl, qt++) : (r = null,
+	            0 === Wt && e(Ql)), null !== r ? (Lt = l, u = Bl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il)) : (qt = l, l = il), l;
 	        }
 	        function C() {
 	            var l, u;
-	            return l = qt, 43 === n.charCodeAt(qt) ? (u = jl, qt++) : (u = null, 0 === Wt && e($l)), 
+	            return l = qt, 43 === n.charCodeAt(qt) ? (u = jl, qt++) : (u = null, 0 === Wt && e($l)),
 	            null !== u && (Lt = l, u = ql()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function g() {
 	            var l, u;
-	            return l = qt, 42 === n.charCodeAt(qt) ? (u = Ll, qt++) : (u = null, 0 === Wt && e(Ml)), 
+	            return l = qt, 42 === n.charCodeAt(qt) ? (u = Ll, qt++) : (u = null, 0 === Wt && e(Ml)),
 	            null !== u && (Lt = l, u = Dl()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function b() {
 	            var l, u;
-	            return l = qt, 63 === n.charCodeAt(qt) ? (u = Hl, qt++) : (u = null, 0 === Wt && e(Ol)), 
+	            return l = qt, 63 === n.charCodeAt(qt) ? (u = Hl, qt++) : (u = null, 0 === Wt && e(Ol)),
 	            null !== u && (Lt = l, u = Wl()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function k() {
 	            var l;
-	            return 63 === n.charCodeAt(qt) ? (l = Hl, qt++) : (l = null, 0 === Wt && e(Ol)), 
+	            return 63 === n.charCodeAt(qt) ? (l = Hl, qt++) : (l = null, 0 === Wt && e(Ol)),
 	            l;
 	        }
 	        function T() {
 	            var l, u, t;
-	            if (l = qt, u = [], zl.test(n.charAt(qt)) ? (t = n.charAt(qt), qt++) : (t = null, 
-	            0 === Wt && e(Il)), null !== t) for (;null !== t; ) u.push(t), zl.test(n.charAt(qt)) ? (t = n.charAt(qt), 
+	            if (l = qt, u = [], zl.test(n.charAt(qt)) ? (t = n.charAt(qt), qt++) : (t = null,
+	            0 === Wt && e(Il)), null !== t) for (;null !== t; ) u.push(t), zl.test(n.charAt(qt)) ? (t = n.charAt(qt),
 	            qt++) : (t = null, 0 === Wt && e(Il)); else u = il;
-	            return null !== u && (Lt = l, u = Jl(u)), null === u ? (qt = l, l = u) : l = u, 
+	            return null !== u && (Lt = l, u = Jl(u)), null === u ? (qt = l, l = u) : l = u,
 	            l;
 	        }
 	        function x() {
 	            var l, u, t, r;
-	            return l = qt, 40 === n.charCodeAt(qt) ? (u = Kl, qt++) : (u = null, 0 === Wt && e(Nl)), 
-	            null !== u ? (t = R(), null === t && (t = F(), null === t && (t = m(), null === t && (t = y()))), 
-	            null !== t ? (41 === n.charCodeAt(qt) ? (r = Pl, qt++) : (r = null, 0 === Wt && e(Vl)), 
-	            null !== r ? (Lt = l, u = Xl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, 40 === n.charCodeAt(qt) ? (u = Kl, qt++) : (u = null, 0 === Wt && e(Nl)),
+	            null !== u ? (t = R(), null === t && (t = F(), null === t && (t = m(), null === t && (t = y()))),
+	            null !== t ? (41 === n.charCodeAt(qt) ? (r = Pl, qt++) : (r = null, 0 === Wt && e(Vl)),
+	            null !== r ? (Lt = l, u = Xl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il)) : (qt = l, l = il), l;
 	        }
 	        function y() {
 	            var n, l;
-	            return n = qt, l = c(), null !== l && (Lt = n, l = Yl(l)), null === l ? (qt = n, 
+	            return n = qt, l = c(), null !== l && (Lt = n, l = Yl(l)), null === l ? (qt = n,
 	            n = l) : n = l, n;
 	        }
 	        function m() {
 	            var l, u, t;
-	            return l = qt, n.substr(qt, 2) === Zl ? (u = Zl, qt += 2) : (u = null, 0 === Wt && e(_l)), 
-	            null !== u ? (t = c(), null !== t ? (Lt = l, u = nu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, n.substr(qt, 2) === Zl ? (u = Zl, qt += 2) : (u = null, 0 === Wt && e(_l)),
+	            null !== u ? (t = c(), null !== t ? (Lt = l, u = nu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        function R() {
 	            var l, u, t;
-	            return l = qt, n.substr(qt, 2) === lu ? (u = lu, qt += 2) : (u = null, 0 === Wt && e(uu)), 
-	            null !== u ? (t = c(), null !== t ? (Lt = l, u = tu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, n.substr(qt, 2) === lu ? (u = lu, qt += 2) : (u = null, 0 === Wt && e(uu)),
+	            null !== u ? (t = c(), null !== t ? (Lt = l, u = tu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        function F() {
 	            var l, u, t;
-	            return l = qt, n.substr(qt, 2) === ru ? (u = ru, qt += 2) : (u = null, 0 === Wt && e(eu)), 
-	            null !== u ? (t = c(), null !== t ? (Lt = l, u = ou(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, n.substr(qt, 2) === ru ? (u = ru, qt += 2) : (u = null, 0 === Wt && e(eu)),
+	            null !== u ? (t = c(), null !== t ? (Lt = l, u = ou(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        function Q() {
 	            var l, u, t, r, o;
-	            if (Wt++, l = qt, 91 === n.charCodeAt(qt) ? (u = iu, qt++) : (u = null, 0 === Wt && e(au)), 
-	            null !== u) if (94 === n.charCodeAt(qt) ? (t = pl, qt++) : (t = null, 0 === Wt && e(vl)), 
+	            if (Wt++, l = qt, 91 === n.charCodeAt(qt) ? (u = iu, qt++) : (u = null, 0 === Wt && e(au)),
+	            null !== u) if (94 === n.charCodeAt(qt) ? (t = pl, qt++) : (t = null, 0 === Wt && e(vl)),
 	            null === t && (t = al), null !== t) {
-	                for (r = [], o = S(), null === o && (o = U()); null !== o; ) r.push(o), o = S(), 
+	                for (r = [], o = S(), null === o && (o = U()); null !== o; ) r.push(o), o = S(),
 	                null === o && (o = U());
-	                null !== r ? (93 === n.charCodeAt(qt) ? (o = fu, qt++) : (o = null, 0 === Wt && e(su)), 
-	                null !== o ? (Lt = l, u = hu(t, r), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	                null !== r ? (93 === n.charCodeAt(qt) ? (o = fu, qt++) : (o = null, 0 === Wt && e(su)),
+	                null !== o ? (Lt = l, u = hu(t, r), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	                l = il)) : (qt = l, l = il);
 	            } else qt = l, l = il; else qt = l, l = il;
 	            return Wt--, null === l && (u = null, 0 === Wt && e(cu)), l;
 	        }
 	        function S() {
 	            var l, u, t, r;
-	            return Wt++, l = qt, u = U(), null !== u ? (45 === n.charCodeAt(qt) ? (t = pu, qt++) : (t = null, 
-	            0 === Wt && e(vu)), null !== t ? (r = U(), null !== r ? (Lt = l, u = wu(u, r), null === u ? (qt = l, 
-	            l = u) : l = u) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, l = il), Wt--, 
+	            return Wt++, l = qt, u = U(), null !== u ? (45 === n.charCodeAt(qt) ? (t = pu, qt++) : (t = null,
+	            0 === Wt && e(vu)), null !== t ? (r = U(), null !== r ? (Lt = l, u = wu(u, r), null === u ? (qt = l,
+	            l = u) : l = u) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, l = il), Wt--,
 	            null === l && (u = null, 0 === Wt && e(du)), l;
 	        }
 	        function U() {
 	            var n, l;
-	            return Wt++, n = G(), null === n && (n = E()), Wt--, null === n && (l = null, 0 === Wt && e(Au)), 
+	            return Wt++, n = G(), null === n && (n = E()), Wt--, null === n && (l = null, 0 === Wt && e(Au)),
 	            n;
 	        }
 	        function E() {
 	            var l, u;
-	            return l = qt, Cu.test(n.charAt(qt)) ? (u = n.charAt(qt), qt++) : (u = null, 0 === Wt && e(gu)), 
+	            return l = qt, Cu.test(n.charAt(qt)) ? (u = n.charAt(qt), qt++) : (u = null, 0 === Wt && e(gu)),
 	            null !== u && (Lt = l, u = bu(u)), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function G() {
 	            var n;
-	            return n = L(), null === n && (n = Y(), null === n && (n = H(), null === n && (n = O(), 
-	            null === n && (n = W(), null === n && (n = z(), null === n && (n = I(), null === n && (n = J(), 
-	            null === n && (n = K(), null === n && (n = N(), null === n && (n = P(), null === n && (n = V(), 
-	            null === n && (n = X(), null === n && (n = _(), null === n && (n = nl(), null === n && (n = ll(), 
+	            return n = L(), null === n && (n = Y(), null === n && (n = H(), null === n && (n = O(),
+	            null === n && (n = W(), null === n && (n = z(), null === n && (n = I(), null === n && (n = J(),
+	            null === n && (n = K(), null === n && (n = N(), null === n && (n = P(), null === n && (n = V(),
+	            null === n && (n = X(), null === n && (n = _(), null === n && (n = nl(), null === n && (n = ll(),
 	            null === n && (n = ul(), null === n && (n = tl()))))))))))))))))), n;
 	        }
 	        function B() {
@@ -6916,154 +6916,154 @@ return /******/ (function(modules) { // webpackBootstrap
 	        }
 	        function j() {
 	            var l, u;
-	            return l = qt, 46 === n.charCodeAt(qt) ? (u = ku, qt++) : (u = null, 0 === Wt && e(Tu)), 
+	            return l = qt, 46 === n.charCodeAt(qt) ? (u = ku, qt++) : (u = null, 0 === Wt && e(Tu)),
 	            null !== u && (Lt = l, u = xu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function $() {
 	            var l, u;
-	            return Wt++, l = qt, mu.test(n.charAt(qt)) ? (u = n.charAt(qt), qt++) : (u = null, 
-	            0 === Wt && e(Ru)), null !== u && (Lt = l, u = bu(u)), null === u ? (qt = l, l = u) : l = u, 
+	            return Wt++, l = qt, mu.test(n.charAt(qt)) ? (u = n.charAt(qt), qt++) : (u = null,
+	            0 === Wt && e(Ru)), null !== u && (Lt = l, u = bu(u)), null === u ? (qt = l, l = u) : l = u,
 	            Wt--, null === l && (u = null, 0 === Wt && e(yu)), l;
 	        }
 	        function q() {
 	            var n;
-	            return n = M(), null === n && (n = D(), null === n && (n = Y(), null === n && (n = H(), 
-	            null === n && (n = O(), null === n && (n = W(), null === n && (n = z(), null === n && (n = I(), 
-	            null === n && (n = J(), null === n && (n = K(), null === n && (n = N(), null === n && (n = P(), 
-	            null === n && (n = V(), null === n && (n = X(), null === n && (n = Z(), null === n && (n = _(), 
-	            null === n && (n = nl(), null === n && (n = ll(), null === n && (n = ul(), null === n && (n = tl()))))))))))))))))))), 
+	            return n = M(), null === n && (n = D(), null === n && (n = Y(), null === n && (n = H(),
+	            null === n && (n = O(), null === n && (n = W(), null === n && (n = z(), null === n && (n = I(),
+	            null === n && (n = J(), null === n && (n = K(), null === n && (n = N(), null === n && (n = P(),
+	            null === n && (n = V(), null === n && (n = X(), null === n && (n = Z(), null === n && (n = _(),
+	            null === n && (n = nl(), null === n && (n = ll(), null === n && (n = ul(), null === n && (n = tl()))))))))))))))))))),
 	            n;
 	        }
 	        function L() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Fu ? (u = Fu, qt += 2) : (u = null, 0 === Wt && e(Qu)), 
+	            return l = qt, n.substr(qt, 2) === Fu ? (u = Fu, qt += 2) : (u = null, 0 === Wt && e(Qu)),
 	            null !== u && (Lt = l, u = Su()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function M() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Fu ? (u = Fu, qt += 2) : (u = null, 0 === Wt && e(Qu)), 
+	            return l = qt, n.substr(qt, 2) === Fu ? (u = Fu, qt += 2) : (u = null, 0 === Wt && e(Qu)),
 	            null !== u && (Lt = l, u = Uu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function D() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Eu ? (u = Eu, qt += 2) : (u = null, 0 === Wt && e(Gu)), 
+	            return l = qt, n.substr(qt, 2) === Eu ? (u = Eu, qt += 2) : (u = null, 0 === Wt && e(Gu)),
 	            null !== u && (Lt = l, u = Bu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function H() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === ju ? (u = ju, qt += 2) : (u = null, 0 === Wt && e($u)), 
+	            return l = qt, n.substr(qt, 2) === ju ? (u = ju, qt += 2) : (u = null, 0 === Wt && e($u)),
 	            null !== u && (Lt = l, u = qu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function O() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Lu ? (u = Lu, qt += 2) : (u = null, 0 === Wt && e(Mu)), 
+	            return l = qt, n.substr(qt, 2) === Lu ? (u = Lu, qt += 2) : (u = null, 0 === Wt && e(Mu)),
 	            null !== u && (Lt = l, u = Du()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function W() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Hu ? (u = Hu, qt += 2) : (u = null, 0 === Wt && e(Ou)), 
+	            return l = qt, n.substr(qt, 2) === Hu ? (u = Hu, qt += 2) : (u = null, 0 === Wt && e(Ou)),
 	            null !== u && (Lt = l, u = Wu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function z() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === zu ? (u = zu, qt += 2) : (u = null, 0 === Wt && e(Iu)), 
+	            return l = qt, n.substr(qt, 2) === zu ? (u = zu, qt += 2) : (u = null, 0 === Wt && e(Iu)),
 	            null !== u && (Lt = l, u = Ju()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function I() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Ku ? (u = Ku, qt += 2) : (u = null, 0 === Wt && e(Nu)), 
+	            return l = qt, n.substr(qt, 2) === Ku ? (u = Ku, qt += 2) : (u = null, 0 === Wt && e(Nu)),
 	            null !== u && (Lt = l, u = Pu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function J() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Vu ? (u = Vu, qt += 2) : (u = null, 0 === Wt && e(Xu)), 
+	            return l = qt, n.substr(qt, 2) === Vu ? (u = Vu, qt += 2) : (u = null, 0 === Wt && e(Xu)),
 	            null !== u && (Lt = l, u = Yu()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function K() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Zu ? (u = Zu, qt += 2) : (u = null, 0 === Wt && e(_u)), 
+	            return l = qt, n.substr(qt, 2) === Zu ? (u = Zu, qt += 2) : (u = null, 0 === Wt && e(_u)),
 	            null !== u && (Lt = l, u = nt()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function N() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === lt ? (u = lt, qt += 2) : (u = null, 0 === Wt && e(ut)), 
+	            return l = qt, n.substr(qt, 2) === lt ? (u = lt, qt += 2) : (u = null, 0 === Wt && e(ut)),
 	            null !== u && (Lt = l, u = tt()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function P() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === rt ? (u = rt, qt += 2) : (u = null, 0 === Wt && e(et)), 
+	            return l = qt, n.substr(qt, 2) === rt ? (u = rt, qt += 2) : (u = null, 0 === Wt && e(et)),
 	            null !== u && (Lt = l, u = ot()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function V() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === ct ? (u = ct, qt += 2) : (u = null, 0 === Wt && e(it)), 
+	            return l = qt, n.substr(qt, 2) === ct ? (u = ct, qt += 2) : (u = null, 0 === Wt && e(it)),
 	            null !== u && (Lt = l, u = at()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function X() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === ft ? (u = ft, qt += 2) : (u = null, 0 === Wt && e(st)), 
+	            return l = qt, n.substr(qt, 2) === ft ? (u = ft, qt += 2) : (u = null, 0 === Wt && e(st)),
 	            null !== u && (Lt = l, u = ht()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function Y() {
 	            var l, u, t;
-	            return l = qt, n.substr(qt, 2) === dt ? (u = dt, qt += 2) : (u = null, 0 === Wt && e(pt)), 
-	            null !== u ? (n.length > qt ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(vt)), 
-	            null !== t ? (Lt = l, u = wt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, n.substr(qt, 2) === dt ? (u = dt, qt += 2) : (u = null, 0 === Wt && e(pt)),
+	            null !== u ? (n.length > qt ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(vt)),
+	            null !== t ? (Lt = l, u = wt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        function Z() {
 	            var l, u, t;
-	            return l = qt, 92 === n.charCodeAt(qt) ? (u = At, qt++) : (u = null, 0 === Wt && e(Ct)), 
-	            null !== u ? (gt.test(n.charAt(qt)) ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(bt)), 
-	            null !== t ? (Lt = l, u = kt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, 92 === n.charCodeAt(qt) ? (u = At, qt++) : (u = null, 0 === Wt && e(Ct)),
+	            null !== u ? (gt.test(n.charAt(qt)) ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(bt)),
+	            null !== t ? (Lt = l, u = kt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        function _() {
 	            var l, u, t, r;
-	            if (l = qt, n.substr(qt, 2) === Tt ? (u = Tt, qt += 2) : (u = null, 0 === Wt && e(xt)), 
+	            if (l = qt, n.substr(qt, 2) === Tt ? (u = Tt, qt += 2) : (u = null, 0 === Wt && e(xt)),
 	            null !== u) {
-	                if (t = [], yt.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(mt)), 
-	                null !== r) for (;null !== r; ) t.push(r), yt.test(n.charAt(qt)) ? (r = n.charAt(qt), 
+	                if (t = [], yt.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(mt)),
+	                null !== r) for (;null !== r; ) t.push(r), yt.test(n.charAt(qt)) ? (r = n.charAt(qt),
 	                qt++) : (r = null, 0 === Wt && e(mt)); else t = il;
-	                null !== t ? (Lt = l, u = Rt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	                null !== t ? (Lt = l, u = Rt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	                l = il);
 	            } else qt = l, l = il;
 	            return l;
 	        }
 	        function nl() {
 	            var l, u, t, r;
-	            if (l = qt, n.substr(qt, 2) === Ft ? (u = Ft, qt += 2) : (u = null, 0 === Wt && e(Qt)), 
+	            if (l = qt, n.substr(qt, 2) === Ft ? (u = Ft, qt += 2) : (u = null, 0 === Wt && e(Qt)),
 	            null !== u) {
-	                if (t = [], St.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(Ut)), 
-	                null !== r) for (;null !== r; ) t.push(r), St.test(n.charAt(qt)) ? (r = n.charAt(qt), 
+	                if (t = [], St.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(Ut)),
+	                null !== r) for (;null !== r; ) t.push(r), St.test(n.charAt(qt)) ? (r = n.charAt(qt),
 	                qt++) : (r = null, 0 === Wt && e(Ut)); else t = il;
-	                null !== t ? (Lt = l, u = Et(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	                null !== t ? (Lt = l, u = Et(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	                l = il);
 	            } else qt = l, l = il;
 	            return l;
 	        }
 	        function ll() {
 	            var l, u, t, r;
-	            if (l = qt, n.substr(qt, 2) === Gt ? (u = Gt, qt += 2) : (u = null, 0 === Wt && e(Bt)), 
+	            if (l = qt, n.substr(qt, 2) === Gt ? (u = Gt, qt += 2) : (u = null, 0 === Wt && e(Bt)),
 	            null !== u) {
-	                if (t = [], St.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(Ut)), 
-	                null !== r) for (;null !== r; ) t.push(r), St.test(n.charAt(qt)) ? (r = n.charAt(qt), 
+	                if (t = [], St.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(Ut)),
+	                null !== r) for (;null !== r; ) t.push(r), St.test(n.charAt(qt)) ? (r = n.charAt(qt),
 	                qt++) : (r = null, 0 === Wt && e(Ut)); else t = il;
-	                null !== t ? (Lt = l, u = jt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	                null !== t ? (Lt = l, u = jt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	                l = il);
 	            } else qt = l, l = il;
 	            return l;
 	        }
 	        function ul() {
 	            var l, u;
-	            return l = qt, n.substr(qt, 2) === Tt ? (u = Tt, qt += 2) : (u = null, 0 === Wt && e(xt)), 
+	            return l = qt, n.substr(qt, 2) === Tt ? (u = Tt, qt += 2) : (u = null, 0 === Wt && e(xt)),
 	            null !== u && (Lt = l, u = $t()), null === u ? (qt = l, l = u) : l = u, l;
 	        }
 	        function tl() {
 	            var l, u, t;
-	            return l = qt, 92 === n.charCodeAt(qt) ? (u = At, qt++) : (u = null, 0 === Wt && e(Ct)), 
-	            null !== u ? (n.length > qt ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(vt)), 
-	            null !== t ? (Lt = l, u = bu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, 
+	            return l = qt, 92 === n.charCodeAt(qt) ? (u = At, qt++) : (u = null, 0 === Wt && e(Ct)),
+	            null !== u ? (n.length > qt ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(vt)),
+	            null !== t ? (Lt = l, u = bu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l,
 	            l = il)) : (qt = l, l = il), l;
 	        }
 	        var rl, el = arguments.length > 1 ? arguments[1] : {}, ol = {
@@ -7234,7 +7234,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	var Util = __webpack_require__(3)
 	var Random = __webpack_require__(5)
 	    /*
-	        
+
 	    */
 	var Handler = {
 	    extend: Util.extend
@@ -7481,7 +7481,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	        return Random.integer(min, max)
 	    },
 	    /*
-	        
+
 	    */
 	    charset: function(node, result, cache) {
 	        // node.invert
@@ -7642,11 +7642,11 @@ return /******/ (function(modules) { // webpackBootstrap
 	    ## valid(template, data)
 
 	    校验真实数据 data 是否与数据模板 template 匹配。
-	    
+
 	    实现思路：
 	    1. 解析规则。
 	        先把数据模板 template 解析为更方便机器解析的 JSON-Schame
-	        name               属性名 
+	        name               属性名
 	        type               属性值类型
 	        template           属性值模板
 	        properties         对象属性数组
@@ -7655,7 +7655,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	    2. 递归验证规则。
 	        然后用 JSON-Schema 校验真实数据，校验项包括属性名、值类型、值、值生成规则。
 
-	    提示信息 
+	    提示信息
 	    https://github.com/fge/json-schema-validator/blob/master/src/main/resources/com/github/fge/jsonschema/validator/validation.properties
 	    [JSON-Schama validator](http://json-schema-validator.herokuapp.com/)
 	    [Regexp Demo](http://demos.forbeslindesay.co.uk/regexp/)
@@ -7693,8 +7693,8 @@ return /******/ (function(modules) { // webpackBootstrap
 	                    +step
 	                    整数部分
 	                    小数部分
-	                boolean 
-	                string  
+	                boolean
+	                string
 	                    min-max
 	                    count
 	    ## properties
@@ -7949,9 +7949,9 @@ return /******/ (function(modules) { // webpackBootstrap
 
 	/*
 	    完善、友好的提示信息
-	    
+
 	    Equal, not equal to, greater than, less than, greater than or equal to, less than or equal to
-	    路径 验证类型 描述 
+	    路径 验证类型 描述
 
 	    Expect path.name is less than or equal to expected, but path.name is actual.
 
@@ -8264,7 +8264,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	Util.extend(MockXMLHttpRequest.prototype, {
 	    // https://xhr.spec.whatwg.org/#the-open()-method
 	    // Sets the request method, request URL, and synchronous flag.
-	    open: function(method, url, async, username, password) {
+	    open: function(method, url, async = true, username, password) {
 	        var that = this
 
 	        Util.extend(this.custom, {
@@ -8310,6 +8310,8 @@ return /******/ (function(modules) { // webpackBootstrap
 	            var xhr = createNativeXMLHttpRequest()
 	            this.custom.xhr = xhr
 
+                MockXMLHttpRequest.prototype.upload = xhr.upload
+
 	            // 初始化所有事件，用于监听原生 XHR 对象的事件
 	            for (var i = 0; i < XHR_EVENTS.length; i++) {
 	                xhr.addEventListener(XHR_EVENTS[i], handle)
@@ -8360,6 +8362,7 @@ return /******/ (function(modules) { // webpackBootstrap
 
 	        // 原生 XHR
 	        if (!this.match) {
+                this.custom.xhr.responseType = this.responseType || ''
 	            this.custom.xhr.send(data)
 	            return
 	        }