<template>
  <div>
    <h1 class="mb-4 text-2xl font-extrabold tracking-tight text-gray-900 sm:mb-6 leding-tight dark:text-white">
      {{ $t('message.my.txjqrpz') }}
    </h1>
    <form ref="formRef" action="#" onsubmit="return false;">
      <div class="grid gap-5 my-6 sm:grid-cols-2">
        <div v-for="(label, item) in formItemLabels" :key="item">
          <label :for="item" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
            {{ toUpperCaseWithUnderscore(label) }}
          </label>
          <input
            :id="item"
            v-model="data[item]"
            type="text"
            :name="item"
            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
            :placeholder="label"
            required
          />
        </div>
      </div>
      <div v-if="0" class="mb-6 space-y-3">
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <input
              id="terms"
              v-model="privacy"
              aria-describedby="terms"
              name="privacy"
              type="checkbox"
              class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
              required
            />
          </div>
          <div class="ml-3 text-sm">
            <label for="terms" class="font-light text-gray-500 dark:text-gray-300"
              >{{ $t('message.my.wyjty') }}
              <a
                class="font-medium text-primary-600 dark:text-primary-500 hover:underline"
                target="_blank"
                href="https://connect-ai.forkway.cn/terms"
                >{{ $t('message.my.fwxy') }}</a
              >
              {{ $t('message.my.and') }}
              <a
                class="font-medium text-primary-600 dark:text-primary-500 hover:underline"
                target="_blank"
                href="https://connect-ai.forkway.cn/privacy"
                >{{ $t('message.my.ystk') }}</a
              >.</label
            >
          </div>
        </div>
      </div>
      <div class="flex space-x-3">
        <a
          href="#"
          class="text-center items-center w-full py-2.5 sm:py-3.5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
          @click="handleBack"
          >{{ $t('message.my.fh') }}</a
        >
        <button
          type="submit"
          class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 sm:py-3.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
          @click="handleNextStep"
        >
          {{ $t('message.my.bcpz') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, toRefs } from 'vue';
import { useVModel } from '@vueuse/core';
import { createConfig } from '@/views/bot/common/createConfig';
import { toUpperCaseWithUnderscore } from '@/utils/common/string';
import { updateAppClientBot } from '@/service/api/messenger';
import { t } from '@/locales';

const refreshList = inject<() => void>('refreshList');

const props = defineProps<{
  data: ApiMessenger.MessengerBot;
  aiInfo: ApiApp.AppInfo;
}>();

const { aiInfo } = toRefs(props);

const formRef = ref<HTMLFormElement | null>(null);
const privacy = ref(true); // 现在不想要用户勾选

const emit = defineEmits(['update:data', 'step-go']);

const data = useVModel(props, 'data', emit);

const formItemKeys = Object.keys(createConfig('feishu')) as (keyof ApiApp.AppClientBot)[];

const formItemLabels: Record<string, string> = {};

formItemKeys.forEach((key) => {
  if (key === 'app_id') {
    formItemLabels[key] = data.value.platform === 'dingding' ? 'app_key' : 'app_id';
  } else if (key === 'encript_key') {
    formItemLabels[key] = data.value.platform === 'wework' ? 'EncodingAESKey' : 'encrypt_key';
  } else if (key === 'validation_token') {
    formItemLabels[key] = data.value.platform === 'wework' ? 'token' : 'verification_token';
  } else {
    formItemLabels[key] = key;
  }
});

function handleBack() {
  emit('step-go', 1);
}

async function handleNextStep() {
  try {
    await handleSubmit();
    emit('step-go', 3);
  } catch (err) {
    console.error(err);
  }
}

function validateFormData() {
  if (!privacy.value) {
    return false;
  }
  for (const key of formItemKeys) {
    if (!data.value[key]) {
      return false;
    }
  }
  return true;
}

async function handleSubmit() {
  if (validateFormData()) {
    await updateAppClientBot({
      id: aiInfo.value.id,
      botId: data.value.bot_id,
      data: data.value
    });
    refreshList?.();
  } else {
    await Promise.reject(new Error(t('message.my.wsxx')));
  }
}
</script>

<style scoped></style>
