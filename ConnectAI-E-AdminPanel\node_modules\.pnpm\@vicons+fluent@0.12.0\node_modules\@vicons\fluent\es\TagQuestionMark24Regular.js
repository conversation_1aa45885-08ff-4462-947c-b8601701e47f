import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M19.75 2A2.25 2.25 0 0 1 22 4.25v5.462a3.25 3.25 0 0 1-.952 2.298l-.026.026a6.472 6.472 0 0 0-1.43-.692l.395-.395a1.75 1.75 0 0 0 .513-1.237V4.25a.75.75 0 0 0-.75-.75h-5.466c-.464 0-.91.185-1.238.513l-8.512 8.523a1.75 1.75 0 0 0 .015 2.462l4.461 4.454a1.755 1.755 0 0 0 2.33.13c.165.487.385.947.654 1.375a3.256 3.256 0 0 1-4.043-.443L3.489 16.06a3.25 3.25 0 0 1-.004-4.596l8.5-8.51a3.25 3.25 0 0 1 2.3-.953h5.465zM17 5.502a1.5 1.5 0 1 1 0 3a1.5 1.5 0 0 1 0-3zM23 17.5a5.5 5.5 0 1 0-11 0a5.5 5.5 0 0 0 11 0zm-6.125 3.005a.625.625 0 1 1 1.25 0a.625.625 0 0 1-1.25 0zm-1.229-4.548c-.01-1.137.806-1.954 1.854-1.954c1.03 0 1.853.846 1.853 1.95c0 .566-.185.913-.663 1.447l-.266.29l-.1.116c-.248.292-.324.462-.324.695a.5.5 0 0 1-1 0c0-.576.187-.926.67-1.468l.266-.29l.1-.113c.242-.286.317-.453.317-.677c0-.558-.38-.95-.853-.95c-.494 0-.86.366-.854.945a.5.5 0 0 1-1 .01z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagQuestionMark24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
