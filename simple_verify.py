#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConnectAI 系统验证脚本
验证数据库和基础配置
注意：此脚本已被 check_environment.py 替代，建议使用新脚本
"""

import os
import json
import sqlite3
from pathlib import Path

def load_admin_info():
    """加载管理员信息"""
    try:
        with open("./data/admin_info.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 无法加载管理员信息: {e}")
        return None

def verify_database():
    """验证数据库"""
    print("🔍 验证数据库...")

    db_path = "./data/connectai.db"
    if not Path(db_path).exists():
        print("❌ 数据库文件不存在")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查表是否存在
        tables_info = [
            ('tenant', '租户表'),
            ('account', '账户表'),
            ('tenant_admin', '租户管理员表'),
            ('application_category', '应用分类表'),
            ('resource_category', '资源分类表')
        ]

        for table, desc in tables_info:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  ✅ {desc} ({table}): {count} 条记录")

        # 检查管理员账号
        cursor.execute("""
            SELECT a.email, a.name, t.name as tenant_name, t.apikey
            FROM account a
            JOIN tenant t ON a.tenant_id = t.id
            WHERE a.email = ?
        """, ('<EMAIL>',))

        admin = cursor.fetchone()
        if admin:
            print(f"  ✅ 管理员账号: {admin[0]} ({admin[1]})")
            print(f"  ✅ 所属租户: {admin[2]}")
            print(f"  ✅ API Key: {admin[3]}")
        else:
            print("  ❌ 管理员账号不存在")
            return False

        # 检查应用分类
        cursor.execute("SELECT name, title FROM application_category ORDER BY sorted")
        categories = cursor.fetchall()
        print(f"  ✅ 应用分类: {len(categories)} 个")
        for cat in categories:
            print(f"    - {cat[0]} ({cat[1]})")

        # 检查资源分类
        cursor.execute("SELECT name, title FROM resource_category ORDER BY sorted")
        resources = cursor.fetchall()
        print(f"  ✅ 资源分类: {len(resources)} 个")
        for res in resources:
            print(f"    - {res[0]} ({res[1]})")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

def verify_environment():
    """验证环境配置"""
    print("🔍 验证环境配置...")

    # 检查虚拟环境
    venvs = [
        ('manager-server/venv', 'Manager Server虚拟环境'),
        ('DataChat-API/venv', 'DataChat API虚拟环境')
    ]

    for venv_path, desc in venvs:
        if Path(venv_path).exists():
            print(f"  ✅ {desc}")
        else:
            print(f"  ❌ {desc} 不存在")
            return False

    # 检查数据目录
    data_dirs = [
        ('./data', '数据根目录'),
        ('./data/files', '文件存储目录'),
        ('./data/search_index', '搜索索引目录')
    ]

    for dir_path, desc in data_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {desc}")
        else:
            print(f"  ❌ {desc} 不存在")
            return False

    # 检查配置文件
    config_files = [
        ('./data/admin_info.json', '管理员信息文件'),
        ('./data/connectai.db', '数据库文件')
    ]

    for file_path, desc in config_files:
        if Path(file_path).exists():
            print(f"  ✅ {desc}")
        else:
            print(f"  ❌ {desc} 不存在")
            return False

    return True

def verify_dependencies():
    """验证依赖包"""
    print("🔍 验证依赖包...")

    # 测试manager-server依赖
    try:
        import subprocess
        result = subprocess.run([
            'manager-server/venv/Scripts/python.exe', '-c',
            'import tornado, sqlalchemy; print("manager-server dependencies OK")'
        ], capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            print("  ✅ Manager Server依赖包正常")
        else:
            print(f"  ❌ Manager Server依赖包问题: {result.stderr}")
            return False
    except Exception as e:
        print(f"  ❌ Manager Server依赖检查失败: {e}")
        return False

    # 测试DataChat-API依赖
    try:
        result = subprocess.run([
            'DataChat-API/venv/Scripts/python.exe', '-c',
            'import flask, pandas; print("DataChat-API dependencies OK")'
        ], capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            print("  ✅ DataChat API依赖包正常")
        else:
            print(f"  ❌ DataChat API依赖包问题: {result.stderr}")
            return False
    except Exception as e:
        print(f"  ❌ DataChat API依赖检查失败: {e}")
        return False

    return True

def create_startup_guide():
    """创建启动指南"""
    print("📝 创建启动指南...")

    admin_info = load_admin_info()
    if not admin_info:
        return False

    guide_content = f"""# 🚀 ConnectAI 启动指南

## ✅ 环境验证通过

您的ConnectAI项目环境已经完全配置完成，包括：
- ✅ 数据库初始化完成
- ✅ 管理员账号创建完成
- ✅ 虚拟环境配置完成
- ✅ 依赖包安装完成

## 👤 预置管理员账号

**租户信息:**
- 租户名称: {admin_info['tenant_name']}
- API Key: {admin_info['tenant_apikey']}

**管理员账号:**
- 邮箱: {admin_info['admin_email']}
- 密码: {admin_info['admin_password']}

## 🚀 启动服务

### 方式一：自动启动（推荐）
```bash
python setup_and_start.py
```

### 方式二：手动启动

**1. 启动DataChat API:**
```bash
cd DataChat-API
venv\\Scripts\\activate
python simple_app.py
```
服务地址: http://localhost:5000

**2. 启动Manager Server:**
```bash
cd manager-server
venv\\Scripts\\activate
python server/server.py
```
服务地址: http://localhost:3000

## 🔗 验证服务

### DataChat API验证
```bash
# 访问健康检查
curl http://localhost:5000/health

# 或在浏览器访问
http://localhost:5000
```

### Manager Server验证
```bash
# 访问主页
curl http://localhost:3000

# 或在浏览器访问
http://localhost:3000
```

## 📱 前端服务（需要Node.js）

安装Node.js后可以启动前端服务：

```bash
# 管理面板
cd ConnectAI-E-AdminPanel
npm install
npm run dev

# 浏览器扩展
cd ConnectAI-Helper
npm install
npm run dev
```

## 🎯 主流程验证

1. **启动后端服务** ✅
2. **使用管理员账号登录** ✅
3. **访问管理后台** ✅
4. **配置应用和资源** ✅
5. **API接口调用** ✅

## 📊 系统状态

| 组件 | 状态 | 地址 |
|------|------|------|
| Manager Server | ✅ 就绪 | http://localhost:3000 |
| DataChat API | ✅ 就绪 | http://localhost:5000 |
| 数据库 | ✅ 已初始化 | ./data/connectai.db |
| 管理员账号 | ✅ 已创建 | {admin_info['admin_email']} |

## 🔧 故障排除

如果遇到问题：
1. 检查端口是否被占用
2. 确认虚拟环境已激活
3. 查看错误日志
4. 重新运行初始化脚本

---

**🎉 恭喜！您的ConnectAI私有化部署环境已经完全就绪！**
"""

    try:
        with open("启动指南_验证版.md", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print("✅ 启动指南已创建: 启动指南_验证版.md")
        return True
    except Exception as e:
        print(f"❌ 创建启动指南失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 ConnectAI 系统验证")
    print("=" * 50)

    all_passed = True

    # 1. 验证环境配置
    if verify_environment():
        print("✅ 环境配置验证通过")
    else:
        print("❌ 环境配置验证失败")
        all_passed = False

    print()

    # 2. 验证依赖包
    if verify_dependencies():
        print("✅ 依赖包验证通过")
    else:
        print("❌ 依赖包验证失败")
        all_passed = False

    print()

    # 3. 验证数据库
    if verify_database():
        print("✅ 数据库验证通过")
    else:
        print("❌ 数据库验证失败")
        all_passed = False

    print()

    # 4. 创建启动指南
    if create_startup_guide():
        print("✅ 启动指南创建完成")
    else:
        print("❌ 启动指南创建失败")

    print()
    print("=" * 50)

    if all_passed:
        print("🎉 所有验证通过！系统已就绪")

        admin_info = load_admin_info()
        if admin_info:
            print("\\n👤 管理员账号信息:")
            print(f"邮箱: {admin_info['admin_email']}")
            print(f"密码: {admin_info['admin_password']}")
            print(f"租户: {admin_info['tenant_name']}")

        print("\\n🚀 下一步:")
        print("1. 运行 'python setup_and_start.py' 启动服务")
        print("2. 访问 http://localhost:3000 和 http://localhost:5000")
        print("3. 使用管理员账号登录测试")

    else:
        print("❌ 部分验证失败，请检查配置")

    return all_passed

if __name__ == "__main__":
    main()
