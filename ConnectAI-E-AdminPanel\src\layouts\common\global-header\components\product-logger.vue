<template>
  <div class="justify-center items-center flex w-40px h-full dark:hover:bg-#333 relative">
    <button
      id="dropdownMenuIconHorizontalButton"
      data-dropdown-toggle="dropdownDotsHorizontal"
      class="inline-flex items-center outline-none text-sm font-medium text-center text-gray-900 rounded-lg outline-none dark:text-white"
      type="button"
    >
      <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 3">
        <path
          d="M2 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm6.041 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM14 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z"
        />
      </svg>
    </button>

    <div
      id="dropdownDotsHorizontal"
      class="hidden z-10 bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600"
    >
      <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownMenuIconHorizontalButton">
        <li>
          <a
            href="https://www.connectai-e.com/logger"
            target="_blank"
            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
            >{{ t('message.header.logger') }}</a
          >
        </li>
        <li>
          <a
            target="_blank"
            href="https://connect-ai.feishu.cn/share/base/form/shrcnTuzAJJppAGUQ5bXfB90Ns3"
            class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
            >{{ t('message.header.yjfk') }}</a
          >
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import { initDropdowns } from 'flowbite';
import { useThemeStore } from '@/store';
import { t } from '@/locales';
onMounted(() => {
  initDropdowns();
});
defineOptions({ name: 'GithubSite' });

const theme = useThemeStore();

function handleClickLink() {
  window.open('https://www.connectai-e.com/logger', '_blank');
}
</script>

<style scoped></style>
