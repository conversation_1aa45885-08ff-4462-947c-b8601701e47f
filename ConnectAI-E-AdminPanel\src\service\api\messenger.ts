import { request } from '~/src/service/request';
import qs from 'query-string';

export function getMessengerList(params: ApiMessenger.Req.MessengerList) {
  return request.get<ApiMessenger.Resp.MessengerList>('/api/messenger', { params });
}

export function getMessengerInfo() {
  return request.get<ApiMessenger.Resp.MessengerInfo>('/api/app/messenger');
}

export function createMessenger(data: ApiMessenger.Req.Create) {
  return request.post('/api/messenger', data);
}

export function updateMessenger(params: ApiMessenger.Req.Update) {
  return request.put(`/api/messenger?${qs.stringify(params)}`);
}

export function getMessengerWebConfig({ id }: ApiMessenger.Req.MessengerWebConfig) {
  return request.get<ApiMessenger.Resp.MessengerWebConfig>(`/api/messenger/${id}/client`);
}

export function updateAccessToken({
  id,
  action
}: ApiMessenger.Req.MessengerWebConfig & { action: 'refresh_access_token' }) {
  return request.put<ApiMessenger.Resp.MessengerWebConfig>(`/api/messenger/${id}/client`, { action });
}

export function updateMessengerWebConfig({ id, data }: ApiMessenger.Req.UpdateMessengerWebConfig) {
  return request.post(`/api/messenger/${id}/client`, data);
}

export function getMessengerChatList({ id }: ApiMessenger.Req.MessengerWebConfig) {
  return request.get<ApiMessenger.Resp.MessengerChatList>(`/api/messenger/${id}/chat`);
}

export function getMessengerChatInfo({ id }: ApiMessenger.Req.MessengerWebConfig) {
  return request.get<ApiMessenger.Resp.MessengerChatInfo>(`/api/messenger/${id}/info`);
}

export function getMessengerChatMemberList({ id }: ApiMessenger.Req.MessengerWebConfig) {
  return request.get<ApiMessenger.Resp.MessengerChatMemberList>(`/api/messenger/${id}/chat/member`);
}

export function getMessengerBot({ id }: ApiMessenger.Req.MessengerWebConfig) {
  return request.get<ApiMessenger.Resp.MessengerWebConfig>(`/api/messenger/${id}/bot`);
}

export function updateMessengerBot({ id, data }: ApiMessenger.Req.UpdateMessengerBot) {
  return request.post(`/api/messenger/${id}/bot`, data);
}

export function getMessengerSeatList(id: string, params: ApiMessenger.Req.MessengerList) {
  return request.get<ApiMessenger.Resp.MessengerSeatList>(`/api/messenger/${id}/seat`, { params });
}

export function createSeatMember({ id, data }: ApiMessenger.Req.CreateSeatMember) {
  return request.post(`/api/messenger/${id}/seat`, data);
}

export function updateSeatMember({ id, data }: ApiMessenger.Req.UpdateSeatMember) {
  return request.put(`/api/messenger/${id}/seat`, data);
}

export function updateSeatMemberAction({ id, data }: ApiMessenger.Req.UpdateSeatMemberAction) {
  return request.put(`/api/messenger/${id}/seat`, data);
}

export function getMessengerAiInfo({ id }: ApiMessenger.Req.MessengerWebConfig) {
  return request.get<ApiApp.Resp.AppInfo>(`/api/messenger/${id}/app`);
}

export function getAppClient({ id: application_id }: ApiApp.Req.GetAppClient) {
  return request.get<ApiApp.Resp.AppClientList>(`/api/app/${application_id}/client`);
}

/**
 *
 * 未来需要把之前那套使用app_id的换成这边这套使用instance_id的
 */
export function getAppSetting({ id: instance_id }: ApiApp.Req.AppSetting) {
  return request.get<ApiApp.Resp.AppSetting>(`/api/instance/${instance_id}/setting`);
}
export function getAppClientBot({ id: instance_id, botId }: ApiApp.Req.GetAppClientBot) {
  return request.get<ApiApp.Resp.AppClientBot>(`/api/instance/${instance_id}/client/${botId}`);
}

export function updateAppClientBot({ id: instance_id, botId, data }: ApiApp.Req.UpdateAppClientBot) {
  return request.post(`/api/instance/${instance_id}/client/${botId}`, data);
}

export function updateAppSetting({ id: instance_id, data }: ApiApp.Req.UpdateAppSetting) {
  return request.post(`/api/instance/${instance_id}/setting`, data);
}

export function getAppInfo({ id: instance_id }: ApiApp.Req.AppInfo) {
  return request.get<ApiApp.Resp.AppInfo>(`/api/instance/${instance_id}/info`);
}

export function deleteApp({ id: instance_id }: ApiApp.Req.AppSetting) {
  return request.delete(`/api/instance/${instance_id}`);
}
