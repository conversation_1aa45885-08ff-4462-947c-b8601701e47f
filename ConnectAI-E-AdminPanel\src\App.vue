<template>
  <n-config-provider
    :theme="theme.naiveTheme"
    :theme-overrides="theme.naiveThemeOverrides"
    :locale="langValue"
    :date-locale="dateValue"
    class="h-full"
  >
    <naive-provider>
      <router-view />
    </naive-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { NConfigProvider, dateZhCN, zhCN, enUS, dateEnUS, viVN, dateViVN } from 'naive-ui';
import { subscribeStore, useThemeStore } from '@/store';
import { useGlobalEvents } from '@/composables';
import { browserLanguage, getCookieValue } from '@/utils';

function hasCookie(cookieName: any) {
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.indexOf(`${cookieName}=`) === 0) {
      return true;
    }
  }
  return false;
}
const blang = browserLanguage();
const havecookie = hasCookie('__lang__');
if (!havecookie) {
  document.cookie = `__lang__=${blang};path=/;`;
}

const lang = getCookieValue('__lang__');

const navieLang: Record<string, any> = {
  zh_CN: [zhCN, dateZhCN],
  vi_VN: [viVN, dateViVN],
  default: [enUS, dateEnUS]
};

const langCode = lang in navieLang ? lang : 'default';
const [langValue, dateValue] = navieLang[langCode];

const theme = useThemeStore();

subscribeStore();
useGlobalEvents();
</script>

<style scoped></style>
