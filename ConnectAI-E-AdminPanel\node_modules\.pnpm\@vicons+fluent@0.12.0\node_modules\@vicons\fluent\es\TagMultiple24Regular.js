import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.5 6.5a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0zm-6.992-3.602A3.25 3.25 0 0 1 12.75 2h5.499A2.75 2.75 0 0 1 21 4.75v4.953a3.25 3.25 0 0 1-.97 2.316l-6.946 6.835a2.25 2.25 0 0 1-3.166-.01l-6.256-6.226a2.25 2.25 0 0 1 .034-3.223l6.812-6.497zm2.243.602c-.45 0-.883.173-1.208.484L4.73 10.48a.75.75 0 0 0-.011 1.075l6.257 6.226a.75.75 0 0 0 1.055.003l6.945-6.834a1.75 1.75 0 0 0 .523-1.247V4.75c0-.69-.56-1.25-1.25-1.25h-5.5zm7.98 9.232l-1.475 1.451c-.04.055-.085.106-.134.155l-5.334 5.229a3.251 3.251 0 0 1-4.575-.013L7.282 17.63l-2.411-2.386a1.262 1.262 0 0 1-.107-.12l-1.63-1.621a2.75 2.75 0 0 0 .682 2.807l4.356 4.311a4.75 4.75 0 0 0 6.666.016l5.334-5.229a2.75 2.75 0 0 0 .69-2.813a4.215 4.215 0 0 1-.131.136z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagMultiple24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
