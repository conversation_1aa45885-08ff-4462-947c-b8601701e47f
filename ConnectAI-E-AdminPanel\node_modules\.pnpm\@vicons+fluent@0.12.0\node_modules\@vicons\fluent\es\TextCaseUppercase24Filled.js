import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M18 3h-3a1 1 0 0 0-1 1v15.25a1 1 0 0 0 1 1h3.875a4.875 4.875 0 0 0 2.44-9.097A4.75 4.75 0 0 0 18 3zm0 7.5h-2V5h2a2.75 2.75 0 1 1 0 5.5zm-2 7.75V12.5h2.875a2.875 2.875 0 0 1 0 5.75H16zM7.257 3a1 1 0 0 1 .934.66l5.5 15.25a1 1 0 0 1-1.881.68L10.515 16h-6.72l-1.359 3.603a1 1 0 0 1-1.871-.706l5.75-15.25A1 1 0 0 1 7.257 3zM4.55 14h5.245L7.229 6.89L4.55 14z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextCaseUppercase24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
