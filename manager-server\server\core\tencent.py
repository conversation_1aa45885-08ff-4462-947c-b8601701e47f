import hmac
import sys
import time
import copy
import json
import hashlib
import httpx
from datetime import datetime


class SMSProvider(object):
    def __init__(
        self,
        secret_id,
        secret_key,
        service="sms",
        host="sms.tencentcloudapi.com",
        endpoint=...,
    ):
        # super().__init__(app_id, template_id, sign)
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.service = service
        self.host = host
        if endpoint is ...:
            self.endpoint = f"https://{self.host}"

    def sign(self, params: dict, timestamp: int, http_method: str = "POST"):
        """Tencent API sign method

        Refs https://cloud.tencent.com/document/api/382/38767
        """

        algorithm = "TC3-HMAC-SHA256"
        date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")

        # format request string
        uri = "/"
        querystring = ""
        ct = "application/json; charset=utf-8"
        payload = json.dumps(params)
        headers = f"content-type:{ct}\nhost:{self.host}\n"
        signed_headers = "content-type;host"
        hashed_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
        request = (
            f"{http_method}\n{uri}\n{querystring}\n{headers}\n"
            f"{signed_headers}\n{hashed_payload}"
        )

        # format sign string
        credential_scope = f"{date}/{self.service}/tc3_request"
        hashed_request = hashlib.sha256(request.encode("utf-8")).hexdigest()
        string_to_sign = (
            f"{algorithm}\n{str(timestamp)}\n" f"{credential_scope}\n{hashed_request}"
        )

        # sign request
        def _sign(key, msg):
            return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

        secret_date = _sign(("TC3" + self.secret_key).encode("utf-8"), date)
        secret_service = _sign(secret_date, self.service)
        secret_signing = _sign(secret_service, "tc3_request")
        signature = hmac.new(
            secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256
        ).hexdigest()

        # put together to get an Authorization string
        return (
            f"{algorithm} Credential={self.secret_id}/{credential_scope}, "
            f"SignedHeaders={signed_headers}, Signature={signature}"
        )

    async def send_sms(
        self, phone_nums: list, sms_app_id: str, sms_sign: str, template_id: str, template_params: list
    ):
        params = {
            "SmsSdkAppid": sms_app_id,
            "TemplateID": template_id,
            "Sign": sms_sign,
            "PhoneNumberSet": phone_nums,
            "TemplateParamSet": template_params,
        }
        timestamp = int(time.time())
        authorization = self.sign(params, timestamp)
        resp = await httpx.AsyncClient().post(
            self.endpoint,
            json=params,
            # data=json.dumps(params),
            headers={
                "Authorization": authorization,
                "Content-Type": "application/json; charset=utf-8",
                "Host": self.host,
                "X-TC-Action": "SendSms",
                "X-TC-Timestamp": str(timestamp),
                "X-TC-Version": "2019-07-11",
            },
        )

        return resp.json()


class EmailProvider(SMSProvider):

    def __init__(
        self,
        secret_id,
        secret_key,
        service="ses",
        host="ses.tencentcloudapi.com",
        region='ap-hongkong',
    ):
        super().__init__(secret_id, secret_key, service, host)
        self.region = region

    async def send_email(
        self, address: list, subject: str,
        template_id: int, template_params: dict,
        from_address: str = '<EMAIL>',
    ):

        params = {
            "FromEmailAddress": from_address,
            "ReplyToAddresses": from_address,
            "Destination": address,
            "Template": {
                "TemplateID": template_id,
                "TemplateData": json.dumps(template_params)
            },
            "Subject": subject
        }
        timestamp = int(time.time())
        authorization = self.sign(params, timestamp)
        print(params)
        headers={
            "Authorization": authorization,
            "Content-Type": "application/json; charset=utf-8",
            "Host": self.host,
            "X-TC-Region": self.region,
            "X-TC-Action": "SendEmail",
            "X-TC-Timestamp": str(timestamp),
            "X-TC-Version": "2020-10-02",
        }
        print('endpoint', self.endpoint)
        print('headers', headers)
        resp = await httpx.AsyncClient().post(
            self.endpoint,
            json=params,
            headers=headers,
        )

        return resp.json()


if __name__ == "__main__":
    import asyncio
    async def main():
        p = SMSProvider(
            # secret_id, secret_key
            '', '',
        )
        response = await p.send_sms(['+'], '**********', '企泥科技', '1870847', ['5432'])
        print('response', response)
        p = EmailProvider(
            # secret_id, secret_key
            '', '',
        )
        response = await p.send_email(['<EMAIL>'], '注册验证码', 86478, {"1": "5432"})
        print('response', response)

    asyncio.run(main())

