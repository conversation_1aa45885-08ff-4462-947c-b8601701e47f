#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试各个服务是否可以正常启动
"""

import os
import sys
import subprocess
from pathlib import Path

def test_manager_server():
    """测试manager-server"""
    print("🔍 测试manager-server...")
    
    try:
        # 切换到manager-server目录
        os.chdir("manager-server")
        
        # 检查虚拟环境
        if os.name == 'nt':
            python_cmd = "venv\\Scripts\\python.exe"
        else:
            python_cmd = "venv/bin/python"
        
        if not Path(python_cmd).exists():
            print("❌ manager-server虚拟环境不存在")
            return False
        
        # 测试导入
        result = subprocess.run([
            python_cmd, "-c", 
            "import tornado; import sqlalchemy; print('✅ manager-server依赖正常')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout.strip())
            return True
        else:
            print(f"❌ manager-server依赖测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ manager-server测试失败: {e}")
        return False
    finally:
        os.chdir("..")

def test_datachat_api():
    """测试DataChat-API"""
    print("🔍 测试DataChat-API...")
    
    try:
        # 切换到DataChat-API目录
        os.chdir("DataChat-API")
        
        # 检查虚拟环境
        if os.name == 'nt':
            python_cmd = "venv\\Scripts\\python.exe"
        else:
            python_cmd = "venv/bin/python"
        
        if not Path(python_cmd).exists():
            print("❌ DataChat-API虚拟环境不存在")
            return False
        
        # 测试导入
        result = subprocess.run([
            python_cmd, "-c", 
            "import flask; import pandas; print('✅ DataChat-API依赖正常')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout.strip())
            return True
        else:
            print(f"❌ DataChat-API依赖测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ DataChat-API测试失败: {e}")
        return False
    finally:
        os.chdir("..")

def test_data_directories():
    """测试数据目录"""
    print("🔍 测试数据目录...")
    
    directories = [
        "./data",
        "./data/files",
        "./data/search_index"
    ]
    
    all_good = True
    for directory in directories:
        if Path(directory).exists():
            print(f"✅ {directory} 存在")
        else:
            print(f"❌ {directory} 不存在")
            all_good = False
    
    return all_good

def create_simple_test_server():
    """创建一个简单的测试服务器"""
    print("🔧 创建简单测试服务器...")
    
    test_server_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试服务器
"""

import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class TestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "message": "ConnectAI Test Server",
                "status": "running",
                "services": {
                    "manager-server": "ready",
                    "datachat-api": "ready"
                }
            }
            self.wfile.write(json.dumps(response, indent=2).encode())
        elif self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {"status": "healthy"}
            self.wfile.write(json.dumps(response).encode())
        else:
            super().do_GET()

if __name__ == "__main__":
    PORT = 8000
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"🚀 测试服务器启动在 http://localhost:{PORT}")
        print("访问 http://localhost:8000 查看状态")
        print("访问 http://localhost:8000/health 查看健康状态")
        print("按 Ctrl+C 停止服务器")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\\n⏹️  测试服务器已停止")
'''
    
    with open("test_server.py", "w", encoding="utf-8") as f:
        f.write(test_server_content)
    
    print("✅ 测试服务器创建完成")

def main():
    """主函数"""
    print("🧪 ConnectAI 服务测试")
    print("=" * 50)
    
    # 测试数据目录
    data_ok = test_data_directories()
    
    # 测试manager-server
    manager_ok = test_manager_server()
    
    # 测试DataChat-API
    datachat_ok = test_datachat_api()
    
    # 创建测试服务器
    create_simple_test_server()
    
    print("\\n📊 测试结果:")
    print(f"数据目录: {'✅' if data_ok else '❌'}")
    print(f"Manager Server: {'✅' if manager_ok else '❌'}")
    print(f"DataChat API: {'✅' if datachat_ok else '❌'}")
    
    if all([data_ok, manager_ok, datachat_ok]):
        print("\\n🎉 所有测试通过！可以启动服务了")
        print("\\n🚀 启动测试服务器:")
        print("python test_server.py")
    else:
        print("\\n⚠️  部分测试失败，请检查环境配置")
    
    return all([data_ok, manager_ok, datachat_ok])

if __name__ == "__main__":
    main()
