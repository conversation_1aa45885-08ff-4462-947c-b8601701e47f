# 代码文档 - server/tasks.py

## 文件作用
Celery任务配置和工具函数库，提供文档加载器映射、向量化处理、定时任务调度和第三方平台集成功能。

## 逐行代码解释

### 导入模块 (1-26行)
```python
import os                                # 操作系统接口
from urllib.parse import urlparse        # URL解析
import httpx                             # HTTP客户端
from functools import cached_property    # 缓存属性装饰器
from tempfile import NamedTemporaryFile  # 临时文件
from time import time                    # 时间戳
from datetime import timedelta, datetime # 日期时间处理
from celery import Celery                # Celery分布式任务队列

from app import app                      # Flask应用实例
from models import (                     # 数据模型操作函数
    save_document, save_embedding, purge_document_by_id, ObjID
)

# LangChain相关导入
from langchain.embeddings import HuggingFaceEmbeddings, OpenAIEmbeddings  # 向量化模型
from langchain.text_splitter import CharacterTextSplitter, RecursiveCharacterTextSplitter  # 文本分割器
from langchain.document_loaders import (  # 各种文档加载器
    PyMuPDFLoader, TextLoader, UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader, UnstructuredPowerPointLoader,
    UnstructuredWordDocumentLoader, UnstructuredExcelLoader,
    UnstructuredFileLoader,
)
from langchain.document_loaders.sitemap import SitemapLoader  # 网站地图加载器
from langchain.schema import Document    # LangChain文档类
```

### 常量定义 (28行)
```python
LARK_HOST = 'https://open.feishu.cn'     # 飞书API主机地址
```

### create_celery_app函数 - Celery应用创建 (31-55行)
```python
def create_celery_app(app=None):
    """
    创建新的Celery对象并将Celery配置与Flask应用配置绑定
    将所有任务包装在应用上下文中
    """
    # 配置Redis作为消息代理和结果后端
    app.config['CELERY_BROKER_URL'] = 'redis://redis:6379/0'
    app.config['CELERY_RESULT_BACKEND'] = 'redis://redis:6379/0'
    
    # 创建Celery实例
    celery = Celery(
        app.import_name, 
        broker=app.config['CELERY_BROKER_URL'], 
        backend=app.config['CELERY_RESULT_BACKEND']
    )

    # 更新Celery配置
    celery.conf.update(app.config.get("CELERY_CONFIG", {}))
    TaskBase = celery.Task

    class ContextTask(TaskBase):
        """带Flask应用上下文的任务基类"""
        abstract = True

        def __call__(self, *args, **kwargs):
            # 在Flask应用上下文中执行任务
            with app.app_context():
                return TaskBase.__call__(self, *args, **kwargs)

    celery.Task = ContextTask  # 设置自定义任务基类
    return celery
```

### Celery实例和定时任务配置 (59-86行)
```python
celery = create_celery_app(app)          # 创建Celery应用实例

# 配置定时任务调度
celery.conf.beat_schedule = {
    "sync_feishudoc": {
        "task": "celery_app.sync_feishudoc",
        "schedule": timedelta(seconds=1800),  # 每30分钟执行一次
        "args": (False)                       # 传递给任务的参数
    },
    "sync_feishuwiki": {
        "task": "celery_app.sync_feishuwiki", 
        "schedule": timedelta(seconds=1500),  # 每25分钟执行一次，错开执行时间
        "args": (False)
    },
    "sync_yuque": {
        "task": "celery_app.sync_yuque",
        "schedule": timedelta(seconds=3700),  # 每约1小时执行一次
        "args": (False)
    },
    "sync_notion": {
        "task": "celery_app.sync_notion",
        "schedule": timedelta(seconds=3800),  # 每约1小时执行一次，错开时间
        "args": (False)
    }
}
```

### 文档加载器映射 (89-99行)
```python
LOADER_MAPPING = {
    "pdf": (PyMuPDFLoader, {}),                      # PDF文档加载器
    "word": (UnstructuredWordDocumentLoader, {}),    # Word文档加载器
    "excel": (UnstructuredExcelLoader, {}),          # Excel文档加载器
    "markdown": (UnstructuredMarkdownLoader, {}),    # Markdown文档加载器
    "ppt": (UnstructuredPowerPointLoader, {}),       # PowerPoint文档加载器
    "txt": (TextLoader, {"encoding": "utf8"}),       # 文本文件加载器
    "html": (UnstructuredHTMLLoader, {}),            # HTML文档加载器
    "sitemap": (SitemapLoader, {}),                  # 网站地图加载器
    "default": (UnstructuredFileLoader, {}),         # 默认文件加载器
}
```

### embedding_single_document函数 - 单文档向量化 (102-134行)
```python
def embedding_single_document(doc, fileUrl, fileType, fileName, collection_id, openai=False, uniqid='', version=0):
    """
    对单个文档进行向量化处理
    
    参数:
        doc: LangChain文档对象
        fileUrl: 文件URL
        fileType: 文件类型
        fileName: 文件名
        collection_id: 知识库ID
        openai: 是否使用OpenAI向量化模型
        uniqid: 唯一标识符
        version: 文档版本号
    """
    # 初始化向量化模型
    if openai:
        embeddings = OpenAIEmbeddings()              # 使用OpenAI向量化模型
    else:
        embeddings = HuggingFaceEmbeddings(model_name="/m3e-base")  # 使用本地m3e模型
    
    # 初始化文本分割器
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=500,                              # 每个分块500字符
        chunk_overlap=50                             # 分块间重叠50字符
    )

    split_docs = text_splitter.split_documents([doc])  # 分割文档
    document_id = ObjID.new_id()                     # 生成文档ID
    
    try:
        # 对所有分块进行向量化
        doc_result = embeddings.embed_documents([d.page_content for d in split_docs])
        
        # 保存每个分块的向量
        for chunk_index, doc in enumerate(split_docs):
            save_embedding(
                collection_id, document_id,
                chunk_index, len(doc.page_content),  # 分块索引和大小
                doc.page_content,                    # 分块内容
                doc_result[chunk_index],             # 向量数据
            )
        
        # 保存文档元数据
        save_document(
            collection_id, fileName or fileUrl, fileUrl, len(split_docs), fileType,
            uniqid=uniqid, version=version,
            document_id=document_id,
        )
        return document_id
    except Exception as e:
        # 出错时清理已创建的数据
        purge_document_by_id(document_id)
```

### 工具函数 (136-148行)
```python
def get_status_by_id(task_id):
    """根据任务ID获取任务状态"""
    return celery.AsyncResult(task_id)

def embed_query(text, openai=False):
    """对查询文本进行向量化"""
    # 初始化向量化模型
    if openai:
        embeddings = OpenAIEmbeddings()
    else:
        embeddings = HuggingFaceEmbeddings(model_name="/m3e-base")

    return embeddings.embed_query(text)              # 返回查询向量
```

### Lark类 - 飞书API客户端 (150-160+行)
```python
class Lark(object):
    """飞书API客户端类"""
    
    def __init__(self, app_id=None, secret_key=None, app_secret=None, 
                 verification_token=None, validation_token=None, 
                 encript_key=None, encrypt_key=None, host=LARK_HOST, **kwargs):
        """初始化飞书客户端"""
        self.app_id = app_id
        self.app_secret = app_secret or secret_key       # 应用密钥
        self.encrypt_key = encrypt_key or encript_key     # 加密密钥
        self.verification_token = verification_token or validation_token  # 验证令牌
        self.host = host                                  # API主机地址

    @cached_property
    def _tenant_access_token(self):
        """获取租户访问令牌（缓存属性）"""
        # 实现租户访问令牌的获取逻辑
        # https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/tenant_access_token_internal
```

## 技术特点

### 分布式任务处理
- **Celery集成**: 基于Redis的分布式任务队列
- **Flask上下文**: 任务在Flask应用上下文中执行
- **定时调度**: 支持定时任务的自动执行

### 多格式文档支持
- **文档加载器**: 支持PDF、Word、Excel、PPT等多种格式
- **统一接口**: 通过映射表统一不同格式的加载器
- **扩展性**: 易于添加新的文档格式支持

### 向量化处理
- **多模型支持**: 支持OpenAI和HuggingFace模型
- **文本分割**: 智能分割长文档为适合的块
- **向量存储**: 将向量数据存储到Elasticsearch

### 定时同步
- **多平台同步**: 支持飞书、语雀、Notion等平台
- **错开执行**: 不同任务错开执行时间避免资源冲突
- **增量更新**: 支持增量同步减少资源消耗

## 使用场景
- **文档导入**: 批量导入各种格式的文档
- **知识库同步**: 定时同步外部平台的内容
- **向量化服务**: 为搜索和问答提供向量化支持
- **任务调度**: 管理后台的异步任务执行

## 配置说明
- **Redis配置**: 使用Redis作为消息代理和结果存储
- **模型配置**: 可选择不同的向量化模型
- **定时配置**: 可调整各个同步任务的执行频率
- **分块配置**: 可调整文档分块的大小和重叠
