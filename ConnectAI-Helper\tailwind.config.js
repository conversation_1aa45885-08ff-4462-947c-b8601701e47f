/** @type {import('tailwindcss/tailwind-config').TailwindConfig} */ module.exports = {
  mode: "jit",
  darkMode: "class",
  content: [
    "./components/**/*.{ts,tsx,js.jsx,html,scss}",
    "./contents/**/*.{ts,tsx,js.jsx,html}",
    "./options/**/*.{ts,tsx,js.jsx,html}",
    "./popup/**/*.{ts,tsx,js.jsx,html}",
  ],
  theme: {
    fontFamily: {
      sans: ["Inter", "sans-serif"],
      serif: ["Georgia", "serif"],
      yanti: ["Yuanti TC", "sans-serif"],
      number: ["DIN Alternate", "Inter", "sans-serif"]

    },
  },
  corePlugins: {
    preflight: true
  },
  variants: { extend: { typography: ["dark"] } },
  plugins: []
}
