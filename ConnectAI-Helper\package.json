{"name": "connectai-helper", "displayName": "ConnectAI Helper", "version": "0.0.22", "description": "🤞One-click deployment for connect-ai app", "author": "<EMAIL>", "contributors": ["<PERSON><PERSON><PERSON>peng"], "scripts": {"dev": "plasmo dev", "build": "plasmo build", "zip:firefox": "pnpm build --target=firefox-mv2 --zip", "zip:chrome": "pnpm build --target=chrome-mv3 --zip", "zip:edge": "pnpm build --target=edge-mv3 --zip", "package": "plasmo package"}, "dependencies": {"@douyinfe/semi-icons": "^2.38.0", "@douyinfe/semi-ui": "^2.38.0", "@plasmohq/messaging": "^0.5.0", "@plasmohq/storage": "^1.6.2", "ace-builds": "^1.23.1", "ahooks": "^3.7.8", "axios": "^1.4.0", "node-fetch": "^3.3.2", "plasmo": "0.76.3", "react": "18.2.0", "react-ace": "^10.1.0", "react-dom": "18.2.0"}, "devDependencies": {"@plasmohq/prettier-plugin-sort-imports": "latest", "@types/chrome": "0.0.237", "@types/node": "20.2.5", "@types/react": "18.2.8", "@types/react-dom": "18.2.4", "browserify-zlib": "^0.2.0", "https-browserify": "^1.0.0", "path-browserify": "^1.0.0", "postcss": "8.4.24", "prettier": "2.8.8", "stream-browserify": "^3.0.0", "stream-http": "^3.1.0", "tailwindcss": "3.3.2", "ts-toolbelt": "^9.6.0", "typescript": "5.1.3", "url": "^0.11.0", "util": "^0.12.3", "vitest": "^0.32.4"}, "manifest": {"host_permissions": ["<all_urls>"], "permissions": ["scripting", "activeTab", "cookies"]}}