# 🔥 启动真实Tornado服务器指南

## 🎯 目标

启动ConnectAI项目的**真实业务代码**，而不是简化版本，以便验证和开发实际功能。

## 📋 真实架构 vs 简化版本

### 当前使用的简化版本
```
test_flask.py           # 我们创建的测试服务器
simple_flask_server.py  # 简化的Flask版本
```
**问题**: 这些都是重新实现的简化版本，无法验证真实的业务逻辑。

### 真实的业务代码
```
manager-server/server/server.py     # Tornado主服务器
manager-server/server/api.py        # 真实的API路由
manager-server/server/modules/      # 真实的业务模块
```
**优势**: 包含完整的业务逻辑、数据模型、权限控制等。

## 🚀 启动真实Tornado服务器

### 步骤1: 安装依赖

```bash
cd manager-server

# 安装Tornado和相关依赖
pip install tornado==6.0.2
pip install sqlalchemy
pip install flask-cors  # 用于CORS支持
```

### 步骤2: 配置数据库

修改 `manager-server/server/settings/config.py`:

```python
# MySQL 配置 - 本地开发环境使用SQLite
define("MYSQL", default={
    "master": "sqlite:///../../data/connectai.db",  # 指向我们的SQLite数据库
})

# 启用内存存储替代Redis
define("USE_MEMORY_STORE", default=True)
```

### 步骤3: 创建启动脚本

创建 `start_tornado_server.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动真实的Tornado服务器
"""

import os
import sys
import subprocess
from pathlib import Path

def start_tornado_server():
    """启动Tornado服务器"""
    print("🔥 启动真实的ConnectAI Tornado服务器...")
    
    # 切换到server目录
    server_dir = Path("manager-server/server")
    
    if not server_dir.exists():
        print("❌ manager-server/server目录不存在")
        return False
    
    try:
        # 启动Tornado服务器
        process = subprocess.Popen(
            [sys.executable, "server.py"],
            cwd=server_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"✅ Tornado服务器已启动，PID: {process.pid}")
        print("📍 服务地址: http://localhost:3000")
        print("⏹️  按 Ctrl+C 停止服务器")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n⏹️  正在停止Tornado服务器...")
        process.terminate()
        process.wait()
        print("✅ 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_tornado_server()
```

### 步骤4: 修复CORS问题

在 `manager-server/server/server.py` 中添加CORS支持:

```python
# 在imports中添加
from tornado.web import RequestHandler

# 添加CORS处理基类
class CORSHandler(RequestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://localhost:3200")
        self.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization")
        self.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        self.set_header("Access-Control-Allow-Credentials", "true")

    def options(self, *args):
        self.set_status(204)
        self.finish()
```

### 步骤5: 启动服务

```bash
# 启动真实的Tornado服务器
python start_tornado_server.py

# 或直接在server目录中启动
cd manager-server/server
python server.py
```

## 🔧 配置真实API路由

### 查看真实的API路由

检查 `manager-server/server/api.py` 中的路由配置:

```python
# 真实的API路由示例
urls = [
    (r"/api/account/login", "modules.account.handler.LoginHandler"),
    (r"/api/account/info", "modules.account.handler.InfoHandler"),
    (r"/api/tenant/handler", "modules.account.handler.TenantHandler"),
    # ... 更多真实的业务路由
]
```

### 修复前端API路径

确保前端发送的API请求路径与真实路由匹配:

```typescript
// ConnectAI-E-AdminPanel/.env-config.ts
const serviceEnv: ServiceEnv = {
  dev: {
    url: 'http://localhost:3000'  // 指向Tornado服务器
  }
}
```

## 🧪 验证真实业务功能

### 测试真实的登录API

```python
# test_real_api.py
import requests

def test_real_login():
    """测试真实的登录API"""
    url = "http://localhost:3000/api/account/login"
    data = {
        "email": "<EMAIL>",
        "passwd": "admin123"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

if __name__ == "__main__":
    test_real_login()
```

### 验证数据库集成

真实的Tornado服务器会:
1. 连接到SQLite数据库
2. 使用SQLAlchemy ORM
3. 执行真实的用户验证逻辑
4. 返回完整的用户信息

## 📊 真实 vs 简化版本对比

| 功能 | 简化版本 | 真实版本 |
|------|---------|---------|
| Web框架 | Flask | Tornado |
| 数据库ORM | 原生SQL | SQLAlchemy |
| 业务逻辑 | 硬编码 | 完整业务模块 |
| 权限控制 | 无 | 完整权限系统 |
| 数据验证 | 简单验证 | 完整数据模型 |
| 错误处理 | 基础处理 | 完整异常处理 |
| 日志记录 | 无 | 完整日志系统 |

## 🎯 开发建议

### 1. 逐步迁移策略

```
阶段1: 使用简化版本快速验证前端
阶段2: 启动真实Tornado服务器
阶段3: 逐个验证真实API接口
阶段4: 完整集成测试
```

### 2. 调试技巧

```bash
# 查看Tornado日志
tail -f manager-server/server/logs/server.log

# 调试数据库连接
python -c "from core.mysql import get_engine_by_name; print(get_engine_by_name('master'))"

# 测试单个模块
python -c "from modules.account.handler import LoginHandler; print('模块加载成功')"
```

### 3. 常见问题解决

**问题1: 模块导入错误**
```bash
# 解决方案: 设置PYTHONPATH
export PYTHONPATH=$PYTHONPATH:./manager-server/server
```

**问题2: 数据库连接失败**
```bash
# 解决方案: 检查数据库路径
ls -la ../../data/connectai.db
```

**问题3: 端口冲突**
```bash
# 解决方案: 修改端口配置
# 在config.py中修改SERVER_PORT
```

## 🔄 完整开发流程

### 1. 环境准备
```bash
# 安装依赖
cd manager-server
pip install -r requirements.txt

# 检查数据库
python ../../check_environment.py
```

### 2. 启动真实服务器
```bash
# 启动Tornado服务器
cd manager-server/server
python server.py
```

### 3. 启动前端
```bash
# 启动前端开发服务器
cd ConnectAI-E-AdminPanel
pnpm run dev
```

### 4. 验证集成
```bash
# 测试完整流程
1. 前端登录: http://localhost:3200
2. 检查API调用: 开发者工具网络面板
3. 验证数据库: 查看SQLite数据
```

## 📝 总结

**使用真实Tornado服务器的优势:**

1. ✅ **验证真实业务逻辑**: 不是重新实现，而是使用原有代码
2. ✅ **完整功能测试**: 包含权限、数据验证、错误处理等
3. ✅ **真实数据库集成**: 使用SQLAlchemy ORM和真实数据模型
4. ✅ **生产环境一致性**: 与Docker部署的代码完全一致

**下一步行动:**

1. 🔥 **立即尝试**: 启动真实的Tornado服务器
2. 🧪 **验证API**: 测试真实的业务接口
3. 🔧 **修复问题**: 解决CORS和路由问题
4. 🚀 **完整集成**: 前后端使用真实代码协作

**这样您就能验证和开发真正的ConnectAI功能了！** 🎉
