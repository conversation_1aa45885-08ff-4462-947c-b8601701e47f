import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3 24.25a.75.75 0 0 0 1.5 0V3.75a.75.75 0 0 0-1.5 0v20.5zm22-17.5A3.75 3.75 0 0 0 21.25 3H10.5a1 1 0 0 0-1 1v20a1 1 0 0 0 1 1h10.75A3.75 3.75 0 0 0 25 21.25V6.75zM11 18.5h6v5h-6v-5zm6-1.5h-6v-6h6v6zm1.5 1.5h5v2.75a2.25 2.25 0 0 1-2.25 2.25H18.5v-5zm5-1.5h-5v-6h5v6zm0-10.25V9.5h-5v-5h2.75a2.25 2.25 0 0 1 2.25 2.25zM17 4.5v5h-6v-5h6z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TableStackLeft28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
