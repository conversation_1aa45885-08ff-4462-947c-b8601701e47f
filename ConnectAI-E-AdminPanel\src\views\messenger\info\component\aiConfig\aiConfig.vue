<template>
  <div
    v-if="loading"
    class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <n-space vertical>
      <n-skeleton height="40px" width="33%" :sharp="false" />
      <n-skeleton height="60px" :sharp="false" />
      <n-skeleton height="60px" />
      <n-skeleton height="60px" />
      <n-skeleton height="40px" width="100px" :sharp="false" />
    </n-space>
  </div>
  <div
    v-else
    class="p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
  >
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold dark:text-white">{{ t('message.messenger.aikfpz') }}</h3>
      <n-switch
        :value="messengerInfo.ai"
        @update-value="handleAiSiwtch"
        :checked-value="1"
        :unchecked-value="0"
      >
        <template #checked>{{ t('message.messenger.yqyai') }}</template>
        <template #unchecked>{{ t('message.messenger.ygbai') }}</template></n-switch
      >
    </div>
    <div class="font-normal text-gray-500 dark:text-gray-400 min-h-12">
      {{ $t('message.messenger.pztitle') }}
    </div>

    <div class="flex flex-wrap content-start items-start gap-[30px] mb-[30px]">
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <component :is="iconRender({ localIcon: 'feishu' })" class="text-36px" />
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ client.name }}
              </h5>
            </div>
            <div>
              <n-tag v-if="client.tenant_status === null" round :bordered="false" class="cursor-pointer">
                {{ t('message.my.dpz') }}
                <template #icon>
                  <n-icon :component="StopCircleSharp" />
                </template>
              </n-tag>
              <n-tag v-else round :bordered="false" type="success" class="cursor-pointer">
                {{ t('message.my.ypz') }}
                <template #icon>
                  <n-icon :component="CheckmarkCircle" />
                </template>
              </n-tag>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            {{ $t('message.messenger.larkinfo') }}
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleDetail()"
            >
              {{ client.tenant_status === null ? t('message.market.ljpz') : t('message.my.xgpz') }}
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ t('message.messenger.glzsk') }}
              </h5>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            <n-select
              v-model:value="data.collection_id"
              filterable
              size="large"
              class="block w-full"
              :placeholder="t('message.my.knowledge_placeholder')"
              clearable
              :options="options"
            />
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleSaveSetting()"
            >
              <icon-akar-icons-save class="mr-2" />

              {{ t('message.messenger.save') }}
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow">
        <div class="p-4 rounded-t-lg w-[355px]" alt="product image">
          <div class="flex justify-between items-center gap-2 mb-4">
            <div class="flex justify-start items-center gap-2">
              <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">
                {{ t('message.messenger.aizygl') }}
              </h5>
            </div>
          </div>
          <div class="mb-4 font-normal text-gray-500 dark:text-gray-400 min-h-12">
            <n-cascader
              :value="data.model_id"
              :options="resourceOptions"
              check-strategy="child"
              filterable
              @update:value="handleResourceChange"
            />
          </div>
          <div class="flex justify-end items-center gap-2">
            <div
              class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click.stop="handleSaveSetting()"
            >
              <icon-akar-icons-save class="mr-2" />

              {{ t('message.messenger.save') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <Prompt v-model:data="data" :loading="loading" :ai-info="aiInfo" />
  </div>

  <n-modal v-model:show="show" :mask-closable="true" :close-on-esc="true" :auto-focus="false">
    <Config v-model:data="messengerBot" :ai-info="aiInfo" @close="show = false" />
  </n-modal>
</template>

<script setup lang="ts">
import { onMounted, ref, provide, computed, toRefs } from 'vue';
import { useMessage } from 'naive-ui';
import ClipboardJS from 'clipboard';
import { CheckmarkCircle, StopCircleSharp } from '@vicons/ionicons5';
import { useIconRender } from '@/composables';
import { getAppClientBot, updateAppSetting, updateMessenger } from '@/service/api/messenger';
import { useVModel } from '@vueuse/core';
import { fetchDatasetList } from '@/service/api/knowledge';
import { useRoute } from 'vue-router';
import Prompt from './prompt.vue';

import Config from './config.vue';
import type { CascaderOption } from 'naive-ui';

import { t } from '@/locales';

provide('close', close);

const route = useRoute();
const { iconRender } = useIconRender();
const messengerBot = ref<ApiMessenger.MessengerBot>();
const show = ref(false);
const options = ref<{ label: string; value: string }[]>([]);

const props = defineProps<{
  data: ApiApp.APPSetting;
  aiInfo: ApiApp.AppInfo;
  client: ApiApp.AppClient;
  loading: boolean;
  resource: ApiApp.AppResource[];
  messengerInfo: ApiMessenger.MessengerChatInfoDetails;
}>();

const { aiInfo, client, resource, messengerInfo } = toRefs(props);

const emit = defineEmits(['change', 'update:data']);
const data = useVModel(props, 'data', emit);

const resourceOptions = computed(() => {
  return resource.value.map((item) => {
    const children = item?.models?.map((r) => {
      return {
        label: r.name,
        value: r.id
      };
    });
    return {
      label: item.name,
      value: item.id,
      children
    };
  });
});

async function getDatasetList() {
  try {
    const res = await fetchDatasetList({
      page: 1,
      size: 99999
    });
    options.value = res.data?.data?.map((item) => ({ value: item.id, label: item.name }));
  } catch (err) {
    console.error(err);
  }
}

const message = useMessage();

async function handleDetail() {
  const instance_id = aiInfo.value.id;
  const {
    data: { data: clientBot }
  } = await getAppClientBot({ id: instance_id, botId: client.value.id });
  messengerBot.value = { ...clientBot };
  show.value = true;
}

function close() {
  show.value = false;
}

async function handleSaveSetting() {
  const instance_id = aiInfo.value.id;

  await updateAppSetting({
    id: instance_id,
    data: data.value
  });
  message.success(t('message.msg.bccg'));
}

async function handleAiSiwtch(value: 1 | 0) {
  await updateMessenger({
    messenger_id: route.query.id as string,
    ai: value
  });
  emit('change')
  message.success(t('message.msg.bccg'));
}

function handleResourceChange(
  value: string | number | Array<string | number> | null,
  option: CascaderOption | Array<CascaderOption | null> | null,
  pathValues: Array<CascaderOption>
) {
  const [resource, model] = pathValues;
  data.value.resource_id = resource.value as string;
  data.value.model_id = model.value as string;
}

onMounted(() => {
  getDatasetList();

  // eslint-disable-next-line no-new
  new ClipboardJS('.copy-btn');
});
</script>

<style scoped></style>
