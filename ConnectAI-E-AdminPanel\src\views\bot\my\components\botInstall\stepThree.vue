<template>
  <div
    class="min-w-[500px] w-full max-w-sm p-4 bg-white border border-gray-200 rounded-lg shadow sm:p-6 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="mb-2 flex-center">
      <h5 class="text-base font-semibold text-gray-900 md:text-xl dark:text-white">{{ t('message.my.aizy') }}</h5>
      <button
        type="button"
        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
        @click="closeModel"
      >
        <svg
          aria-hidden="true"
          class="w-5 h-5"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          ></path>
        </svg>
        <span class="sr-only">Close modal</span>
      </button>
    </div>
    <n-alert type="info" class="mb-4">
      <template #header>
        <div class="flex justify-between text-sm">
          <div class="flex-1 pr-2">{{ t('message.my.wkyzy') }}</div>
          <div class="">
            <a href="#" class="text-blue-700" @click="handleGotoAIResource">{{ t('message.my.qwpz') }}</a>
          </div>
        </div>
      </template>
    </n-alert>
    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">{{ t('message.my.xzaizy') }}</p>
    <div class="mt-4 space-y-3">
      <ResourcesForm ref="formRef" v-model:data="selectedResources" :resources="resources" />
      <!-- <n-select v-else v-model:value="selectedResource" :options="options" :placeholder="t('message.my.qxzaizy')" /> -->
    </div>
    <div class="text-gray-500 mt-4">
      {{ t('message.my.bzd') }}，<a
        href="https://www.connectai-e.com/contact"
        target="_blank"
        class="text-blue-600 hover:underline"
      >
        {{ t('message.my.tjkf') }}
      </a>
    </div>
    <div class="mt-4 flex justify-end">
      <button
        type="button"
        class="py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
        @click="handlePrevStep"
      >
        {{ t('message.my.syb') }}
      </button>

      <button
        type="button"
        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
        :disabled="disabled"
        @click="handleNextStep"
      >
        {{ t('message.my.xyb') }}
      </button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, inject, toRefs, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { useRouterPush } from '@/composables';
import { updateAppStatus, updateAppSetting } from '@/service/api/app';
import { t } from '@/locales';
import ResourcesForm from '@/views/bot/info/component/resourcesForm.vue';
import { isEmpty } from 'lodash-es';

const { routerPush } = useRouterPush();

const close = inject<() => void>('close');

const props = defineProps<{
  step: number;
  resource: ApiApp.AppResource[];
  resources: ApiApp.AppResources[];
  data: ApiApp.APPSetting;
  app: ApiApp.Application;
}>();
const emit = defineEmits(['update:step', 'update:data']);

const formRef = ref();

const selectedResource = ref();

const selectedResources = ref({});

const step = useVModel(props, 'step', emit);

const data = useVModel(props, 'data', emit);

const { app, data: appSettingValues } = toRefs(props);

const disabled = computed(() => {
  if (isEmpty(props.resources)) {
    return !selectedResource.value;
  }
  return isEmpty(selectedResources.value);
});

const options = props.resource.map((item) => ({
  label: item.name,
  value: item.id
}));

async function handlePrevStep() {
  data.value.resource_id = '';
  step.value -= 1;
}

async function handleNextStep() {
  if (isEmpty(props.resources)) {
    data.value.resource_id = selectedResource.value;
  } else {
    await formRef.value.validate();
    data.value.resource_ids = selectedResources.value;
  }
  const id = app.value.id;
  await updateAppStatus({ id, action: 'deploy' });
  await updateAppSetting({ id, data: appSettingValues.value });
  step.value += 1;
}

function closeModel() {
  close?.();
}

function handleGotoAIResource(e: Event) {
  e.preventDefault();
  routerPush({ name: 'dashboard_ai' }, false);
}

onMounted(async () => {});
</script>
<style scoped>
.n-alert:deep(.n-alert-body) {
  padding-top: 8px;
  padding-bottom: 8px;
}

.n-alert:deep(.n-alert__icon) {
  margin-top: 6px;
  margin-left: 10px;
}
</style>
