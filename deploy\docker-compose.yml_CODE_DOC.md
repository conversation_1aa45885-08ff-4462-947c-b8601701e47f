# 代码文档 - docker-compose.yml

## 文件作用
生产环境的Docker容器编排配置，定义完整的微服务架构部署方案。

## 服务架构

### 核心应用服务
- **manager**: 主管理服务
  - 镜像: connectai-manager:1.0
  - 端口: 3000
  - 域名: manager.connect.ai
  - 挂载: 静态文件、配置文件

- **admin**: 管理后台服务
  - 镜像: connectai-manager:1.0
  - 域名: admin.connect.ai
  - 特殊: 安装Gradio并运行admin.py

### 消息队列消费者
- **appconsumer**: 应用消息消费者
- **feishuconsumer**: 飞书消息消费者
- **dingdingconsumer**: 钉钉消息消费者
- **weworkconsumer**: 企业微信消息消费者
- **messengerconsumer**: 通用消息消费者

### 基础设施服务

#### 数据存储
- **mysql**: 主数据库
  - 镜像: mysql:5.7
  - 数据库: connectai-manager
  - 密码: connectai2023
  - 字符集: utf8mb4

- **redis**: 缓存数据库
  - 镜像: redis:alpine
  - 端口: 6379

- **elasticsearch**: 搜索引擎
  - 镜像: elasticsearch:8.9.0
  - 内存: 512MB
  - 单节点模式
  - 禁用安全认证

#### 消息队列
- **rabbitmq**: 消息队列服务
  - 镜像: rabbitmq:3.7-management-alpine
  - 用户名/密码: rabbitmq/rabbitmq
  - 管理界面: rabbitmq-manager.connectai.ai:15672

#### 通信服务
- **nchan**: 实时通信服务
  - 镜像: lloydzhou/nchan
  - 域名: nchan.connect.ai
  - 配置: nchan.conf

### 知识服务
- **know-server**: 知识管理服务
  - 镜像: know-server:es
  - 域名: know.connect.ai
  - OpenAI配置: Azure端点
  - 文件上传: 100MB限制
  - ES集成: elasticsearch:9200

### 反向代理
- **proxy**: Nginx反向代理
  - 镜像: jwilder/nginx-proxy:alpine
  - 端口映射: 8080:80, 8081:81, 8082:82
  - 自动服务发现
  - 静态文件服务

## 网络配置
- 所有服务使用Docker内部网络通信
- 通过VIRTUAL_HOST实现域名路由
- 代理服务统一对外暴露端口

## 数据持久化
- MySQL数据: ./data/mysql
- Elasticsearch数据: ./data/elasticsearch
- RabbitMQ数据: ./data/rabbitmq
- 文件存储: ./files

## 技术特点
- 微服务架构
- 容器化部署
- 自动服务发现
- 数据持久化
- 负载均衡支持
