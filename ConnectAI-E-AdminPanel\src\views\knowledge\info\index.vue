<template>
  <div class="">
    <n-card class="h-full shadow-sm rounded-16px">
      <template #header>
        <div class="w-full flex justify-between items-center">
          <form>
            <label for="default-search" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
              >Search</label
            >
            <div class="relative max-w-[400px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg
                  aria-hidden="true"
                  class="w-5 h-5 text-gray-500 dark:text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path>
                </svg>
              </div>
              <input
                id="default-search"
                v-model="keyword"
                type="search"
                class="block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                :placeholder="t('message.knowledge.qsrdoc')"
              />
              <button
                type="submit"
                class="text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                @click="(e) => handleSearch(e)"
              >
                {{ t('message.knowledge.search') }}
              </button>
            </div>
          </form>
          <div class="flex justify-end gap-4 items-center min-w-280px" v-if="deleteable">
            <n-popover v-if="taskList.length > 0" trigger="hover" placement="top">
              <template #trigger>
                <n-badge :value="taskList.length" processing> </n-badge>
              </template>
              <div class="flex flex-col gap-2">
                <n-spin>
                  <div v-for="item in taskList" :key="item.taskId">
                    {{ item.name }}
                  </div>
                </n-spin>
              </div>
            </n-popover>

            <n-dropdown trigger="click" :options="options" @select="handleSelect">
              <button
                type="button"
                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                :disabled="uploadCloudDocLoading"
              >
                {{ t('添加在线云文档') }}
              </button>
            </n-dropdown>

            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
              @click="handleAddPDocument"
            >
              <icon-akar-icons-circle-plus class="mr-2" />
              {{ t('message.knowledge.mkdir') }}
            </button>
          </div>
          <div class="flex justify-end gap-4 items-center min-w-280px" v-if="!deleteable">
            <span class="text-sm mr-2">本知识库同步自飞书，请在对应飞书知识库中管理文档</span>
          </div>
        </div>
      </template>
      <loading-empty-wrapper class="min-h-350px" :loading="loading" :empty="empty">
        <DatasetTable
          v-if="!loading && !empty"
          :data="data"
          :pagination-options="paginationOptions"
          :deleteable="deleteable"
          @handle-delete="handleDelete"
        />
      </loading-empty-wrapper>
    </n-card>

    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        :title="t('message.knowledge.mkdir')"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-p depth="3" style="margin: 0 0 8px 0"> {{ t('message.knowledge.tip1') }} </n-p>
        <n-upload
          :show-remove-button="false"
          multiple
          directory-dnd
          :action="action"
          :custom-request="customRequest"
          :max="5"
        >
          <n-upload-dragger>
            <div style="display: flex">
              <div>
                <icon-akar-icons-file class="text-6xl" />
              </div>
              <div style="flex: 1">
                <n-text style="font-size: 16px"> {{ t('message.knowledge.tip2') }} </n-text>
                <n-p depth="3" style="margin: 8px 0 0 0"> {{ t('message.knowledge.tip3') }} </n-p>
              </div>
            </div>
          </n-upload-dragger>
        </n-upload>
        <template #footer>
          <div class="text-center">
            <button
              type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              @click="handleClose"
            >
              {{ t('message.knowledge.end') }}
            </button>
            <n-p depth="3" style="margin: 8px 0 0 0">{{ t('message.knowledge.close') }}</n-p>
          </div>
        </template>
      </n-card>
    </n-modal>
    <FeishuDocModal
      v-model:show-lark-doc="showLarkDocModal"
      v-model:show-tips="showTipsModal"
      v-model:data="botSetting"
      @after-upload="handleAfterUpload"
    />
    <YuQueDocModal v-model:show-yu-que="showYuQue" v-model:data="yuqueConfig" @after-upload="handleAfterUpload" />
    <NotionDocModal v-model:show-notion="showNotion" v-model:data="notionConfig" @after-upload="handleAfterUpload" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onBeforeUnmount, provide, h, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import type { UploadCustomRequestOptions } from 'naive-ui';
import axios from 'axios';
import { useIconRender } from '@/composables';
import { useLoadingEmpty, usePagination } from '@/hooks';
import {
  fetchDocuments,
  uploadDocument,
  getDocumentStatus,
  removeDocument,
  fetchDatasetBotConfig,
  fetchDatasetYuQueConfig,
  fetchDatasetNotionConfig,
  fetchDatasetInfo
} from '@/service/api/knowledge';
import DatasetTable from './components/dataset-table.vue';
import { t } from '@/locales';
import { isEmpty } from 'lodash-es';
import FeishuDocModal from './components/feishu-doc-modal.vue';
import type { Component } from 'vue';
import YuQueDocModal from './components/yuque-doc-modal.vue';
import NotionDocModal from './components/notion-doc-modal.vue';

const { iconRender } = useIconRender();

const route = useRoute();

const action = import.meta.env.PROD === false ? '/api/api/upload' : '/api/upload';

const { loading, startLoading, endLoading, empty, setEmpty } = useLoadingEmpty();
const data = ref<ApiDataset.Document[]>([]);

const itemCount = ref<number>(0);
const { pagination, paginationOptions } = usePagination({ itemCount });

const keyword = ref('');

const message = useMessage();

const showModal = ref(false);

const taskList = ref<{ name: string; taskId: string }[]>([]);

const timers = ref<NodeJS.Timer[]>([]);

const datasetId = route.query.id as string;
const typeMapping = {
  pdf: ['.pdf'],
  word: ['.doc', '.docx'],
  excel: ['.xls', '.xlsx'],
  markdown: ['.md'],
  ppt: ['.ppt', '.pptx'],
  txt: ['.txt'],
  sitemap: ['.xml']
};

const showTipsModal = ref(false);

const showLarkDocModal = ref(false);

const uploadCloudDocLoading = ref(false);

const botSetting = ref({
  platform: 'feishu'
});

const showYuQue = ref(false);

const yuqueConfig = ref({
  token: ''
});

const showNotion = ref(false);

const notionConfig = ref({
  token: ''
});
const info = ref();
const deleteable = computed(() => info.value && info.value.type !== 'feishuwiki');
function getDatasetInfo() {
  fetchDatasetInfo(datasetId).then(({ data }) => {
    info.value = data.data;
  });
}

const renderIcon = (icon: Component) => {
  return () => {
    return h(icon, null);
  };
};
const options = [
  {
    label: '飞书云文档',
    key: 'feishu',
    icon: renderIcon(iconRender({ localIcon: 'feishu' }))
  },
  {
    label: '语雀云文档',
    key: 'yuque',
    icon: renderIcon(iconRender({ localIcon: 'yuque' }))
  },
  {
    label: 'notion云文档',
    key: 'notion',
    icon: renderIcon(iconRender({ localIcon: 'notion' }))
  }
];

provide('close', () => (showTipsModal.value = false));

watch(pagination, () => {
  getDocumentsList();
});

async function getDocumentsList(keyword?: string) {
  startLoading();
  try {
    const res = await fetchDocuments(datasetId, {
      ...pagination,
      keyword
    });
    data.value = res.data?.data || [];
    data.value = data.value.map(item => {
      item.path = item.path.replace('http://know-server', window.location.origin)
      return item
    })
    itemCount.value = res.data!.total;
    endLoading();
    setEmpty(data.value.length === 0);
  } catch (err) {
    console.error(err);
  }
}

function handleSearch(e: Event) {
  e.preventDefault();

  getDocumentsList(keyword.value);
}

function handleAddPDocument() {
  showModal.value = true;
}

function handleClose() {
  showModal.value = false;
}

async function customRequest(options: UploadCustomRequestOptions) {
  const { file, onFinish, onError, onProgress } = options;
  const formData = new FormData();
  formData.append('file', file.file as File);
  const fileExtension = file.name?.split?.('.')?.pop()?.toLowerCase();
  const fileType = getFileType(`.${fileExtension}`);
  try {
    const { data } = await axios.post(action, formData, {
      withCredentials: true,
      onUploadProgress(e) {
        if (e.lengthComputable) {
          const percent = Math.round((e.loaded * 100) / e.total);
          onProgress({ percent });
        }
      }
    });
    const url = data?.url;
    if (url) {
      const res = await uploadDocument({
        id: datasetId,
        data: {
          fileUrl: url,
          fileType
        }
      });
      const taskId = res.data.data?.task_id;
      taskList.value.push({ name: file.name, taskId });
      pollTaskListStatus();
      message.success(t('message.knowledge.drcg'));
    }
    onFinish();
  } catch (err) {
    onError();
  }
}

function getFileType(name: string) {
  for (const [type, extensions] of Object.entries(typeMapping)) {
    if (extensions.includes(name)) {
      return type;
    }
  }
  return null; // 如果没有匹配的类型，则返回 null 或其他适当的默认值
}

function pollTaskListStatus() {
  taskList.value.forEach(({ taskId }) => {
    const timer = setInterval(async () => {
      const { data } = await getDocumentStatus({ id: datasetId, taskId });
      if (data.data?.status === 'SUCCESS') {
        clearInterval(timer);
        const findIndex = taskList.value.findIndex((item) => item.taskId === taskId);
        if (findIndex !== -1) {
          taskList.value.splice(findIndex, 1);
        }
        getDocumentsList();
        message.success(t('message.knowledge.jxwc'));
      }
    }, 1000);
    timers.value.push(timer);
  });
}

async function handleDelete({ id }: { id: string }) {
  await removeDocument({ collection_id: datasetId, id });
  getDocumentsList(keyword.value);
}

onBeforeUnmount(() => {
  timers.value.forEach((timer) => {
    clearInterval(timer);
  });
});

function handleAddLarkDocument() {
  const { callback_url, platform, ...rest } = botSetting.value;
  if (isEmpty(rest)) {
    showTipsModal.value = true;
  } else {
    showLarkDocModal.value = true;
  }
}
async function getDatasetBotConfig() {
  try {
    uploadCloudDocLoading.value = true;
    const { data } = await fetchDatasetBotConfig();
    botSetting.value = {
      ...data?.data,
      platform: 'feishu'
    };
    uploadCloudDocLoading.value = false;
  } catch (error) {
    uploadCloudDocLoading.value = false;
  }
}

async function getchDatasetYuQueConfig() {
  try {
    uploadCloudDocLoading.value = true;
    const { data } = await fetchDatasetYuQueConfig();
    yuqueConfig.value = {
      token: data?.data?.token
    };
    uploadCloudDocLoading.value = false;
  } catch (error) {
    uploadCloudDocLoading.value = false;
  }
}

async function getchDatasetNotionConfig() {
  try {
    uploadCloudDocLoading.value = true;
    const { data } = await fetchDatasetNotionConfig();
    notionConfig.value = {
      token: data?.data?.token
    };
    uploadCloudDocLoading.value = false;
  } catch (error) {
    uploadCloudDocLoading.value = false;
  }
}

function handleAfterUpload({ name, taskId }: { name: string; taskId: string }) {
  taskList.value.push({ name, taskId });
  pollTaskListStatus();
}

function handleSelect(key: string) {
  if (key === 'feishu') {
    handleAddLarkDocument();
  } else if (key === 'yuque') {
    showYuQue.value = true;
  } else if (key === 'notion') {
    showNotion.value = true;
  }
}
onMounted(() => {
  getDocumentsList();
  getDatasetBotConfig();
  getchDatasetYuQueConfig();
  getchDatasetNotionConfig();
  getDatasetInfo();
});
</script>
