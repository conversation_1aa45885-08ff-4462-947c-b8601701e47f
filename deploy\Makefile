
all:
	sudo docker-compose up -d

elasticsearch-plugin:
	sudo docker cp elasticsearch-analysis-ik-8.9.0.zip build-elasticsearch-1:/tmp/
	sudo docker-compose exec elasticsearch bin/elasticsearch-plugin install file:///tmp/elasticsearch-analysis-ik-8.9.0.zip
	sudo docker-compose exec elasticsearch bin/elasticsearch-plugin list
	sudo docker-compose restart elasticsearch

manager:
	sudo docker cp build-manager-1:/server/scripts/initData.py ./
	sudo vim initData.py
	sudo docker cp initData.py build-manager-1:/server/scripts/initData.py
	sudo docker-compose restart manager
	sudo docker-compose exec manager python server/scripts/init_application.py

FORCE:
