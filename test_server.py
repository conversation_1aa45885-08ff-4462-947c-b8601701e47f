#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试服务器
"""

import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class TestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "message": "ConnectAI Test Server",
                "status": "running",
                "services": {
                    "manager-server": "ready",
                    "datachat-api": "ready"
                }
            }
            self.wfile.write(json.dumps(response, indent=2).encode())
        elif self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {"status": "healthy"}
            self.wfile.write(json.dumps(response).encode())
        else:
            super().do_GET()

if __name__ == "__main__":
    PORT = 8000
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"🚀 测试服务器启动在 http://localhost:{PORT}")
        print("访问 http://localhost:8000 查看状态")
        print("访问 http://localhost:8000/health 查看健康状态")
        print("按 Ctrl+C 停止服务器")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n⏹️  测试服务器已停止")
