<template>
  <div
    class="w-[340px] h-[320px] bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow"
  >
    <div class="p-8 rounded-t-lg flex-center h-[180px]" alt="product image">
      <component :is="iconRender({ cdnIcon: icon, fontSize: 160 })" />
    </div>
    <div class="px-5 pb-5">
      <div class="i-flex-y-center gap-2">
        <h5 class="text-xl font-semibold tracking-tight text-gray-900 dark:text-white">{{ title }}</h5>
      </div>
      <div class="flex justify-start mt-2.5 mb-5">
        <span
          v-if="name"
          class="bg-indigo-100 text-indigo-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-indigo-900 dark:text-indigo-300"
          >{{ name }}</span
        >
        <span
          class="bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 ml-2"
          >{{ $t('message.market.feishu') }}</span
        >
        <span
          class="bg-gray-100 text-gray-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300 ml-2"
          >{{ $t('message.market.dingding') }}</span
        >
        <span
          v-if="deny(`app.buy.${id}`)"
          class="bg-yellow-100 text-yellow-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300 ml-2"
          >{{ UPGRADE_LABEL }}</span
        >
      </div>
      <div class="flex items-center justify-between">
        <div>
          <div v-if="!tenantStatus" class="text-2xl font-bold">
            <span v-if="price === 0" class="dark:text-white">{{ $t('message.market.free') }}</span>
            <span v-else class="text-[#DE350B]">¥ {{ price.toFixed(2) }}</span>
          </div>
        </div>

        <div
          v-if="!tenantStatus"
          class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          @click.stop="handleBuy"
        >
          {{ $t('message.market.ljhq') }}
        </div>
        <div
          v-else-if="tenantStatus === 1"
          class="text-white cursor-pointer bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
          @click.stop="handleInstall"
        >
          {{ $t('message.market.az') }}
        </div>
        <div
          v-else-if="tenantStatus === 2"
          class="text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          @click.stop="handleDetail(id)"
        >
          {{ $t('message.market.ckxq') }}
        </div>
        <div
          v-else-if="tenantStatus === -1"
          class="text-white cursor-not-allowed bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
        >
          {{ $t('message.market.jjfb') }}
        </div>
      </div>
    </div>
    <!-- 弹框 -->
    <purchase-tip v-model:value="showTip" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { UPGRADE_LABEL } from '@/constants';
import { routeName } from '@/router';
import { useIconRender, useRouterPush } from '@/composables';
import { useTenantPrivilege } from '@/hooks';

const { allow, deny } = useTenantPrivilege();
const showTip = ref(false);
const { iconRender } = useIconRender();

interface Props {
  title: string;
  price: number;
  tenantStatus: number;
  description: string;
  name: string;
  id: string;
  icon: string;
}

defineOptions({ name: 'BotCard' });
const props = defineProps<Props>();
const emit = defineEmits(['handle-buy', 'handle-install']);
const { routerPush } = useRouterPush();

function handleBuy() {
  if (allow(`app.buy.${props.id}`)) {
    emit('handle-buy');
  } else {
    showTip.value = true;
  }
}

function handleInstall() {
  emit('handle-install');
}

function handleDetail(id: string) {
  routerPush({ name: routeName('bot_info'), query: { id } });
}
</script>

<style scoped lang="scss">
.cardShadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
  &:hover {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    transition: all 0.3s ease-in-out;
  }
}
</style>
