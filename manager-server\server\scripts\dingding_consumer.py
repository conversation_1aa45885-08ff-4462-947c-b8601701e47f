# -*- coding: utf-8 -*-
import json
import logging
from base import *
from tornado.options import options, parse_command_line
from core.consumer import Base<PERSON>onsumer, LOGGER, LOG_FORMAT
from core.rabbitmq import MqSession
from core.dingding import TextMessage, MarkdownMessage, DingDing
from modules.callback.model import DingDingModel
from settings.constant import AppResult


class DingDingConsumer(BaseConsumer):

    async def process_message(self, tag, app_id, data, **kwargs):
        try:
            logging.info("DEBUG %r", data)
            # TODO 
            logging.info("DEBUG result_type %r", data.result_type)
            with DingDingModel() as model:
                model.init_by_bot_id(data.extra.bot_instance_id)
                if data.result_type == AppResult.ReplyText.value:
                    await model.client.reply_text(data.extra.extra.sessionWebhook, data.result_content)
                elif data.result_type == AppResult.ReplyTuWenCard04.value:
                    await model.client.send_card(
                        openConversationId=data.extra.chat_id,
                        robotCode=data.extra.extra.robotCode,
                        cardData=data.result_content,
                    )
                elif data.result_type == AppResult.ReplyActionCard.value:
                    await model.client.reply(
                        data.extra.extra.sessionWebhook,
                        data.result_content,
                    )
                elif data.result_type == AppResult.CardWithButton.value:
                    # TODO 测试markdown消息
                    await model.client.reply_markdown(data.extra.extra.sessionWebhook, 'markdown title', data.data)
            self.acknowledge_message(tag)
        except Exception as e:
            LOGGER.exception(e)
            self.reject_message(tag, requeue=False)
            return False


def main():
    logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)

    logging.info("DEBUG %r", options.RABBIT_MQ_URI)
    consumer = DingDingConsumer(
        options.RABBIT_MQ_URI,
        queue=options.QUEUE_DINGDING,
        exchange=options.RABBIT_MQ_EXCHANGE,
        prefetch_count=10,
    )
    try:
        consumer.run()
    except KeyboardInterrupt:
        consumer.stop()


if __name__ == '__main__':
    main()

