import logging
from base import *
from tornado.options import options, parse_command_line
from core.consumer import <PERSON>Consumer, LOGGER, LOG_FORMAT
from core.web import Web
from core.utils import _
from core.feishu import (
    FeishuMessageText,
    FeishuMessageCard,
    FeishuMessageNote,
    FeishuMessagePlainText,
    FeishuMessageDiv
)
from settings.constant import AppResult
from modules.messenger.model import MessengerModel, MessengerSession
from modules.callback.model import FeishuModel


class MessengerConsumer(BaseConsumer):

    async def process_message(self, tag, app_id, data, **kwargs):
        try:
            logging.info("DEBUG %r", data)
            with MessengerModel() as model:
                # 这里的bot_id实际上是messenger.bot_id
                model.init_by_bot_id(data.bot_id)
                if data.result_type == AppResult.ReplyText.value:
                    # 1. 发送消息到飞书
                    with FeishuModel() as fsmodel:
                        fsmodel.init_by_bot_id(data.extra.bot_instance_id)
                        response = await fsmodel.client.reply(
                            data.extra.message_id,
                            FeishuMessageText(data.result_content),
                            msg_type='text'
                        )
                        logging.info("DEBUG result %r", response)
                        # 2. 发送消息到im server，并添加到session里面
                        message = Web.create_message_from_feishu(response.data)
                        with MessengerSession(data.bot_id, data.visitor_id) as session:
                            await model.client.pubsub('POST', data.bot_id, data.visitor_id, message)
                            session.add_message(message)
                elif data.result_type == AppResult.MessengerSystem.value:
                    # 转发系统通知消息
                    message = Web.create_system_message(data.result_content)
                    await model.client.pubsub('POST', data.bot_id, data.visitor_id, message)
                elif data.result_type == AppResult.ForwardMessengerSeat.value:
                    with FeishuModel() as fsmodel:
                        fsmodel.init_by_bot_id(data.extra.bot_instance_id)
                        with MessengerSession(data.bot_id, data.visitor_id) as session:
                            try:
                                # 找到配置的座席人员
                                open_id, name = await model.choice_seat(model.messenger.id)
                                reply_text = f'''{_('转人工：', lang=data.lang)}<at id={open_id}></at>'''
                                # 之前计划使用卡片，尝试在卡片中发@消息失败，还是使用文本消息
                                # result = await fsmodel.client.reply_text(session.root_message_id, reply_text)
                                result = await fsmodel.client.reply(
                                    session.root_message_id,
                                    FeishuMessageCard(FeishuMessageDiv(reply_text, tag="lark_md", quote=False)),
                                    msg_type='interactive',
                                )
                                logging.info('result %r', result)
                                session.switch_to_manual()
                            except Exception as e:
                                logging.error('error %r', e)
                                await fsmodel.client.reply(
                                    session.root_message_id,
                                    FeishuMessageCard(FeishuMessageNote(FeishuMessagePlainText(str(e)))),
                                    msg_type='interactive',
                                )
                else:
                    logging.error('unkown message type')
            self.acknowledge_message(tag)
        except Exception as e:
            LOGGER.exception(e)
            self.reject_message(tag, requeue=False)
            return False


def main():
    logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)

    logging.info("DEBUG %r", options.RABBIT_MQ_URI)
    consumer = MessengerConsumer(
        options.RABBIT_MQ_URI,
        queue=options.QUEUE_MESSENGER,
        exchange=options.RABBIT_MQ_EXCHANGE,
        prefetch_count=10,
    )
    try:
        consumer.run()
    except KeyboardInterrupt:
        consumer.stop()


if __name__ == '__main__':
    main()

