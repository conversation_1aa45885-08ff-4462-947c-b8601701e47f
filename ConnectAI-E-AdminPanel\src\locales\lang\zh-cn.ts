// ts-ignore
import type { LocaleMessages } from 'vue-i18n';

const locale: LocaleMessages<I18nType.Schema> = {
  message: {
    system: {
      title: '企联AI',
      description:
        '企业级低代码AI平台，基于飞书等协作工具，赋能企业中的每一位员工。自主管理AI资产，无忧接入海量行业场景应用。',
      htgl: '企联AI管理后台',
      fxai: '让企业放心的AI',
      jdgx: '简单高效的企业级AI应用编排平台，数据自主管理，无忧接入海量AI应用。',
      rbsbjd: '让部署变得简单易行，让企业用的大胆放心。',
      dl: '请登录您的账户',
      uid: '账号',
      pwd: '密码',
      yyd: '已阅读并同意企联AI的',
      fwxy: '服务协议',
      ystk: '隐私条款',
      login: '登录',
      myzh: '没有账户？',
      djsqcn: '点击申请内测资格',
      and: '和',

      kjdl: '手机登录',
      email: '邮箱注册',
      phone: '请输入手机号登录',
      phoneid: '手机号',
      code: '验证码',
      dlzc: '登录/注册',
      pwdlogin: '密码登录',
      get: '获取验证码',
      lasttime: '秒后重新获取',
      phoneerror: '手机号格式错误！',
      nophone: '手机号码不能为空！',
      emailerror: '邮箱格式错误！',
      noemail: '邮箱不能为空！',
      codesuccess: '验证码发送成功！',
      qsremail: '请输入您的邮箱注册',
      zc: '注册',
      phoneoremail: '手机号或邮箱',
      phonezc:'手机号注册',

      zznc: '正在内测，敬请期待',

      yy: '没有发现数据, 游泳休息一会吧',
      lq: '没有发现数据, 出去打会篮球吧',
      pb: '没有发现数据, 出去跑跑步吧',
      tq: '没有发现数据, 出去踢踢球吧',
      dq: '没有发现数据, 出去打会球吧',
      djcs: '点击重试',
      fhsy: '返回首页',

      teamname:'公司/团队名称',
      qsrmc:'不少于两字',
      qsrgsmc:'请输入公司名称',
      kqaizl:'开启 AI 之旅',
      xgqyxx:'修改企业信息',
      tsinfo:'企业信息会在产品的多个地方展示，请谨慎修改',
      xgxx:'修改信息',
      xgxxinfo:'企业信息会在产品的多个地方展示，请谨慎修改',
      save:'保存',
      qx:'取消',
    },
    messenger: {
      xxsj:'客户信息收集',
      xxsjinfo:'开启后，客户在第一次进行对话时，需要先填写昵称+联系方式方可发送消息',
      helpdoc:'帮助文档配置',
      helpinfo:'帮助文档会展示在官网客服入口首页，供客户前置查看使用',
      qsrbt:'请输入标题',
      qsrlj:'请输入链接',
      lang:'语言',
      zts:'主题色',
      hyy:'欢迎语',
      jcpz:'基础配置',
      fsxxzf:'飞书消息转发机器人',
      fsinfo:'用于连通外部客户和飞书中的客服，配置后需关联话题群进行使用',
      pz:"配置",
      glfshtq:'关联飞书话题群',
      dwsx:'若无话题群可选，点我刷新',
      save:'保存',
      webpz:'官网Web端配置',
      webinfo:'用于在官网中接收客户的咨询信息',
      ckpz:'查看配置',
      apipz:'API接入配置',
      apiinfo:'以API的形式接入',
      tit:'方式一、二任选一种方式复制代码到',
      and:'和',
      bql:'标签里',
      fs1:'方式一',
      fs2:'方式二',
      fs3:'方式三',
      iframe:'使用iframe嵌入',
      qd:'确定',
      qx:'取消',
      kqapi:'开启API',
      tjtxx:'所有的接口都需要添加头信息',
      hqxx:'GET 获取配置',
      fsxx:'POST 发送消息',
      fstp:'POST 发送图片',
      jdry:'接待人员配置',
      xzcy:'新增成员',
      pzhinfo:'配置后，系统将自动分配相应成员进行回复。配置前，请确认完成基础配置部分，并已经关联相应飞书话题群',
      tjkfinfo:'添加客服接待人员，当有客户资讯时，系统会进行分配',
      xzcytj:'选择成员（添加前请确保已关联话题群）',
      kssj:'选择接待开始时间',
      jssj:'选择接待结束时间',
      qsrxm:'请输入姓名',
      qsrnl:'请输入年龄',
      cyxm:'成员姓名',
      zt:'状态',
      on:'启用',
      off:'禁用',
      jdsjfw:'接待时间范围',
      bj:'编辑',
      sc:'删除',
      qxglfs:'请先关联飞书话题群',
      jycg:'禁用成功',
      qycg:'启用成功',
      warn:'警告',
      qrsc:'确认删除该成员吗',
      sccg:'删除成功',
      sxtoken:'刷新Token会导致之前的接口失效，确认刷新吗？',
      title:'开启智能客服新纪元',
      xzaikf:'新增飞书AI客服',
      kf:'客服',
      alert:'请注意填写客服渠道名称，方便后续区分',
      kfmc:'客服名称',
      qsr:'请输入',
      kfms:'客服描述',
      kfqd:'客服渠道',
      gw:'官网',
      dykfz:'抖音（开发中）',
      dy:'抖音',
      aikfpz:'AI客服配置',
      yqyai:'已启用AI能力',
      ygbai:'已关闭AI能力',
      pztitle:'配置后系统将根据配置，优先使用 AI 回复客户咨询寻问题，仅当问题无法回答或者客户要求人工处理时，分配人工进行处理',
      larkinfo:'用于自动回复客户咨询，配置后需关联知识库进行使用',
      glzsk:'关联知识库',
      aizygl:'AI资源关联',

    },
    dashboard: {
      pzsc: '资源配置手册',
      freeuse: '免费试用',
      seven: '7天免费试用',
      ljsq: '立即申请',
      // pricing
      title: '借助企联 AI 给成员赋能',
      zfwt: '申请试用/支付遇到问题',
      title2: '根据需要选择适合的解决方案',
      month: '按季',
      year: '按年',
      yh: '额外优惠20%',
      lxwm: '联系我们',
      xw: '席位',
      xwm: '席位/月',
      gcyh: '享受共创专属优惠',
      yj: '原价',
      ljlx: '立即联系',
      buy: '立即购买',
      wechat: '微信支付',
      alipay: '支付宝',
      openwechat: '打开微信，扫描二维码，完成支付',
      price: '支付金额',
      tip: '若您为版本升级，未到期套餐将自动折现抵扣',
      rmb: '元',
      zhmc: '账户名称',
      gsmc: '武汉企泥科技有限公司',
      zhhm: '账户号码',
      bankid: '银行行号',
      bank: '银行信息',
      bankname: '兴业银行股份有限公司武汉汉正街支行',
      payinfo: '打款完成后，添加客服微信（左）/飞书（右）完成确认',
      suc: '已支付成功，立即开启AI之旅',
      info1: '由于使用配置较为复杂，使用前请详细查看使用教程，配置过程中如果遇到任何问题，都可以联系客服获取帮助',
      info2: '点我添加客服',
      aishop: '前往AI应用市场',
      ckjc: '查看使用教程',

      zxzf: '在线支付',
      dgzz: '对公转账',
      qsrbsmc: '请输入部署名称',
      pldr: '批量导入',
      search_placeholder: '请输入',
      addseat: '添加成员',
      auto_add_seat: '开启自动添加成员后，系统会自动将新使用的员工添加到坐席中，无需手动添加',
      disable_auto_add_seat: '自动添加成员：',
      import_by_extension: '安装插件，快捷添加成员',
      name_label: '姓名',
      name_validate: '请输入姓名',
      max_seats_label: '已使用座席',
      more_seats_label: '提升座席数量',
      telephone_label: '手机号',
      department_label: '部门',
      status_label: '状态',
      status_paused: '暂停',
      status_actived: '正常',
      status_removed: '删除',
      status_active: '启用',
      status_pause: '暂停',
      remove_seat: '删除成员',
      upload_confirm: '确认导入成员',
      upload_title: '批量导入',
      upload_tip: '点击或将文件拖到这里上传',
      template_lable: '请严格按照模板填写',
      unkown: '未知',
      out1: 'AI 相关应用使用需要消耗 token ，',
      out2: '每 1000 万 token 约可生图 350 张，对话 100000 次',
      choose: '请至少选择一个模型',
      fillin: '请填写部署名称',
      zfcg: '支付成功',
      zfsb: '支付失败，请重试'
    },
    market: {
      feishuai: '飞书-OpenAI',
      feishu: '飞书',
      bot: '机器人',
      free: '免费',
      ljbs: '立即部署',
      jjfb: '即将发布',
      dingdingai: '钉钉-OpenAI',
      dingding: '钉钉',
      feishumj: '飞书-Midjourney',
      feishuwxyylt: '飞书-文心一言聊天机器人',
      wxyy: '文心一言',
      gptswbs: 'ChatGPT私网部署版',
      web: 'web',
      feishuzsk: '飞书-内部知识库',
      feishukypl: '飞书雅思口语陪聊',
      feishupdf: '飞书-PDF辅助阅读',
      feishuxxss: '飞书-联网信息搜索',
      feishukefu: '飞书-客服问答',
      feishujiaoyu: '飞书-教育解题',
      wechatai: '企业微信-OpenAI',
      wechat: '企微',
      feishuxnpl: '飞书-虚拟陪聊',
      feishusd: '飞书-StableDiffusion',
      feishubgzs: '飞书-表格助手',
      dwbg: '多维表格',
      lunwenfuzhu: '学术论文辅助',
      buy: '购买',
      az: '立即安装',
      gl: '管理',
      xznxhd: '选择你喜欢的🤞AI应用',
      sryymc: '输入应用名称......',
      ss: '搜索',
      author: '作者',
      River: 'AI 会成为先进团队首选的生产资料',
      sqsjgd: '申请上架更多AI应用',

      ljhq: '立即获取',
      ckxq: '查看详情',
      ljaz: '立即安装',
      ljpz: '立即配置',
      wado: '查看演示',
      copy_success: '链接复制成功'
    },
    my: {
      qrcodedeploy: '扫码部署',
      wxwork_auth_title: '企业微信应用授权',
      wxwork_auth_success: '企业微信应用授权成功',
      wework_trusted_ip_title: '复制下面的IP地址，添加到企业可信IP中',
      wework_trusted_ip_label: '复制IP地址',
      wsxx: '请完善信息',
      xggd: '选购更多',
      xjyy: '新建应用',
      name: '名称',
      lx: '类型',
      all: '全部',
      feishu: '飞书',
      dingding: '钉钉',
      web: '网页',
      dhrz: '对话日志',
      scyy: '删除应用',
      qwbs: '前往部署',
      xq: '详情',
      nqdysc: '您确定要删除此应用吗？',
      qd: '确定',
      qx: '取消',

      czz: '操作中',
      yxz: '运行中',
      dpz: '待配置',
      yty: '已停用',

      bsaiyy: '部署AI应用',
      xzygr: '选择已购入的AI应用，快速部署至您的服务器',
      scxz: '上次选择',
      qwaisd: '前往商店获取更多AI应用',

      jqrqxgl: '机器人权限管理',
      xzkyq: '限制可用群',
      gdltjqr: '规定聊天机器人在部分群聊中可用',
      yxqlmd: '允许群聊名单',
      xzslsy: '限制私聊使用',
      gdltjqrsl:
        '规定聊天机器人在部分私聊中可用，请注意该限制为坐席成员的子集，如果成员不在坐席成员中，则该成员无权限使用应用',
      yxslmc: '允许私聊名单',
      dgyhy: '多个用户用逗号(英文)分隔',
      dgqmy: '多个群名用逗号(英文)分隔',
      bcqx: '保存权限',
      jjzc: '即将支持',
      ckdhrz: '查看对话日志',
      zwnc: '暂无名称',
      zwms: '暂无描述',
      zygl: '资源关联',
      qnxz: '请您选择账户',
      xzmx: '选择模型',
      fxcgl: '风险词关联',
      qnxzfxc: '请您选择风险词主题',
      cjcgl: '场景词关联',
      bczy: '保存资源',
      wxyy: '文心一言',

      cjyh: '超级用户',
      cyhbs: '此用户不受权限管理约束',
      mrgly: '默认管理员',
      jz: '禁止',
      drgly: '第二管理员',
      qy: '启用',

      // creat
      fh: '返回',
      jqrcjzn: '机器人创建指南',
      djqwfs: '点击前往飞书开发者后台',
      djqwdd: '点击前往钉钉开发者后台',
      fs1: '点击创建应用, 选择企业自建应用',
      fs2: '添加应用能力-机器人',
      fs3: '打开权限管理-勾选消息与群组内全部权限',
      fs4: '打开权限管理-允许上传图片资源',
      fs5: '打开权限管理-获取通讯录基本信息',
      fs6: '前往基础信息-复制应用凭证',
      fs7: '前往事件订阅-复制加密秘钥',
      fs8: '事件订阅-配置请求地址',
      fs9: '机器人-配置消息卡片请求地址',
      fs10: '添加事件订阅-消息与群组-消息已读',
      fs11: '添加事件订阅-消息与群组-接收消息',
      fs12: '打开权限管理-查看知识库',
      fs13: '打开权限管理-查看新版文档',
      fs14: '打开权限管理-查看画板节点',

      dd1: '前往钉钉开发者后台',
      dd2: '创建机器人, 选择企业自建应用',
      dd3: '填写机器人的基本信息',
      dd4: '按要求填写机器人的应用凭证',
      dd5: '开发管理-消息接收地址-配置机器人',
      dd6: '版本管理-上线应用',

      wework_tutior_title: '点击前往企业微信开发者后台',
      wework1: '前往企业微信应用管理后台',
      wework2: '创建应用，选择企业自建应用',
      wework3: '填写应用基本信息',
      wework4: '填写应用的基础凭证信息',
      wework5: '接收消息-设置API接收链接及凭证',
      wework6: '配置企业可信IP',
      wework7: '应用上线',

      cjjqr: '创建机器人',
      txpzxx: '填写配置信息',
      hqhddz: '获取回调地址',

      sfwc: '是否完成？',
      yjbs: '一键部署',
      xxyx: '休息一下，稍后创建',
      wyjwc: '我已经完成创建',
      xyb: '下一步',
      yjwcjqr: '已经完成机器人配置?',
      djhqhd: '点击直接获取回调地址',
      plugin: '安装浏览器插件，一键部署 AI 应用（推荐此方式）',
      zxqwkfz: '自行前往开发者平台，创建应用',
      ndfszs: '你的飞书 AI 助手，助你提升工作效率',

      zyxz: '资源选择',
      wyjty: '我已阅读同意企联AI产品的',
      fwxy: '服务协议',
      ystk: '隐私条款',
      jxxyb: '继续下一步',

      jqrcjcg: '机器人创建成功',
      fzxm: '复制下面回调地址至开发者平台',
      fzsjhd: '复制事件回调',
      fzxxjsdz: '复制消息接受地址',
      yfz: '已复制',
      fzkphd: '复制卡片回调',
      ytjjqr: '已添加机器人所需的事件订阅:',
      xxyd: '消息已读',
      jsxx: '接收消息',
      fhyylb: '返回应用列表',
      xyjyb: '需要进一步配置？',
      djqwjqr: '点击前往机器人详情页',

      // feishu
      fsjqrpz: '飞书机器人配置信息',
      jqrmc: '机器人名称',
      cfspthq: '从飞书开发平台获取',
      bcxx: '保存信息',

      hddz: '回调地址',
      djqwtx: '点击前往填写',
      sjhd: '事件回调',
      and: '和',
      kphd: '卡片回调',
      djfz: '点击复制',

      // dingding
      ddjqrpz: '钉钉机器人配置信息',
      cddpthq: '从钉钉开发平台获取',
      xxjsdz: '消息接收地址',

      // new
      fwpz: '服务配置',
      ypz: '已配置',
      cxpz: '重新配置',
      xgpz: '修改配置',
      txjqrpz: '填写机器人配置信息',
      kphddz: '卡片回调地址',
      bcpz: '保存配置',
      bc: '保存',

      xzsfky: '选择是否可用......',
      qsrqmc: '请输入群名称......',
      qsryhmc: '请输入用户名称......',
      bsfs: '部署方式',
      xzbsfs: '选择部署方式',
      qyzjyy: '企业自建应用',
      yysdaz: '应用商店安装',
      aizy: 'AI资源',
      xzaizy: '选择AI资源',
      wcpz: '完成配置',
      ky: '可用',
      bky: '不可用',

      wkyzy: '无资源可选择？前往配置AI资源',
      qwpz: '前往配置',
      bzd: '不知道如何配置？联系客服获取帮助',
      tjkf: '点我添加客服',
      syb: '上一步',
      qxzaizy: '请选择AI资源......',
      knowledge: '知识库',
      knowledge_select_label: '知识库关联',
      knowledge_placeholder: '选择知识库',
      prompt_label: '提示词',
      prompt_tip: '提示词可以用来约束回答的方式和范围，更好的按照要求响应，可根据自己的需求进行调整',
      prompt_placeholder: '选择提示词'
    },
    header: {
      logout: '退出登录',
      bug: '内测BUG反馈',
      qp: '全屏',
      kysq: '开源社区',
      xqzz: '需求追踪',
      cprz: '产品日志',
      qhzt: '切换主题',
      yjfk: '意见反馈',

      // 退出弹框
      qdytc: '您确定要退出企联AI吗？',
      qrtc: '确认退出',
      qxcz: '取消操作',

      per: '个人版',
      team: '企业版',
      end: '到期',
      xf: '续费',
      up: '升级企业版',
      logger: '产品日志'
    },

    global: {
      nrqp: '内容全屏',
      cxjz: '重新加载',
      gb: '关闭',
      gbqt: '关闭其他',
      bgzc: '关闭左侧',
      bgyc: '关闭右侧',
      gbsy: '关闭所有',

      zzfw: '增值服务',
      pwdlogin: '账密登陆',
      codelogin: '手机验证码登录',
      register: '注册',
      repwd: '重置密码',
      wechat: '微信绑定',
      superadmin: '超级管理员',
      admin: '管理员',
      user: '普通用户',
      woman: '女',
      man: '男',
      use: '启用',
      ban: '禁用',
      ice: '冻结',
      delete: '删除',

      qtxemail: '请填写正确的邮箱',
      qtxuid: '请填写正确的帐号',
      mobiletip: '为了获得更好的体验，请使用PC设备访问我们的网站。',
      mobiletip2: '未来我们会支持移动端...',
      fzlj: '复制链接',
      yfz: '已复制'
    },

    msg: {
      // success
      sccg: '删除成功',
      gxcg: '更新成功',
      zdgxcg: '账单更新成功',
      zh: '账户',
      ltzc: '连通正常',
      cjcg: '创建成功',
      tjcg: '提交成功',
      bccg: '保存成功',
      fzcg: '复制成功',
      bscg: '部署成功',
      xgcg: '修改成功',
      addcg: '添加成功',
      drwc: '导入完成',
      gmcg: '购买成功',
      gxztcg: '更新状态成功',
      sxcg:'刷新成功',

      // error
      zdgxyc: '账单更新异常',
      ltyc: '连通异常',
      scsb: '删除失败',
      qtxwzxx: '请填写完整信息',
      zyglbt: '资源关联必填',
      fxztw: '风险主题不能为',
      wxzwj: '未选择文件',
      drsb: '导入失败',
      qzjtf: '请在具体分类下新建提示词！',
      czwjbj: '存在未经编辑的新增词条，请先编辑后再新增',
      zfsb: '支付失败，请重试',

      // loading
      hqzhye: '获取账户余额...',
      zhltxcs: '账户连通测试中...',

      // welcome
      xxtx: '消息提醒',
      hello: '哈喽！',
      hyjrql: '欢迎加入企联AI，应用市场又有新的AI机器人啦~',
      djqwck: '点击前往查看',

      qsrfwym: '请输入服务域名',
      qsrapi: '请输入API KEY'
    },

    bot: {
      sxw: '对话上下文',
      mrkq: '上下文默认开启，开启上下文有利于大模型更好到理解你的意图，但也意味着更多的费用消耗',
      save: '保存',
      ISee: '我知道了',
      ConfigTutorial: '配置教程',
      ckpz: '查看配置指南',
      websearch: '联网搜索',
      websearchinfo: '默认开启，开启联网搜索可以让 GPT 查询最新的信息，并提供更多最新和准确的答复',
      artifacts: 'Artifacts',
      artifactsinfo: '默认开启，开启Artifacts可以渲染和执行HTML代码',
      artifactsalert: '注意，开启前需进行如下配置,否则Artifacts功能无法正常使用',
      artifatsone: '1、复制以下链接',
      artifatstwo: '2、添加链接到重定向URL中，并保存'
    },

    tip: {
      tip1: '该功能为企业版功能',
      tip2: '升级企业版',
      wdy: '您还未完成订阅，暂时无法使用',
      sjbb: '升级版本以使用此高级功能',
      dyhsy: '该功能订阅后方可使用，请前往订阅页面进行订阅',
      lxkf: '联系客服',
      qx: '取消',
      qwsj: '前往升级',
      qwdy: '前往订阅'
    },

    openai: {
      openai: 'OpenAI 资源管理',
      azwr: 'Azure 微软',
      apift: 'Api2d 方糖',
      opaiwg: 'OpenAI 国外源',
      zhsytk: '账户剩余Token',
      zcmx: '支持模型',
      ljdymx: '累计调用次数',
      ljxhtk: '累计消耗Token',
      glaiyy: '关联AI应用',

      tyzhbg: '体验账户不够用? 联系我们，协助你快速拿到自己的Azure OpenAI企业账户',
      djsq: '点击申请',
      srndsjh: '输入您的手机号',

      sqzswr: '申请正式微软账户',
      jjzc: '即将支持',
      ltxcs: '连通性测试',
      sxzd: '刷新账单',
      zhnb: '内置账户不允许编辑',
      bj: '编辑',
      xxpzxx: '详细配置信息',
      qdysc: '确定要删除',
      qd: '确定',
      qx: '取消',

      // card
      sytoken: '账户剩余Token',
      ljdy: '累计调用次数',
      xhtoken: '累计消耗Token',
      zhye: '账户余额',
      ljsy: '累计使用',
      glyy: '关联应用:',

      fwms: '访问密匙',
      api2d: 'API2D账户名',
      api2dmm: 'API2D密码',
      kx: '可选',
      qwapi2d: '前往API2D官网充值',
      api2dzh: 'API2D OpenAI 账户',

      qwopenai: '前往OpenAI官网充值',
      ms: '密匙',
      openaizh: 'OpenAI 账户',

      xz: '新增',
      sc: '删除',

      fwym: '服务域名',
      ymqz: '域名前缀',
      apibb: 'API版本',
      mxmc: '模型名称',
      zhmc: '账户名称',
      bt: '必填',
      qsrzhmc: '请输入账户名称',
      mr1: '默认：openai.azure.com/openai/deployments',
      mr2: '默认：2023-03-15-preview',

      xzzh: '新增账户',
      bcxx: '保存信息',
      qsr: '请输入',
      bjzh: '编辑账户',
      aztitle: 'Azure OpenAI 账户'
    },

    log: {
      srfxc: '输入风险词......',
      ss: '搜索',
      xzfxzt: '新增风险主题',
      pldc: '批量导出',

      fxzt: '风险主题',
      fxc: '风险词',
      cz: '操作',
      sc: '删除',
      qrsc: '确认删除',
      qsrfxzt: '请输入风险主题',
      qsrfxc: '请输入风险词',
      jjsc: '即将删除风险主题',
      jjsc2: '及其包括的所有风险词',

      cxrz: '查询日志',
      anrgl: '按内容过滤',
      ayygl: '按应用名称过滤',
      yymc: '应用名称',
      yhm: '用户名',
      wt: '问题',
      hd: '回答',
      dymx: '调用模型',
      fxtw: '风险提问',
      fx: '风险词',
      cjsj: '创建时间',

      zl: '指令',
      nr: '内容',
      img: '图片',
      yl: '预览',
      yes: '是',
      no: '否',

      xzt: '新主题',
      xfxc: '新风险词',

      add: '新增',
      next: '下一步',
      bj: '编辑',
      sx: '上线',
      qsr: '请输入......',
      qd: '确定',
      qx: '取消',
      id: '编号',
      gjcl: '关键词量',
      zt: '状态',
      time: '创建时间',
      xx: '下线',

      name: '名称',
      path: '路径',
      type: '类型',
      action: '操作',
      delete: '删除',
      jjdelete: '即将删除',
      qrdelete: '确认删除',

      ayhm: '按用户名过滤',
      qsrenter:'请输入风险词并回车(可以添加多个)',
    },

    prompt: {
      bdbg: '本地表格',
      zxlj: '在线链接',
      pldrtsc: '批量导入提示词',
      djxz: '点击下载',
      mbbg: '模板表格',
      tzxz: '拖拽或选择上传表格文件',
      wtyzs: '我同意遵守',
      cpfwxy: '产品服务协议',
      ystk: '隐私条款',
      ksdr: '开始导入',
      czwc: '操作完成',
      qwwdtsc: '前往我的提示词查看',

      srcjgjz: '输入场景关键字......',
      ss: '搜索',
      xzcj: '新增场景',
      pldc: '批量导出',

      lb: '类别',
      bt: '标题',
      js: '介绍',
      nr: '内容',
      jl: '举例',
      cz: '操作',
      sc: '删除',
      qsrfx: '请输入风险主题',
      qsrjj: '请输入简介',
      qsrnr: '请输入内容',
      qsrjl: '请输入举例',
      qrsc: '确认删除',
      jjsc: '即将删除提示词',
      cjcmc: '<场景词—名称>',
      cjcms: '<场景词-描述>',
      cjcnr: '<场景词-内容>',
      cjcsl: '<场景词-示例>',

      qyy: '前一页',
      hyy: '后一页',
      hdsy: '回到首页',

      bj: '编辑',
      id: 'ID',
      bjcj: '编辑场景',
      qsr: '请输入......',
      qxz: '请选择'
    },

    ai: {
      srzy: '输入资源名称......',
      ss: '搜索',
      dpz: '待配置',
      ypz: '已配置',
      glyy: '关联应用',
      ye: '余额',
      cz: '充值',
      pz: '配置',
      fwym: '服务域名',
      qx: '取消',
      qd: '确定',
      qsr: '请输入......'
    },

    knowledge: {
      qsrzsk: '请输入知识库名称......',
      search: '搜索',
      newzsk: '新增知识库',
      xgzsk: '修改知识库',
      zskmc: '知识库名称',
      qsrzskmc: '请输入知识库名称',
      zskinfo: '知识库简介',
      qsrzskinfo: '请输入知识库简介',
      qx: '取消',
      qd: '确定',
      rename: '重命名',
      delete: '删除',
      doc: '文档',
      gl: '管理',
      qsrdoc: '请输入文件名......',
      mkdir: '添加本地文件',
      tip1: '选择文件添加后，文件将会被直接添加到知识库中，一次最多5个文件',
      tip2: '点击或者拖动文件到该区域来上传',
      tip3: '支持TXT、Markdown、PDF、Doc、Docx,每个文件不超过100MB',
      end: '结束添加，关闭窗口',
      close: '关闭窗口后，文件将在后台进行向量化，向量化完成后才可使用',
      warn: '警告',
      qdsc: '确定删除该知识库吗?',
      drcg: '导入成功',
      jxwc: '解析完成'
    },

    routes: {
      dashboard: {
        _value: '我的账户',
        zhqy: '账户权益',
        aizygl: 'AI资源管理',
        seats: '座席管理'
      },
      bot: {
        _value: 'AI应用管理',
        yysc: '应用市场',
        cyjqr: '常用机器人',
        wdyy: '我的应用',
        yyxx: '应用信息'
      },
      log: {
        _value: '对话管理',
        fxcgl: '风险词管理',
        dhrz: '对话日志'
      },
      prompt: {
        _value: '提示词管理',
        tscsc: '提示词市场',
        pldr: '批量导入提示词',
        wdtsc: '我的提示词'
      },
      knowledge: {
        _value: '知识库',
        zsksc: '知识库市场',
        wdzsk: '我的知识库',
        mxxl: '模型训练'
      },
      messenger: {
        _value: 'AI轻客服',
        info: {
          _value: 'AI轻客服配置'
        }
      }
    }
  }
};

export default locale;
