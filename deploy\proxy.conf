# proxy 配置

# HTTP 1.1 support
proxy_http_version 1.1;
proxy_buffering off;
proxy_set_header Host $http_host;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection $proxy_connection;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $proxy_x_forwarded_proto;
proxy_set_header X-Forwarded-Ssl $proxy_x_forwarded_ssl;
proxy_set_header X-Forwarded-Port $proxy_x_forwarded_port;
proxy_set_header X-Original-URI $request_uri;

# Mitigate httpoxy attack (see README for details)
proxy_set_header Proxy "";

proxy_read_timeout 600;
client_max_body_size 100m;

root /var/www/html;

server {
    listen 81 default_server;
    server_name _;

    location / {
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
    }
    location /upload { index index.html; }
    location /feishu {
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    location /dingding {
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    location /wework {
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    location /api {
        proxy_buffering off;
        proxy_set_header Host manager.connect.ai;
        proxy_pass http://manager.connect.ai;
    }
    location /api/file {
        proxy_buffering off;
        proxy_set_header Host know.connect.ai;
        proxy_pass http://know.connect.ai;
    }
    location /chat {
        proxy_set_header Host nchan.connect.ai;
        proxy_pass http://nchan.connect.ai;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}


server {
    listen 82 default_server;
    server_name _;

    location /files {
        root /home/<USER>/DataChat-API;
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
    }

    location / {
        proxy_set_header Host know.connect.ai;
        proxy_pass http://know.connect.ai;
    }
}
