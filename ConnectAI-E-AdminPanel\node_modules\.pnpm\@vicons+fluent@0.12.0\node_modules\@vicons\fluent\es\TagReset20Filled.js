import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M16.02 2.036a2 2 0 0 1 1.986 1.997l.008 4.95a2 2 0 0 1-.586 1.417l-.15.15A5 5 0 0 0 15 10h-1.085a1.5 1.5 0 0 0-2.476-1.56l-2 2a1.5 1.5 0 0 0 0 2.12l1.207 1.207c-.39.27-.646.722-.646 1.233a5 5 0 0 0 .536 2.252a2 2 0 0 1-2.567-.22l-4.949-4.95a2 2 0 0 1 .002-2.83l6.682-6.664a2 2 0 0 1 1.425-.584l4.89.032zM13 6a1 1 0 1 0 2 0a1 1 0 0 0-2 0zm-.146 3.854a.5.5 0 0 0-.708-.708l-2 2a.5.5 0 0 0 0 .708l2 2a.5.5 0 0 0 .708-.708L11.707 12H15a3 3 0 1 1-3 3a.5.5 0 1 0-1 0a4 4 0 1 0 4-4h-3.293l1.147-1.146z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagReset20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
