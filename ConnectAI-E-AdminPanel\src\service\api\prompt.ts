import { request } from '~/src/service/request';

export function fetchPromptCateList() {
  return request.get<ApiPrompt.Resp.PromptCateList>('/api/prompt/category');
}

export function fetchPromptList(params: ApiPrompt.Req.PromptList) {
  return request.get<ApiPrompt.Resp.PromptList>('/api/prompt', { params });
}

export function createPrompt(data: ApiPrompt.Req.Create) {
  return request.post('/api/prompt', data);
}
export function updatePrompt({ id, data }: ApiPrompt.Req.Update) {
  return request.put(`/api/prompt/${id}`, data);
}
export function deletePrompt({ id }: ApiPrompt.Req.Delete) {
  return request.delete(`/api/prompt/${id}`);
}

export function fetchPromptOptionList(data: ApiPrompt.Req.PromptCateList) {
  return request.post<ApiPrompt.Resp.PromptCateList>('/api/prompt/tree', data);
}
export function exportPromptList(data?: ApiPrompt.Req.Export) {
  return request.post<{ backend: any; disp: string }>('/api/prompt/export', data);
}
export function importExample(data?: null) {
  return request.post<{ backend: any; disp: string }>('/api/prompt/importExample', data);
}
export function importPromptList(data: FormData) {
  return request.post<{ backend: any; disp: string }>('/api/prompt/import', data);
}
