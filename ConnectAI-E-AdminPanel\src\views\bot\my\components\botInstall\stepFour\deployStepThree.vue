<template>
  <div>
    <h1 class="mb-2 text-2xl font-extrabold tracking-tight text-gray-900 leding-tight dark:text-white">
      {{ $t('message.my.jqrcjcg') }}
    </h1>
    <p v-if="data.platform === 'wework'" class="font-light text-gray-500 dark:text-gray-400 md:mb-6">{{ $t('message.my.wework_trusted_ip_title') }}</p>
    <div class="w-full mx-auto lg:ml-0 mb-6" v-if="data.platform === 'wework'">
      <div class="relative my-2">
        <input
          id="trusted_ip"
          type="search"
          readonly
          :value="data.trusted_ip"
          class="block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
          placeholder="x.x.x.x"
        />
        <n-tooltip placement="bottom" trigger="click">
          <template #trigger>
            <button
              class="copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              data-clipboard-target="#trusted_ip"
            >
              <icon-akar-icons-chat-add class="text-lg mr-2" />
              {{ t('message.my.wework_trusted_ip_label') }}
            </button>
          </template>
          <span>{{ t('message.my.yfz') }}</span>
        </n-tooltip>
      </div>
    </div>
    <p class="font-light text-gray-500 dark:text-gray-400 md:mb-6">{{ $t('message.my.fzxm') }}</p>
    <div class="w-full mx-auto lg:ml-0 mb-6">
      <div class="relative my-2">
        <input
          id="event"
          type="search"
          readonly
          :value="data.callback_url.event"
          class="block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
          placeholder="xxxx/webhook/event"
        />
        <n-tooltip placement="bottom" trigger="click">
          <template #trigger>
            <button
              class="copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              data-clipboard-target="#event"
            >
              <icon-akar-icons-chat-add class="text-lg mr-2" />
              {{ data.platform === 'feishu' ? t('message.my.fzsjhd') : t('message.my.fzxxjsdz') }}
            </button>
          </template>
          <span>{{ t('message.my.yfz') }}</span>
        </n-tooltip>
      </div>

      <div v-if="data.platform === 'feishu'" class="relative">
        <input
          id="card"
          type="search"
          readonly
          :value="data.callback_url.card"
          class="block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
          placeholder="xxxx/webhook/card"
        />
        <n-tooltip placement="bottom" trigger="click">
          <template #trigger>
            <button
              class="copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              data-clipboard-target="#card"
            >
              <icon-akar-icons-credit-card-alt1 class="text-lg mr-2" />
              {{ t('message.my.fzkphd') }}
            </button>
          </template>
          <span>{{ t('message.my.yfz') }}</span>
        </n-tooltip>
      </div>
    </div>
    <div class="mb-4 space-y-1">
      <div v-if="data.platform === 'feishu'" class="flex items-start">
        <div class="flex items-center h-5">
          <input
            id="terms"
            aria-describedby="terms"
            name="privacy"
            type="checkbox"
            class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
            required
          />
        </div>
        <div class="ml-3 text-sm">
          <label for="terms" class="font-light text-gray-500 dark:text-gray-300"
            >{{ $t('message.my.ytjjqr') }}
            <a class="font-medium text-primary-600 dark:text-primary-500 hover:underline" target="_blank">{{ $t('message.my.xxyd') }}</a>
            {{ $t('message.my.and') }}
            <a class="font-medium text-primary-600 dark:text-primary-500 hover:underline" target="_blank">{{ $t('message.my.jsxx') }}</a
            >.</label
          >
        </div>
      </div>
    </div>
    <a
      href="#"
      class="block w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 sm:py-3.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
      @click="handleReturn"
    >
    {{ t('message.my.wcpz') }}</a
    >
    <p class="mt-4 text-sm font-light text-gray-500 dark:text-gray-400">
      {{ t('message.my.xyjyb') }}
      <a href="#" class="font-medium text-blue-600 hover:underline dark:text-blue-500" @click="handleDetail"
        >{{ t('message.my.djqwjqr') }}</a
      >.
    </p>
  </div>
</template>

<script setup lang="ts">
import { onMounted, toRefs, inject } from 'vue';
import ClipboardJS from 'clipboard';
import { routeName } from '~/src/router';
import { useRouterPush } from '~/src/composables';
import {t} from '@/locales';
const close = inject<() => void>('close');

const props = defineProps<{
  data: ApiApp.AppClientBot;
  app: ApiApp.Application;
}>();
const { routerPush } = useRouterPush();

const { app } = toRefs(props);

function handleReturn() {
  close?.();
}
function handleDetail() {
  // 移除了这个跳转逻辑
  routerPush({ name: routeName('bot_info'), query: { id: app.value.id } });
}
onMounted(() => {
  // eslint-disable-next-line no-new
  new ClipboardJS('.copy-btn');
});
</script>

<style scoped></style>
