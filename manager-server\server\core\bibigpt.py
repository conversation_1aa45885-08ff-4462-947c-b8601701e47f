import json
import math
import logging
from typing import Any, Optional, List

import httpx
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chat_models.base import SimpleChatModel
from langchain.schema import BaseMessage, ChatResult, AIMessage, ChatGeneration


def format_time(seconds):
    minutes, seconds = divmod(seconds, 60)
    if seconds < 3600:
        return f'{minutes:02d}:{seconds:02d}'
    hours, minutes = divmod(minutes, 60)
    return f'{hours:02d}:{minutes:02d}:{seconds:02d}'


class BibiGPTClient(object):
    def __init__(self, api_base, api_key=''):
        self.api_base = api_base
        self.api_key = api_key

    def stream(self, url, timeout=600, **kwargs):
        req_url = '{}/api/open/{}'.format(self.api_base, self.api_key)
        headers = {'Content-Type': 'application/json'}
        action = kwargs.get('action')
        if action == 'chat':
            if not kwargs.get('question'):
                yield {'status': 'error'}
                return
            req_url += '/chat'
            params = {'url': url, 'question': kwargs['question']}
            if 'language' in kwargs:
                params.update({'language': kwargs['language']})
        elif action == 'subtitle':
            req_url += '/subtitle'
            params = {'url': url}
        else:
            params = {
                'url': url,
                'includeDetail': kwargs.get('includeDetail', False),
                'promptConfig': {
                    'showEmoji': True,
                    'showTimestamp': False,
                    'outlineLevel': 1,
                    'sentenceNumber': 5,
                    'detailLevel': 1000,
                    'outputLanguage': 'zh-CN',
                    'isRefresh': False,
                    **kwargs.get('promptConfig', {})
                }
            }
        logging.info('bibi req: %r %r', req_url, params)
        try:
            result = httpx.post(url=req_url, headers=headers, data=json.dumps(params), timeout=timeout)
            if result.status_code != 200:
                logging.error('create error: %r %r', result.url, result.status_code)
                yield {'status': 'error', 'reason': 'error code {}'.format(result.status_code)}
                return
            result = result.json()
            if not result.get('success'):
                logging.error('create error: %r', result)
                yield {'status': 'error', 'reason': result.get('errorMessage', '')}
                return
            yield result
        except Exception as e:
            logging.error('stream error: %r', e)
            yield {'status': 'error'}

    def create(self, stream=False, timeout=600, **kwargs):
        result = self.stream(timeout=timeout, **kwargs)
        if stream:
            return result
        else:
            return list(result).pop()


class BibiGPTChat(SimpleChatModel):
    client: Any
    api_base: Optional[str] = ''
    api_key: Optional[str] = ''
    streaming: bool = False

    def _llm_type(self) -> str:
        return 'bibigpt'

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        pass

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        params = {'stream': self.streaming, **kwargs}
        message = messages.pop()
        params.update({**message.additional_kwargs})
        self.client = BibiGPTClient(
            api_base=self.api_base,
            api_key=self.api_key
        )
        response = {}
        if self.streaming:
            for resp in self.client.create(**params):
                # if run_manager:
                #     run_manager.on_llm_new_token()
                response = resp
        else:
            response = self.client.create(**params)
        content = ''
        action = params.get('action', '')
        if action == 'chat':
            if 'answer' not in response:
                response['status'] = 'error'
            else:
                content = response['answer']
                response['answer'] = ''
        elif action == 'subtitle':
            if 'detail' not in response or 'subtitlesArray' not in response['detail']:
                response['status'] = 'error'
            else:
                content_lines = []
                subtitles = response['detail']['subtitlesArray'] or []
                subtitles.sort(key=lambda x: x['index'])
                for title in subtitles:
                    start = format_time(math.floor(title['startTime']))
                    # end字段可能不存在
                    if 'end' not in title:
                        line = '{} {}'.format(start, title['text'])
                    else:
                        end = format_time(math.floor(title['end']))
                        line = '{} - {} {}'.format(start, end, title['text'])
                    content_lines.append(line)
                content = '\n'.join(content_lines)
                response['detail']['subtitlesArray'] = []
        else:
            if 'summary' not in response:
                response['status'] = 'error'
            else:
                content = response['summary']
                response['summary'] = ''
        if response.get('status', '') == 'error':
            raise Exception(response.get('reason', 'error'))
        message = AIMessage(content=content, additional_kwargs=response)
        return ChatResult(generations=[ChatGeneration(message=message)])


if __name__ == '__main__':
    import asyncio

    async def main():
        api_base = ''
        api_key = ''
        from langchain.schema import HumanMessage
        chat = BibiGPTChat(
            api_base=api_base,
            api_key=api_key,
            streaming=True,
        )
        url = 'https://www.bilibili.com/video/BV1kH4y1U797/?share_source=copy_web'
        params = {
            'url': url,
            'includeDetail': True,
            'promptConfig': {
                'showEmoji': False,
                'outputLanguage': 'en-US',
                'isRefresh': True,
            }
        }
        messages = [HumanMessage(content=url, additional_kwargs=params)]
        result = chat(messages)
        print(result)

    asyncio.run(main())
