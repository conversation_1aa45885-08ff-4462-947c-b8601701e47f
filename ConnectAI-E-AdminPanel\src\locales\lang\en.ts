import type { LocaleMessages } from 'vue-i18n';

const locale: LocaleMessages<I18nType.Schema> = {
  message: {
    system: {
      title: 'Connect-AI',
      description:
        'Enterprise-level low-code AI platform, based on collaboration tools such as flying books, empowers every employee in the enterprise.',
      htgl: 'Connect-AI',
      fxai: 'AI You Can Trust for Enterprises',
      jdgx: 'An Easy and Efficient Enterprise AI App Orchestration Platform, with Self-managed Data and Seamless Integration with a Vast Array of AI Apps.',
      rbsbjd: 'Simplify Deployments, Empower Enterprises with Confidence.',
      dl: 'Please Sign In to Your Account',
      uid: 'Username',
      pwd: 'Password',
      yyd: 'I have read and agree to the ',
      fwxy: 'Terms of Service',
      ystk: 'Privacy Policy',
      login: 'Log In',
      myzh: "Don't have an account yet?",
      djsqcn: 'Click to Apply for Beta Access',
      and: 'and',

      kjdl: 'Quick Login',
      email: 'Email Registration',
      phone: 'Login with Phone Number',
      phoneid: 'Phone Number',
      code: 'Verification Code',
      dlzc: 'Login/Register',
      pwdlogin: 'Password Login',
      get: 'Get',
      lasttime: 'retry after ',
      phoneerror: 'Invalid phone number format!',
      nophone: 'Phone number cannot be empty!',
      emailerror: 'Invalid email format!',
      noemail: 'Email cannot be empty!',
      codesuccess: 'Verification code sent successfully!',
      qsremail: 'Please enter your email for registration',
      zc: 'Register',
      phoneoremail: 'Phone Number or Email',

      zznc: 'In Beta Testing, Coming Soon!',
      yy: 'No data found, take a break and go swimming.',
      lq: 'No data found, go play some basketball outside.',
      pb: 'No data found, go for a run outside.',
      tq: 'No data found, go play some soccer outside.',
      dq: 'No data found, go play some tennis outside.',

      djcs: 'Click to Retry',
      fhsy: 'Return to Homepage',

      teamname: 'Company/Team Name',
      qsrmc: 'At least two characters',
      qsrgsmc: 'Enter company name',
      kqaizl: 'Start the AI Journey',
      xgqyxx: 'Edit Company Information',
      tsinfo: 'Company information will be displayed in multiple places in the product, please modify it carefully',
      xgxx: 'Edit Information',
      xgxxinfo: 'Company information will be displayed in multiple places in the product, please modify it carefully',
      save: 'Save',
      qx: 'Cancel',
    },
    messenger: {
      xxsj: 'Connect-AI Information Collection',
      xxsjinfo: 'When enabled, customers need to provide their nickname and contact information before sending messages for the first time.',
      helpdoc: 'Help Document Configuration',
      helpinfo: "The help document will be displayed on the official website's customer service entrance for customers to preview and use.",
      qsrbt: 'Enter Title',
      qsrlj: 'Enter Link',
      lang: 'Language',
      zts: 'Theme Color',
      hyy: 'Welcome Message',
      jcpz: 'Basic Configuration',
      fsxxzf: 'Lark Message Forwarding Robot',
      fsinfo: 'Used to connect external customers and Lark customer service. After configuration, it needs to be associated with a topic group for use.',
      pz: 'Configuration',
      glfshtq: 'Associate with Lark Topic Group',
      dwsx: 'If there is no topic group to choose from, click here to refresh',
      save: 'Save',
      webpz: 'Official Website Web Configuration',
      webinfo: 'Used to receive customer inquiries on the official website.',
      ckpz: 'View Configuration',
      apipz: 'API Access Configuration',
      apiinfo: 'Connect via API',
      tit: 'Copy the code to',
      and: 'and',
      bql: 'tags in either way one or two',
      fs1: 'Way One',
      fs2: 'Way Two',
      fs3: 'Way Three',
      iframe: 'Embed using iframe',
      qd: 'OK',
      qx: 'Cancel',
      kqapi: 'Enable API',
      tjtxx: 'All interfaces require adding header information',
      hqxx: 'GET Get Configuration',
      fsxx: 'POST Send Message',
      fstp: 'POST Send Image',
      jdry: 'Receptionist Configuration',
      xzcy: 'Add Member',
      pzhinfo: 'After configuration, the system will automatically allocate corresponding members for replies. Before configuration, please confirm the completion of the basic configuration and the association with the corresponding Lark topic group.',
      tjkfinfo: 'Add customer service receptionist. When there is customer information, the system will allocate accordingly.',
      xzcytj: 'Select Members (Please make sure to associate with the topic group before adding)',
      kssj: 'Select Reception Start Time',
      jssj: 'Select Reception End Time',
      qsrxm: 'Enter Name',
      qsrnl: 'Enter Age',
      cyxm: 'Member Name',
      zt: 'Status',
      on: 'Enable',
      off: 'Disable',
      jdsjfw: 'Reception Time Range',
      bj: 'Edit',
      sc: 'Delete',
      qxglfs: 'Please associate with Lark Topic Group first',
      jycg: 'Successfully Disabled',
      qycg: 'Successfully Enabled',
      warn: 'Warning',
      qrsc: 'Are you sure you want to delete this member?',
      sccg: 'Successfully Deleted',
      sxtoken: 'Refreshing Token will cause the previous interfaces to be invalid. Are you sure you want to refresh?',
      title: 'Starting a New Era of Intelligent Customer Service',
      xzaikf: 'Add New Lark AI Customer Service',
      kf: 'Customer Service',
      alert: 'Please fill in the customer service channel name for easier identification later',
      kfmc: 'Customer Service Name',
      qsr: 'Please Enter',
      kfms: 'Customer Service Description',
      kfqd: 'Customer Service Channel',
      gw: 'Official Website',
      dykfz: 'TikTok (Under Development)',
      dy: 'TikTok',
      aikfpz: 'AI Customer Service Configuration',
      yqyai: 'AI Capability Enabled',
      ygbai: 'AI Capability Disabled',
      pztitle: 'After configuration, the system will prioritize AI responses to customer inquiries, transferring to human   operators only when necessary or requested by the customer',
      larkinfo: 'Used for automated customer inquiries responses; requires knowledge base linkage after configuration',
      glzsk: 'Link Knowledge Base',
      aizygl: 'AI Resource Association',
  },
    dashboard: {
      pzsc: 'Resource Allocation Manual',
      freeuse: 'Free Trial',
      seven: '7-Day Free Trial',
      ljsq: 'Apply Now',
      // pricing
      title: 'Empower Members with Connect-AI',
      zfwt: 'Apply for a trial/Encountering Payment Issues',
      title2: 'Choose the Right Solution According to Your Needs',
      month: 'seasonally',
      year: 'Yearly',
      yh: 'Extra 20% Discount',
      lxwm: 'Contact Us',
      xw: 'Seat',
      xwm: 'Seat/Month',
      gcyh: 'Enjoy Exclusive Co-Creation Discount',
      yj: 'Original Price',
      ljlx: 'Contact Now',
      buy: 'Buy Now',
      wechat: 'WeChat Pay',
      alipay: 'Alipay',
      openwechat: 'Open WeChat, Scan QR Code, and Complete Payment',
      price: 'Payment Amount',
      tip: 'If you are upgrading a version, the unused package will be automatically deducted as credit',
      rmb: 'RMB',
      zhmc: 'Account Name',
      gsmc: 'Wuhan Qini Technology Co., Ltd.',
      zhhm: 'Account Number',
      bankid: 'Bank ID',
      bank: 'Bank Information',
      bankname: 'Industrial Bank Co., Ltd. Wuhan Hanzheng Street Branch',
      payinfo: 'After completing the payment, add customer service WeChat (left) / Lark (right) for confirmation',
      suc: 'Payment Successful, Start Your AI Journey Now',
      info1:
        'Due to the complexity of the config, please carefully review the usage tutorial before use. If you encounter any problems during the config, feel free to contact customer service for assistance.',
      info2: 'Click here to add customer service',
      aishop: 'Visit AI App Market',
      ckjc: 'View Usage Tutorial',
      qsrbsmc: 'Please Enter Deployment Name',

      zxzf: 'Online Payment',
      dgzz: 'Corporate Bank Transfer',
      pldr: 'Batch Import',
      import_by_extension: 'Install plugins and quickly import members',
      search_placeholder: 'Please input',
      addseat: 'Add Seat',
      auto_add_seat:
        'After enabling automatic member addition, the system will automatically add newly used employees to the seat without the need for manual addition',
      disable_auto_add_seat: 'Automatically add members:',
      name_label: 'Name',
      name_validate: 'Please input name',
      max_seats_label: 'Used seats',
      more_seats_label: 'Increase seats',
      telephone_label: 'Telephone',
      department_label: 'Department',
      status_label: 'Status',
      status_paused: 'Paused',
      status_actived: 'Actived',
      status_removed: 'Removed',
      status_active: 'Active',
      status_pause: 'Pause',
      remove_seat: 'Remove Seat',
      upload_confirm: 'Confirm Member Import',
      upload_title: 'Bulk Import',
      upload_tip: 'Click or drag the file here to upload',
      template_lable: 'Please fill in accordance with the template',
      unkown: 'Unkown',
      out1: 'AI related applications require the consumption of token,',
      out2: 'with approximately 350 images generated per 10 million tokens and 100,000 conversations.',
      choose: 'Please choose at least one model.',
      fillin: 'Fill in the deployment name.',
      zfcg: 'Payment Successful',
      zfsb: 'Payment Failed, Please Retry'
    },
    market: {
      feishuai: 'Lark-OpenAI',
      feishu: 'Lark',
      bot: 'Bot',
      free: 'Free',
      ljbs: 'Deploy Now',
      jjfb: 'Coming Soon',
      dingdingai: 'DingTalk-OpenAI',
      dingding: 'DingTalk',
      feishumj: 'Lark-Midjourney',
      feishuwxyylt: 'Lark-ERNIE Chatbot',
      wxyy: 'ERNIE',
      gptswbs: 'ChatGPT Private Deployment',
      web: 'Web',
      feishuzsk: 'Lark-Internal Knowledge Base',
      feishukypl: 'Lark IELTS Speaking Partner',
      feishupdf: 'Lark-PDF Assistant for Reading',
      feishuxxss: 'Lark-Internet Information Search',
      feishukefu: 'Lark-Customer Service Q&A',
      feishujiaoyu: 'Lark-Education Problem Solving',
      wechatai: 'WeChat Work-OpenAI',
      wechat: 'WeChat Work',
      feishuxnpl: 'Lark-Virtual Chat Companion',
      feishusd: 'Lark-StableDiffusion',
      feishubgzs: 'Lark-Table Assistant',
      dwbg: 'Multidimensional Table',
      lunwenfuzhu: 'Academic Paper Assistance',
      buy: 'Buy',
      az: 'Install Now',
      gl: 'Manage App',
      xznxhd: 'Choose your favorite 🤞 AI App',
      sryymc: 'Enter the App name...',
      ss: 'Search',
      author: 'Author',
      River: 'AI: The Preferred Means of Production for Advanced Teams.',
      sqsjgd: 'Apply for more AI Apps to be listed.',
      ljhq: 'Get Now',
      ckxq: 'View Details',
      ljaz: 'install now',
      ljpz: 'Configure Now',
      wado: 'Watch Demo',
      copy_success: 'Share link copy success'
    },
    my: {
      qrcodedeploy: 'Scan QR Code for Deployment',
      wxwork_auth_title: 'WeChat Auth',
      wxwork_auth_success: 'WeChat Auth Success',
      wework_trusted_ip_title: 'Copy the following IP address and add it to the trusted IP of the enterprise',
      wework_trusted_ip_label: 'Copy IP Address',
      wsxx: 'Please Complete the Information',
      xggd: 'Browse More',
      xjyy: 'Create New App',
      name: 'Name',
      lx: 'Type',
      all: 'All',
      feishu: 'Lark',
      dingding: 'DingTalk',
      web: 'Web',
      dhrz: 'Chats Logs',
      scyy: 'Delete App',
      qwbs: 'Go to Deployment',
      xq: 'Details',
      nqdysc: 'Are you sure you want to delete this app?',
      qd: 'Confirm',
      qx: 'Cancel',

      czz: 'In Progress',
      yxz: 'Running',
      dpz: 'To be Configured',
      yty: 'Disabled',

      bsaiyy: 'Deploy AI Apps',
      xzygr: 'Select a purchased AI app and deploy it quickly to your server',
      scxz: 'Last Selection',
      qwaisd: 'Go to the store for more AI apps',

      jqrqxgl: 'Bot Permissions Management',
      xzkyq: 'Limit Available Groups',
      gdltjqr: 'Specify in which group chats the chatbot is available',
      yxqlmd: 'Allowed Group Chats List',
      xzslsy: 'Limit Private Chats Usage',
      gdltjqrsl: 'Specify in which p2p chats the chatbot is available.',
      yxslmc: 'Allowed Private Chats List',
      dgyhy: 'Multiple users separated by commas (English)',
      dgqmy: 'Multiple group names separated by commas (English)',
      bcqx: 'Save Permissions',
      jjzc: 'Coming Soon',
      ckdhrz: 'View Chats Logs',
      zwnc: 'No Name',
      zwms: 'No Description',
      zygl: 'Resource Association',
      qnxz: 'Please select an account',
      xzmx: 'Select a model',
      fxcgl: 'Risk Word Association',
      qnxzfxc: 'Please select a risk word topic',
      cjcgl: 'Scene Word Association',
      bczy: 'Save Resources',
      wxyy: 'ERNIE',

      cjyh: 'Super User',
      cyhbs: 'This user is not restricted by permission management',
      mrgly: 'Default Administrator',
      jz: 'Prohibit',
      drgly: 'Secondary Administrator',
      qy: 'Enable',

      // creat
      fh: 'Back',
      jqrcjzn: 'Robot Creation Guide',
      djqwfs: 'Click to go to Lark Developer Console',
      djqwdd: 'Click to go to DingTalk Developer Console',
      fs1: 'Click Create App, select Custom App',
      fs2: 'Add App Capability - Robot',
      fs3: 'Open Permission Management - Check All Permissions in Messages and Group Chats',
      fs4: 'Open Permission Management - Allow Uploading Image Resources',
      fs5: 'Open Permission Management - Get basic information from the address book',
      fs6: 'Go to Basic Information - Copy App Credential',
      fs7: 'Go to Event Subscription - Copy Encryption Key',
      fs8: 'Event Subscription - Configure Request Address',
      fs9: 'Robot - Configure Message Card Request Address',
      fs10: 'Add Event Subscription - Messages and Group Chats - Message Read',
      fs11: 'Add Event Subscription - Messages and Group Chats - Receive Messages',
      fs12: 'Open Permission Management - View Knowledge Base',
      fs13: 'Open Permission Management - View Latest Documents',
      fs14: 'Open permission management - view board nodes',

      dd1: 'Go to DingTalk Developer Console',
      dd2: 'Create Robot, select Custom App',
      dd3: 'Fill in basic information for the Robot',
      dd4: 'Fill in App Credential as required for the Robot',
      dd5: 'Development Management - Message Receive Address - Configure Robot',
      dd6: 'Version Management - Release the App',

      wework_tutior_title: 'Go to the enterprise WeChat developer backend',
      wework1: 'Go to the enterprise WeChat application management backend',
      wework2: 'Create an application and select a self built enterprise application',
      wework3: 'Fill in the basic information of the application',
      wework4: 'Fill in the basic voucher information for the application',
      wework5: 'Receive messages - Set API to receive links and credentials',
      wework6: 'Configure Enterprise Trusted IP',
      wework7: 'Application launch',

      cjjqr: 'Create Robot',
      txpzxx: 'Config Info',
      hqhddz: 'Get Callback Address',

      sfwc: 'Is it completed?',
      yjbs: 'One-Click Deployment',
      xxyx: 'Take a break, create it later',
      wyjwc: 'I have completed the creation',
      xyb: 'Next Step',
      yjwcjqr: 'Has the Robot config been completed?',
      djhqhd: 'Click to directly get the callback address',
      plugin: 'Install the browser plugin for one-click deployment of AI applications (recommended method)',
      zxqwkfz: 'Visit the developer platform to create applications on your own',
      ndfszs: 'Your Lark AI Assistant, boosting your work efficiency',

      zyxz: 'Resource Selection',
      wyjty: 'I have read and agreed to the Connect-AI Product',
      fwxy: 'Terms of Service',
      ystk: 'Privacy Policy',
      jxxyb: 'Continue to the Next Step',

      jqrcjcg: 'Robot Creation Successful',
      fzxm: 'Copy the following callback address to the Developer Console',
      fzsjhd: 'Copy Event Callback',
      fzxxjsdz: 'Copy Message Receive Address',
      yfz: 'Copied',
      fzkphd: 'Copy Card Callback',
      ytjjqr: 'Required event subscriptions for the Robot have been added:',
      xxyd: 'Message Read',
      jsxx: 'Receive Messages',
      fhyylb: 'Return to App List',
      xyjyb: 'Need further config?',
      djqwjqr: 'Click to go to Robot Details Page',

      // feishu
      fsjqrpz: 'Lark Robot config',
      jqrmc: 'Robot Name',
      cfspthq: 'Get from Lark Developer Console',
      bcxx: 'Save Information',

      hddz: 'Callback Address',
      djqwtx: 'Click to Fill In',
      sjhd: 'Event Callback',
      and: 'and',
      kphd: 'Card Callback',
      djfz: 'Click to Copy',

      // dingding
      ddjqrpz: 'DingTalk Robot config',
      cddpthq: 'Get from DingTalk Developer Console',
      xxjsdz: 'Message Receive Address',

      // new
      fwpz: 'Service config',
      ypz: 'Configured',
      cxpz: 'Reconfigure',
      xgpz: 'Modify config',
      txjqrpz: 'Fill in Robot config Information',
      kphddz: 'Card Callback Address',
      bcpz: 'Save config',
      bc: 'Save',

      xzsfky: 'Select Availability...',
      qsrqmc: 'Enter Group Name...',
      qsryhmc: 'Enter User Name...',
      bsfs: 'Deployment Method',
      xzbsfs: 'Select Deployment Method',
      qyzjyy: 'Enterprise Custom App',
      yysdaz: 'App Store Installation',
      aizy: 'AI Resources',
      xzaizy: 'Select AI Resources',
      wcpz: 'Finish config',
      ky: 'Allow',
      bky: 'Deny',

      wkyzy: 'No available resources? Go to configure AI resources.',
      qwpz: 'config',
      bzd: 'Not sure how to configure? Contact support for assistance.',
      tjkf: 'Add Customer Support',
      syb: 'Previous Step',
      qxzaizy: 'Please select AI resources...',
      knowledge: 'Knowledge',
      knowledge_select_label: 'Knowledge',
      knowledge_placeholder: 'Choose Knowledge',
      prompt_label: 'Prompt',
      prompt_tip:
        "Prompt can be used to constrain the way and scope of response, better responding to requirements, and can be adjusted according to one's own needs",
      prompt_placeholder: 'Choose Prompt'
    },

    header: {
      logout: 'Logout',
      bug: 'Feedback for Connect-AI Beta Bugs',
      qp: 'Full Screen',
      kysq: 'Open Source Community',
      xqzz: 'Demand Tracking',
      cprz: 'Product Log',
      qhzt: 'Switch Theme',
      yjfk: 'Feedback',
      // 退出弹框
      qdytc: 'Are you sure you want to logout of Connect-AI?',
      qrtc: 'Confirm Logout',
      qxcz: 'Cancel',

      per: 'Personal Edition',
      team: 'Business Edition',
      end: 'Expiration',
      xf: 'Renew',
      up: 'Upgrade to Enterprise Edition',
      logger: 'Product Logs'
    },
    global: {
      nrqp: 'Full Screen Content',
      cxjz: 'Reload',
      gb: 'Close',
      gbqt: 'Close Others',
      bgzc: 'Close to the Left',
      bgyc: 'Close to the Right',
      gbsy: 'Close All',
      zzfw: 'Premium',
      pwdlogin: 'Login with Username and Password',
      codelogin: 'Login with Phone Verification Code',
      register: 'Register',
      repwd: 'Reset Password',
      wechat: 'Bind with WeChat',
      superadmin: 'Super Administrator',
      admin: 'Administrator',
      user: 'Regular User',
      woman: 'Female',
      man: 'Male',
      use: 'Enable',
      ban: 'Disable',
      ice: 'Freeze',
      delete: 'Delete',

      qtxemail: 'Please enter a valid email address',
      qtxuid: 'Please enter a valid account',
      mobiletip: 'For a better experience, please access our website using a PC device.',
      mobiletip2: 'We will be supporting mobile devices in the future...',
      fzlj: 'Copy Link',
      yfz: 'Copied'
    },

    msg: {
      // success
      sccg: 'Deletion successful',
      gxcg: 'Update successful',
      zdgxcg: 'Bill update successful',
      zh: 'Account',
      ltzc: 'Connected normally',
      cjcg: 'Creation successful',
      tjcg: 'Submission successful',
      bccg: 'Save successful',
      fzcg: 'Copy successful',
      bscg: 'Deployment successful',
      xgcg: 'Modification successful',
      addcg: 'Addition successful',
      drwc: 'Import completed',
      gmcg: 'Purchase successful',
      gxztcg: 'Status update successful',
      sxcg: 'Refresh successful',

      // error
      zdgxyc: 'Bill update exception',
      ltyc: 'Connect exception',
      scsb: 'Deletion failed',
      qtxwzxx: 'Please fill in all information',
      zyglbt: 'Resource association is required',
      fxztw: 'Risk theme cannot be',
      wxzwj: 'File not selected',
      drsb: 'Import failed',
      qzjtf: 'Please create prompts under specific categories!',
      czwjbj: 'There are unedited new entries. Please edit them before adding.',
      zfsb: 'Payment failed, please try again',

      // loading
      hqzhye: 'Fetching account balance...',
      zhltxcs: 'Account connectivity testing...',

      // welcome
      xxtx: 'Message Reminder',
      hello: 'Hello!',
      hyjrql: 'Welcome to Connect-AI! There are new AI robots in the App market~',
      djqwck: 'Click to view',

      qsrfwym: 'Please enter the service domain',
      qsrapi: 'Please enter the API KEY'
    },

    bot: {
      sxw: 'Context of the Conversation',
      mrkq: 'Context Enabled by Default: Enabling context helps larger models better understand your intent, but it also implies higher cost consumption.',
      save: 'Save',
      ISee: 'I see',
      ConfigTutorial: 'ConfigTutorial',
      ckpz: 'Config Guide',
      websearch: 'Web Search',
      websearchinfo: 'Enabled by default, enabling web search allows GPT to query the latest information and provide more up-to-date and accurate responses.',
      artifacts: 'Artifacts',
      artifactsinfo: 'Enabled by default, enabling artifacts allows render HTML code.',
      artifactsalert:'Note: The following configuration is required before enabling, otherwise the Artifacts function will not work properly.',
      artifatsone: '1、Copy the link below',
      artifatstwo: '2、Add the link to the redirect URL and save it'
    },

    tip: {
      tip1: 'This feature is available in the Connect-AI Enterprise Edition.',
      tip2: 'Upgrade to Enterprise Edition',
      wdy: 'Your subscription is incomplete, and the feature is temporarily unavailable.',
      sjbb: 'Upgrade to a higher version to access this advanced feature.',
      dyhsy: 'This feature requires a subscription. Please visit the subscription page to subscribe.',
      lxkf: 'Contact Customer Support',
      qx: 'Cancel',
      qwsj: 'Upgrade Now',
      qwdy: 'Subscribe Now'
    },

    openai: {
      openai: 'OpenAI Resource Management',
      azwr: 'Azure Microsoft',
      apift: 'Api2d Fangtang',
      opaiwg: 'OpenAI Overseas Source',
      zhsytk: 'Account Remaining Tokens',
      zcmx: 'Supported Models',
      ljdymx: 'Total Call Count',
      ljxhtk: 'Total Token Consumed',
      glaiyy: 'Associated AI Apps',

      tyzhbg:
        'Need more tokens for your trial account? Contact us to quickly obtain your own Azure OpenAI enterprise account',
      djsq: 'Click to Apply',
      srndsjh: 'Enter your mobile number',

      sqzswr: 'Apply for Formal Microsoft Account',
      jjzc: 'Coming Soon',
      ltxcs: 'Connectivity Test',
      sxzd: 'Refresh Bill',
      zhnb: 'Built-in accounts cannot be edited',
      bj: 'Edit',
      xxpzxx: 'Detailed config Information',
      qdysc: 'Are you sure you want to delete',
      qd: 'Confirm',
      qx: 'Cancel',

      // card
      sytoken: 'Account Remaining Tokens',
      ljdy: 'Total Call Count',
      xhtoken: 'Total Token Consumed',
      zhye: 'Account Balance',
      ljsy: 'Total Used',
      glyy: 'Associated Apps',

      fwms: 'Access Key',
      api2d: 'API2D Account Name',
      api2dmm: 'API2D Password',
      kx: 'Optional',
      qwapi2d: 'Go to API2D Official Website to Top-up',
      api2dzh: 'API2D OpenAI Account',

      qwopenai: 'Go to OpenAI Official Website to Top-up',
      ms: 'Secret Key',
      openaizh: 'OpenAI Account',

      xz: 'Add New',
      sc: 'Delete',

      fwym: 'Service Domain',
      ymqz: 'Domain Prefix',
      apibb: 'API Version',
      mxmc: 'Model Name',
      zhmc: 'Account Name',
      bt: 'Required',
      qsrzhmc: 'Enter Account Name',
      mr1: 'Default: openai.azure.com/openai/deployments',
      mr2: 'Default: 2023-03-15-preview',

      xzzh: 'Add New Account',
      bcxx: 'Save Information',
      qsr: 'Please enter',
      bjzh: 'Edit Account',
      aztitle: 'Azure OpenAI Account'
    },

    log: {
      srfxc: 'Enter risk term...',
      ss: 'Search',
      xzfxzt: 'Add New Risk Theme',
      pldc: 'Export',

      fxzt: 'Risk Theme',
      fxc: 'Risk Term',
      cz: 'Actions',
      sc: 'Delete',
      qrsc: 'Confirm Deletion',
      qsrfxzt: 'Enter risk theme...',
      qsrfxc: 'Enter risk term...',
      jjsc: 'Will delete risk theme',
      jjsc2: 'and all its associated risk terms.',

      cxrz: 'Query Logs',
      anrgl: 'Filter by Content',
      ayygl: 'Filter by App Name',
      yymc: 'App Name',
      yhm: 'Username',
      wt: 'Question',
      hd: 'Answer',
      dymx: 'Model Used',
      fxtw: 'Risk Questions',
      fx: 'Risk Term',
      cjsj: 'Create Time',

      zl: 'Command',
      nr: 'Content',
      img: 'Image',
      yl: 'Preview',
      yes: 'Yes',
      no: 'No',

      xzt: 'New Theme',
      xfxc: 'New Risk Term',

      add: 'Add',
      next: 'Next',
      bj: 'Edit',
      sx: 'Online',
      qsr: 'Please enter...',
      qd: 'Confirm',
      qx: 'Cancel',
      id: 'ID',
      gjcl: 'Keyword Count',
      zt: 'Status',
      time: 'Create Time',
      xx: 'Offline',

      name: 'Name',
      path: 'Path',
      type: 'Type',
      action: 'Action',
      delete: 'Delete',
      jjdelete: 'About to Delete',
      qrdelete: 'Confirm Delete',

      ayhm: 'Filter by Username',
      qsrenter:'Please enter risk words and press enter (multiple inputs allowed).',
    },
    prompt: {
      bdbg: 'Local Table',
      zxlj: 'Online Link',
      pldrtsc: 'Batch Import prompts',
      djxz: 'Click to Download',
      mbbg: 'template',
      tzxz: 'Drag and Drop or Select to Upload Table File',
      wtyzs: 'I Agree to Abide by',
      cpfwxy: 'Product Service Agreement',
      ystk: 'Privacy Policy',
      ksdr: 'Start Importing',
      czwc: 'Operation Completed',
      qwwdtsc: 'Go to my prompts to View',
      srcjgjz: 'Enter scene keywords...',
      ss: 'Search',
      xzcj: 'Add Scene',
      pldc: 'Export in Batch',
      lb: 'Category',
      bt: 'Title',
      js: 'Introduction',
      nr: 'Content',
      jl: 'Example',
      cz: 'Operation',
      sc: 'Delete',
      qsrfx: 'Please enter the risk topic',
      qsrjj: 'Please enter the introduction',
      qsrnr: 'Please enter the content',
      qsrjl: 'Please enter an example',
      qrsc: 'Confirm deletion',
      jjsc: 'Prompt for imminent deletion',
      cjcmc: '<Scene Word - Name>',
      cjcms: '<Scene Word - Description>',
      cjcnr: '<Scene Word - Content>',
      cjcsl: '<Scene Word - Example>',
      qyy: 'Previous Page',
      hyy: 'Next Page',
      hdsy: 'Go back to Homepage',

      bj: 'Edit',
      id: 'ID',
      bjcj: 'Edit Scene',
      qsr: 'Please enter...',
      qxz: 'Please select'
    },
    ai: {
      srzy: 'Enter Resource Name...',
      ss: 'Search',
      dpz: 'To Be Configured',
      ypz: 'Configured',
      glyy: 'Apps:',
      ye: 'BAL',
      cz: 'Recharge',
      pz: 'Config',
      fwym: 'Service Domain',
      qx: 'Cancel',
      qd: 'Confirm',
      qsr: 'Please Enter...'
    },
    knowledge: {
      qsrzsk: 'Enter Knowledge Base Name...',
      search: 'Search',
      newzsk: 'Create New Knowledge Base',
      xgzsk: 'Edit Knowledge Base',
      zskmc: 'Knowledge Base Name',
      qsrzskmc: 'Enter Knowledge Base Name',
      zskinfo: 'Knowledge Base Description',
      qsrzskinfo: 'Enter Knowledge Base Description',
      qx: 'Cancel',
      qd: 'Confirm',
      rename: 'Rename',
      delete: 'Delete',
      doc: 'Document',
      gl: 'Manage',
      qsrdoc: 'Enter File Name...',
      mkdir: 'Add Local File',
      tip1: 'When you select to add files, they will be directly added to the knowledge base. You can add up to 5 files at once.',
      tip2: 'Click or drag files to this area to upload.',
      tip3: 'Supported formats: TXT, Markdown, PDF, Doc, Docx; Each file should not exceed 100MB.',
      end: 'Finish adding and close the window.',
      close:
        'After closing the window, files will be processed in the background for vectorization. They will be usable after vectorization is complete.',
      warn: 'Warning',
      qdsc: 'Are you sure you want to delete this knowledge base?',
      drcg: 'Import successful',
      jxwc: 'Parsing completed'
    },

    routes: {
      dashboard: {
        _value: 'My account',
        zhqy: 'Account Equity',
        aizygl: 'AI Resources',
        seats: 'Seats'
      },
      bot: {
        _value: 'Apps',
        yysc: 'App Market',
        cyjqr: 'Frequently Used Bots',
        wdyy: 'My Apps',
        yyxx: 'App Information'
      },
      log: {
        _value: 'Chats',
        fxcgl: 'Risk Term',
        dhrz: 'Chat Log'
      },
      prompt: {
        _value: 'Prompts',
        tscsc: 'Word Market',
        pldr: 'Batch Import',
        wdtsc: 'My Prompts'
      },
      knowledge: {
        _value: 'Knowledge',
        zsksc: 'Knowledge Market',
        wdzsk: 'My Knowledge',
        mxxl: 'Model Training'
      },
      messenger: {
        _value: 'AI Customer Service',
        info: {
          _value: 'AI Customer Service Configuration'
        }
      }
    }
  }
};

export default locale;
