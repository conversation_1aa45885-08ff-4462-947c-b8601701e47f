import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.318 5.54A2 2 0 0 1 18.684 5H25.5A1.5 1.5 0 0 1 27 6.5v6.757a2 2 0 0 1-.586 1.415l-.365.365c.533.41.98.926 1.312 1.516l.467-.467A4 4 0 0 0 29 13.257V6.5A3.5 3.5 0 0 0 25.5 3h-6.816a4 4 0 0 0-2.732 1.079L3.77 15.474a4.25 4.25 0 0 0-.101 6.11l6.326 6.325a4.252 4.252 0 0 0 5.054.72a4.032 4.032 0 0 1-.05-.629v-1.914l-.408.409a2.25 2.25 0 0 1-3.182 0l-6.326-6.326a2.25 2.25 0 0 1 .054-3.234l12.18-11.396zM22.5 12a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zm-3 7.5H19a2.5 2.5 0 0 0-2.5 2.5v6a2.5 2.5 0 0 0 2.5 2.5h8a2.5 2.5 0 0 0 2.5-2.5v-6a2.5 2.5 0 0 0-2.5-2.5h-.5V19a3.5 3.5 0 1 0-7 0v.5zm2-.5a1.5 1.5 0 1 1 3 0v.5h-3V19zm3.5 6a2 2 0 1 1-4 0a2 2 0 0 1 4 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TagLock32Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
