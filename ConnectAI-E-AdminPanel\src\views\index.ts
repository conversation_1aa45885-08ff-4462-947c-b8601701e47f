import type { RouteComponent } from 'vue-router';

export const views: Record<
  PageRoute.LastDegreeRouteKey,
  RouteComponent | (() => Promise<{ default: RouteComponent }>)
> = {
  403: () => import('./_builtin/403/index.vue'),
  404: () => import('./_builtin/404/index.vue'),
  500: () => import('./_builtin/500/index.vue'),
  'constant-page': () => import('./_builtin/constant-page/index.vue'),
  login: () => import('./_builtin/login/index.vue'),
  'not-found': () => import('./_builtin/not-found/index.vue'),
  'share-app': () => import('./_builtin/share-app/index.vue'),
  bot_common: () => import('./bot/common/index.vue'),
  bot_info: () => import('./bot/info/index.vue'),
  bot_market: () => import('./bot/market/index.vue'),
  bot_my: () => import('./bot/my/index.vue'),
  dashboard_ai: () => import('./dashboard/ai/index.vue'),
  dashboard_pricing: () => import('./dashboard/pricing/index.vue'),
  dashboard_seats: () => import('./dashboard/seats/index.vue'),
  document_naive: () => import('./document/naive/index.vue'),
  'document_project-link': () => import('./document/project-link/index.vue'),
  document_project: () => import('./document/project/index.vue'),
  document_vite: () => import('./document/vite/index.vue'),
  document_vue: () => import('./document/vue/index.vue'),
  knowledge_app: () => import('./knowledge/app/index.vue'),
  knowledge_appInfo: () => import('./knowledge/appInfo/index.vue'),
  knowledge_import: () => import('./knowledge/import/index.vue'),
  knowledge_info: () => import('./knowledge/info/index.vue'),
  knowledge_market: () => import('./knowledge/market/index.vue'),
  knowledge_my: () => import('./knowledge/my/index.vue'),
  log_chat: () => import('./log/chat/index.vue'),
  log_image: () => import('./log/image/index.vue'),
  log_word: () => import('./log/word/index.vue'),
  management_auth: () => import('./management/auth/index.vue'),
  management_role: () => import('./management/role/index.vue'),
  management_route: () => import('./management/route/index.vue'),
  management_user: () => import('./management/user/index.vue'),
  messenger_info: () => import('./messenger/info/index.vue'),
  messenger_list: () => import('./messenger/list/index.vue'),
  prompt_importexport: () => import('./prompt/importexport/index.vue'),
  prompt_market: () => import('./prompt/market/index.vue'),
  prompt_my: () => import('./prompt/my/index.vue')
};
