//vitest
import { describe, expect, it } from "vitest";
import type { FeishuLoginCookies } from "~utils/open-feishu-api/configuration";
import { Configuration } from "./configuration";


const testConfig: FeishuLoginCookies = {
  lark_oapi_csrf_token: "JUQuwxLaNvWvKCQRr6VYl4ioez87XNyMVr7epcAr8tw=",
  session: "XN0YXJ0-7ceg07d1-ee7e-4907-a552-37e240qpn4fv-WVuZA",
  // baseUrl: "https://open.larksuite.com"
};
describe("configuration", () => {
  const aClient = new Configuration(testConfig);

  it("should be able to get configuration", () => {
    expect(aClient).toBeDefined();
    expect(aClient.baseUrl).toBe("https://open.larksuite.com");
  });

  it("should can be request", async () => {
    await aClient.processCsrfToken();
    console.log(aClient.csrfToken);
    expect(aClient.csrfToken).not.toBeUndefined();
  });

  it("should can be request", async () => {
    expect(await aClient.requestBaseApp()).not.toBeUndefined();
  });
  it("should can get avatar", async () => {
    const userInfo = await aClient.getUserInfo();
    console.log(userInfo);
    expect(userInfo.avatar).not.toBeUndefined();
  });
});


